{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"],
    "paths": {
      "@/*": ["src/*"]
    },
    "skipLibCheck": true,
    "types": ["node", "unplugin-vue-router/client", "@intlify/unplugin-vue-i18n/messages"],
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "importHelpers": true,
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
    "baseUrl": ".",

    /* Linting */
    "strictNullChecks": false,
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "esModuleInterop": true
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
