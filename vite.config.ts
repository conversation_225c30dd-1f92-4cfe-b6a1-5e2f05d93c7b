import path from 'node:path'
import { loadEnv } from 'vite'
import type { ConfigEnv, UserConfig } from 'vite'
import viewport from 'postcss-mobile-forever'
import autoprefixer from 'autoprefixer'
import { createVitePlugins } from './build/vite'

// https://vitejs.dev/config/
export default ({ mode, command }: ConfigEnv): UserConfig => {
  const root = process.cwd()
  const env = loadEnv(mode, root)
  const { VITE_APP_ENV, VITE_APP_PUBLIC_PATH } = env

  return {
    base: VITE_APP_PUBLIC_PATH,
    plugins: createVitePlugins(env, command === 'build'),
    esbuild: {
      pure: VITE_APP_ENV === 'test' ? [] : ['console.log']
    },
    server: {
      host: true,
      port: 8080,
      proxy: {
        '/api': {
          // target: 'https://livco.me',
          target: 'https://livco-dev.wujialin.top/',
          ws: false,
          changeOrigin: true
          // rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    resolve: {
      alias: {
        '~@': path.join(__dirname, './src'),
        '@': path.join(__dirname, './src'),
        '~': path.join(__dirname, './src/assets')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import 'src/assets/styles/variables';`
        }
      },
      postcss: {
        plugins: [
          autoprefixer(),
          // https://github.com/wswmsword/postcss-mobile-forever
          viewport({
            appSelector: '#app',
            viewportWidth: 375,
            maxDisplayWidth: 500,
            propertyBlackList: ['border', 'border-radius', 'clip-path'],
            selectorBlackList: ['loading-phone-call', 'guide-border', 'loading-bar'],
            rootContainingBlockSelectorList: ['van-tabbar', 'van-popup', 'van-popover', 'top-bar']
          })
        ]
      }
    }
  }
}
