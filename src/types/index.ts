export type StringKeyValueTType<T> = {
  [key: string]: T
}

export interface PaginationPayload {
  page: number
  limit: number
}

export interface INormalResult<T> {
  result: T[]
  count: number
}

export interface INormalList<T> {
  list: T[]
  count: number
}

export interface INormalResultNoneArr<T> {
  result: T
  count: number
}
export interface IRequestPageNormalParams {
  page: number
  limit: number
}
