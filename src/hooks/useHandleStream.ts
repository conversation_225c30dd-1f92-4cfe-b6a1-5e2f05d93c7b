// import { ContentGameRecordType, EventRecordType, IContentRes } from '@/api/contentGame/types.ts'
// import { StoryRecordType, StoryRecordTypeEnum } from '@/enums'
// import { INIT_QUERY } from '@/constant'
//
// export enum DifyErrorCode {
//   INTERNAL_ERROR = 500
// }
//
// export type IOnDataMoreInfo = {
//   conversation_id?: string
//   task_id?: string
//   message_id: string
// }
//
// export type MessageEnd = {
//   conversation_id: string
//   message_id: string
//   created_at: number
//   task_id: string
//   id: string
//   metadata: {
//     usage: {
//       prompt_tokens: number
//       prompt_unit_price: string
//       prompt_price_unit: string
//       prompt_price: string
//       completion_tokens: number
//       completion_unit_price: string
//       completion_price_unit: string
//       completion_price: string
//       total_tokens: number
//       total_price: string
//       currency: string
//       latency: number
//     }
//   }
// }
// export type AudioData = {
//   data: {
//     audio: string
//     status: number
//     ced: string
//   }
//   trace_id: string
//   base_resp: {
//     status_code: number
//     status_msg: string
//   }
//   extra_info?: {
//     audio_length: number
//     audio_sample_rate: number
//     audio_size: number
//     bitrate: number
//     word_count: number
//     invisible_character_ratio: number
//     usage_characters: number
//     audio_format: string
//     audio_channel: number
//   }
// }
// export type IOnVoid = () => void
// export type IOnData = (message: string, moreInfo: IOnDataMoreInfo, isFirstMessage?: boolean) => void
// export type IOnAudioToText = (input_query: string) => void
// export type IOnAudioData = (data: AudioData, isEffectiveAudio: boolean) => void
// export type IOnVideo = (video_id: number, conversationID: string) => void
// export type IOnSegmentationMessage = (message: string, conversationID: string, isFirstMessage?: boolean, pass?: boolean) => void
// export type IOnMaterialData = (material_id: number, conversationID: string, isFirstMessage: boolean) => void
// export type IOnImage = (material_id: number, conversationID: string, isFirstMessage: boolean) => void
// export type IOnNpc = (npc_id: number, message: string, conversationID: string) => void
// export type IOnContinue = (message: string, conversationID: string) => void
// export type IOnEnd = (end_id: number, conversationID: string) => void
// export type IOnTryAgain = (query: string, conversationID: string) => void
// export type IOnIntimacyInfo = (intimacy_level: number, intimacy_now: number, intimacy_upgrade: number, intimacy_interest_tags: string[]) => void
// export type IOnPhoneTimeBalance = (phone_time_balance: number) => void
// export type IOnMessageEnd = (messageEnd?: MessageEnd) => void
// // export type IOnMessageReplace = (messageReplace: MessageReplace) => void
// export type IOnCompleted = (hasError?: boolean, errorMessage?: string) => void
// export type IOnError = (msg: string, code?: number) => void
// export type IOnNormalResponse = (code: number, data: any, msg: string) => void
// export interface StreamFunctionType {
//   onData?: IOnData
//   onCompleted?: IOnCompleted
//   onMessageEnd?: IOnMessageEnd
//   onAudioToText?: IOnAudioToText
//   onAudioData?: IOnAudioData
//   onNoneIdentify?: IOnVoid
//   onIntimacyInfo?: IOnIntimacyInfo
//   onPhoneTimeBalance?: IOnPhoneTimeBalance
//   onFakeComment?: IOnSegmentationMessage
//   onNarration?: IOnSegmentationMessage
//   onMine?: IOnSegmentationMessage
//   onAgent?: IOnSegmentationMessage
//   onIntoFreeTalk?: IOnVoid
//   onBackground?: IOnSegmentationMessage
//   onVideo?: IOnVideo
//   onMaterial?: IOnMaterialData
//   onImage?: IOnImage
//   onNpc?: IOnNpc
//   onContinue?: IOnContinue
//   onEnd?: IOnEnd
//   onFinish?: IOnVoid
//   onTryAgain?: IOnTryAgain
//   onError?: IOnError
//   onStreamError?: IOnError
//   onNormalResponse?: IOnNormalResponse
// }
//
// export const FAKE_COMMENT_START_FLAG = '♭'
// export const FAKE_COMMENT_END_FLAG = '𝄽'
//
// export const useHandleStream = (
//   response: ReadableStream,
//   {
//     onData,
//     onCompleted,
//     onMessageEnd,
//     onAudioToText,
//     onAudioData,
//     onNoneIdentify,
//     onIntimacyInfo,
//     onPhoneTimeBalance,
//     onFakeComment,
//     onNarration,
//     onMine,
//     onAgent,
//     onIntoFreeTalk,
//     onBackground,
//     onVideo,
//     onMaterial,
//     onImage,
//     onNpc,
//     onContinue,
//     onEnd,
//     onTryAgain,
//     onFinish,
//     onError,
//     onStreamError,
//     onNormalResponse
//   }: StreamFunctionType
// ) => {
//   console.log(response)
//   const decoder = new TextDecoder('utf-8')
//   const reader = response.getReader()
//   let isFirstMessage = true
//   let isFirstFakeComment = true
//   let isEffectiveAudio = true
//   let buffer = ''
//   let bufferObj: Record<string, any>
//   const read = () => {
//     let hasError = false
//     reader
//       ?.read()
//       .then((result: any) => {
//         if (result.done) {
//           console.log('done')
//           onCompleted && onCompleted()
//           return
//         }
//         buffer += decoder.decode(result.value, { stream: true })
//         const lines = buffer.split('\n')
//         // console.log(lines, 'line')
//         try {
//           lines.forEach((message) => {
//             if (message.startsWith('data: ')) {
//               // check if it starts with data:
//               try {
//                 bufferObj = JSON.parse(message.substring(6)) as Record<string, any> // remove data: and parse as json
//               } catch (e) {
//                 // mute handle message cut off
//                 // console.warn(e)
//                 return
//               }
//               console.log(bufferObj)
//               if (bufferObj.event === 'message' || bufferObj.event === 'agent_message') {
//                 // can not use format here. Because message is splitted.
//                 onData(
//                   bufferObj.answer,
//                   {
//                     conversation_id: bufferObj.conversation_id,
//                     task_id: bufferObj.task_id,
//                     message_id: bufferObj.message_id
//                   },
//                   isFirstMessage
//                 )
//                 isFirstMessage = false
//               }
//               // 普通聊天
//               else if (bufferObj.event === 'message_end') {
//                 onMessageEnd?.(bufferObj as MessageEnd)
//               } else if (bufferObj.event === 'intimacy_info') {
//                 onIntimacyInfo?.(bufferObj.intimacy_level, bufferObj.intimacy_now, bufferObj.intimacy_upgrade, bufferObj.intimacy_interest_tags)
//               } else if (bufferObj.event === 'phone_time_balance') {
//                 onPhoneTimeBalance?.(bufferObj.phone_time_balance)
//               } else if (bufferObj.event === 'audio-to-text') {
//                 onAudioToText?.(bufferObj.input_query)
//               }
//               // 假两句
//               else if (bufferObj.event === 'fake_comment') {
//                 onFakeComment(bufferObj.answer, bufferObj.conversation_id, isFirstFakeComment)
//                 isFirstFakeComment = false
//               } else if (bufferObj.event === 'no_identify') {
//                 isEffectiveAudio = false
//                 onNoneIdentify?.()
//               } else if (bufferObj.trace_id) {
//                 console.log(bufferObj)
//                 onAudioData(bufferObj as AudioData, isEffectiveAudio)
//               }
//               // 内容玩法与接待引导
//               else if (bufferObj.event === 'narration') {
//                 onNarration?.(bufferObj.answer, bufferObj.conversation_id, isFirstMessage)
//                 isFirstMessage = false
//               } else if (bufferObj.event === 'mine') {
//                 onMine?.(bufferObj.answer, bufferObj.conversation_id, isFirstMessage)
//                 isFirstMessage = false
//               } else if (bufferObj.event === 'disconnect') {
//                 onAgent?.(bufferObj.answer, bufferObj.conversation_id, isFirstMessage, bufferObj?.pass)
//                 isFirstMessage = false
//               } else if (bufferObj.event === 'free_talk') {
//                 onIntoFreeTalk?.()
//               } else if (bufferObj.event === 'video') {
//                 onVideo?.(bufferObj.video_id, bufferObj.conversation_id)
//                 isFirstMessage = false
//               } else if (bufferObj.event === 'material') {
//                 onMaterial?.(bufferObj.material_id, bufferObj.conversation_id, isFirstMessage)
//               } else if (bufferObj.event === 'image_material') {
//                 onImage?.(bufferObj.material_id, bufferObj.conversation_id, isFirstMessage)
//               } else if (bufferObj.event === 'npc') {
//                 onNpc?.(bufferObj.npc_id, bufferObj.answer, bufferObj.conversation_id)
//               } else if (bufferObj.event === 'continue') {
//                 onContinue?.(bufferObj.answer, bufferObj.conversation_id)
//                 isFirstMessage = false
//               } else if (bufferObj.event === 'ending') {
//                 onEnd?.(bufferObj.ending_id, bufferObj.conversation_id)
//               } else if (bufferObj.event === 'background') {
//                 onBackground?.(bufferObj.answer, bufferObj.conversation_id)
//               } else if (bufferObj.event === 'try_again') {
//                 onTryAgain?.(bufferObj.query, bufferObj.conversation_id)
//               } else if (bufferObj.event === 'finish') {
//                 onFinish?.()
//               } else if (bufferObj.event === 'error') {
//                 onStreamError?.(bufferObj.message, bufferObj.status)
//               }
//             } else if (message.startsWith('{')) {
//               console.log(message)
//               try {
//                 bufferObj = JSON.parse(message) as Record<string, any> // remove data: and parse as json
//               } catch (e) {
//                 // mute handle message cut off
//                 console.warn(e)
//                 return
//               }
//               onNormalResponse?.(bufferObj.code, bufferObj.data, bufferObj.msg)
//             }
//           })
//           buffer = lines[lines.length - 1]
//         } catch (e: any) {
//           hasError = true
//           console.warn(e)
//           onError?.(e)
//           return
//         }
//         if (!hasError) read()
//       })
//       .catch((err) => {
//         console.warn(err)
//       })
//   }
//   read()
// }
//
// export const handleFakeCommentRecord = (input: string) => {
//   // 使用正则表达式匹配以♭开头，𝄽结尾的部分
//   const regex = /♭(.*?)𝄽/gs
//   let match
//   const matches = []
//   while ((match = regex.exec(input)) !== null) {
//     matches.push(match[1].trim()) // 去除前后空格后加入数组
//   }
//   // 如果没有匹配到任何内容，则将整个文本作为一条消息
//   if (matches.length === 0) {
//     matches.push(input)
//   }
//   console.log(matches, 'sdsd')
//
//   return matches
// }
//
// export const handleCacheChat = (input: string) => {
//   return input.split('|')
// }
//
// export const handleStoryRecord = (
//   data: ContentGameRecordType[],
//   storyMessage?: IContentRes
// ): {
//   record: StoryRecordType[]
//   isFreeTalk: boolean
// } => {
//   let isFreeTalk = false
//   const processEvent = (event: string, item: EventRecordType): StoryRecordType | undefined => {
//     switch (event) {
//       case 'narration':
//         return { type: StoryRecordTypeEnum.NARRATION, content: item.answer || '' }
//       case 'mine':
//         return { type: StoryRecordTypeEnum.USER, content: item.answer || '' }
//       case 'disconnect':
//         return {
//           type: StoryRecordTypeEnum.AGENT,
//           content: item.answer || '',
//           avatar_url: storyMessage?.ai_avatar_url,
//           skip_text: item.pass
//         }
//       case 'material':
//         return {
//           type: StoryRecordTypeEnum.VIDEO,
//           content: storyMessage?.video_list[Number(item.material_id) - 1]?.path || '',
//           video_id: storyMessage?.video_list[Number(item.material_id) - 1]?.video_id || ''
//         }
//       case 'image_material':
//         return {
//           type: StoryRecordTypeEnum.IMAGE,
//           content: storyMessage?.image_list[Number(item.material_id) - 1]?.path || ''
//         }
//       case 'npc':
//         return {
//           type: StoryRecordTypeEnum.NPC,
//           content: item.answer,
//           avatar_url: storyMessage?.prefix_url + storyMessage?.avatar_list[item.npc_id - 1]
//         }
//       case 'free_talk':
//         isFreeTalk = !isFreeTalk
//         return undefined
//       case 'continue':
//         return {
//           type: StoryRecordTypeEnum.USER,
//           content: item.answer,
//           isContinue: true
//         }
//       case 'ending':
//         return {
//           type: StoryRecordTypeEnum.REWARD,
//           content: '',
//           giftType: storyMessage?.reward_type,
//           giftNum: storyMessage?.reward_value
//         }
//       case 'background':
//         return {
//           type: StoryRecordTypeEnum.BACKGROUND,
//           content: item.answer
//         }
//       default:
//         return undefined
//     }
//   }
//
//   const arr = data
//     .map((item) => {
//       if ((item.input_type === 1 || item.input_type === 2) && item.input_query !== INIT_QUERY) {
//         item.output_answer_event.unshift({
//           event: 'mine',
//           answer: item.input_query
//         })
//       }
//       return item.output_answer_event
//     })
//     .reverse()
//     .flat(1)
//     .map((item) => processEvent(item.event, item))
//     .filter(Boolean) // 过滤掉 undefined 的项
//
//   if (arr[arr.length - 1]?.type === StoryRecordTypeEnum.REWARD) {
//     if (storyMessage.get_reward) {
//       arr.pop()
//     }
//     arr.push({
//       type: StoryRecordTypeEnum.END,
//       content: storyMessage?.win_text
//     })
//   }
//
//   return {
//     record: arr,
//     isFreeTalk
//   }
// }
