import { ref, onUnmounted, computed, readonly } from 'vue'

export enum VideoType {
  STANDBY = 'standby',
  TRANSITION = 'transition'
}

type VideoElement = Record<VideoType | string, HTMLVideoElement>
type EnvironmentVideoRecord = Record<string, VideoElement>
type pushVideoType = {
  type: VideoType | string
  url: string
}

export function useVideoPreload() {
  const videoElements = ref<EnvironmentVideoRecord>({})
  const videoLoadingStatus = ref<Record<string, 'loading' | 'loaded' | 'error'>>({})
  const loadedCount = ref(0)
  const totalCount = ref(0)

  const emotionMap = ref<Record<string, number>>({})
  const nowEmotion = ref('normal')
  const cd = 5

  const preloadVideos = (videoUrls: pushVideoType[], environment: string) => {
    totalCount.value = videoUrls.length
    loadedCount.value = 0
    videoLoadingStatus.value = {}

    videoUrls.forEach((videoItem, index) => {
      const video = document.createElement('video')
      video.className = 'chat-video'
      video.src = videoItem.url
      video.preload = 'auto'
      video.muted = true
      video.playsInline = true
      video.loop = videoItem.type === VideoType.STANDBY
      video.style.opacity = '0'

      videoLoadingStatus.value[videoItem.url] = 'loading'

      video.addEventListener('loadeddata', () => {
        console.log(`Video ${index + 1} loaded successfully.`)
        videoLoadingStatus.value[videoItem.url] = 'loaded'
        loadedCount.value++
        // Remove the video element after loading to free up resources
      })

      video.addEventListener('error', (event) => {
        console.error(`Failed to load video ${index + 1}:`, event)
        videoLoadingStatus.value[videoItem.url] = 'error'
        loadedCount.value++
      })
      videoElements.value[environment] = videoElements.value[environment] || {}
      videoElements.value[environment][videoItem.type] = video
    })
  }

  const abortAllLoads = () => {
    // videoElements.value.forEach((video) => {
    //   video[Object.keys(video)[0]].pause() // Pause the video to stop loading
    //   video[Object.keys(video)[0]].src = '' // Clear the source to stop loading
    //   video[Object.keys(video)[0]].remove() // Remove the video element from the DOM
    // })
    // videoElements.value = []
    // videoLoadingStatus.value = {}
    // loadedCount.value = 0
    // totalCount.value = 0
    // console.log('All video loads aborted.')
  }

  const emotionCdController = (emotion: string) => {
    // 无表情
    if (!emotion) return false
    if (emotion === 'normal') return false
    // 第一次触发该表情
    if (!emotionMap.value[emotion]) {
      emotionMap.value[emotion] = 1
      nowEmotion.value = emotion
      return true
    }

    // 如果换了一个新表情，立即触发，并重置计数
    if (nowEmotion.value !== emotion) {
      nowEmotion.value = emotion
      emotionMap.value[emotion] = 1
      return true
    }

    // 如果是同一个表情，增加计数
    emotionMap.value[emotion]++

    // 当累计次数达到 cd 时，触发并重置计数
    if (emotionMap.value[emotion] > cd) {
      emotionMap.value[emotion] = 1
      return true
    }

    // 还在冷却中，不触发
    return false
  }

  const isAllLoaded = computed(() => {
    return loadedCount.value === totalCount.value && totalCount.value > 0
  })

  const loadingProgress = computed(() => {
    return totalCount.value > 0 ? (loadedCount.value / totalCount.value) * 100 : 0
  })

  // 尝试注册组件卸载时的清理，如果在组件上下文中的话
  try {
    onUnmounted(() => {
      abortAllLoads()
    })
  } catch (error) {
    // 如果不在组件上下文中，onUnmounted 会抛出错误，这里忽略
    console.warn('useVideoPreload: 不在组件上下文中，请手动调用 abortAllLoads() 进行清理')
  }

  return {
    preloadVideos,
    abortAllLoads,
    emotionCdController,
    videoLoadingStatus: readonly(videoLoadingStatus),
    loadedCount: readonly(loadedCount),
    totalCount: readonly(totalCount),
    isAllLoaded,
    loadingProgress,
    videoElements
  }
}
