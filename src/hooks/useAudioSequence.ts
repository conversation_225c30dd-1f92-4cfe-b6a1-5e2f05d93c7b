import { ref, onUnmounted } from 'vue'
import { Howl } from 'howler'

/**
 * 音频序列播放器 Hook - 支持按顺序播放多个音频URL
 * 当新的音频序列到达时，会中断当前播放的序列
 */
export function useAudioSequence() {
  // 当前的Howl实例
  const currentHowl = ref<Howl | null>(null)

  // 当前播放的URL数组
  const currentUrlSequence = ref<string[]>([])

  // 当前播放索引
  const currentIndex = ref(0)

  // 是否正在播放
  const isPlaying = ref(false)

  /**
   * 播放指定索引的音频
   */
  const playAtIndex = (index: number) => {
    // 检查索引是否有效
    if (index >= currentUrlSequence.value.length) {
      isPlaying.value = false
      return
    }

    // 更新当前索引
    currentIndex.value = index

    // 停止之前的音频
    if (currentHowl.value) {
      currentHowl.value.stop()
      currentHowl.value.unload()
    }

    // 获取当前要播放的URL
    const audioUrl = currentUrlSequence.value[index]

    // 创建新的Howl实例
    currentHowl.value = new Howl({
      src: [audioUrl],
      html5: true, // 对于流媒体或较大文件建议启用
      onend: () => {
        // 当前音频播放完毕，播放下一个
        playAtIndex(currentIndex.value + 1)
      },
      onloaderror: () => {
        console.error(`音频加载错误: ${audioUrl}`)
        // 出错时跳到下一个
        playAtIndex(currentIndex.value + 1)
      }
    })

    // 开始播放
    currentHowl.value.play()
    isPlaying.value = true
  }

  /**
   * 播放新的URL序列，会中断当前播放的序列
   * @param urlSequence 音频URL数组
   */
  const playUrlSequence = (urlSequence: string[]) => {
    // 检查是否有有效的URL
    if (!urlSequence || urlSequence.length === 0) return

    // 停止当前播放
    if (currentHowl.value) {
      currentHowl.value.stop()
      currentHowl.value.unload()
      currentHowl.value = null
    }

    // 设置新的URL序列
    currentUrlSequence.value = [...urlSequence]
    currentIndex.value = 0
    isPlaying.value = false

    // 开始播放第一个
    playAtIndex(0)
  }

  /**
   * 停止当前播放
   */
  const stop = () => {
    if (currentHowl.value) {
      currentHowl.value.stop()
      currentHowl.value.unload()
      currentHowl.value = null
    }
    isPlaying.value = false
  }

  // 在组件卸载时自动清理资源
  onUnmounted(() => {
    stop()
  })

  return {
    // 状态
    currentUrlSequence,
    currentIndex,
    isPlaying,

    // 方法
    playUrlSequence,
    stop
  }
}
