import { createVNode, render } from 'vue'
import Modal from '@/components/Modal.vue'

interface UseModalOptions {
  message: string
  duration?: number
  autoClose?: boolean
  loading?: boolean
  onClose?: () => void
}

export function useModal({ message, duration = 1000, autoClose = true, onClose, loading = false }: UseModalOptions) {
  const div = document.createElement('div')
  document.body.appendChild(div)

  const destroy = () => {
    // 移除组件和 DOM 节点
    render(null, div)
    document.body.removeChild(div)
  }

  const vnode = createVNode(Modal, {
    message,
    duration,
    autoClose,
    loading,
    onClose: () => {
      if (onClose) onClose()
      destroy()
    }
  })

  render(vnode, div) // 渲染组件
  vnode.component?.exposed?.show()

  if (!autoClose) {
    return { close: vnode.component?.exposed?.close }
  }
}
