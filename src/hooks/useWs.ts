import { useWebSocket, UseWebSocketReturn } from '@vueuse/core'
import useUserStore from '@/stores/modules/user.ts'

// 定义消息类型接口
interface WsMessage {
  type: string
  data: any
}

type MessageMatcher = (data: any) => boolean
interface SubscribeOptions {
  matcher: MessageMatcher
  handler: (data: any) => void
}

class WebSocketClient {
  private ws: UseWebSocketReturn<any> | null = null
  private readonly url: string
  private subscribers: SubscribeOptions[] = []

  constructor(url: string) {
    this.url = url
  }

  connect() {
    if (this.ws) {
      return
    }

    const userStore = useUserStore()
    this.ws = useWebSocket(this.url, {
      immediate: true,
      heartbeat: {
        message: JSON.stringify({
          client_id: userStore.userInformation.client_id,
          type: 'online'
        }),
        interval: 1000,
        pongTimeout: 3000
      },
      autoReconnect: {
        retries: 3,
        delay: 1000,
        onFailed: () => {
          console.log('WebSocket Reconnection failed')
          this.close()
          this.ws = null
        }
      },
      onConnected: () => {
        console.log('WebSocket connected')
      },
      onError: (error) => {
        console.error('WebSocket error:', error)
      },
      onMessage: (_, event) => {
        try {
          const message: WsMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          this.handleMessage(event.data)
        }
      },
      onDisconnected: () => {
        console.log('WebSocket disconnected')
      }
    })
  }

  // 处理接收到的消息
  private handleMessage(data: any) {
    // 遍历所有订阅者，如果 matcher 匹配则执行对应的 handler
    this.subscribers.forEach(({ matcher, handler }) => {
      if (matcher(data)) {
        handler(data)
      }
    })
  }

  // 新的订阅方法
  subscribe(matcher: MessageMatcher, handler: (data: any) => void) {
    const subscription = { matcher, handler }
    this.subscribers.push(subscription)

    // 返回取消订阅的函数
    return () => {
      const index = this.subscribers.indexOf(subscription)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  send(message: unknown) {
    if (!this.ws || this.ws.status.value !== 'OPEN') {
      // throw new Error('WebSocket is not connected')
      return
    }
    this.ws.send(typeof message === 'string' ? message : JSON.stringify(message))
  }

  close() {
    console.log('ws close')
    this.ws?.close()
    this.ws = null
    this.subscribers = []
  }

  status() {
    return this.ws?.status.value
  }
}

export default new WebSocketClient(import.meta.env.VITE_APP_WS_PATH)
