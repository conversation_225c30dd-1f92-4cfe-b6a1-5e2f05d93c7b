import { createVNode, render } from 'vue'
import Confirm from '@/components/ConfirmDialog.vue'

interface UseConfirmOptions {
  content: string
  title?: string
  cancelText: string
  confirmText: string
  onClose?: () => void
  onConfirm?: () => void
}

export function useConfirm({ content, title, cancelText, confirmText, onClose, onConfirm }: UseConfirmOptions) {
  const div = document.createElement('div')
  document.body.appendChild(div)

  const destroy = () => {
    // 移除组件和 DOM 节点
    render(null, div)
    document.body.removeChild(div)
  }

  const vnode = createVNode(Confirm, {
    content,
    title,
    cancelText,
    confirmText,
    onClose: () => {
      if (onClose) onClose()
      destroy()
    },
    onConfirm: () => {
      if (onConfirm) onConfirm()
      destroy()
    }
  })

  try {
    render(vnode, div) // 渲染组件
    vnode.component?.exposed?.show() // 显示组件
  } catch (error) {
    console.error('Error rendering Confirm dialog:', error)
    destroy()
  }
}
