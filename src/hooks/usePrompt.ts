import { createVNode, render } from 'vue'
import PromptDialog from '@/components/PromptDialog.vue'

interface UsePromptOptions {
  title?: string
  message?: string
  placeholder?: string
  confirmText?: string
  cancelText?: string
  onClose?: () => void
  onConfirm?: (value: string) => void
}

export function usePrompt({
  title = '',
  message = '',
  placeholder = '',
  confirmText = '确定',
  cancelText = '取消',
  onClose,
  onConfirm
}: UsePromptOptions): Promise<string> {
  return new Promise((resolve, reject) => {
    const div = document.createElement('div')
    document.body.appendChild(div)

    const destroy = () => {
      // 移除组件和 DOM 节点
      render(null, div)
      document.body.removeChild(div)
    }

    const vnode = createVNode(PromptDialog, {
      title,
      message,
      placeholder,
      confirmText,
      cancelText,
      onClose: () => {
        if (onClose) onClose()
        destroy()
        reject(new Error('User cancelled'))
      },
      onConfirm: (value: string) => {
        if (onConfirm) onConfirm(value)
        destroy()
        resolve(value)
      }
    })

    try {
      render(vnode, div) // 渲染组件
      vnode.component?.exposed?.show() // 显示组件
    } catch (error) {
      console.error('Error rendering Prompt dialog:', error)
      destroy()
      reject(error)
    }
  })
}
