declare module 'vue-router' {
  interface RouteMeta {
    /** page title */
    title?: string
    /** i18n key */
    i18n?: string
    /** keepalive */
    keepAlive?: boolean
  }
}

declare global {
  interface Window {
    JsAndroid: {
      finishWebView: () => void;
      clickRouter: (path: string) => void;
      jumpOutBrowser: (path: string) => void;
      saveImage: (url: string) => void;
      requestVideoPermission: () => void;
    };
    OG_H5_GAME_SDK: {
      config: (options: Record<string, unknown>) => void;
      preLoadInterstitial: ({ placementId: string }) => void;
      showInterstitial: ({ placementId: string }) => void;
      getStreamVolume: () => void;
      loadAds: ({id: string}) => void;
      showAds: ({id: string}) => void;
    };
    /**
     * 1：广告播放成功
     * 2：广告加载成功
     * 3：广告加载失败
     * 4：广告播放失败
     * 5：关闭了广告
     * 6：点击了广告
     * 7：广告播放结束
     */
    ug_showInterstitial: ({ i: number }) => void;
    ug_loadAds: ({ status: number }) => void;
    ug_showAds: ({ type: number }) => void;
    /**
     *
     * / 激励成功 1
     * // 广告播放中点击了广告内容 2
     * // 被关闭 3
     * // 已完播 6
     * / 开始播放 7
     * // 播放失败 8
     */
    ug_getStreamVolume: ({ volume: number }) => void;
    ug_backShowInterstitialAd: () => void;
    clickRouter: (path: string) => void;
  }
}

export {}
