import useAppStore from '@/stores/modules/app.ts'
import { getAdvInfo } from '@/api/chat'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { AdvType } from '@/api/chat/types.ts'
import { useModal } from '@/hooks/useModal.ts'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { i18n } from '@/utils/i18n.ts'

export const useAdStore = defineStore('ad', {
  state: () => ({
    insetAdID: 'n67ea09c7242d0',
    incentiveAdID: 'n67fc7e324cc29',
    adLoading: false,
    adRequesting: false,
    insetStatus: false,
    incentiveStatus: false
  }),
  actions: {
    async checkAdStatus(type: AdvType) {
      const { code, data } = await getAdvInfo()
      if (code === ResultEnum.SUCCESS) {
        console.log(data, 'checkAdStatus')
        if (data[type].status === 1) {
          return {
            status: data[type].status,
            adType: data[type].type
          }
        }
      }
      return undefined
    },
    loadInsetAd() {
      const appStore = useAppStore()
      if (!appStore.isAndroid) return
      this.adLoading = true
      window.OG_H5_GAME_SDK.config({})
      window.OG_H5_GAME_SDK.preLoadInterstitial({
        placementId: this.insetAdID
      })
      this.insetStatus = true
      this.adLoading = false
      window.ug_showInterstitial = ({ i }) => {
        switch (i) {
          case 1:
            break
          case 2:
            this.adLoading = false
            break
          case 3:
            break
          case 4:
            break
          case 5:
            break
          case 6:
            break
          case 7:
            break
        }
      }
    },
    loadIncentiveAd() {
      console.log('my LoadIncentiveAd')
      window.OG_H5_GAME_SDK?.config({})
      window.OG_H5_GAME_SDK?.loadAds({ id: this.incentiveAdID })
      const executedCases: Record<number, boolean> = {}
      window.ug_loadAds = ({ status }) => {
        if (executedCases[status]) return
        console.log('ug_loadAds回调', status)
        executedCases[status] = true
        if (status == 1) {
          this.adLoading = false
          this.incentiveStatus = true
        }
      }
    },
    async showCheckAdv(
      type: AdvType,
      {
        aiID,
        trigger_position,
        recall_content
      }: {
        aiID: number
        trigger_position: string
        recall_content?: string
      }
    ) {
      if (this.adRequesting) {
        return Promise.reject('loading') // 已有请求中，直接返回 reject 避免重复触发
      }

      this.adRequesting = true

      const appStore = useAppStore()
      const result = await this.checkAdStatus(type)

      const { status, adType } = result || {}

      if (!appStore.isAndroid || !status) {
        this.adRequesting = false
        return Promise.reject('false')
      }

      Howler.mute(true)
      let promise
      if (adType === 1) {
        if (!this.insetStatus) {
          this.adRequesting = false
          return Promise.resolve('无广告')
        }
        eventReport({
          event_type: EventTypeEnum.CLICK_AD_BUTTON,
          ai_id: aiID,
          adv_id: this.insetAdID,
          adv_type: '插页广告',
          trigger_position,
          recall_content
        }).catch(() => {})

        promise = this.showInsetAd({ aiID, trigger_position, recall_content })
      } else {
        if (!this.incentiveStatus) {
          this.adRequesting = false
          useModal({
            message: i18n.global.t('loadAdFail')
          })
          return Promise.reject('无广告')
        }
        eventReport({
          event_type: EventTypeEnum.CLICK_AD_BUTTON,
          ai_id: aiID,
          adv_id: this.incentiveAdID,
          adv_type: '激励广告',
          trigger_position,
          recall_content
        }).catch(() => {})

        promise = this.showIncentiveAd({ aiID, trigger_position, recall_content })
      }

      // ✅ 最关键的部分：无论广告展示成功还是失败，都要重置状态
      return promise.finally(() => {
        this.adRequesting = false
        Howler.mute(false)
        if (appStore.isAndroid) {
          setTimeout(() => {
            if (localStorage.getItem('showAppTips') === new Date().toLocaleDateString()) return
            appStore.showAppTips = true
            localStorage.setItem('showAppTips', new Date().toLocaleDateString())
          }, 2000)
        }
      })
    },
    async showInsetAd({ aiID, trigger_position, recall_content }: { aiID: number; trigger_position: string; recall_content?: string }) {
      window.OG_H5_GAME_SDK?.showInterstitial({
        placementId: this.insetAdID
      })
      return new Promise<number>((resolve, reject) => {
        // 使用对象记录每个case是否已执行
        const executedCases: Record<number, boolean> = {}

        window.ug_showInterstitial = ({ i }) => {
          // 如果当前case已经执行过，直接返回
          if (executedCases[i]) return
          executedCases[i] = true

          console.log(i, 'showInsetAdType')
          switch (i) {
            case 1:
              eventReport({
                event_type: EventTypeEnum.SHOW_AD_SUCCESS,
                ai_id: aiID,
                adv_id: this.insetAdID,
                adv_type: '插页广告',
                trigger_position,
                recall_content
              })
              console.log('ug_showInterstitial广告成功展示')
              break
            case 2:
              this.adLoading = false
              console.log('ug_showInterstitial广告加载成功')
              break
            case 3:
              useModal({
                message: i18n.global.t('loadAdFail')
              })
              reject(i)
              this.loadInsetAd()
              break
            case 4:
              useModal({
                message: i18n.global.t('loadAdFail')
              })
              reject(i)
              this.loadInsetAd()
              break
            case 5:
              eventReport({
                event_type: EventTypeEnum.CLOSE_AD_MIDDLE,
                ai_id: aiID,
                adv_id: this.insetAdID,
                adv_type: '插页广告',
                trigger_position,
                recall_content
              })
              console.log('ug_showInterstitial广告中途关闭')
              resolve(i)
              this.loadInsetAd()
              break
            case 6:
              break
            case 7:
              eventReport({
                event_type: EventTypeEnum.AD_FULL_PLAY,
                ai_id: aiID,
                adv_id: this.insetAdID,
                adv_type: '插页广告',
                trigger_position,
                recall_content
              })
              console.log('ug_showInterstitial广告完整播放')
              resolve(i)
              this.loadInsetAd()
              break
          }
        }
      })
    },
    async showIncentiveAd({ aiID, trigger_position, recall_content }: { aiID: number; trigger_position: string; recall_content?: string }) {
      window.OG_H5_GAME_SDK.showAds({ id: this.incentiveAdID })
      return new Promise<boolean>((resolve, reject) => {
        // 使用对象记录每个case是否已执行
        const executedCases: Record<number, boolean> = {}

        window.ug_showAds = ({ type }) => {
          // 如果当前case已经执行过，直接返回
          if (executedCases[type]) return
          executedCases[type] = true

          console.log(type, 'ug_showAds回调')
          switch (type) {
            case 1:
              eventReport({
                event_type: EventTypeEnum.AD_FULL_PLAY,
                ai_id: aiID,
                adv_id: this.incentiveAdID,
                adv_type: '激励广告',
                trigger_position,
                recall_content
              })
              console.log('ug_showAds广告完整播放')
              resolve(type)
              this.loadIncentiveAd()
              break
            case 3:
              if (!executedCases[1]) {
                eventReport({
                  event_type: EventTypeEnum.CLOSE_AD_MIDDLE,
                  ai_id: aiID,
                  adv_id: this.incentiveAdID,
                  adv_type: '激励广告',
                  trigger_position,
                  recall_content
                })
              }
              console.log('ug_showAds广告中途关闭')
              reject(type)
              this.loadIncentiveAd()
              break
            case 7:
              eventReport({
                event_type: EventTypeEnum.SHOW_AD_SUCCESS,
                ai_id: aiID,
                adv_id: this.incentiveAdID,
                adv_type: '激励广告',
                trigger_position,
                recall_content
              })
              console.log('ug_showAds广告成功展示')
              this.loadIncentiveAd()
              break
            case 8:
              useModal({
                message: i18n.global.t('loadAdFail')
              })
              reject(type)
              this.loadIncentiveAd()
              break
          }
        }
      })
    }
  }
})
