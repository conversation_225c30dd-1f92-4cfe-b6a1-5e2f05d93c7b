export enum POPUP_TYPE_NAME {
  SEND_GIFT = 'sendGift',
  BUY_GIFT = 'buyGift',
  BUY_SKIN = 'buySkin',
  BUY_CALL_TIME = 'buyCallTime',
  BUY_SELFIE = 'buySelfie'
}

export const usePopupMemoryStore = defineStore('popup-memory', {
  state: () => ({
    memoryCache: {
      [POPUP_TYPE_NAME.SEND_GIFT]: {
        show: false,
        path: '',
        id: undefined,
        type: POPUP_TYPE_NAME.SEND_GIFT
      },
      [POPUP_TYPE_NAME.BUY_GIFT]: {
        show: false,
        path: '',
        id: undefined,
        type: POPUP_TYPE_NAME.BUY_GIFT
      },
      [POPUP_TYPE_NAME.BUY_SKIN]: {
        show: false,
        path: '',
        id: undefined,
        type: POPUP_TYPE_NAME.BUY_SKIN
      },
      [POPUP_TYPE_NAME.BUY_CALL_TIME]: {
        show: false,
        path: '',
        id: undefined,
        type: POPUP_TYPE_NAME.BUY_CALL_TIME
      },
      [POPUP_TYPE_NAME.BUY_SELFIE]: {
        show: false,
        path: '',
        id: undefined,
        type: POPUP_TYPE_NAME.BUY_SELFIE
      }
    }
  }),
  actions: {
    setPopupMemory(type: POPUP_TYPE_NAME, path: string, id?: number) {
      this.memoryCache[type].show = true
      this.memoryCache[type].path = path
      this.memoryCache[type].id = id
    },
    resetAllPopupMemory() {
      Object.keys(this.memoryCache).forEach((key) => {
        this.memoryCache[key as POPUP_TYPE_NAME].show = false
        this.memoryCache[key as POPUP_TYPE_NAME].path = ''
        this.memoryCache[key as POPUP_TYPE_NAME].id = undefined
      })
    },
    getPopupMemoryPath() {
      const key = Object.keys(this.memoryCache).find((key) => this.memoryCache[key as POPUP_TYPE_NAME].show)
      if (key) {
        return this.memoryCache[key as POPUP_TYPE_NAME].path
      } else {
        return undefined
      }
    },
    getPopupMemory() {
      const key = Object.keys(this.memoryCache).find((key) => this.memoryCache[key as POPUP_TYPE_NAME].show)
      if (key) {
        return this.memoryCache[key as POPUP_TYPE_NAME]
      } else {
        return undefined
      }
    }
  }
})
