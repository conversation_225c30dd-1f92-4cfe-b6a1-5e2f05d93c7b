import { defineStore } from 'pinia'
import type { RouteRecordName } from 'vue-router'
import type { EnhancedRouteLocation } from '@/router/types'

const useRouteCacheStore = defineStore('route-cache', () => {
  const routeCaches = ref<RouteRecordName[]>([])

  const addRoute = (route: EnhancedRouteLocation) => {
    console.log('route', route)
    if (routeCaches.value.includes(route.name)) return
    console.log('route', route.name)
    if (route?.meta?.keepAlive) {
      console.log('keep', route.name)
      routeCaches.value.push(route.name)
    }
  }

  return {
    routeCaches,
    addRoute
  }
})

export default useRouteCacheStore
