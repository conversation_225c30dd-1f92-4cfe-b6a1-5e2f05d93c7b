import { facebookLogin, googleLogin, appleLogin } from '@/api/login'
import { ICallBackParams } from '@/api/login/types.ts'
import useUserStore from './user'
import useAppStore from './app'
import useHomeStore from './home'
import { useModal } from '@/hooks/useModal'
import useNotifyStore from '@/stores/modules/notification.ts'

const useLoginStore = defineStore('login', () => {
  const userStore = useUserStore()
  const appStore = useAppStore()
  const notifyStore = useNotifyStore()
  const homeStore = useHomeStore()
  const router = useRouter()
  const route = useRoute()
  const btnLoading = ref(false)
  const { t } = useI18n()

  const loginBtnDisabled = reactive<{ google: boolean; apple: boolean; facebook: boolean; [key: string]: any }>({
    google: false,
    apple: false,
    facebook: false
  })
  const handleLogin = (val: { type: string; value: ICallBackParams }, callBack?: Function) => {
    let func: Function, params: ICallBackParams
    switch (val.type) {
      case 'facebook':
        func = facebookLogin
        params = { ...val.value }
        break
      case 'google':
        func = googleLogin
        params = { ...val.value }
        break
      case 'apple':
        func = appleLogin
        params = { ...val.value }
        break
      default:
        break
    }
    thirdPartyLogin(
      () => {
        return {
          func,
          params
        }
      },
      val.type,
      callBack
    )
  }
  /**
   *
   * @param handleData func,params 登录接口、登录参数
   * @param type 登录方式 google apple facebook
   * @param callBack 通过第三方申请注销的回调
   */
  const thirdPartyLogin = (handleData: Function, type: string, callBack?: Function) => {
    const { func, params } = handleData()
    btnLoading.value = true
    const loginParams = {
      ...params,
      register_channel: sessionStorage.getItem('is_ugphone') || undefined,
      invite_code: localStorage.getItem('invite_code') || undefined,
      is_cancel: callBack ? 1 : undefined
    }
    func(loginParams)
      .then((res: any) => {
        userStore.getUserInfo()
        sessionStorage.removeItem('is_ugphone')
        if (res.code !== 200) return
        if (callBack) {
          // 注销验证
          console.log(res.data, userStore.userInformation.client_id)

          if (res.data.client_id !== userStore.userInformation.client_id) {
            useModal({
              message: t('inconsistentAccounts'),
              duration: 2000
            })
            if (type === 'apple') {
              localStorage.removeItem('loginType')
            }
            return
          }
          // 注销验证
          callBack(() => {
            return {
              func,
              params
            }
          }, type)
          return
        }
        if (appStore.loginRedirectPath) {
          router.push(appStore.loginRedirectPath)
          appStore.loginRedirectPath = ''
        }
        notifyStore.triggerChatPageEvent('loginSendChat', {
          data: {},
          // @ts-ignore
          aiId: route.params?.ai_id
        })
        userStore.saveLoginInfo(res.data)
        appStore.showLogin = false
        localStorage.getItem('invite_code') ? localStorage.removeItem('invite_code') : ''
        loginBtnDisabled[type] = false
        homeStore.setUpdateBanner(true)
      })
      .catch(() => {
        loginBtnDisabled[type] = false
      })
      .finally(() => {
        btnLoading.value = false
      })
  }
  const changeDisabled = (val: { type: string; value: boolean }) => {
    loginBtnDisabled[val.type] = val.value
  }
  return {
    btnLoading,
    loginBtnDisabled,
    handleLogin,
    thirdPartyLogin,
    changeDisabled
  }
})

export default useLoginStore
