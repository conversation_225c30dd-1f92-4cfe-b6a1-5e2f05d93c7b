import { defineStore } from 'pinia'

const useAgentCreateStore = defineStore(
  'agentCreate',
  () => {
    const id = ref(0)
    const template_id = ref(0)
    const preset_image_id = ref(0)
    const showAgentCreateModePopup = ref(false)
    const agentCreateMode = ref('')
    const showRolePopup = ref(false)
    const showAvatar = ref(false)
    const sex = ref(0)
    const is_show = ref(false)
    const name = ref('')
    const sound = ref(0)
    const role_setting = ref('')
    const opening_statement = ref('')
    const back_story = ref('')
    const synopsis = ref('')
    const talk_example = ref<any>([])
    const tags = ref([])
    const avatar = ref('https://eros.wujialin.top/eros-mvp/assets/erosback-HByJAA6p.png')
    const img_url = ref('')
    const type = ref('')
    return {
      id,
      template_id,
      preset_image_id,
      showAgentCreateModePopup,
      showRolePopup,
      agent<PERSON>reateMode,
      showAvatar,
      sex,
      is_show,
      sound,
      name,
      role_setting,
      opening_statement,
      back_story,
      synopsis,
      talk_example,
      tags,
      avatar,
      img_url,
      type
    }
  },
  {
    persist: {
      storage: sessionStorage,
      paths: ['avatar', 'showRolePopup', 'name', 'template_id', 'preset_image_id', 'sound', 'sex']
    }
  }
)

export default useAgentCreateStore
