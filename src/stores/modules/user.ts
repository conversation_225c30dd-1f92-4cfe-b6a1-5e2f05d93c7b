import { defineStore } from 'pinia'
import { user_info } from '@/api/login'
import { setLoginId, setToken, removeToken, removeLoginId, getToken, setUserInfo, removeUserInfo, removeMqttConfig } from '@/utils/auth.ts'
import type { UserInfo } from '@/api/login/types'
import useAppStore from './app'
import useNotifyStore from '@/stores/modules/notification.ts'
import { MQTT_LOCALSTORAGE_KEY } from '@/constant'
import useWs from '@/hooks/useWs.ts'
const useUserStore = defineStore(
  'user',
  () => {
    const appStore = useAppStore()
    const notifyStore = useNotifyStore()
    const userInformation = ref(
      JSON.parse(localStorage.getItem('userInformation')) || {
        bgm: 1,
        sound: 1
      }
    )
    const userToken = ref(getToken())
    const token = computed(() => userToken.value)
    const newerOption = ref({
      hadSwitch: false,
      hadSwitchToClearMode: false,
      hadTouch: false,
      hadChangeSelfImage: false
    })

    const saveLoginInfo = (val: any) => {
      const { login_id, access_token, first_login, ...mqtt_config } = val
      setLoginId(login_id)
      setToken(access_token)
      userToken.value = access_token
      localStorage.setItem(MQTT_LOCALSTORAGE_KEY, JSON.stringify(mqtt_config))
      const notifyStore = useNotifyStore()
      notifyStore.mqttConfig = mqtt_config
      // 登录后显示开屏海报消息
      if (!appStore.isUg) {
        notifyStore.initNoticeAndPoster(true)
        useWs.connect()
      }
    }

    const getUserInfo = (): Promise<ResultData<UserInfo>> => {
      return new Promise((resolve, reject) => {
        user_info()
          .then((res) => {
            if (res.code === 200) {
              if (!res.data.avatar_url) {
                res.data.avatar_url = (import.meta.env.VITE_APP_PUBLIC_PATH === '/' ? '' : import.meta.env.VITE_APP_PUBLIC_PATH) + '/setting/default-avatar.png'
              }
              setUserInfo(JSON.stringify(res.data))
              userInformation.value = res.data
              if (res.data.is_delete === 1) {
                appStore.showCancelLogoff = true
              }
              resolve(res)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    }

    const removeAllUserInfo = () => {
      console.log(1212121)
      notifyStore.clearMqttInstance()
      removeToken()
      removeMqttConfig()
      removeLoginId()
      removeUserInfo()
      userToken.value = ''
      userInformation.value = {}
      useWs.close()
    }

    const isLogin = computed(() => {
      return !!userToken.value
    })

    const checkLoginStatus = (): boolean => {
      const userStore = useUserStore()
      const appStore = useAppStore()

      if (!userStore.isLogin) {
        appStore.showLogin = true
        return false
      }
      return true
    }

    return {
      saveLoginInfo,
      getUserInfo,
      userInformation,
      token,
      newerOption,
      userToken,
      isLogin,
      removeAllUserInfo,
      checkLoginStatus
    }
  },
  {
    persist: {
      paths: ['newerOption']
    }
  }
)

export default useUserStore
