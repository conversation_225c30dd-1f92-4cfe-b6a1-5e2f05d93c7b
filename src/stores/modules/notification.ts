import { defineStore } from 'pinia'
import MqttClient, { MqttConfig, MqttInstanceType } from '@/utils/mqtt.ts'
import { ref } from 'vue'
import { msgRead, noReadTextList, posterList, readMsg, unReadMsg } from '@/api/msg'
import { UnReadMsgItem, UnReadMsgRes, BannerItem } from '@/api/msg/type.ts'
import { isEmpty } from 'lodash-es'
import { MQTT_LOCALSTORAGE_KEY } from '@/constant'
import { IDialogueRecord } from '@/api/chat/types.ts'

const useNotifyStore = defineStore('notification', () => {
  const mqttConfig = ref<MqttConfig>(JSON.parse(localStorage.getItem(MQTT_LOCALSTORAGE_KEY) ?? '{}'))
  const mqttInstance = ref<MqttInstanceType>(null)
  // 开屏文本通知、即时文本通知
  const noticeList = ref<UnReadMsgItem[]>([])
  const bannerList = ref<BannerItem[]>([])
  const aiNoRead = ref<number>(0)
  const emo = ref({
    timestamp: 0,
    emo: ''
  })
  const hasUnReadMsg = ref(false)
  // 标记已经弹过窗的消息
  const delayNotice = ref(false)
  // 首登用户选择偏好延迟消息标记用

  // 聊天页面事件触发器
  const chatPageEvents = ref({
    newMessage: {
      timestamp: 0,
      data: null,
      aiId: null
    },
    loginSendChat: {
      timestamp: 0,
      aiId: null
    }
  })
  const initMqttClient = (config: MqttConfig = mqttConfig.value) => {
    if (mqttInstance.value) {
      console.log('already had mqttInstance', mqttInstance.value)
      return
    }
    mqttInstance.value = new MqttClient({
      config,
      onMessage: (data: { mqtt_type: string; msg_id: number; emo?: string; ai_id?: number; content?: any }) => {
        console.log(data, 'mqttData')
        if (data.mqtt_type === 'mail') {
          const { msg_id } = data
          console.log(window.location.pathname)
          if (data.content?.pjh_resp && window.location.pathname.includes(`/chat/${data.ai_id}`)) {
            console.log('收到聊天消息，触发聊天页面事件')
            // 触发聊天页面新消息事件
            triggerChatPageEvent('newMessage', {
              data: data.content,
              aiId: data.ai_id
            })
            readMsg({
              ai_id: data.ai_id
            }).then(() => {})
            return
          }
          getNoReadTextList('mqtt', msg_id)
        }
        if (data.mqtt_type === 'ai_no_read_count') {
          console.log('mqtt_type: ai_no_read_count')
          aiNoRead.value = data.content
        }
        if (data.mqtt_type === 'emo') {
          emo.value.emo = data.emo
          emo.value.timestamp = Date.now()
        }
      },
      onError: (error: Error) => {
        console.log('onError', error)
      }
    })
  }
  // 开屏通知
  const getNoticeList = (): Promise<UnReadMsgRes | any> => {
    return new Promise((resolve) => {
      noReadTextList().then((res) => {
        if (res.code === 200) {
          noticeList.value.push(...res.data.list)
        }
        resolve(res.data)
      })
    })
  }
  const getBannerList = () => {
    return new Promise<BannerItem[]>((resolve) => {
      posterList().then((res) => {
        if (res.code === 200) {
          bannerList.value = res.data.list.sort((pre, cur) => pre.sort - cur.sort)
        }
        resolve(res.data.list)
      })
    })
  }

  // 即时通知
  const getNoReadTextList = (from = '', msg_id = 0) => {
    const getUnReadMsgList = () => {
      unReadMsg({
        limit: 99999,
        page: 1
      }).then((res) => {
        if (res.code === 200) {
          if (from === 'mqtt') {
            const curMsgItem = res.data.list.find((item) => item.id === msg_id)
            console.log('curMsgItem', curMsgItem)
            curMsgItem && noticeList.value.push(curMsgItem)
            msgRead({ msg_id })
          }
        }
      })
    }

    if (noticeList.value.length) {
      const curMsgItem = noticeList.value.find((item) => item.id === msg_id)
      if (from === 'mqtt' && !isEmpty(curMsgItem)) {
        noticeList.value.push(curMsgItem)
      } else {
        getUnReadMsgList()
      }
    } else {
      getUnReadMsgList()
    }
  }

  // 刷新、登录初始化开屏海报、消息
  // is_first_open 第一次进入eros，刷新页面不算
  const initNoticeAndPoster = (is_first_open: boolean = false, is_first_login: boolean = false) => {
    sessionStorage.setItem('livco_notification', JSON.stringify(1))
    const mqtt_config: MqttConfig = mqttConfig.value
    initMqttClient(mqtt_config)
    // 第一次进入eros、且非首登 展示开屏通知
    if (is_first_open && !is_first_login) {
      getBannerList().then((list) => {
        if (!list?.length) {
          getNoticeList()
        } else {
          // 延迟消息弹出
          delayNotice.value = true
        }
      })
    }
  }

  // 触发聊天页面事件的方法
  const triggerChatPageEvent = (eventType: 'newMessage' | 'loginSendChat', payload: { data?: IDialogueRecord; aiId: number }) => {
    if (eventType === 'newMessage') {
      chatPageEvents.value.newMessage = {
        timestamp: Date.now(),
        data: payload.data,
        aiId: payload.aiId
      }
    } else if (eventType === 'loginSendChat') {
      chatPageEvents.value.loginSendChat = {
        timestamp: Date.now(),
        aiId: payload.aiId
      }
    }
  }

  const clearMqttInstance = () => {
    mqttInstance.value?.disconnect()
    mqttInstance.value = null
  }

  return {
    mqttConfig,
    mqttInstance,
    initMqttClient,
    getNoticeList,
    noticeList,
    getBannerList,
    bannerList,
    aiNoRead,
    getNoReadTextList,
    hasUnReadMsg,
    delayNotice,
    initNoticeAndPoster,
    emo,
    chatPageEvents,
    triggerChatPageEvent,
    clearMqttInstance
  }
})

export default useNotifyStore
