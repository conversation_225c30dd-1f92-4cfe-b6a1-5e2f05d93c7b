import { defineStore } from 'pinia'

const useHomeStore = defineStore('home', () => {
  const isUpdateData = ref(false) // 是否更新
  const isUpdateBanner = ref(false)
  const isUpdateAlbum = ref(false)
  const setUpdateAlbum = (value: boolean) => {
    isUpdateAlbum.value = value
  }
  const setUpdateData = (value: boolean) => {
    isUpdateData.value = value
  }
  const setUpdateBanner = (value: boolean) => {
    isUpdateBanner.value = value
  }
  return { isUpdateData, isUpdateBanner, setUpdateData, setUpdateBanner, isUpdateAlbum, setUpdateAlbum }
})

export default useHomeStore
