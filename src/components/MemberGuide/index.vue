<script setup lang="ts">
import { promotion_list } from '@/api/mine'
import router from '@/router'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
const { t } = useI18n()
const show = defineModel<boolean>()
const rightsList = ref([])
function gotoMember() {
  router.push({ name: 'Member' })
  show.value = false
}

watch(
  () => show.value,
  (val) => {
    if (val) {
      eventReport({
        event_type: EventTypeEnum.SHOW_AD_VIP
      })
      promotion_list({ type: '高级' }).then((res) => {
        if (res.code !== 200) return
        rightsList.value = res.data.map((item) => {
          return item.interest_list.map((citem) => {
            return {
              ...citem
            }
          })
        })
        rightsList.value = rightsList.value.reduce((acc, val) => acc.concat(val), []).slice(0, 4)
      })
    }
  }
)
</script>
<template>
  <van-overlay
    :show="show"
    class="flex-center-center"
    z-index="9999"
  >
    <div class="member-ad flex-center-center">
      <img
        src="@/assets/images/ad-vip.png"
        alt=""
      />
      <img
        src="@/assets/images/ad-vip-frog.png"
        alt=""
        class="frog"
      />
      <div class="ad-big-title">{{ t('noAds') }}</div>
      <div class="ad-price">
        {{ t('from') }}
        <img
          src="@/assets/images/ad-vip-price.png"
          alt=""
        />
      </div>
      <div class="ad-box">
        <div class="ad-title">
          <div
            class="title-decoration"
            style="height: 1px"
          ></div>
          {{ t('unlockVipPower', { vipNum: rightsList.length }) }}
          <div
            class="title-decoration"
            style="height: 1px"
          ></div>
        </div>
        <div class="ad-content">
          <div
            class="ad-item"
            v-for="(item, i) in rightsList"
            :key="i"
          >
            <div class="ad-item-icon">
              <img
                :src="item.icon"
                alt=""
              />
            </div>
            <div class="ad-item-text">{{ item.name }}</div>
          </div>
        </div>
        <div class="ad-btn-shadow">
          <div class="ad-btn-border">
            <div
              class="ad-btn"
              @click="gotoMember"
            >
              {{ t('subscribeNow') }}
            </div>
            <img
              src="@/assets/images/ad-vip-flash.png"
              alt=""
              class="flash"
            />
          </div>
        </div>
      </div>
      <div
        class="close-btn"
        @click="show = false"
      >
        <SvgIcon
          icon-class="close"
          class="fsize-24"
        />
      </div>
    </div>
  </van-overlay>
</template>
<style lang="scss" scoped>
.member-ad {
  position: relative;
  width: 100%;
  margin-top: -128px;
  scale: 0.9;
  animation: show-bounce 0.5s ease-out;

  .frog {
    position: absolute;
    z-index: -1;
    transform: translate(-3px, 12px);
    animation: bounce 2s infinite;
    animation-delay: 0.5s;
  }

  img {
    width: 100%;
  }

  .ad-big-title {
    position: absolute;
    top: 111px;
    left: 61px;
    width: 168px;
    font-family: RammettoOne, 'AlibabaPuHuiTi Heavy', serif;
    font-size: 29px;
    font-weight: 800;
    line-height: 37px;
    color: #fff;
    // 只要下阴影
    text-shadow: #101641 0 4px 0;
  }

  .ad-price {
    position: absolute;
    top: 148px;
    right: 56px;
    display: flex;
    column-gap: 6px;
    align-items: center;
    width: 168px;
    padding-top: 16px;
    font-family: RammettoOne, 'AlibabaPuHuiTi Heavy', serif;
    font-size: 20px;
    font-weight: 400;
    color: #fff;
    text-shadow: #101641 0 4px 0;
    word-break: keep-all;
    transform: rotate(-5deg);

    img {
      width: 128px;
      margin-top: -16px;
      transform: rotate(5deg);
    }
  }

  .ad-box {
    position: absolute;
    right: 44px;
    bottom: 0;
    left: 39px;
    height: 200px;
    padding: 16px 12px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 50%) 0%, rgba(255, 255, 255, 80%) 20.23%, #fff 100%);
    backdrop-filter: blur(16px);
    border: 1px solid #fff;
    border-radius: 16px 24px;

    .ad-title {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      font-weight: 400;
      line-height: 100%;
      color: #2f2101;

      .title-decoration {
        flex: 1;
        opacity: 0.2;

        &:first-child {
          margin-right: 8px;
          background: linear-gradient(90deg, rgba(1, 31, 47, 0%) 0%, #011f2f 100%);
        }

        &:last-child {
          margin-left: 8px;
          background: linear-gradient(90deg, #011f2f 0%, rgba(1, 31, 47, 0%) 100%);
        }
      }
    }

    .ad-content {
      display: flex;
      flex-wrap: wrap;
      margin-top: 16px;

      .ad-item {
        display: flex;
        column-gap: 8px;
        align-items: center;
        justify-content: flex-start;
        width: 50%;
        margin-bottom: 16px;

        .ad-item-icon {
          width: 32px;
          height: 32px;
          padding: 5px;
          overflow: hidden;
          background: #c8ddf34d;
          border: 1px solid #d1e3ff7a;
          border-radius: 50%;

          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }

        .ad-item-text {
          margin-top: 4px;
          font-size: 11px;
          line-height: 100%;
          color: #282827e5;
        }
      }
    }

    .ad-btn-shadow {
      position: absolute;
      bottom: 0;
      left: 50%;
      margin-top: 24px;
      transform: translate(-50%, 50%);

      &::after {
        position: absolute;
        top: -2px;
        left: -2px;
        z-index: -1;
        width: calc(100% + 4px);
        height: calc(100% + 4px);
        content: '';
        background: linear-gradient(151.47deg, #ebdfac 13.2%, #f2b5fd 28.34%, #3f62ff 54.45%, #fff4db 86.44%);
        filter: blur(12px);
        border-radius: 32px;
        opacity: 0.5;
      }
    }

    .ad-btn-border {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: fit-content;
      height: 52px;
      padding: 0 2px;
      overflow: hidden;
      white-space: nowrap;
      background: linear-gradient(151.47deg, #ebdfac 13.2%, #f2b5fd 28.34%, #3f62ff 54.45%, #fff4db 86.44%);
      border-radius: 32px;

      .ad-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: fit-content;
        height: 48px;
        padding: 0 48px;
        font-size: 16px;
        font-weight: 900;
        line-height: 16px;
        color: #fff;
        text-align: center;
        background: #181818;
        border-radius: 32px;
      }

      .flash {
        position: absolute;
        top: 0;
        left: -30%;
        width: 59px;
        height: 83px;
        animation: flash 2s linear infinite;
      }
    }
  }

  .close-btn {
    position: absolute;
    bottom: -41px;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    color: #fff;
    cursor: pointer;
    border: 2px solid #ffffff80;
    border-radius: 50%;
    transform: translate(-50%, 100%);
  }
}

@keyframes show-bounce {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0%,
  100% {
    scale: 1;
  }

  50% {
    scale: 1.02;
  }
}

@keyframes flash {
  0% {
    left: -30%;
  }

  100% {
    left: 100%;
  }
}
</style>
