<script setup lang="ts">
// import useAgentCreate from '@/stores/modules/agentCreate'
// import useAppStore from '@/stores/modules/app'
import useUserStore from '@/stores/modules/user'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
import { useModal } from '@/hooks/useModal'
import { usePrompt } from '@/hooks/usePrompt'
import useNotifyStore from '@/stores/modules/notification.ts'

const userStore = useUserStore()
const notifyStore = useNotifyStore()
// const appStore = useAppStore()
// const agentCreateStore = useAgentCreate()

// const { t } = useI18n()
const active = ref(0)
const route = useRoute()
const reportTypeList = [EventTypeEnum.OPEN_HOME, EventTypeEnum.OPEN_LEADERBOARD, EventTypeEnum.OPEN_MESSAGE_CHAT_HISTORY, EventTypeEnum.OPEN_MINE]
const forwardAddressList = [ForwardAddressEnum.HOME_PAGE, ForwardAddressEnum.MESSAGE_CHAT_HISTORY, ForwardAddressEnum.RANKING_PAGE, ForwardAddressEnum.MINE]

// 连续点击计数器和相关逻辑
const homeClickCount = ref(0)
const clickTimer = ref<NodeJS.Timeout | null>(null)
const CLICK_RESET_TIME = 3000 // 3秒内无点击则重置计数
const TARGET_CLICK_COUNT = 15 // 目标点击次数

const display = computed(() => {
  return route.meta.level && route.meta.level !== 2
})

// 重置点击计数器
const resetClickCount = () => {
  homeClickCount.value = 0
  if (clickTimer.value) {
    clearTimeout(clickTimer.value)
    clickTimer.value = null
  }
}

// 处理home tab点击
const handleHomeTabClick = () => {
  homeClickCount.value++

  // 重置定时器
  if (clickTimer.value) {
    clearTimeout(clickTimer.value)
  }

  // 设置新的重置定时器
  clickTimer.value = setTimeout(resetClickCount, CLICK_RESET_TIME)

  // 检查是否达到目标点击次数
  if (homeClickCount.value >= TARGET_CLICK_COUNT) {
    showVConsoleDialog()
    resetClickCount()
  }
}

// 显示vconsole激活对话框
const showVConsoleDialog = () => {
  usePrompt({
    confirmText: '确定',
    cancelText: '取消'
  })
    .then((value) => {
      if (value === 'tyFrontend') {
        activateVConsole()
      } else {
        useModal({
          message: '激活码错误',
          duration: 1500
        })
      }
    })
    .catch(() => {
      // 用户取消
    })
}

// 激活vconsole
const activateVConsole = () => {
  try {
    // 动态导入vconsole
    import('vconsole')
      .then((VConsole) => {
        // 检查是否已经存在vconsole实例
        if (!(window as any).vConsole) {
          const vConsole = new VConsole.default()
          ;(window as any).vConsole = vConsole
          useModal({
            message: 'vConsole已激活',
            duration: 2000
          })
        } else {
          useModal({
            message: 'vConsole已经处于激活状态',
            duration: 1500
          })
        }
      })
      .catch((error) => {
        console.error('Failed to load vConsole:', error)
        useModal({
          message: '激活失败，请稍后重试',
          duration: 1500
        })
      })
  } catch (error) {
    console.error('Error activating vConsole:', error)
    useModal({
      message: '激活失败，请稍后重试',
      duration: 1500
    })
  }
}

const beforeChange = (name: number | string) => {
  // if (name === 'agentCreate') {
  //   if (!userStore.token) {
  //     appStore.showLogin = true
  //     return false
  //   } else {
  //     agentCreateStore.showAgentCreateModePopup = true
  //     return false
  //   }
  // }
  if (userStore.isLogin) {
    eventReport({
      event_type: reportTypeList[name as number]
    }).catch((err) => {
      console.warn(err)
    })
  }
  sessionStorage.setItem('login_front_address', forwardAddressList[name as number])

  return true
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (clickTimer.value) {
    clearTimeout(clickTimer.value)
    clickTimer.value = null
  }
})
</script>

<template>
  <van-tabbar
    class="tabbar"
    v-show="display"
    v-model="active"
    route
    :before-change="beforeChange"
  >
    <van-tabbar-item
      replace
      to="/"
      class="tabbar-item"
      @click="handleHomeTabClick"
    >
      <template #icon="props">
        <SvgIcon
          :icon-class="props.active ? 'home-tab-active' : 'home-tab'"
          class="fsize-24"
        />
      </template>
    </van-tabbar-item>
    <van-tabbar-item
      replace
      :to="{ name: 'RankingList' }"
      class="tabbar-item"
    >
      <template #icon="props">
        <SvgIcon
          :icon-class="props.active ? 'game-tab-active' : 'game-tab'"
          class="fsize-24"
        />
      </template>
    </van-tabbar-item>
    <van-tabbar-item
      replace
      :to="{ name: 'agentMessage' }"
      class="tabbar-item"
    >
      <template #icon="props">
        <SvgIcon
          :icon-class="props.active ? 'message-tab-active' : 'message-tab'"
          class="fsize-24"
        />
        <div
          class="un-read-point"
          v-show="notifyStore.aiNoRead > 0"
        >
          {{ notifyStore.aiNoRead }}
        </div>
      </template>
    </van-tabbar-item>
    <van-tabbar-item
      replace
      :to="{ name: 'mine' }"
      class="tabbar-item"
    >
      <template #icon="props">
        <SvgIcon
          :icon-class="props.active ? 'mine-tab-active' : 'mine-tab'"
          class="fsize-24"
        />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>
<style lang="scss" scoped>
@import 'src/assets/styles/variables';

.tabbar {
  height: 62px;
  user-select: none;
  background: $backColorOpacity;
  backdrop-filter: blur(48px);

  :deep(.van-tabbar-item--active) {
    background: unset;
  }

  :deep(.van-tabbar-item__icon) {
    margin-bottom: -4px;
    transition: opacity 0.1s;
  }

  .tabbar-item:active {
    :deep(.van-tabbar-item__icon) {
      opacity: 0.2;
    }
  }

  .un-read-point {
    position: absolute;
    top: -20%;
    left: 60%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 16px;
    padding: 1px 4px;
    font-size: 12px;
    line-height: 100%;
    color: #fff;
    background: #ff482b;
    border-radius: 41px;
  }
}
</style>
