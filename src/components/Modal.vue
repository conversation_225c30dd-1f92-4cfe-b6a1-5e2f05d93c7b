<script setup lang="ts">
// 使用 withDefaults 设置默认值
const props = withDefaults(
  defineProps<{
    message: string
    duration?: number
    loading: boolean
    autoClose?: boolean
    onClose?: () => void
  }>(),
  {
    duration: 1000, // 默认自动关闭时间为 1000 毫秒
    autoClose: true, // 默认值为 true，自动关闭
    loading: false
  }
)

// 控制模态框的可见性
const visible = ref(false)

// 显示模态框的方法
const show = () => {
  visible.value = true
  if (props.autoClose && props.duration > 0) {
    setTimeout(close, props.duration)
  }
}

// 关闭模态框的方法
const close = () => {
  visible.value = false
  if (props.onClose) {
    props.onClose()
  }
}

// 将 show 和 close 方法暴露给父组件
defineExpose({ show, close })
</script>

<template>
  <Transition name="modal-fade">
    <div
      v-if="visible"
      class="modal-overlay"
      :style="!props.loading && { pointerEvents: 'none' }"
    >
      <div class="modal">
        <template v-if="props.loading">
          <div class="gl-container">
            <div class="global-dot"></div>
            <div class="global-dot"></div>
            <div class="global-dot"></div>
            <div class="global-dot"></div>
            <div class="global-dot"></div>
            <div class="global-dot"></div>
          </div>
        </template>
        <slot>{{ message }}</slot>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.modal {
  z-index: 99999;
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  align-items: center;
  justify-content: center;
  max-width: 260px;
  padding: 16px 24px;
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 80%);
  border-radius: 16px;
}

.gl-container {
  --uib-size: 40px;
  --uib-color: rgb(235, 223, 172);
  --uib-speed: 1.5s;
  --global-dot-size: calc(var(--uib-size) * 0.17);

  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: var(--uib-size);
  height: var(--uib-size);
  animation: smoothRotate calc(var(--uib-speed) * 1.8) linear infinite;
}

.global-dot {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
  height: 100%;
  animation: rotate var(--uib-speed) ease-in-out infinite;
}

.global-dot::before {
  width: var(--global-dot-size);
  height: var(--global-dot-size);
  content: '';
  background-color: var(--uib-color);
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.global-dot:nth-child(2),
.global-dot:nth-child(2)::before {
  animation-delay: calc(var(--uib-speed) * -0.835 * 0.5);
}

.global-dot:nth-child(3),
.global-dot:nth-child(3)::before {
  animation-delay: calc(var(--uib-speed) * -0.668 * 0.5);
}

.global-dot:nth-child(4),
.global-dot:nth-child(4)::before {
  animation-delay: calc(var(--uib-speed) * -0.501 * 0.5);
}

.global-dot:nth-child(5),
.global-dot:nth-child(5)::before {
  animation-delay: calc(var(--uib-speed) * -0.334 * 0.5);
}

.global-dot:nth-child(6),
.global-dot:nth-child(6)::before {
  animation-delay: calc(var(--uib-speed) * -0.167 * 0.5);
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  65%,
  100% {
    transform: rotate(360deg);
  }
}

@keyframes smoothRotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes run {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}
</style>
