<script setup lang="ts"></script>

<template>
  <div class="container">
    <div class="loading-bar"></div>
    <div class="loading-bar"></div>
    <div class="loading-bar"></div>
    <div class="loading-bar"></div>
  </div>
</template>

<style scoped lang="scss">
.loading-bar {
  width: 2px;
  height: 80%;
  margin: 0 1px;
  background-color: #8d005d;
  border-radius: 9999px;
  animation: bounce 0.6s infinite ease-in-out;
}

.container {
  display: flex;
  align-items: center;
  width: 100%;
}

@for $i from 1 through 4 {
  .loading-bar:nth-child(#{$i}) {
    animation-delay: #{($i - 1) * 0.2}s;
  }
}

@keyframes bounce {
  0%,
  100% {
    height: 40%;
  }

  50% {
    height: 80%;
  }
}
</style>
