<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    opacity?: number
  }>(),
  {
    opacity: 0
  }
)
</script>

<template>
  <div
    class="loading-overlay"
    :style="{ background: props.opacity === 1 ? '#181818' : 'transparent' }"
  >
    <div class="loading-box">
      <div class="container">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.container {
  --uib-size: 40px;
  --uib-color: rgb(235, 223, 172);
  --uib-speed: 1.5s;
  --dot-size: calc(var(--uib-size) * 0.17);

  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: var(--uib-size);
  height: var(--uib-size);
  animation: smoothRotate calc(var(--uib-speed) * 1.8) linear infinite;
}

.dot {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
  height: 100%;
  animation: rotate var(--uib-speed) ease-in-out infinite;
}

.dot::before {
  width: var(--dot-size);
  height: var(--dot-size);
  content: '';
  background-color: var(--uib-color);
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.dot:nth-child(2),
.dot:nth-child(2)::before {
  animation-delay: calc(var(--uib-speed) * -0.835 * 0.5);
}

.dot:nth-child(3),
.dot:nth-child(3)::before {
  animation-delay: calc(var(--uib-speed) * -0.668 * 0.5);
}

.dot:nth-child(4),
.dot:nth-child(4)::before {
  animation-delay: calc(var(--uib-speed) * -0.501 * 0.5);
}

.dot:nth-child(5),
.dot:nth-child(5)::before {
  animation-delay: calc(var(--uib-speed) * -0.334 * 0.5);
}

.dot:nth-child(6),
.dot:nth-child(6)::before {
  animation-delay: calc(var(--uib-speed) * -0.167 * 0.5);
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  65%,
  100% {
    transform: rotate(360deg);
  }
}

@keyframes smoothRotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: calc(100% - 50px);
  background: transparent;
}

.loading-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 104px;
  height: 104px;
  //   background: #120b0f;
  border-radius: 16px;
  opacity: 0.8;
}

.loading {
  width: 56px;
  height: 56px;
  animation: run 1s infinite linear;
}

@keyframes run {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
