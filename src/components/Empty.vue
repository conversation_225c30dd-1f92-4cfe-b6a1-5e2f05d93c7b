<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    text: string
  }>(),
  {
    text: '暂无数据'
  }
)
</script>

<template>
  <div class="w-full flex-1 flex-center-center">
    <div class="flex-center-center flex-column mb-100">
      <slot name="empty-img">
        <img
          src="@/assets/images/emptyImg/empty-package.png"
          alt="empty"
          class="empty-img"
          width="196"
        />
      </slot>
      <slot name="text">
        <div class="text">{{ props.text }}</div>
      </slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.empty-img {
}

.text {
  font-size: 13px;
  color: rgba(255, 255, 255, 50%);
}
</style>
