<script setup lang="ts">
import useAppStore from '@/stores/modules/app.ts'
import useNotifyStore from '@/stores/modules/notification.ts'

const appStore = useAppStore()
const router = useRouter()
const notifyStore = useNotifyStore()

const goStory = () => {
  // appStore.showNewer = false
  router.push('/newerStoryChat')
}

const close = () => {
  appStore.showNewer = false
  nextTick(() => {
    // appStore.showPreference = true
    notifyStore.initNoticeAndPoster(true)
  })
}
</script>

<template>
  <van-overlay
    v-model:show="appStore.showNewer"
    :close-on-click-overlay="false"
    class="flex-center-center flex-column"
  >
    <img
      @click="goStory"
      src="@/assets/images/newer/newer-pic.png"
      alt="newer"
      class="newer-pic"
    />
    <div
      class="flex-center-center mt-16"
      @click="close"
    >
      <div class="dialog-close"></div>
    </div>
  </van-overlay>
</template>

<style scoped lang="scss">
.newer-pic {
  max-width: 90%;
}

.dialog-close {
  width: 32px;
  height: 32px;
  background: url('~@/assets/images/login/close-btn.png') no-repeat;
  background-size: contain;
}
</style>
