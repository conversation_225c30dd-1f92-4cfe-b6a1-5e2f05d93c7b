<script setup lang="ts">
import { How<PERSON>, How<PERSON> } from 'howler'

const props = withDefaults(
  defineProps<{
    soundUrl: string
  }>(),
  {}
)

const isPlaying = ref(false)
const sound = ref<Howl | null>(null)
const duration = ref(0)
const loading = ref(true)

const play = () => {
  if (sound.value) {
    Howler.stop()
    sound.value.play()
  }
}

const stop = () => {
  if (sound.value) {
    sound.value.stop()
  }
}

watch(
  () => props.soundUrl,
  (newUrl) => {
    if (newUrl) {
      sound.value = new Howl({
        src: newUrl,
        preload: 'metadata', // 设置为 true 以异步加载元数据
        onplay: () => {
          isPlaying.value = true
          console.log('Audio is playing.')
        },
        onstop: () => {
          isPlaying.value = false
          console.log('Audio has stopped.')
        },
        onload: () => {
          console.log('Audio has loaded.')
          loading.value = false
          duration.value = Number(sound.value?.duration().toFixed(0)) || 0
        },
        onend: () => {
          isPlaying.value = false
          console.log('Audio has ended.')
        }
      })
    }
  },
  {
    immediate: true
  }
)

onUnmounted(() => {
  if (sound.value) {
    sound.value.unload() // 组件卸载时释放资源
  }
})
</script>

<template>
  <slot
    :play="play"
    :duration="duration"
    :sound="sound"
    :isPlaying="isPlaying"
    :loading="loading"
    :stop="stop"
  >
    <div
      v-if="sound"
      @click="play"
    >
      播放 {{ duration }}s
    </div>
  </slot>
</template>

<style scoped lang="scss"></style>
