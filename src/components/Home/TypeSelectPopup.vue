<script setup lang="ts">
import type { IAlbumListItem } from '@/api/home/<USER>'

const { t } = useI18n()
const showHomeTypeSelectPopup = defineModel<boolean>()
const props = withDefaults(
  defineProps<{
    tabsList: IAlbumListItem[]
  }>(),
  {
    tabsList: () => []
  }
)

const emit = defineEmits<{
  (e: 'comfirm', value: { sort_type: number; tag_id: number; name: string }): void
}>()

const tabList = [
  {
    name: t('trending'),
    index: 3
  },
  {
    name: t('mostPopular'),
    index: 1
  },
  {
    name: t('latest'),
    index: 2
  }
]
// 默认流行
const activeSortWayIndex = ref<number>(3)
const activeTypeIndex = ref<number>(-1)

const closeModePopup = () => {
  showHomeTypeSelectPopup.value = false
}
const typeClickHadle = (item: IAlbumListItem) => {
  activeTypeIndex.value = item.id
}
const tabClickHandle = (item: { name: string; index: number }) => {
  activeSortWayIndex.value = item.index
}

function comfirmHandle() {
  emit('comfirm', {
    sort_type: activeSortWayIndex.value,
    tag_id: activeTypeIndex.value,
    name: props.tabsList.find((item) => item.id === activeTypeIndex.value)?.name
  })
  showHomeTypeSelectPopup.value = false
}

watch(
  () => showHomeTypeSelectPopup.value,
  (val) => {
    if (val) {
      if (activeTypeIndex.value === -1) {
        activeTypeIndex.value = props.tabsList[0]?.id
      }
    }
  }
)
</script>
<template>
  <van-popup
    v-model:show="showHomeTypeSelectPopup"
    round
    position="bottom"
    class="popup"
  >
    <div class="title">
      <div class="flex-center-center">
        <div>{{ t('aiFilter') }}</div>
      </div>
      <img
        src="@/assets/images/agentCreate/close.png"
        class="close"
        @click="closeModePopup"
      />
    </div>
    <div class="type-title">{{ t('sortOptions') }}</div>
    <div class="tab-list sort-way">
      <div
        :class="['item flex-center-center', activeSortWayIndex === item.index ? 'active' : '']"
        v-for="item in tabList"
        :key="item.name"
        @click="tabClickHandle(item)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="type-title">{{ t('aiType') }}</div>
    <div class="tab-list ai-type">
      <div
        :class="['item flex-center-center', activeTypeIndex === item.id ? 'active' : '']"
        v-for="item in props.tabsList"
        :key="item.id"
        @click="typeClickHadle(item)"
      >
        {{ item.name }}
      </div>
    </div>
    <div
      class="sumit-btn flex-center-center"
      @click="comfirmHandle"
    >
      {{ t('confirmSelection') }}
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
.popup {
  min-height: 300px;
  max-height: 85vh;
  padding: 0 16px 32px;
  border-radius: 24px 24px 0 0;

  .title {
    display: flex;
    justify-content: space-between;
    padding: 24px 0;
    font-size: 18px;
    font-weight: 600;

    .tab-item {
      position: relative;
      margin-right: 16px;
      color: rgba(255, 255, 255, 30%);
      transition: all 0.2s;

      &.active {
        color: #fff;

        &::after {
          position: absolute;
          bottom: -5px;
          left: 0;
          width: 100%;
          height: 10px;
          content: '';
          background: linear-gradient(90deg, #d311c6 0%, rgba(211, 17, 198, 0%) 100%);
          border-radius: 22px;
        }
      }
    }

    .close {
      width: 20px;
      height: 20px;
    }
  }

  .type-title {
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 500;
    color: #8b8889;
  }

  .tab-list {
    display: flex;
    flex-wrap: wrap;

    .item {
      min-width: 80px;
      height: 38px;
      padding: 12px 16px;
      margin: 8px 8px 0 0;
      font-size: 14px;
      font-weight: 500;
      text-align: center;
      border: 1px solid rgba(255, 255, 255, 10%);
      border-radius: 41px;

      &.active {
        font-weight: 500;
        background: rgba(255, 53, 242, 30%);
        border: 1px solid #ff35f2;
      }
    }

    &.sort-way {
      margin-bottom: 22px;
    }

    &.ai-type {
      margin-bottom: 36px;
    }
  }

  .sumit-btn {
    padding: 16px;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    background: linear-gradient(90deg, #ff35f2 0%, #98f 100%);
    border-radius: 40px;
  }
}
</style>
