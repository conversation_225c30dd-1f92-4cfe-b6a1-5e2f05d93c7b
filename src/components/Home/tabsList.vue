<template>
  <div class="tabs-container">
    <van-skeleton :loading="props.loading">
      <template #template>
        <div class="skeleton-banner flex-center-center">
          <div class="skeleton w-76"></div>
          <div class="skeleton w-114"></div>
          <div class="skeleton w-60"></div>
          <div class="skeleton w-200"></div>
        </div>
      </template>
      <van-tabs
        type="card"
        swipe-threshold="3"
        v-model:active="tabActive"
        @click-tab="emit('tab-click', $event)"
      >
        <van-tab
          v-for="item in props.tabsList"
          :title="item.name"
          :name="item.id"
          :key="item.id"
        />
      </van-tabs>
    </van-skeleton>
    <div
      class="more-btn flex-center-center"
      @click="emit('more-click')"
    >
      <img
        class="more-btn-img"
        src="@/assets/images/index/more-btn.png"
        alt="more-btn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { IAlbumListItem } from '@/api/home/<USER>'
const active = ref<number>(0)
const props = withDefaults(
  defineProps<{
    tabsList: IAlbumListItem[]
    loading: boolean
    tabActive: number
  }>(),
  {
    tabsList: () => [],
    loading: false,
    tabActive: 0
  }
)
const tabActive = ref(props.tabActive)
watch(
  () => props.tabActive,
  (newVal) => {
    tabActive.value = newVal
  }
)
defineExpose({
  setActive
})
const emit = defineEmits<{
  (e: 'more-click'): void
  (e: 'tab-click', value: IAlbumListItem): void
}>()
if (props.tabsList.length > 0) {
  active.value = props.tabsList[0].id
}
function setActive(id: number) {
  active.value = id
}
</script>

<style lang="scss" scoped>
:deep(.van-skeleton) {
  padding: 0;
}

.skeleton-banner {
  height: 48px;
}

.skeleton {
  width: 223px;
  height: 32px;
  margin-right: 12px;
  background: #242424;
  border-radius: 16px;

  &.w-76 {
    width: 76px;
  }

  &.w-60 {
    width: 60px;
  }

  &.w-114 {
    width: 114px;
  }

  &.w-200 {
    width: 50px;
  }
}

:deep(.van-tabs) {
  height: 48px;

  .van-tabs__wrap {
    height: 100%;
    padding-right: 31.5px;

    .van-tabs__nav {
      height: 100%;
      padding: 9px 0;
      background: #181818;
    }
  }

  .van-tabs__nav {
    padding: 8px 0 !important;
    margin: 0;
    border: none;

    .van-tab--card {
      min-width: 80px;
      // height: 32px;
      margin-right: 12px;
      font-weight: 400;
      color: #fff;
      border: 1px solid #2e2d2d;
      border-radius: 16px;

      &:first-child {
        width: fit-content;
        max-width: 120px;
      }

      &:last-child {
        margin-right: 0;
      }

      &.van-tab--active {
        font-weight: 600;
        background: linear-gradient(90deg, #ff35f2 0%, #98f 100%);
        border: 1px solid #181818;
      }
    }
  }
}

.tabs-container {
  position: relative;

  .more-btn {
    position: absolute;
    top: 0;
    right: -16px;
    width: 48px;
    height: 48px;
    background: #181818;

    &::before {
      position: absolute;
      top: 0;
      left: -10px;
      width: 10px;
      height: 100%;
      content: '';
      background: linear-gradient(270deg, #181818 0%, rgba(24, 24, 24, 0%) 100%);
    }

    .more-btn-img {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
