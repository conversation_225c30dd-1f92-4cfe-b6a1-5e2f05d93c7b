<script setup lang="ts">
import { useIntersectionObserver } from '@vueuse/core'
import { IAiListItem, IAiListParams } from '@/api/home/<USER>'
import { queryAiList } from '@/api/home'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
import useUserStore from '@/stores/modules/user.ts'
const router = useRouter()
const agentListRef = ref<HTMLElement[]>([])
const videoListRef = ref<HTMLVideoElement[]>([])
const contentListRef = ref<HTMLElement[]>([])
const loadingRef = ref<HTMLElement>()

const scrollContainerRef = inject<Ref<HTMLElement>>('scrollContainerRef')
const aiListParams = ref<IAiListParams>({
  album_id: 1, // 专辑，现在只有1个，直接传1
  page: 1,
  limit: 10,
  sort_type: 3,
  keyword: undefined
})
const agentList = ref<IAiListItem[]>([])
const loading = ref(false)
const isRequestAll = ref(false)
const userStore = useUserStore()

const goAgentChat = (item: IAiListItem) => {
  if (!userStore.isLogin) {
    sessionStorage.setItem('login_front_address', `AI体${item.id}`)
  }
  router.push(`/chat/${item.id}`)
  eventReport({
    event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
    front_address: ForwardAddressEnum.HOME_AI_LIST,
    ai_id: item.id
  }).catch((err) => {
    console.warn(err)
  })
}

const clearData = () => {
  isRequestAll.value = false
  agentList.value = []
  aiListParams.value = {
    album_id: 1,
    page: 1,
    limit: 10,
    sort_type: 3,
    keyword: undefined
  }
}

const getAIList = (params?: IAiListParams) => {
  loading.value = true
  aiListParams.value = { ...aiListParams.value, ...params }
  queryAiList(aiListParams.value)
    .then((res) => {
      if (res.code === 200) {
        const list = res.data.list.map((item) => {
          return {
            ...item,
            height: 250
          }
        })
        agentList.value.push(...list)

        if (agentList.value.length >= res.data.count) {
          isRequestAll.value = true
        }
        nextTick(() => {
          agentScrollEvent()
        })
      }
    })
    .catch(() => {
      isRequestAll.value = true
    })
    .finally(() => {
      loading.value = false
      // firstLoading.value = false
    })
}

const agentScrollEvent = () => {
  const deviceHeight = window.innerHeight
  const deviceWidth = window.innerWidth / 10
  const getTransX = (x: number) => {
    return -(deviceWidth / deviceHeight) * x + deviceWidth / 2
  }
  agentListRef.value.forEach((item, index) => {
    const transTop = item.offsetTop - scrollContainerRef.value.scrollTop
    // if (transTop < deviceHeight / 2 && transTop + item.clientHeight > deviceHeight / 2) {
    //   videoListRef.value?.[index].play()
    // } else {
    //   videoListRef.value?.[index].pause()
    // }
    // if (index === agentListRef.value.length - 1) {
    //   if (scrollContainerRef.value.scrollTop + scrollContainerRef.value.clientHeight >= scrollContainerRef.value.scrollHeight) {
    //     videoListRef.value?.[index].play()
    //     videoListRef.value?.[index - 1].pause()
    //   }
    // }
    if (transTop < deviceHeight / 2) {
      const transX = getTransX(transTop)
      if (index % 2 === 0) {
        contentListRef.value[index].style.transform = `translateX(${transX < 0 ? '' : '-'}${transX}px)`
      } else {
        contentListRef.value[index].style.transform = `translateX(${transX < 0 ? Math.abs(transX) : transX}px)`
      }
    } else {
      contentListRef.value[index].style.transform = `translateX(0)`
    }
  })
}

getAIList()

defineExpose({
  getAIList,
  clearData,
  agentScrollEvent
})

onMounted(() => {
  scrollContainerRef.value.addEventListener('scroll', () => {
    agentScrollEvent()
  })
  useIntersectionObserver(
    loadingRef.value,
    ([{ isIntersecting }]) => {
      if (isIntersecting) {
        if (agentList.value.length === 0) return
        aiListParams.value.page++
        getAIList()
      }
    },
    {
      threshold: 0.8
    }
  )
})

onActivated(() => {
  videoListRef.value.forEach((item) => {
    item.muted = true
    item.play()
  })
})
</script>

<template>
  <div class="agent-list">
    <div
      ref="agentListRef"
      class="agent-item"
      v-for="item in agentList"
      :key="item.id"
      @click="goAgentChat(item)"
    >
      <div
        class="content-black"
        ref="contentListRef"
      ></div>
      <div class="content">
        <div>
          <div class="name">
            {{ item.name }}
          </div>
          <div class="intro">
            {{ item.synopsis }}
          </div>
        </div>

        <div class="tag-list">
          <div class="tag-item flex-center-center cg-2">
            <SvgIcon
              icon-class="home-chat-time"
              class="fsize-12"
            />
            {{ item.chat_times }}
          </div>
          <div
            v-if="item.has_3d"
            class="tag-item flex-center-center cg-2"
          >
            <SvgIcon
              icon-class="home-3d"
              class="fsize-12"
            />3D
          </div>
        </div>
      </div>
      <video
        ref="videoListRef"
        :src="item.cover_url"
        :poster="item.cover_img_url"
        muted
        autoplay
        loop
        disablepictureinpicture
        playsinline
      ></video>
    </div>
    <div
      ref="loadingRef"
      class="loading"
      v-show="!isRequestAll && agentList.length > 0"
    >
      <img
        class="loading-icon"
        src="../../assets/images/index/loading-icon.png"
        alt="loading"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '../../assets/styles/mixin';

.agent-list {
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  width: 100%;
  padding-top: 36px;
  padding-bottom: 110px;
  font-family: IBMSerif, serif;
  background: $livCoBackColor;

  .agent-item {
    position: relative;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    height: 180px;
    overflow: hidden;

    video {
      position: absolute;
      z-index: 0;
      display: block;
      height: 99%;
    }

    .content-black {
      position: absolute;
      z-index: 1;
      width: 50%;
      height: 100%;
      background: $livCoBackColor;
    }

    .content {
      z-index: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 50%;
      height: 100%;

      .name {
        @include multiLine(2);

        font-size: 22px;
        line-height: 26px;
        color: #fff;
        text-align: left;
      }

      .intro {
        @include multiLine(3);

        margin-top: 8px;
        font-family: Roboto, serif;
        font-size: 12px;
        font-weight: 400;
        line-height: 14px;
        color: rgba(141, 151, 166, 78%);
        text-align: left;
      }

      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12px 12px;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        font-family: Roboto, serif;

        .tag-item {
          padding: 4px 6px;
          font-size: 10px;
          line-height: 10px;
          color: rgba(255, 255, 255, 60%);
          text-align: left;
          background: rgba(255, 255, 255, 8%);
          //backdrop-filter: blur(48px);
          border-radius: 4px;
        }
      }
    }

    &:nth-child(2n) {
      justify-content: flex-end;

      .content {
        padding: 16px 16px 16px 24px;
      }

      .content-black {
        clip-path: polygon(0% 0%, 100% -1px, 100% calc(100% + 1px), calc(0% + 10px) 100%);
      }

      video {
        left: 0;
      }
    }

    &:nth-child(2n + 1) {
      justify-content: flex-start;

      .content {
        padding: 16px 24px 16px 16px;
      }

      .content-black {
        clip-path: polygon(0% 0%, 100% -1px, calc(100% - 10px) calc(100% + 1px), 0% 100%);
      }

      video {
        right: 0;
      }
    }
  }

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    color: #fff;

    .loading-icon {
      width: 24px;
      height: 24px;
      animation: loading 1s linear infinite;
    }
  }

  @keyframes loading {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
