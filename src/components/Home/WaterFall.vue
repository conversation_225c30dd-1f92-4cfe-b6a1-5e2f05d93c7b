<template>
  <div
    class="wraps"
    :style="{
      height: setHeight
    }"
  >
    <van-skeleton :loading="firstLoading">
      <template #template>
        <CardSkeleton
          v-if="firstLoading"
          :loading="firstLoading"
        />
      </template>
      <div
        :style="{ height: item.height + 'px', top: item.top + 'px', left: item.left + 'px' }"
        v-for="(item, index) in state.waterFallList"
        :key="index"
        class="items"
        @click="cardClickHandle(item)"
      >
        <van-image
          class="card-img"
          :src="item.image_url"
          alt="Image Background"
          fit="cover"
          :lazy-load="true"
          position="top"
        >
          <template #loading>
            <div class="image-slot"></div>
          </template>
          <template #error>
            <div class="image-slot"></div>
          </template>
        </van-image>
        <div class="view-banner flex-start-center">
          <div
            class="view flex-center-center mr-4"
            v-if="item.is_repeat === 1"
          >
            <SvgIcon
              icon-class="orignal-icon"
              class="fsize-12 mr-2"
            />
            <div class="view-num">Orignal</div>
          </div>
          <div class="view flex-center-center">
            <div class="view-icon"></div>
            <div class="view-num">{{ item.chat_times }}</div>
          </div>
        </div>

        <div class="card-content">
          <!-- <SoundPlay
            :soundUrl="item.opening_statement_voice"
            v-if="item.opening_statement_voice"
          >
            <template #default="slotProps">
              <div
                class="play-icon mb-12 flex-center-center"
                @click.stop="playSoundHandle(slotProps)"
              >
                <SoundLoading
                  class="h-full"
                  v-if="slotProps.isPlaying"
                />
                <SvgIcon
                  icon-class="sound-loding-icon"
                  class="fsize-16"
                  v-else
                />
                <div class="sound-time-length">{{ slotProps.duration }}“</div>
              </div>
            </template>
          </SoundPlay> -->
          <div class="title mb-8">
            {{ item.name }}
          </div>

          <!-- <div
            class="author mb-8"
            :class="item.tag_list.length > 0 ? '' : 'no-bottom'"
          >
            @{{ item.client_info?.name }}
          </div> -->
          <div class="flex-between-center">
            <div
              class="label"
              @click.stop="tabClickHandle(item.tag_list[0].name)"
              v-if="item.tag_list.length > 0"
            >
              {{ item.tag_list[0].name }}
            </div>
            <img
              v-if="item.has_3d === 1"
              class="switch-icon"
              src="@/assets/images/index/3D-icon.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </van-skeleton>

    <div
      id="loading"
      class="loading"
      v-show="!isRequestAll && state.waterFallList.length > 0"
    >
      <img
        class="loading-icon"
        src="../../assets/images/index/loading-icon.png"
        alt="loading"
      />
    </div>
    <div
      class="no-data"
      v-if="state.waterFallList.length === 0 && !loading"
    >
      <EmptyIcon />
    </div>
  </div>
</template>

<script setup lang="ts">
import { IAiListParams, IAiListItem } from '@/api/home/<USER>'
import { queryAiList } from '@/api/home'
import { eventReport, ForwardAddressEnum, EventTypeEnum } from '@/api/eventReport'
import { getOpeningStatementVoice } from '@/api/home'
import router from '@/router'
import useAppStore from '@/stores/modules/app'
import useUserStore from '@/stores/modules/user'
const userStore = useUserStore()
const appStore = useAppStore()
interface IState {
  waterFallList: IAiListItem[]
  homeCardList: IAiListItem[]
}
interface IEmits {
  (e: 'tab-click', value: string): void
}
const props = withDefaults(
  defineProps<{
    firstHeight?: number
    waterFallHeight?: number
    firstAiId?: number
  }>(),
  {
    firstHeight: 208,
    waterFallHeight: 258,
    firstAiId: 1
  }
)
const emit = defineEmits<IEmits>()

const loading = ref(false)
const firstLoading = ref(false)
const isRequestAll = ref(false)
const swiperObserver = ref<IntersectionObserver | null>(null)
const state = reactive<IState>({
  waterFallList: [],
  homeCardList: []
})
function tabClickHandle(label: string) {
  emit('tab-click', label)
}
function cardClickHandle(item: IAiListItem) {
  if (!userStore.token) {
    sessionStorage.setItem('login_front_address', `AI体${item.id}`)
    appStore.showLogin = true
    return
  }
  eventReport({
    event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
    front_address: ForwardAddressEnum.HOME_AI_LIST,
    ai_id: item.id
  }).catch((err) => {
    console.warn(err)
  })
  router.push(`/chat/${item.id}`)
}
const init = () => {
  state.waterFallList = []
  const heightList: number[] = []
  const inner_width = window.innerWidth >= 500 ? 500 : window.innerWidth
  const width = (inner_width - 25) / 2
  const column = 2

  for (let i = 0; i < state.homeCardList.length; i++) {
    if (i < column) {
      state.homeCardList[i].top = 9
      state.homeCardList[i].left = i * width
      heightList.push(state.homeCardList[i].height + 8)
      state.waterFallList.push(state.homeCardList[i])
    } else {
      let current = heightList[0]
      let index = 0
      heightList.forEach((h, inx) => {
        if (current > h) {
          current = h
          index = inx
        }
      })
      state.homeCardList[i].top = current + 9
      state.homeCardList[i].left = index * width
      heightList[index] = heightList[index] + state.homeCardList[i].height + 8
      state.waterFallList.push(state.homeCardList[i])
    }
  }
}
function observerHandle() {
  if (!document.querySelector('#loading')) return
  swiperObserver.value = new IntersectionObserver(
    (entries) => {
      // console.log(entries[0])
      if (state.homeCardList.length === 0) return
      if (entries[0].isIntersecting) {
        aiListParams.value.page++
        queryAiListHandle()
      }
    },
    {
      root: null,
      threshold: 0
    }
  )
  swiperObserver.value.observe(document.querySelector('#loading'))
}
const setHeight = computed(() => {
  // 33 = 加载icon 24 + 边距 9
  // 17 = 多出 50 - 加载icon 24 - 边距 9
  return state.waterFallList.length % 2 == 0
    ? (state.waterFallList.length / 2) * props.waterFallHeight + 33 + 'px'
    : (state.waterFallList.length / 2 + 0.5) * props.waterFallHeight - 17 + 'px'
})
const aiListParams = ref<IAiListParams>({
  album_id: props.firstAiId,
  page: 1,
  limit: 10,
  sort_type: 3,
  keyword: undefined
})
function queryAiListHandle(params?: IAiListParams) {
  loading.value = true
  aiListParams.value = { ...aiListParams.value, ...params }
  firstLoading.value = aiListParams.value.page === 1
  queryAiList(aiListParams.value)
    .then((res) => {
      if (res.code === 200) {
        const list = res.data.list.map((item) => {
          return {
            ...item,
            height: 250
          }
        })
        list.forEach((item) => {
          if (!item.opening_statement_voice) {
            getOpeningStatementVoice({ ai_id: item.id }).then((res) => {
              if (res.code !== 200) return
              item.opening_statement_voice = res.data as string
            })
          }
        })
        state.homeCardList.push(...list)

        if (state.homeCardList.length >= res.data.count) {
          isRequestAll.value = true
        }
        if (state.homeCardList.length === 0) return (isRequestAll.value = true)
        if (state.homeCardList[0].height > props.firstHeight) {
          state.homeCardList[0].height = props.firstHeight
        }
        init()
      }
    })
    .catch(() => {
      isRequestAll.value = true
    })
    .finally(() => {
      loading.value = false
      firstLoading.value = false
    })
}

function clearData() {
  isRequestAll.value = false
  state.homeCardList = []
  state.waterFallList = []
  aiListParams.value = {
    album_id: props.firstAiId,
    page: 1,
    limit: 10,
    sort_type: 3,
    keyword: undefined
  }
}

// function playSoundHandle(slotProps: any) {
//   !slotProps.isPlaying ? slotProps.play() : slotProps.stop()
// }

// function getSoundHandle(slotProps: any, item: any) {
//   console.log(slotProps, 'fffff')
//   getOpeningStatementVoice({ ai_id: item.id }).then((res) => {
//     console.log(res, 'dffff')
//     item.opening_statement_voice = res.data
//     !slotProps.isPlaying ? slotProps.play() : slotProps.stop()
//   })
// }

defineExpose({
  queryAiListHandle,
  clearData
})

onMounted(() => {
  queryAiListHandle()
  observerHandle()
  window.onresize = () => init()
})
onUnmounted(() => {
  if (swiperObserver.value) {
    swiperObserver.value.unobserve(document.querySelector('#loading'))
    swiperObserver.value.disconnect()
    swiperObserver.value = null
  }
})
</script>

<style scoped lang="scss">
:deep(.van-skeleton) {
  padding: 0 !important;
}

:deep(.loading-bar) {
  background-color: #fff !important;
}

:deep(.van-image) {
  img {
    border-radius: 16px !important;
  }
}

:deep(.van-image__loading) {
  background: #242424;
  border-radius: 16px !important;
}

:deep(.van-image__error) {
  background-color: #242424;
  border-radius: 16px !important;
}

.wraps {
  position: relative;
  height: 100%;
  min-height: calc(100vh - 60px);

  .items {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    width: calc((100% - 8px) / 2);
    padding-top: 12px;
    overflow: hidden;
    background: linear-gradient(180deg, rgba(36, 36, 36, 0%) 0%, rgba(36, 36, 36, 0%) 50%, rgba(36, 36, 36, 60%) 70%, #242424 100%);
    border-radius: 16px;

    .card-img {
      position: absolute;
      top: 0;
      right: 0;
      z-index: -1;
      width: 100%;
      height: 99.9%;
    }

    .view-banner {
      position: absolute;
      top: 8px;
      left: 8px;
    }

    .view {
      width: fit-content;
      padding: 4px 8px;
      background: rgba(23, 23, 23, 50%);
      backdrop-filter: blur(8px);
      border-radius: 16px;
    }

    .switch-icon {
      width: 24px;
      height: 24px;
    }

    .view-icon {
      width: 12px;
      height: 12px;
      margin-right: 2px;
      background: url('~@/assets/images/index/view-icon.png') no-repeat;
      background-size: contain;
    }

    .view-num {
      font-size: 12px;
      font-weight: 500;
    }

    .card-content {
      padding: 0 8px 12px;
      border-radius: 16px;

      .author {
        font-size: 11px;
        color: rgba(255, 255, 255, 60%);
      }

      .play-icon {
        display: flex;
        width: fit-content;
        height: 24px;
        padding: 5px 8px;
        background: rgba(193, 41, 246, 70%);
        backdrop-filter: blur(3px);
        border: 1px solid rgba(255, 255, 255, 15%);
        border-radius: 16px;
      }

      .active {
        animation: sound 1s linear infinite;
      }

      @keyframes sound {
        0% {
          transform: scale(1);
        }

        50% {
          transform: scale(1.2);
        }

        100% {
          transform: scale(1);
        }
      }

      .sound-time-length {
        margin-left: 4px;
        font-size: 14px;
      }

      .sound-time-length {
        font-size: 14px;
        font-weight: 500;
        line-height: 16px;
        color: #fff;
      }

      .title {
        font-size: 14px;
        font-weight: 800;
        line-height: 14px;
        word-break: break-all;
        overflow-wrap: break-word;
      }

      .no-bottom {
        margin-bottom: 0;
      }

      .label {
        display: flex;
        width: fit-content;
        padding: 4px 6px;
        font-size: 10px;
        color: #ffefcf;
        background: rgba(24, 24, 24, 70%);
        backdrop-filter: blur(4px);
        border-radius: 6px;
      }
    }
  }

  .loading {
    position: absolute;
    bottom: 0;
    left: 50%;
    color: #fff;
    transform: translateX(-50%);

    .loading-icon {
      width: 24px;
      height: 24px;
      animation: loading 1s linear infinite;
    }
  }

  @keyframes loading {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .no-data {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .image-slot {
    width: 110px;
    height: 45px;
    //background: url('~@/assets/images/eros-black2.png') no-repeat center center / contain;
  }
}
</style>
