<script setup lang="ts">
import type { <PERSON>ban<PERSON>, bannerLogParams, bannerSettingRes } from '@/api/home/<USER>'
import { queryBanner, bannerLog, bannerSetting } from '@/api/home'
import useUserStore from '@/stores/modules/user'
import { eventReport, ForwardAddressEnum, EventTypeEnum } from '@/api/eventReport'
import useAppStore from '@/stores/modules/app'

const appStore = useAppStore()
const userStore = useUserStore()
const router = useRouter()
const imgList = ref<Ibannner[]>([])
const isEnd = ref(false)
const firstLoading = ref(false)
const bannerSettingInfo = ref<bannerSettingRes>({
  roll: 0,
  time: 0
})

const title = (active: number) => {
  if (Number.isNaN(active)) return
  return imgList.value[active].subtitle
}

function bannerClickHandle(item: Ibannner) {
  console.log(item, ';;;;;;;;;;;;;;;;;;;')

  // 1- url， 2 -智能体
  bannerLogHandle({
    banner_id: item.id,
    action: 2
  })
  switch (item.type) {
    case 1:
      if (!item.redirect_url_lang) return
      if (item.is_outside === 0) {
        sessionStorage.setItem('login_front_address', ForwardAddressEnum.HOME_BANNER + item.id)
        return router.push(item.redirect_url_lang)
      }
      window.open(item.redirect_url_lang)
      break
    case 2:
      if (!userStore.isLogin) {
        sessionStorage.setItem('login_front_address', `AI体${item.ai_info?.id}`)
        appStore.showLogin = true
        return
      }
      if (!item.ai_info?.id) return
      eventReport({
        event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
        front_address: ForwardAddressEnum.HOME_BANNER,
        ai_id: item.ai_info.id
      }).catch((err) => {
        console.warn(err)
      })
      router.push('/chat/' + item.ai_info.id)
      break
    default:
      break
  }
}
function bannerChangeHandle(index: number) {
  if (isEnd.value) return
  // console.log(index)
  if (index === imgList.value.length - 1) {
    isEnd.value = true
  }
  bannerLogHandle({
    banner_id: imgList.value[index].id,
    action: 1
  })
}

function bannerLogHandle(params: bannerLogParams) {
  if (!userStore.isLogin) return
  bannerLog(params)
    .then(() => {})
    .catch(() => {})
}
function queryBannerHandle() {
  firstLoading.value = true
  queryBanner()
    .then((res) => {
      if (res.code !== 200) return
      imgList.value = res.data
      if (!imgList.value.length) return
      bannerLogHandle({ action: 1, banner_id: imgList.value[0].id })
    })
    .finally(() => {
      firstLoading.value = false
    })
}
function bannerSettingHandle() {
  bannerSetting().then((res) => {
    bannerSettingInfo.value = res.data
  })
}
bannerSettingHandle()
queryBannerHandle()

defineExpose({
  queryBannerHandle,
  isEnd,
  imgList,
  firstLoading
})
</script>
<template>
  <div
    class="swiper-container .w-full"
    v-if="imgList.length > 0 || firstLoading"
  >
    <van-swipe
      :stop-propagation="true"
      :autoplay="bannerSettingInfo.roll === 1 ? bannerSettingInfo.time * 1000 : ''"
      lazy-render
      class="my-swipe"
      @change="bannerChangeHandle"
    >
      <van-swipe-item
        v-for="item in imgList"
        :key="item.id"
        @click="bannerClickHandle(item)"
      >
        <van-image
          width="100%"
          height="100%"
          :src="item.banner_img_lang"
          lazy-load
          fit="cover"
        >
          <!--          <template #loading>-->
          <!--            <img-->
          <!--              class="image-slot"-->
          <!--              src="@/assets/images/eros-black2.png"-->
          <!--            />-->
          <!--          </template>-->
        </van-image>
        <div class="mask"></div>
      </van-swipe-item>
      <template #indicator="{ active, total }">
        <div class="custom-indicator">
          <div class="indicator-line flex-end-center">
            <div
              class="indicator-item"
              :class="active === index - 1 ? 'active' : ''"
              v-for="index in total"
              :key="index"
            ></div>
          </div>
          <van-skeleton :loading="firstLoading">
            <template #template>
              <div class="skeleton"></div>
            </template>
            <div class="swiper-title">{{ title(active) }}</div>
          </van-skeleton>
        </div>
      </template>
    </van-swipe>
  </div>
</template>

<style scoped lang="scss">
:deep(.van-skeleton) {
  padding: 0;
}

.swiper-container {
  position: relative;
  height: 375px;

  .skeleton {
    width: 223px;
    height: 24px;
    background: #242424;
    border-radius: 6px;
  }

  .my-swipe {
    height: 100%;

    .van-swipe-item {
      position: relative;
      height: 100%;
      color: #fff;
      text-align: center;
    }

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        180deg,
        rgba(24, 24, 24, 80%) 0%,
        rgba(24, 24, 24, 0%) 24%,
        rgba(24, 24, 24, 0%) 60%,
        rgba(24, 24, 24, 40%) 70%,
        rgba(24, 24, 24, 60%) 80%,
        rgba(24, 24, 24, 80%) 90%,
        #181818 100%
      );
    }

    .custom-indicator {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 102px;
      padding: 42px 16px 0;

      .indicator-line {
        height: 8px;
        margin-bottom: 4px;

        .indicator-item {
          width: 6px;
          height: 6px;
          margin-left: 8px;
          background-image: url('~@/assets/images/index/indicators.png');
          background-size: contain;

          &.active {
            width: 8px;
            height: 8px;
            background-image: url('~@/assets/images/index/indicators-active.png');
            background-size: contain;
          }
        }
      }

      .swiper-title {
        overflow: hidden;
        font-size: 20px;
        font-weight: bold;
        line-height: 24px;
        color: #fff;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
