<script setup lang="ts">
import { eventReport, EventTypeEnum } from '@/api/eventReport'
const props = withDefaults(
  defineProps<{
    showSearch: boolean
    isFixed?: boolean
  }>(),
  {
    showSearch: false,
    isFixed: false
  }
)
const { t } = useI18n()
const router = useRouter()
function searchClick() {
  eventReport({
    event_type: EventTypeEnum.CLICK_SEARCH_BOX
  }).catch((err) => {
    console.warn(err)
  })
  router.push('/search')
}
</script>

<template>
  <div class="rowBtn-container">
    <div
      class="ipt"
      :style="{ top: props.showSearch ? '0' : '-140px', position: props.isFixed ? 'fixed' : 'static' }"
    >
      <div
        class="ipt-box"
        @click="searchClick"
      >
        <div class="ipt-box-mask flex-start-center">
          <SvgIcon
            icon-class="search-icon"
            class="fsize-18 mr-8"
          />
          <span class="search-txt">{{ t('searchWant') }}</span>
        </div>
      </div>
      <!-- <TabsList
        ref="tabListRef"
        :tabsList="tabsList"
        :loading="tablistLoading"
        :tabActive="tabActive"
        @tab-click="tabLabelClickHandle"
        @more-click="showHomeTypeSelectPopup = true"
      /> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ipt {
  position: fixed;
  top: -150px;
  z-index: 999;
  width: 100%;
  // height: 96px;
  padding: 4px 16px;
  background-color: $livCoBackColor;
  transition: all 0.2s ease-in-out;
}

.ipt-box {
  width: 343px;
  height: 40px;
  padding: 1px;
  background: conic-gradient(#8975f0b2, #b55bdf91, #826cffa9, #7c73afbb);
  border-radius: 52px;
}

.ipt-box-mask {
  width: 100%;
  height: 100%;
  padding: 10px 16px;
  background: #181818;
  border-radius: 52px;

  .search-txt {
    font-size: 14px;
    line-height: 20px;
    color: rgba(255, 255, 255, 30%);
  }
}
</style>
