<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    firstHeight?: number
  }>(),
  {
    firstHeight: 208
  }
)
const carlist = ref([250, 250, 250, 250])
</script>

<template>
  <div class="skeleton-container flex-center-start">
    <div class="left-container">
      <div
        v-for="(item, index) in carlist"
        :key="index"
        :style="{ height: index == 0 ? `${props.firstHeight}px` : `${item}px` }"
        class="skeleton-items"
      >
        <div class="skeleton-item"></div>
        <div class="skeleton-item w-143"></div>
        <div class="skeleton-item w-97"></div>
      </div>
    </div>
    <div class="right-container">
      <div
        v-for="(item, index) in carlist"
        :key="index"
        :style="{ height: item + 'px' }"
        class="skeleton-items"
      >
        <div class="skeleton-item"></div>
        <div class="skeleton-item w-143"></div>
        <div class="skeleton-item w-97"></div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.skeleton-container {
  justify-content: space-between;
  width: 100%;
  margin-top: 9px;

  .left-container,
  .right-container {
    width: calc((100% - 10px) / 2);
  }

  .skeleton-items {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    width: 100%;
    // height: 250px;
    padding: 12px;
    margin-bottom: 8px;
    background: #242424;
    border-radius: 16px;
  }

  .skeleton-item {
    width: 46px;
    height: 22px;
    margin-bottom: 8px;
    background: #2c2b2b;
    border-radius: 8px;

    &.w-143 {
      width: 143px;
    }

    &.w-97 {
      width: 97px;
    }
  }
}
</style>
