<template>
  <div
    @click.stop="$emit('search-click')"
    class="search-btn flex-center-center"
    :style="{ top: props.top + 'px', right: props.right + 'px' }"
  >
    <img
      src="../../assets/images/index/search-icon.png"
      alt=""
    />
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  top: {
    type: Number,
    default: 4
  },
  right: {
    type: Number,
    default: 16
  }
})
</script>

<style lang="scss" scoped>
.search-btn {
  position: absolute;
  z-index: 999;
  width: 50px;
  height: 40px;
  padding: 1px;
  background: rgba(24, 24, 24, 50%);
  border: 1px solid rgba(255, 255, 255, 10%);
  border-radius: 52px;

  img {
    width: 18px;
    height: 18px;
  }
}
</style>
