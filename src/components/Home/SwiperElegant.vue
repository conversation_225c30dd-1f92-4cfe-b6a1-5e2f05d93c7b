<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import type { bannerLogParams, bannerSettingRes, Ibannner } from '@/api/home/<USER>'
import { bannerLog, bannerSetting, queryBanner } from '@/api/home'
import useUserStore from '@/stores/modules/user.ts'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
import useAppStore from '@/stores/modules/app.ts'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { BannerShowEnum } from '@/enums'
const userStore = useUserStore()
const router = useRouter()
const appStore = useAppStore()
const isEnd = ref(false)
const hadShowBannerMap = new Map()

const swiperItemRef = ref<HTMLElement[]>([])
const pointerListRef = ref<HTMLElement[]>([])
const scrollRef = ref<HTMLElement | null>(null)

const swiperImageRef = ref()
const swiperImageListRef = ref()
const agentListRef = inject('agentListRef')
const scrollContainerRef = inject<Ref<HTMLElement>>('scrollContainerRef')

const bannerList = ref<Ibannner[]>([])
const bannerSettingInfo = ref<bannerSettingRes>({
  roll: 0,
  time: 5
})

const activeIndex = ref(0)
let autoplayInterval: number | null = null

const clearIntervalEvent = () => {
  if (bannerSettingInfo.value.roll) {
    autoplayInterval && clearInterval(autoplayInterval)
    autoplayInterval = null
  }
}

const setIntervalEvent = () => {
  if (bannerSettingInfo.value.roll) {
    if (autoplayInterval) return
    // @ts-ignore
    autoplayInterval = setInterval(updateActiveIndex, bannerSettingInfo.value.time * 1000)
  }
}

const updateActiveIndex = () => {
  const next = (activeIndex.value + 1) % swiperItemRef.value.length
  scrollRef.value?.scrollTo({
    left: swiperItemRef.value[next].offsetLeft,
    behavior: next ? 'smooth' : 'instant'
  })
}

function bannerAutoChangeHandle(index: number) {
  if (isEnd.value) return
  if (hadShowBannerMap.has(index)) return
  if (hadShowBannerMap.size === bannerList.value.length) {
    isEnd.value = true
  }
  hadShowBannerMap.set(index, true)
  bannerLogHandle({
    banner_id: bannerList.value[index].id,
    action: BannerShowEnum.SHOW
  })
  hadShowBannerMap.set(index, true)
}

const bannerClickHandle = (item: Ibannner) => {
  // 1- url， 2 -智能体
  bannerLogHandle({
    banner_id: item.id,
    action: BannerShowEnum.CLICK
  })
  switch (item.type) {
    case 1:
      if (!item.redirect_url_lang) return
      if (item.is_outside === 0) {
        sessionStorage.setItem('login_front_address', ForwardAddressEnum.HOME_BANNER + item.id)
        return router.push(item.redirect_url_lang)
      }
      window.open(item.redirect_url_lang)
      break
    case 2:
      if (!userStore.isLogin) {
        sessionStorage.setItem('login_front_address', `AI体${item.ai_info?.id}`)
        appStore.showLogin = true
        appStore.loginRedirectPath = `/chat/${item.ai_info.id}`
        return
      }
      if (!item.ai_info?.id) return
      eventReport({
        event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
        front_address: ForwardAddressEnum.HOME_BANNER,
        ai_id: item.ai_info.id
      }).catch((err) => {
        console.warn(err)
      })
      router.push('/chat/' + item.ai_info.id)
      break
    default:
      break
  }
}

const bannerLogHandle = (params: bannerLogParams) => {
  if (!userStore.isLogin) return
  bannerLog(params)
    .then(() => {})
    .catch(() => {})
}

const queryBannerHandle = () => {
  // firstLoading.value = true
  queryBanner()
    .then(({ code, data }) => {
      if (code === ResultEnum.SUCCESS) {
        bannerList.value = data
        nextTick(() => {
          // @ts-ignore
          agentListRef?.value?.agentScrollEvent()
          scrollRef.value?.addEventListener('touchstart', () => {
            clearIntervalEvent()
          })
          scrollRef.value?.addEventListener('touchend', () => {
            setIntervalEvent()
          })

          // 启动自动轮播
          if (bannerSettingInfo.value.roll) {
            if (autoplayInterval) return
            setIntervalEvent()
          }
        })
        if (!bannerList.value.length) return
        bannerLogHandle({ action: 1, banner_id: bannerList.value[0].id })
      }
    })
    .finally(() => {
      // firstLoading.value = false
    })
}

const getBannerSetting = () => {
  bannerSetting()
    .then(({ code, data }) => {
      if (code === ResultEnum.SUCCESS) {
        bannerSettingInfo.value = data
        queryBannerHandle()
      }
    })
    .catch((err) => {
      console.warn(err)
    })
}
getBannerSetting()

watch(
  () => activeIndex.value,
  (val) => {
    if (val) {
      bannerAutoChangeHandle(val)
    }
  },
  {
    immediate: true
  }
)

defineExpose({
  bannerList,
  activeIndex,
  queryBannerHandle,
  autoplayInterval
})

const swiperImageEvent = () => {
  const scrollTop = scrollContainerRef.value.scrollTop // 获取指定元素的滚动距离
  swiperImageRef.value.style.transform = `translateY(-${scrollTop / 3}px)`
}

const swiperEvent = () => {
  const scrollLeft = scrollRef.value?.scrollLeft ?? 0 // 当前滚动位置
  // const visibleWidth = scrollRef.value?.offsetWidth ?? 0 // 可视区域宽度

  swiperItemRef.value.forEach((item: HTMLElement, index: number) => {
    const itemOffsetLeft = item.offsetLeft // 子元素相对于容器的左偏移
    const itemWidth = item.offsetWidth // 子元素宽度

    // 子元素的可视区域范围
    const itemVisibleStart = itemOffsetLeft - scrollLeft // 子元素在可视区域的起点
    const itemVisibleEnd = itemVisibleStart + itemWidth // 子元素在可视区域的终点

    // 计算透明度
    let opacity
    swiperImageListRef.value[index].style.opacity = ((-1 / itemWidth) * itemVisibleStart + 1).toString()
    // 子元素与可视区域有重叠
    if (itemVisibleStart >= 0 && itemVisibleStart < 375) {
      opacity = Math.max(0, 1 - itemVisibleStart / itemWidth)
    } else if (itemVisibleEnd > 0 && itemVisibleEnd <= 375) {
      // 进入左侧 1/2 范围时，逐渐从 0 增加到 1
      opacity = Math.min(1, itemVisibleEnd / itemWidth)
    } else {
      opacity = 1
    }

    if (itemVisibleStart < 100 && itemVisibleEnd > itemWidth - 100) {
      activeIndex.value = index
    }
    // 设置透明度
    item.style.opacity = opacity.toString()
  })
}

document.addEventListener('visibilitychange', () => {
  clearIntervalEvent()
})

document.addEventListener('visibilitychange', () => {
  if (autoplayInterval) return
  setIntervalEvent()
})

onDeactivated(() => {
  clearIntervalEvent()
})

onActivated(() => {
  if (autoplayInterval) return
  swiperEvent()
  swiperImageEvent()
  setIntervalEvent()
})

onMounted(() => {
  scrollContainerRef.value.addEventListener('scroll', swiperImageEvent)

  swiperEvent()
  scrollRef.value?.addEventListener('scroll', swiperEvent)
})

onUnmounted(() => {
  if (autoplayInterval) {
    clearIntervalEvent()
  }
})
</script>

<template>
  <div
    class="swiper-img"
    ref="swiperImageRef"
  >
    <div
      ref="swiperImageListRef"
      class="img-container"
      v-for="item in bannerList"
      :key="item.id"
    >
      <van-image
        :src="item.banner_img_lang"
        width="100%"
        alt=""
        fit="cover"
      >
        <template #loading>
          <img
            class="image-slot"
            src="/ugenie.png"
            alt="loading"
          />
        </template>
      </van-image>
    </div>
  </div>
  <div
    class="swiper-scroll"
    ref="scrollRef"
  >
    <div
      ref="swiperItemRef"
      class="swiper-item"
      v-for="item in bannerList"
      :key="item.id"
      @click="bannerClickHandle(item)"
    >
      <div>
        <div class="title">{{ item.name }}</div>
        <div class="small-title">{{ item.subtitle }}</div>
      </div>
    </div>
  </div>
  <div class="scroll-pointer">
    <div
      ref="pointerListRef"
      v-for="(_, index) in bannerList"
      :key="index"
      class="pointer-item"
      :class="{ active: activeIndex === index }"
      @click="
        () => {
          activeIndex = index
          scrollRef?.scrollTo({
            left: swiperItemRef[activeIndex].offsetLeft,
            behavior: 'instant'
          })
        }
      "
    ></div>
  </div>
</template>

<style scoped lang="scss">
.swiper-img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 70%;
  max-height: 456px;

  .img-container {
    position: absolute;
    width: 100%;
    height: 100%;

    :deep(.van-image) {
      position: absolute;
      height: 100%;
    }

    &:first-child {
      opacity: 1;
    }

    &:not(:first-child) {
      opacity: 0;
    }
  }
}

.swiper-scroll {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  font-family: IBMSerif, serif;
  background: linear-gradient(180deg, rgba(0, 0, 0, 20%) 0%, rgba(0, 0, 0, 40%) 60%, rgba(0, 0, 0, 60%) 80%, #000 100%);
  scroll-snap-type: x mandatory;
}

.swiper-item {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  scroll-snap-align: start;
  scroll-snap-stop: always;

  &:first-child {
    opacity: 1;
  }
}

.title {
  padding: 0 20px;
  margin-bottom: 8px;
  font-family: IBMSerif, serif;
  font-size: 32px;
  line-height: 32px;
  color: #fff;
  text-align: left;
}

.small-title {
  padding: 0 20px 24px;
  font-family: Roboto, serif;
  font-size: 14px;
  line-height: 16px;
  color: rgba(141, 151, 166, 78%);
  text-align: left;
}

.scroll-pointer {
  display: flex;
  column-gap: 8px;
  align-items: flex-end;
  justify-content: flex-start;
  width: 100%;
  height: 4px;
  padding-left: 20px;
  background: $livCoBackColor;

  .pointer-item {
    width: 8px;
    height: 3px;
    background: rgba(235, 223, 172, 40%);
    border-radius: 0 2px;
    transition: all 0.2s ease;
  }

  .active {
    width: 16px;
    background-color: #ebdfac;
  }
}
</style>
