<script setup lang="ts">
interface Props {
  value: string
  loading?: boolean
}
const emit = defineEmits<{
  (e: 'submit', val: string): void
}>()
const props = withDefaults(defineProps<Props>(), {
  value: '',
  loading: false
})

const { t } = useI18n()
const internalValue = props.value
const isExpanded = ref(false)
const currentCount = ref(0)
const isError = ref(false)
const isShow = ref(false)
const textareaHeight = ref('auto')
const textareaRef = ref()
const updateCount = () => {
  currentCount.value = internalValue.length
  emit('submit', internalValue)
  textareaHeight.value = 'auto' // 重置高度
  nextTick(() => {
    textareaHeight.value = `${textareaRef.value.scrollHeight}px` // 设置为内容的高度
    if (textareaRef.value.scrollHeight > 100) {
      isShow.value = true
      isExpanded.value = true
    } else {
      isShow.value = false
    }
  })
}
const validateInput = () => {
  isError.value = internalValue.trim() === ''
}
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}
</script>
<template>
  <textarea
    ref="textareaRef"
    v-model="internalValue"
    :style="{ height: isExpanded ? textareaHeight : '100px', resize: 'none' }"
    @input="updateCount"
    @blur="validateInput"
    maxLength="300"
    :placeholder="t('inputContentTips')"
    class="textarea"
  ></textarea>
  <div class="character-limit">{{ currentCount }} / 300</div>
  <div class="toggleBtn">
    <img
      v-if="isExpanded && isShow"
      src="@\assets\images\agentCreate\fold.png"
      alt=""
      style="width: 24.25px; height: 13px"
      @click="toggleExpand"
    />
    <img
      v-else-if="!isExpanded && isShow"
      src="@\assets\images\agentCreate\expand.png"
      alt=""
      style="width: 24.25px; height: 13px"
      @click="toggleExpand"
    />
  </div>
  <div
    v-if="isError"
    class="error-message"
  >
    {{ t('inputEmptyTips') }}
  </div>
</template>
<style lang="scss" scoped>
.textarea {
  background-color: transparent;
  border: none;
}

.character-limit {
  font-size: 12px;
  color: #b3a6af;
  text-align: right;
}

.toggleBtn {
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}

.error-message {
  margin-bottom: 16px;
  font-size: 12px;
  color: #fe1375;
}
</style>
