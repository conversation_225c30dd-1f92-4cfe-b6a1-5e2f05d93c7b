<script setup lang="ts">
import Cropper from 'cropperjs'
import useAgentCreateStore from '@/stores/modules/agentCreate'
import Loading from '../Loading.vue'
import 'cropperjs/dist/cropper.css'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'

interface Props {
  closePopup: () => void
  goPage?: () => void
}
const props = withDefaults(defineProps<Props>(), {
  closePopup: () => {},
  goPage: () => {}
})
const { t } = useI18n()
const agentCreateStore = useAgentCreateStore()
const loading = ref(true)
const imgRef = ref<HTMLImageElement | null>(null)
let cropper: Cropper
onMounted(() => {
  ready()
  showCropper()
})
const ready = () => {
  imgRef.value.addEventListener('ready', () => {
    loading.value = false
  })
}
const showCropper = () => {
  cropper = new Cropper(imgRef.value, {
    aspectRatio: 1,
    viewMode: 1,
    dragMode: 'none',
    autoCropArea: 0.4,
    background: false,
    scalable: true,
    zoomable: true,
    cropBoxResizable: true,
    cropBoxMovable: true,
    minCropBoxWidth: 100,
    minCropBoxHeight: 100,
    center: false
  })
}

const cropImage = () => {
  cropper.getCroppedCanvas().toBlob((res) => {
    agentCreateStore.avatar = URL.createObjectURL(res)
    agentCreateStore.showAvatar = true
    eventReport({ event_type: EventTypeEnum.COMPLETE_EDIT_AI_AVATAR, front_address: ForwardAddressEnum.NEW_CREATION })
    props.closePopup()
    props.goPage()
  })

  // console.log('imgUrl', imgUrl);
  // console.log(cropper.getCanvasData())
  // console.log(cropper.getContainerData())
  // console.log(cropper.getCropBoxData())
  // console.log(cropper.getCroppedCanvas())
  // console.log(cropper.getData())
  // console.log(cropper.getImageData())
}
</script>
<template>
  <div class="img-box flex-center-center">
    <img
      ref="imgRef"
      :src="agentCreateStore.img_url"
      class="img"
    />
  </div>
  <div class="btn-box">
    <div
      class="btn flex-center-center"
      @click="closePopup"
    >
      {{ t('cancelButton') }}
    </div>
    <div
      class="btn btn-primary flex-center-center"
      @click="cropImage"
    >
      <span v-if="!loading"> {{ t('confirmSelection') }} </span>
      <BtnLoading v-else />
    </div>
  </div>
  <Loading v-if="loading" />
</template>
<style lang="scss" scoped>
.img-box {
  position: relative;
  width: 271px;
  margin: auto;

  &::after {
    position: absolute;
    top: -4px;
    left: -4px;
    z-index: -1;
    width: calc(100% + 8px);
    height: calc(100% + 8px);
    content: '';
    background: linear-gradient(180deg, rgba(255, 53, 242, 100%), rgba(255, 109, 89, 100%));
    border-radius: 8px;
  }

  :deep(.cropper-container) {
    img {
      border-radius: 8px;
    }

    .cropper-modal {
      border-radius: 8px;
    }

    .cropper-line {
      background: #fff;
    }

    .cropper-point {
      background: #fff;
    }

    .cropper-view-box {
      outline: none;
      outline-color: transparent;
    }
    // .cropper-dashed {
    //   border: 1px solid #eee;
    //   opacity: 1;
    // }
  }

  .img {
    width: 100%;
    border-radius: 8px;
    object-fit: cover;
  }
}

.btn-box {
  display: flex;
  justify-content: space-evenly;
  margin-top: 60px;

  .btn {
    width: 164px;
    height: 52px;
    color: #fff;
    background: #2b2b2b;
    border-radius: 100px;
  }

  .btn-primary {
    background: #ff35f2;
  }
}
</style>
