<script setup lang="ts">
import { debounce } from 'lodash-es'

interface Props {
  title?: string
  fixed?: boolean
  safeAreaInsetTop?: boolean
  placeholder?: boolean
  border?: boolean
  zIndex?: number
  isShowClose?: boolean
}
const emit = defineEmits<{
  navBarClose: []
}>()

withDefaults(defineProps<Props>(), {
  fixed: true,
  safeAreaInsetTop: true,
  placeholder: true,
  border: false,
  zIndex: 1,
  isShowClose: true
})
let scrollDom: HTMLElement
const themeVars = reactive({
  navBarBackground: 'transparent'
})
onMounted(() => {
  scrollDom = document.getElementById('nav-create-agent-container')
  scrollDom && addScrollListener()
})

onBeforeUnmount(() => {
  scrollDom.removeEventListener('scroll', scroll)
})

const addScrollListener = () => {
  scrollDom.addEventListener('scroll', scroll)
}
const scroll = debounce(() => {
  const top = scrollDom.scrollTop
  if (top > 60) {
    themeVars.navBarBackground = '#120B0F'
  } else {
    themeVars.navBarBackground = 'transparent'
  }
}, 100)
const navBarClose = () => {
  emit('navBarClose')
}
</script>
<template>
  <van-config-provider :theme-vars="themeVars">
    <van-nav-bar
      :title="title"
      :fixed="fixed"
      :safe-area-inset-top="safeAreaInsetTop"
      :placeholder="true"
      :border="border"
      :z-index="zIndex"
      class="nav"
    >
      <template #left>
        <slot name="left"> </slot>
      </template>
      <template
        v-if="isShowClose"
        #right
      >
        <img
          src="@/assets/images/agentCreate/close.png"
          style="width: 20px; height: 20px"
          @click="navBarClose"
        />
      </template>
    </van-nav-bar>
  </van-config-provider>
</template>
<style lang="scss" scoped>
.nav {
  :deep(.van-nav-bar) {
    transition: background 1s;
  }
}
</style>
