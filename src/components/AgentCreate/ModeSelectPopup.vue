<script setup lang="ts">
import useAgentCreateStore from '@/stores/modules/agentCreate'

const agentCreateStore = useAgentCreateStore()
const { t } = useI18n()
const modeSelect = (type: string) => {
  agentCreateStore.agentCreateMode = type
  agentCreateStore.showRolePopup = true
}
const closeModePopup = () => {
  agentCreateStore.showAgentCreateModePopup = false
}
</script>
<template>
  <van-popup
    v-model:show="agentCreateStore.showAgentCreateModePopup"
    round
    position="bottom"
    class="popup"
  >
    <div class="title">
      <span>{{ t('creationMethod') }}</span>
      <img
        src="@/assets/images/agentCreate/close.png"
        class="close"
        @click="closeModePopup"
      />
    </div>
    <div class="mode-item-box">
      <div
        class="item"
        @click="modeSelect('quick')"
      >
        <img
          src="@/assets/images/agentCreate/fast-bg.png"
          class="item-bg"
        />
        <div class="content">
          <img
            src="@/assets/images/agentCreate/fast.png"
            class="img"
          />
          <div class="name">{{ t('quickCreate') }}</div>
        </div>
      </div>
      <div
        class="item"
        @click="modeSelect('advanced')"
      >
        <img
          src="@/assets/images/agentCreate/senior-bg.png"
          class="item-bg"
        />
        <div class="content">
          <img
            src="@/assets/images/agentCreate/senior.png"
            class="img"
          />
          <div class="name">{{ t('advancedCreation') }}</div>
        </div>
      </div>
    </div>
  </van-popup>
</template>
<style lang="scss" scoped>
.popup {
  border-radius: 24px 24px 0 0;

  .title {
    display: flex;
    justify-content: space-between;
    padding: 24px 16px;
    font-size: 18px;

    .close {
      width: 20px;
      height: 20px;
    }
  }

  .mode-item-box {
    display: flex;
    justify-content: space-evenly;
    margin-top: 24px;
    margin-bottom: 50px;

    .item {
      position: relative;
      width: 164px;
      height: 142px;

      .item-bg {
        width: 100%;
        height: 100%;
      }

      .content {
        position: absolute;
        inset: 16px 4px 4px;
        margin: auto;
        text-align: center;

        .img {
          width: 80px;
          height: 80px;
        }

        .name {
          margin-top: 8px;
        }
      }
    }
  }
}
</style>
