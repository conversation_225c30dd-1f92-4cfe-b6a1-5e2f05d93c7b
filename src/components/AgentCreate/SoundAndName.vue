<script setup lang="ts">
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { getRandomAiApi, getVoicePreList } from '@/api/agentCreate/index'

import Loading from '../Loading.vue'

const { t } = useI18n()
const agentCreateStore = useAgentCreateStore()
const router = useRouter()
const loading = ref(false)
const goSound = () => {
  router.push({ name: 'SoundSetting' })
}
const getRandomAi = async () => {
  loading.value = true
  const res = await getRandomAiApi({ action: 'add' })
  if (res.data !== null) {
    agentCreateStore.name = res.data.name
    agentCreateStore.back_story = res.data.back_story
    agentCreateStore.opening_statement = res.data.opening_statement
    agentCreateStore.role_setting = res.data.role_setting
    agentCreateStore.synopsis = res.data.synopsis
    agentCreateStore.talk_example = res.data.talk_example
  }
  loading.value = false
}

const lang = ref('')
const soundList = ref([])
const checkSound = ref([])
const handleVoicePreList = () => {
  loading.value = true
  getVoicePreList({ lang: lang.value || 'cn', sex: 0 }).then((res) => {
    if (res.code === 200) {
      setTimeout(() => {
        soundList.value = res.data.list
        checkSound.value = soundList.value.filter((item) => {
          if (item.voice_id === agentCreateStore.sound) {
            return item
          }
        })
        loading.value = false
      }, 100)
    }
  })
}
handleVoicePreList()
</script>
<template>
  <div class="sound-container">
    <div class="nickname-box">
      <van-field
        v-model="agentCreateStore.name"
        :center="true"
        :placeholder="t('enterNickname')"
        class="inp"
      >
        <template #right-icon>
          <img
            src="@/assets/images/agentCreate/dice.png"
            class="right-img"
            @click="getRandomAi"
          />
        </template>
      </van-field>
    </div>
    <div
      class="sound-box"
      style="margin-top: 15px"
      @click="goSound"
    >
      <div style="display: flex; justify-content: space-between; width: 100%">
        <div class="label">
          <img
            src="@/assets/images/agentCreate/music-play.png"
            class="music-play"
          />
          <span>{{ t('voiceOption') }}</span>
        </div>
        <img
          src="@/assets/images/agentCreate/add3.png"
          class="right-img"
        />
      </div>
      <van-tag
        v-if="agentCreateStore.sound > 0"
        class="tag"
        type="primary"
        >{{ checkSound[0]?.name }}</van-tag
      >
    </div>
  </div>
  <Loading v-if="loading" />
</template>
<style lang="scss" scoped>
.sound-container {
  margin: 36px 16px;

  .nickname-box {
    .inp {
      height: 50px;
      font-size: 16px;
      background: #3f353b;
      border-radius: 16px;

      :deep(.van-field__control::placeholder) {
        font-size: 16px;
        color: #888;
      }
    }
  }

  .right-img {
    width: 24px;
    height: 24px;
    vertical-align: middle;
  }

  .sound-box {
    padding: 20px 16px;
    font-size: 16px;
    background: #3f353b;
    border-radius: 16px;

    .label {
      display: flex;
      align-items: center;

      .music-play {
        width: 20px;
        height: 20px;
        margin-right: 12px;
      }
    }

    .tag {
      padding: 6px 10px;
      margin-top: 12px;
      margin-right: 8px;
      color: #fff;
      background-color: rgba(237, 59, 230, 50%);
      border-color: rgba(237, 59, 230, 100%);
      border-radius: 12px;
    }
  }
}
</style>
