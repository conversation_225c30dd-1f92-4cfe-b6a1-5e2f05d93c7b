<script setup lang="ts">
import { useModal } from '@/hooks/useModal'
import useAgentCreateStore from '@/stores/modules/agentCreate'
const agentCreateStore = useAgentCreateStore()
const router = useRouter()
const { t } = useI18n()
const roleType = ref('')
const open = ref(false)
const btnDisabled = ref(true)
const roleSelect = (type: string) => {
  roleType.value = type
  let sex = type === 'male' ? 1 : type === 'female' ? 2 : type === 'neutral' ? 3 : 0
  agentCreateStore.sex = sex
  btnDisabled.value = false
}
const closeRolePopup = () => {
  agentCreateStore.sex = 0
  open.value = false
  roleType.value = ''
  btnDisabled.value = true
  agentCreateStore.showRolePopup = false
}

const submit = () => {
  if (btnDisabled.value) return
  if (agentCreateStore.sex === 0) {
    useModal({
      message: t('selectGender'),
      duration: 1500
    })
    return
  }
  agentCreateStore.showAgentCreateModePopup = false
  agentCreateStore.showRolePopup = false
  agentCreateStore.is_show = open.value
  agentCreateStore.type = 'create'
  roleType.value = ''
  open.value = false
  btnDisabled.value = true
  router.push({ name: agentCreateStore.agentCreateMode === 'quick' ? 'QuickCreate' : 'AdvancedCreate' })
}
</script>
<template>
  <van-popup
    v-model:show="agentCreateStore.showRolePopup"
    round
    position="bottom"
    class="popup"
  >
    <div class="title">
      <span>{{ t('selectGender') }}</span>
      <img
        src="@/assets/images/agentCreate/close.png"
        class="close"
        @click="closeRolePopup"
      />
    </div>
    <div class="role-item-box">
      <div
        :class="{ item: true, 'item-active': roleType === 'male' }"
        @click="roleSelect('male')"
      >
        <img
          src="@/assets/images/agentCreate/male.png"
          class="img"
        />
        <div class="name">{{ t('maleOption') }}</div>
      </div>
      <div
        :class="{ item: true, 'item-active': roleType === 'female' }"
        @click="roleSelect('female')"
      >
        <img
          src="@/assets/images/agentCreate/female.png"
          class="img"
        />
        <div class="name">{{ t('femaleOption') }}</div>
      </div>
      <div
        :class="{ item: true, 'item-active': roleType === 'neutral' }"
        @click="roleSelect('neutral')"
      >
        <img
          src="@/assets/images/agentCreate/neutral.png"
          class="img"
        />
        <div class="name">{{ t('nonBinaryOption') }}</div>
      </div>
    </div>
    <CreateAgentBtn
      :btnDisabled="btnDisabled"
      class="btn"
      @submit="submit"
    >
      <template #center>
        <span>{{ t('confirmSelection') }}</span>
      </template>
      <template #right>
        <span></span>
      </template>
    </CreateAgentBtn>
    <div class="open-box">
      <div
        class="check-box"
        @click="open = !open"
      >
        <img
          v-if="!open"
          src="@/assets/images/agentCreate/check-no.png"
          class="img"
        />
        <img
          v-else
          src="@/assets/images/agentCreate/check-yes.png"
          class="img"
        />
        <span>{{ t('setPublic') }}</span>
      </div>
      <div class="tips">{{ t('publicSettingWarning') }}</div>
    </div>
  </van-popup>
</template>
<style lang="scss" scoped>
.popup {
  border-radius: 24px 24px 0 0;

  .title {
    display: flex;
    justify-content: space-between;
    padding: 24px 16px;
    font-size: 18px;

    .close {
      width: 20px;
      height: 20px;
    }
  }

  .role-item-box {
    display: flex;
    justify-content: space-evenly;

    .item {
      width: 104px;
      height: 142px;
      text-align: center;
      background: #2f2d2d;
      border-radius: 16px;

      .img {
        width: 34px;
        height: 34px;
        margin-top: 38px;
      }

      .name {
        margin-top: 32px;
        font-size: 14px;
      }
    }

    .item-active {
      background: rgba(255, 53, 242, 30%);
      border: 1px solid #ff35f2;
    }
  }

  .btn {
    justify-content: center;
    width: 327px;
    height: 48px;
    margin: 24px auto 32px;
    line-height: 48px;
    text-align: center;
    background: #ff35f2;
    border-radius: 40px;
  }

  .open-box {
    margin-bottom: 24px;
    text-align: center;

    .check-box {
      display: flex;
      align-items: center;
      justify-content: center;

      .img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        vertical-align: middle;
      }
    }

    .tips {
      margin-top: 9px;
      font-size: 12px;
      color: rgba(255, 255, 255, 50%);
    }
  }
}
</style>
