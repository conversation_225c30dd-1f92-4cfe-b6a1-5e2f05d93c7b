<script setup lang="ts">
interface Props {
  btnDisabled: boolean
  loading?: boolean
}
const emit = defineEmits<{
  submit: []
}>()
withDefaults(defineProps<Props>(), {
  btnDisabled: false,
  loading: false
})
const submit = () => {
  emit('submit')
}
</script>
<template>
  <div
    :class="{ 'submit-btn': true, 'btn-disabled': btnDisabled }"
    @click="submit"
  >
    <slot name="left"></slot>
    <slot name="center"></slot>
    <slot name="right">
      <img
        v-if="!loading"
        src="@/assets/images/agentCreate/btn-arrow.png"
        style="width: 50px; height: 30px"
      />
      <BtnLoading v-else />
    </slot>
  </div>
</template>
<style lang="scss" scoped>
.submit-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 52px;
  padding: 0 24px;
  overflow: hidden;
  background: #ff35f2;
  border-radius: 40px;
}
</style>
