<script setup lang="ts">
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
const router = useRouter()
const { t } = useI18n()
const toFree = () => {
  eventReport({ event_type: EventTypeEnum.SELECT_FREE_AVATAR })
  router.push({ name: 'FreeFigure' })
}
const goDiyFigure = () => {
  eventReport({ event_type: EventTypeEnum.CREATE_CUSTOM_AI_AVATAR, front_address: ForwardAddressEnum.NEW_CREATION })
  router.push({ name: 'DiyFigure' })
}
</script>
<template>
  <div class="figure-container">
    <div
      class="custom-box"
      @click="goDiyFigure"
    >
      <div class="name">
        <img
          src="@/assets/images/agentCreate/avatar.png"
          class="avatar"
        />
        <span>{{ t('customizeImage') }}</span>
      </div>
      <img
        src="@/assets/images/agentCreate/add1.png"
        class="add"
      />
      <img
        src="@/assets/images/agentCreate/figure.png"
        class="figure"
      />
    </div>
    <div
      class="free-box"
      @click="toFree"
    >
      <div class="name">
        <img
          src="@/assets/images/agentCreate/music-play.png"
          class="music-play"
        />
        <span>{{ t('freeImage') }}</span>
      </div>
      <img
        src="@/assets/images/agentCreate/add2.png"
        class="add"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.figure-container {
  margin: 35px 18px;

  .custom-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 343px;
    height: 137px;
    padding: 8px;
    background: linear-gradient(90deg, #d311c6 0%, #9521fe 100%);
    border: 2px solid rgba(255, 255, 255, 30%);
    border-radius: 16px;

    .name {
      display: flex;
      align-items: center;

      .avatar {
        width: 34px;
        height: 34px;
        margin-right: 4px;
      }
    }

    .figure {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 200px;
      height: 162px;
    }
  }

  .free-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 343px;
    height: 60px;
    padding: 8px;
    margin-top: 23px;
    background: #ff35f2;
    border: 2px solid rgba(255, 255, 255, 30%);
    border-radius: 16px;

    .name {
      display: flex;
      align-items: center;

      .music-play {
        width: 24px;
        height: 24px;
        margin-right: 12px;
      }
    }
  }

  .add {
    width: 24px;
    height: 24px;
  }
}
</style>
