<script setup lang="ts">
import Popup from '../Popup.vue'
import Dialog from '../Dialog.vue'
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
const { t } = useI18n()
const popupRef = ref<InstanceType<typeof Popup> | null>(null)
const agentCreateStore = useAgentCreateStore()
const dialogRef = ref<InstanceType<typeof Dialog> | null>(null)
const showCropper = () => {
  eventReport({
    event_type: EventTypeEnum.RE_EDIT_AI_AVATAR,
    front_address: ForwardAddressEnum.NEW_CREATION
  })
  popupRef.value.showPopup()
}
const closePopup = () => {
  popupRef.value.closePopup()
}
const goBack = () => {
  show.value = true
}
const show = ref(false)
const onRecreate = () => {
  agentCreateStore.showAvatar = false
  agentCreateStore.name = ''
  agentCreateStore.sound = 0
  agentCreateStore.role_setting = ''
  agentCreateStore.opening_statement = ''
  agentCreateStore.back_story = ''
  agentCreateStore.synopsis = ''
  agentCreateStore.talk_example = []
  agentCreateStore.tags = []
  popupRef.value.closePopup()
}
const showRestartTips = () => {
  dialogRef.value.showDialog()
}
</script>
<template>
  <div class="avatar-container">
    <div class="avatar-box">
      <img
        :src="agentCreateStore.avatar"
        class="avatar"
      />
      <img
        src="@/assets/images/agentCreate/cut.png"
        class="cut"
        @click="showCropper"
      />
    </div>
    <div class="restart-box flex-center-center">
      <div
        class="btn"
        @click="showRestartTips"
      >
        <img
          @click="goBack"
          src="@/assets/images/agentCreate/recreate.png"
          class="recreate"
        />
        <div
          class="text flex-center-center"
          @click="goBack"
        >
          <span>{{ t('createAgain') }}</span>
          <img
            src="@/assets/images/agentCreate/restart.png"
            class="restart"
          />
        </div>
      </div>
    </div>
    <Popup
      ref="popupRef"
      style="width: 100%; max-width: 100%; padding: 15px 0; background-color: transparent"
    >
      <Cropper :closePopup="closePopup" />
    </Popup>
    <Dialog ref="dialogRef">
      <template #title>
        <span class="dialog-title">{{ t('selectFigureConfirm') }}</span>
      </template>
      <div class="dialog-content flex-center-center">
        <span>{{ t('choiceFigureTips') }}</span>
      </div>
      <template #footer>
        <div class="flex-between-center dialog-footer">
          <div
            class="cancel-btn dialog-footer-btn"
            @click="dialogRef.closeDiaog()"
          >
            {{ t('cancelButton') }}
          </div>
          <div
            class="confirm-btn dialog-footer-btn"
            @click="onRecreate"
          >
            {{ t('confirmButton') }}
          </div>
        </div>
      </template>
    </Dialog>
  </div>
</template>
<style lang="scss" scoped>
.avatar-container {
  margin-top: 32px;

  .avatar-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 188px;
    height: 188px;
    margin: auto;
    background: linear-gradient(180deg, rgba(255, 53, 242, 100%), rgba(255, 109, 89, 100%));
    border-radius: 100px;

    .avatar {
      width: 180px;
      height: 180px;
      object-fit: cover;
      object-position: top;
      border-radius: 100px;
    }

    .cut {
      position: absolute;
      top: -5px;
      left: 5px;
      width: 40px;
      height: 40px;
    }
  }

  .restart-box {
    margin-top: 32px;

    .back {
      width: 48px;
      height: 48px;
      margin-right: -8px;
    }

    .btn {
      position: relative;
      height: 60px;
      margin: 0 16px;

      .recreate {
        width: 100%;
        height: 100%;
      }

      .text {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        font-size: 18px;

        .restart {
          width: 32px;
          height: 32px;
          margin-left: 12px;
        }
      }
    }
  }

  .dialog-content {
    padding: 0 0 20px;
    text-align: center;

    p {
      margin-bottom: 4px;
      font-size: 14px;
    }
  }
}

.dialog-title {
  font-size: 18px;
  color: #fff;
}

.dialog-content {
  padding: 24px 10%;

  span {
    font-size: 14px;
    line-height: 1.5;
    color: #c1c1c1;
    text-align: center;
  }
}

.dialog-footer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 14px;
  justify-content: center;
  width: 90%;
  padding-bottom: 20px;
  margin: 0 auto;
}

.dialog-footer-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 0;
  border-radius: 16px;
}

.cancel-btn {
  background: #3d3d3d;
}

.confirm-btn {
  background: #ff35f2;
}
</style>
