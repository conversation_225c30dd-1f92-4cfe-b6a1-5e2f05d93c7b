<template>
  <van-popup
    v-model:show="posterPopup"
    :duration="0"
    bgColor="transparent"
    :closeable="true"
    :overlay="true"
    :key="currentPoster.id"
    :closeOnClickOverlay="true"
    :style="{ background: 'none' }"
    :overlay-style="{
      background: 'rgba(0, 0, 0, 0.6)'
    }"
    teleport="body"
    z-index="9999"
    @click-close-icon="posterPopup = false"
    @close="close"
  >
    <div style="padding: 14% 0">
      <van-image
        :src="currentPoster.poster"
        @click="lookDetail"
      >
        <template #loading>
          <van-loading
            type="spinner"
            size="20"
          />
        </template>
        <template #error>
          <van-icon
            name="photo-fail"
            size="20"
          />
        </template>
      </van-image>
    </div>
  </van-popup>
</template>

<script setup>
import { isEmpty } from 'lodash-es'

const posterPopup = ref(false)
const emits = defineEmits(['close', 'clickNotice'])
const props = defineProps({
  currentPoster: {
    type: Object,
    default: () => ({})
  }
})
const router = useRouter()
const close = () => {
  posterPopup.value = false
  console.log('close')
  emits('close')
}
const lookDetail = () => {
  if (!props.currentPoster.redirect_url) {
    emits('clickNotice')
    close()
    return
  }
  // if(['/AgentChat', '/contentGame'].includes(props.currentPoster.redirect_url)) {
  //   router.push(props.currentPoster.redirect_url)
  // } else {
  //   window.open(props.currentPoster.redirect_url)
  // }
  if (['http:', 'https:'].some((item) => props.currentPoster.redirect_url?.includes(item))) {
    window.open(props.currentPoster.redirect_url)
  } else {
    router.push(props.currentPoster.redirect_url)
  }
  close()
  emits('clickNotice')
}

watch(
  () => props.currentPoster,
  (val) => {
    if (!isEmpty(val)) {
      posterPopup.value = true
    }
  },
  {
    deep: true
  }
)
</script>

<style lang="scss">
.posterPopup {
  position: relative;
  width: auto !important;
  background-color: transparent;

  image {
    width: 100%;
    height: 100%;
  }

  .close-icon {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.poster-img {
  width: 100%;
}
</style>
