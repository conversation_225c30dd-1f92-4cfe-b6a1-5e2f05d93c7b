<template>
  <div>
    <!--    手动关闭-->
    <Poster
      :currentPoster="datas.currentPoster"
      @click-notice="clickNotice"
      @close="handleClose('poster')"
    />
    <!--    手动关闭或自动关闭-->
    <NewNotice
      ref="newNoticeRef"
      v-model="showNotice"
      :currentNotice="currentNotice"
      @click-notice="clickNotice"
      @close="handleClose('notice')"
    />
  </div>
</template>
<script setup>
import NewNotice from './newNotice.vue'
import Poster from './poster.vue'
import useUserStore from '@/stores/modules/user.ts'
import useNotifyStore from '@/stores/modules/notification.ts'
import { isEmpty } from 'lodash-es'

const userStore = useUserStore()
const notifyStore = useNotifyStore()
const newNoticeRef = ref()
const datas = reactive({
  noticeList: [],
  bannerList: [],
  showPoster: false,
  showNotice: false,
  timer: '',
  currentNotice: {},
  currentPoster: {}
})

const { currentNotice, showNotice } = toRefs(datas)

watch(
  () => notifyStore.noticeList,
  (val) => {
    if (val && val.length && !datas.showNotice) {
      datas.currentNotice = val[0]
      datas.showNotice = true
    }
  },
  {
    deep: true,
    immediate: true
  }
)

watch(
  () => notifyStore.bannerList,
  (val) => {
    if (val && val.length && !datas.showPoster) {
      datas.currentPoster = val[0]
      datas.bannerList = val
      datas.showPoster = true
    }
  },
  {
    deep: true
  }
)

const handleClose = (val) => {
  if (val === 'notice') {
    datas.showNotice = false
    notifyStore.noticeList.shift()
    if (notifyStore.noticeList?.length) {
      notifyStore.noticeList.reduce((pre, cur) => pre.sort - cur.sort)
      datas.currentNotice = notifyStore.noticeList[0]
      // 为空则没有新的通知信息
      !isEmpty(datas.currentNotice) && (datas.showNotice = true)
    }
  } else {
    datas.showPoster = false
    notifyStore.bannerList.shift()
    if (notifyStore.bannerList?.length) {
      datas.currentPoster = notifyStore.bannerList[0]
      datas.showPoster = true
    } else {
      if (notifyStore.delayNotice) {
        notifyStore.getNoticeList(1)
      }
    }
  }
}

const clickNotice = () => {}
</script>
