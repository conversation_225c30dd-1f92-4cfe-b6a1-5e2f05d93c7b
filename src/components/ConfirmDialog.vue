<script setup lang="ts">
// 使用 withDefaults 设置默认值
const props = withDefaults(
  defineProps<{
    content: string
    title?: string
    cancelText: string
    confirmText: string
    onClose?: () => void
    onConfirm?: () => void
    destroy?: () => void
  }>(),
  {
    content: '',
    title: '',
    cancelText: '',
    confirmText: '',
    onClose: () => {},
    onConfirm: () => {},
    destroy: () => {}
  }
)

// 控制模态框的可见性
const visible = ref(false)

// 显示模态框的方法
const show = () => {
  visible.value = true
}

// 关闭模态框的方法
const close = () => {
  visible.value = false
  props.destroy()
  if (props.onClose) {
    props.onClose()
  }
}

const confirm = () => {
  visible.value = false
  if (props.onConfirm) {
    props.onConfirm()
  }
}

// 将 show 和 close 方法暴露给父组件
defineExpose({ show, close })
</script>

<template>
  <van-dialog
    v-model:show="visible"
    show-cancel-button
    :title="props.title"
  >
    <div :class="props.title ? 'text' : 'only-text'">{{ props.content }}</div>
    <template #footer>
      <div class="w-full flex-around-center padding-16 cg-16">
        <van-button
          class="flex-1 btn cancel"
          @click="close"
        >
          {{ props.cancelText }}
        </van-button>
        <van-button
          type="primary"
          class="flex-1 btn"
          @click="confirm"
        >
          {{ props.confirmText }}
        </van-button>
      </div>
    </template>
  </van-dialog>
</template>

<style scoped lang="scss">
.text {
  padding: 16px 24px;
  font-size: 14px;
  font-weight: 400;
  color: #c1c1c1;
  text-align: center;
}

.only-text {
  padding: 42px 24px 26px;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  text-align: center;
}

.cancel {
  color: #c1c1c1;
  background: #3d3c3c;
}

.btn {
  height: 48px;
  font-size: 16px;
}
</style>
