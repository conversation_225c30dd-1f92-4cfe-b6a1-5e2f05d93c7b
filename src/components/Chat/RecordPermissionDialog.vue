<script setup lang="ts">
const { t } = useI18n()
const show = defineModel<boolean>()

const emits = defineEmits(['getPermission'])
</script>

<template>
  <van-dialog
    v-model:show="show"
    show-cancel-button
    :title="t('permissionRequire')"
  >
    <div class="text">{{ t('micPermissionRequired') }}</div>
    <template #footer>
      <div class="w-full flex-around-center padding-16 cg-16">
        <van-button
          class="flex-1 btn"
          @click="show = false"
        >
          {{ t('cancelButton') }}
        </van-button>
        <van-button
          type="primary"
          class="flex-1 btn"
          @click="
            () => {
              show = false
              emits('getPermission')
            }
          "
        >
          {{ t('confirmButton') }}
        </van-button>
      </div>
    </template>
  </van-dialog>
</template>

<style scoped lang="scss">
.text {
  padding: 16px 32px;
  font-size: 14px;
  font-weight: 400;
  color: #c1c1c1;
  text-align: center;
}

.btn {
  height: 48px;
}
</style>
