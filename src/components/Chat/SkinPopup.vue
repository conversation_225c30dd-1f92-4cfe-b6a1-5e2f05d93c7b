<script setup lang="ts">
import PurchaseSkinPopUp from '@/components/SkinGiftGroup/PurchaseSkinPopUp.vue'
import { getDressByID, setDress } from '@/api/agentChat'
import { Ref } from 'vue'
import useUserStore from '@/stores/modules/user.ts'
import { useModal } from '@/hooks/useModal.ts'
import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { usePopupMemoryStore } from '@/stores/modules/popupMemory.ts'
const { t } = useI18n()

const popupMemoryStore = usePopupMemoryStore()
const userStore = useUserStore()
const showBottom = defineModel<boolean>()
const purchaseVisible = ref(false)
const threeDVisible = ref(false)

const skinList = ref<DressType[]>([])
const aiID = inject<Ref<number>>('aiID')
const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')

const emits = defineEmits(['switch', 'getDress'])
const purchaseSkinData = ref()
const threeDSkin = ref()
const loading = ref(false)

const openPurchaseSkin = (item: DressType) => {
  if (item.type === SkinTypeEnum.ThreeD) {
    threeDSkin.value = item
    threeDVisible.value = true
  } else {
    purchaseSkinData.value = item
    purchaseVisible.value = true
  }
}

const changeSelected = (index: number, dressItem: DressType) => {
  skinList.value.forEach((item, i) => {
    if (i === index) {
      // 如果点击的元素是当前元素，且不是锁定的，选中它
      if (item.is_buy) {
        if (item.isSelected) return
        item.isSelected = true
        const { close } = useModal({
          message: t('settingUp'),
          loading: true,
          autoClose: false
        })
        setDress({
          set_id: dressItem.set_id,
          ai_id: aiID.value
        })
          .then((res) => {
            if (res.code === ResultEnum.SUCCESS) {
              useModal({
                message: t('setupSuccessful')
              })
              console.log(item)
              emits('switch', item)
            }
          })
          .catch((err) => {
            console.warn(err)
          })
          .finally(() => {
            close()
          })
      } else {
        openPurchaseSkin(dressItem)
      }
    } else {
      // 否则清除其他元素的选中状态，前提是它们不是锁定的
      if (skinList.value[index].is_buy) {
        item.isSelected = false
      }
    }
  })
}

const getDressList = () => {
  emits('getDress')
  loading.value = true
  getDressByID({
    ai_id: aiID.value
  })
    .then((res) => {
      const { code, data } = res
      if (code === 200) {
        console.log(data)
        skinList.value = data.map((item) => {
          return {
            ...item,
            isSelected:
              item.type === SkinTypeEnum.Video
                ? agentMessage.value.default_background === SkinTypeEnum.Video
                : agentMessage.value?.bg_info?.bg_url === item.pic_url
          }
        })

        const popup = popupMemoryStore.getPopupMemory()
        if (popup) {
          openPurchaseSkin(data.find((item) => item.id === popup.id))
          popupMemoryStore.resetAllPopupMemory()
        }
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      loading.value = false
    })
}

watch(
  () => showBottom.value,
  (val) => {
    if (val) {
      console.log('test')
      userStore.getUserInfo()
      getDressList()
    }
  }
)
</script>

<template>
  <van-popup
    round
    teleport="#app"
    v-model:show="showBottom"
    position="bottom"
    :style="{ maxHeight: '85%' }"
  >
    <div class="popup">
      <div class="flex-between-center mb-16">
        <div>
          <div class="fsize-18">{{ t('outfits') }}</div>
        </div>
        <SvgIcon
          @click="showBottom = false"
          icon-class="close"
          class="fsize-24 mr-16"
        />
      </div>
      <div class="skin-list">
        <div
          class="load"
          v-show="loading"
        >
          <div class="gl-container">
            <div class="global-dot"></div>
            <div class="global-dot"></div>
            <div class="global-dot"></div>
            <div class="global-dot"></div>
            <div class="global-dot"></div>
            <div class="global-dot"></div>
          </div>
        </div>
        <div
          class="border-box"
          v-for="(item, index) in skinList"
          v-show="!loading"
          :key="index"
        >
          <SkinItemInChat
            :skin-item="item"
            @click="changeSelected(index, item)"
          />
        </div>
      </div>
      <PurchaseSkinPopUp
        v-model="purchaseVisible"
        :skin-item="purchaseSkinData"
        @get-list="getDressList"
      />
      <Unlock3DPopup
        v-model="threeDVisible"
        :skin-item="threeDSkin"
        @get-list="getDressList"
      />
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
.popup {
  padding: 24px 0 12px 16px;

  .skin-list {
    display: flex;
    column-gap: 12px;
    width: 100%;
    overflow-x: auto;

    .load {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 200px;
    }

    .border-box {
      padding: 2px;
    }
  }
}
</style>
