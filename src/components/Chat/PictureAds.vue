<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { updateRecord } from '@/api/chat'
import { useAdStore } from '@/stores/modules/ad.ts'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import useAppStore from '@/stores/modules/app.ts'
import { Ref } from 'vue'
const { t } = useI18n()

const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')
const adStore = useAdStore()
const props = withDefaults(
  defineProps<{
    record: RecordType
  }>(),
  {}
)
const aiID = inject<Ref<number>>('aiID')
const appStore = useAppStore()
const imgUrl = computed(() => {
  return agentMessage.value?.image_list?.find((item) => String(item?.rank) === props.record.revert_content.picture_backend_id?.content)?.path
})

const unlock = async () => {
  adStore
    .showCheckAdv('unlock_image', {
      aiID: aiID.value,
      trigger_position: '解锁模糊图片'
    })
    .then(() => {
      updateRecord({
        record_id: props.record.record_id,
        output_text: {
          video_backend_id: {
            ...props.record.revert_content.picture_backend_id,
            isShow: true
          }
        }
      }).then(() => {
        props.record.revert_content.picture_backend_id.isShow = true
      })
    })
    .catch(() => {
      updateRecord({
        record_id: props.record.record_id,
        output_text: {
          video_backend_id: {
            ...props.record.revert_content.picture_backend_id,
            isShow: true
          }
        }
      }).then(() => {
        props.record.revert_content.picture_backend_id.isShow = true
      })
    })
}

onMounted(() => {
  if (appStore.isAndroid) {
    eventReport({
      event_type: EventTypeEnum.SHOW_AD_BUTTON,
      ai_id: aiID?.value,
      adv_id: adStore.incentiveAdID,
      trigger_position: '解锁图片'
    })
  }
})
</script>

<template>
  <div class="ads-picture">
    <img
      :src="imgUrl"
      alt=""
      onload="document.getElementById('chatPage').scrollTo({ top: document.getElementById('chatPage').scrollHeight })"
    />
    <div
      v-android-only
      v-if="!record.revert_content.picture_backend_id.isShow"
      class="overlay"
      @click="unlock"
    >
      <SvgIcon
        icon-class="ads-lock"
        class="fsize-24"
      />
      <div class="text">{{ t('unlockByAds') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ads-picture {
  position: relative;
  width: 80%;
  height: auto;
  overflow: hidden;
  border-radius: 12px;

  img {
    display: block;
    width: 100%;
    border-radius: inherit;
  }

  .overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    display: flex;
    flex-direction: column;
    row-gap: 6px;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(12px);
    border-radius: 12px;

    .text {
      font-size: 9px;
    }
  }
}
</style>
