<script setup lang="ts">
import { GlobalScoreObj } from '@/api/chat/types'

const props = withDefaults(
  defineProps<{
    options: GlobalScoreObj[]
  }>(),
  {
    options: () => []
  }
)

const emits = defineEmits(['submit', 'restart'])
</script>

<template>
  <div class="global-progress">
    <div
      v-for="item in props.options"
      :key="item.score_name"
      class="progress-item"
    >
      <div class="progress-item-text">
        <div>{{ item.score_name }}</div>
        <div>{{ item.current_score }}/{{ item.total_score }}</div>
      </div>
      <div class="progress-item-bar">
        <div
          class="progress-item-bar-inner"
          :style="{ width: `${(item.current_score / item.total_score) * 100}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.global-progress {
  position: fixed;
  top: 62px;
  left: 0;
  z-index: 4;
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding: 0 12px;
  overflow: auto;

  .progress-item {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    gap: 8px;
    align-items: center;
    justify-content: center;
    width: 158px;
    padding: 8px 10px 12px;
    background: #18181899;
    backdrop-filter: blur(14px);
    border-radius: 12px;

    .progress-item-text {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      font-size: 11px;
      color: white;
    }

    .progress-item-bar {
      width: 100%;
      height: 4px;
      background: #1d1d1d;
      border-radius: 24px;
    }

    .progress-item-bar-inner {
      height: 100%;
      background: linear-gradient(90deg, #ffee9f 0%, #e1ea82 100%);
      border-radius: 24px;
      box-shadow: 0 0 4px 0 #fff inset;
      transition: width 0.5s ease;
    }
  }
}
</style>
