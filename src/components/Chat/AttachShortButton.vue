<script setup lang="ts">
import { BtnContentType } from '@/api/chat/types.ts'
import useAppStore from '@/stores/modules/app.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    options: BtnContentType[]
  }>(),
  {}
)
const appStore = useAppStore()

const { share } = useShare()
const show = defineModel<boolean>()
const shareVisible = ref(false)
// const loading = defineModel<boolean>('loading')
const emits = defineEmits(['submit', 'restart', 'web_search'])

const submit = (query: BtnContentType) => {
  console.log(query, 'btn')
  if (query.function) {
    switch (query.function) {
      case 'restart':
        show.value = false
        emits('restart')
        break
      case 'share':
        if (appStore.isAndroid) {
          shareVisible.value = true
        } else {
          share({
            title: t('shareContent'),
            text: 'Livco',
            url: window.location.href
          })
        }
        break
      case 'tools':
        show.value = false
        emits('web_search', {
          tools: query.tools
        })
        break
    }
    return
  } else {
    emits('submit', { text: query, responseWay: '不遮挡聊天框的短按钮' })
  }
}
</script>

<template>
  <div>
    <div class="attach-btn">
      <div
        v-for="item in props.options"
        :key="item.content"
        class="btn"
        @click="submit(item)"
      >
        {{ item.content }}
      </div>
      <Teleport to="body">
        <ShareCard v-model="shareVisible" />
      </Teleport>
    </div>
  </div>
</template>

<style scoped lang="scss">
.attach-btn {
  display: flex;
  column-gap: 4px;
  align-items: center;
  justify-content: flex-start;

  .btn {
    display: flex;
    column-gap: 4px;
    align-items: center;
    justify-content: center;
    width: fit-content;
    padding: 6px 12px;
    margin-top: 8px;
    background: rgba(54, 54, 54);
    border-radius: 8px;
  }
}
</style>
