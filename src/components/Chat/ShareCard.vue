<script setup lang="ts">
import QRCode from 'qrcode'
import { downloadFile, handleDomToImg } from '@/utils'
import useAppStore from '@/stores/modules/app.ts'
import { ugPhoneLinkSdk } from '@/utils/ugPhoneLinkSdk.ts'
import { base64ToUrl } from '@/api/contentGame'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { Ref } from 'vue'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { useModal } from '@/hooks/useModal.ts'
const { t } = useI18n()

const appStore = useAppStore()
const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')

const isIOS = navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)

const visible = defineModel<boolean>()
const code = ref('')

const getQrCode = async () => {
  let url = window.location.href
  QRCode.toDataURL(url)
    .then((url) => {
      code.value = url
    })
    .catch((err) => {
      console.error(err)
    })
}

const savePic = async () => {
  const { close } = useModal({
    loading: true,
    message: t('saveButton'),
    autoClose: false
  })
  const fileUrl = await handleDomToImg('story-card')
  if (appStore.isAndroid) {
    base64ToUrl({ b64: fileUrl }).then(({ code, data }) => {
      if (code === ResultEnum.SUCCESS) {
        try {
          ugPhoneLinkSdk.saveImage(data)
          close()
        } catch {
          downloadFile(fileUrl, 'story.png')
          close()
        }
      }
    })
  } else {
    downloadFile(fileUrl, 'story.png')
    close()
  }
  visible.value = false
}

getQrCode()
</script>

<template>
  <van-overlay
    v-model:show="visible"
    :lock-scroll="false"
    z-index="9999"
    class="overlay"
  >
    <div class="card-popup">
      <div
        class="close-btn flex-center-center"
        @click="visible = false"
      >
        <SvgIcon
          icon-class="close"
          class="fsize-21"
        />
      </div>
      <div
        class="card-content"
        id="story-card"
      >
        <div
          class="card-picture"
          :style="{ backgroundImage: `url('${agentMessage.bg_info.bg_url}')` }"
        ></div>
        <div class="text-content">
          <div class="text">
            <img
              src="@/assets/images/newer/vip-eros.png"
              alt=""
            />
            <div class="fsize-14 mt-8">{{ t('scanQRCode') }}</div>
          </div>
          <img
            class="qrcode"
            id="qrcode"
            alt=""
            :src="code"
          />
        </div>
      </div>
      <div
        class="btn"
        v-if="!isIOS"
      >
        <van-button
          class="download-btn"
          @click="savePic"
        >
          {{ t('savePicture') }}
        </van-button>
      </div>
    </div>
  </van-overlay>
</template>

<style scoped lang="scss">
.overlay {
  --van-overlay-background: rgba(0, 0, 0, 80%);

  padding: 80px 32px 131px;
  overflow: auto;
}

.card-popup {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  border-radius: 16px;

  .close-btn {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    width: 30px;
    height: 30px;
    background: rgba(82, 82, 82, 80%);
    border-radius: 50%;
    transform: translate(50%, -50%);
  }

  .card-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    border: 3px solid #dcb37f;
    border-radius: 16px;

    .card-picture {
      width: 100%;
      aspect-ratio: 9 / 16;
      background: url('@/assets/images/story-test.png') top / contain no-repeat;
    }

    .text-content {
      display: flex;
      column-gap: 60px;
      align-items: center;
      justify-content: space-between;
      padding: 12px 12px 12px 16px;
      color: #967040;
      background: url('@/assets/images/newer/vip-card.png') center / cover no-repeat;

      .text {
        img {
          height: 24px;
        }
      }

      .qrcode {
        width: 78px;
        height: 78px;
        border-radius: 12px;
      }
    }
  }

  .btn {
    display: flex;
    column-gap: 8px;
    justify-content: center;
    width: 100%;
    margin-top: 24px;
    font-size: 32px;

    .download-btn {
      width: 60%;
      font-weight: bold;
      background: rgba(24, 24, 24, 20%);
      backdrop-filter: blur(8px);
      border: 2px solid rgba(255, 255, 255, 50%);
      border-radius: 32px;
    }

    .l2d-btn {
      width: 60%;
      font-weight: bold;
      color: black;
      background: rgba(255, 255, 255, 100%);
      backdrop-filter: blur(8px);
      border: 2px solid rgba(255, 255, 255, 50%);
      border-radius: 32px;
    }
  }
}
</style>
