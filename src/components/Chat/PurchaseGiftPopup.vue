<script setup lang="ts">
import { getAmountID, purchase } from '@/api/purchase'
import { ICurrencyTypeEnum, IPurchaseTypeEnum } from '@/enums'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { useModal } from '@/hooks/useModal.ts'
import useUserStore from '@/stores/modules/user.ts'
import { usePaymentError } from '@/hooks/useCommon.ts'
import { GiftListResType } from '@/api/agentChat/types.ts'
import { debounce } from 'lodash-es'
import { POPUP_TYPE_NAME } from '@/stores/modules/popupMemory.ts'

const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    giftItem: GiftListResType | undefined
  }>(),
  {}
)
const aiID = inject<Ref<number>>('aiID')
const route = useRoute()
const userStore = useUserStore()
const showBottom = defineModel<boolean>()
const loading = ref(false)
const num = ref(1)
const max = computed(() => {
  if (!props.giftItem.day_limit_buy && !props.giftItem.weekly_limit_buy) {
    return 0
  }
  return props.giftItem.weekly_limit_buy < props.giftItem.day_limit_buy ? props.giftItem.weekly_limit_buy : props.giftItem.day_limit_buy
})

const emits = defineEmits(['getList'])

const close = () => {
  showBottom.value = false
  num.value = 1
}

const debouncedModal = debounce(
  () =>
    useModal({
      message: props.giftItem?.is_weekly_limit_buy ? t('weeklyPurchaseLimit') : t('dailyPurchaseLimit')
    }),
  1000,
  {
    leading: true,
    trailing: false
  }
)
const addNum = () => {
  if (num.value >= (max.value || 999)) {
    debouncedModal()
    return
  }
  navigator?.vibrate?.(30)
  num.value++
}

const subNum = () => {
  if (num.value <= 1) return
  navigator?.vibrate?.(30)
  num.value--
}

const submitPurchase = async () => {
  loading.value = true
  try {
    const { data, code, msg } = await getAmountID({
      currency_type: props.giftItem.currency_type,
      product_type: IPurchaseTypeEnum.Gift,
      product_id: props.giftItem.id,
      count: num.value,
      front_address: route.path,
      ai_id: route.path.toLowerCase().includes('/chat') ? aiID.value : undefined
    })
    if (code === ResultEnum.SUCCESS) {
      const { code: code1, msg: msg1 } = await purchase({ amount_id: data.amount_id, currency_type: props.giftItem.currency_type })
      if (code1 === ResultEnum.SUCCESS) {
        useModal({
          message: t('purchaseSuccessful'),
          onClose: async () => {
            showBottom.value = false
            loading.value = false
            emits('getList')
            await userStore.getUserInfo()
          }
        })
      } else {
        useModal({
          message: msg1
        })
      }
    } else {
      usePaymentError(code, msg, POPUP_TYPE_NAME.BUY_GIFT, route.path + '?tab=gift', props.giftItem.id)
    }
  } catch (e) {
    console.warn(e)
  }
  loading.value = false
}
</script>

<template>
  <van-popup
    round
    teleport="#app"
    v-model:show="showBottom"
    position="bottom"
    :style="{ maxHeight: '85%' }"
    @close="close"
  >
    <div class="popup">
      <div class="flex-between-center mb-16">
        <div>
          <div>{{ t('itemPurchase') }}</div>
        </div>
        <div @click="close">
          <van-icon name="cross" />
        </div>
      </div>
      <CrystalBalance
        class="mb-12"
        v-if="props.giftItem.currency_type === ICurrencyTypeEnum.Crystal"
      />
      <!--      <CoinBalance-->
      <!--        class="mb-12"-->
      <!--        v-if="props.giftItem.currency_type === ICurrencyTypeEnum.Coin"-->
      <!--      />-->
      <div class="gift">
        <div class="gift-img">
          <img
            class="img"
            :src="props.giftItem.icon"
            alt="gift"
          />
          <div class="flex-center-center">{{ props.giftItem.name }}</div>
        </div>
        <div class="num flex-center-center cg-36">
          <SvgIcon
            :style="num === 1 && { opacity: 0.5 }"
            icon-class="sub"
            class="fsize-26"
            @click="subNum"
          />
          <span class="text">{{ num }}</span>
          <SvgIcon
            :style="num === max && { opacity: 0.5 }"
            icon-class="add"
            class="fsize-26"
            @click="addNum"
          />
        </div>
      </div>
      <div class="purchase">
        <div class="ml-20 flex-center-start flex-column">
          <div class="num flex-start-center cg-4">
            <SvgIcon
              v-if="props.giftItem.price && props.giftItem.currency_type === ICurrencyTypeEnum.Coin"
              icon-class="coin"
              class="mr-4"
            />
            <img
              v-if="props.giftItem.price && props.giftItem.currency_type === ICurrencyTypeEnum.Crystal"
              width="16"
              src="../../assets/images/crystal.png"
              alt=""
            />{{ props.giftItem.price * num || t('free') }}
          </div>
        </div>
        <van-button
          size="large"
          class="btn"
          @click="submitPurchase"
          :loading="loading"
        >
          <div>{{ t('confirmButton') }}</div>
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
.popup {
  padding: 24px 16px 50px;

  :deep(.balance) {
    background: #262626;
  }

  .gift {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    align-items: center;
    justify-content: center;

    img {
      width: 62px;
      height: 62px;
    }

    .num {
      padding: 6px;
      user-select: none;
      background: #302f2f;
      border-radius: 16px;

      .text {
        font-weight: bold;
      }
    }
  }

  .purchase {
    display: flex;
    justify-content: space-between;
    height: 54px;
    margin-top: 40px;
    background: #302f2f;
    border-radius: 32px;

    .text {
      color: #8b8889;
    }

    .num {
      font-weight: 600;
    }

    .btn {
      width: 124px;
      height: auto;
      color: $livCoTextColor;
      background: $livCoThemeColor;
      border: none;
      border-radius: 40px;
    }
  }
}
</style>
