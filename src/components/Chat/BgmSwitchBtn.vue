<script setup lang="ts">
import { debounce } from 'lodash-es'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { Howl } from 'howler'
import useUserStore from '@/stores/modules/user.ts'
import { Ref } from 'vue'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { updateUserInfo } from '@/api/chat'

const bgmHowlerInstance = ref<Howl | null>(null)
const userStore = useUserStore()
const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')
const phoneCall = inject<Ref<boolean>>('phoneCall')

/**
 * 初始化BGM
 */
const initBgm = () => {
  if (agentMessage.value.bgm && !bgmHowlerInstance.value) {
    bgmHowlerInstance.value = new Howl({
      src: agentMessage.value.bgm,
      loop: true,
      volume: 0.3
    })
    controlBgm()
  }
}

/**
 * 控制BGM的播放状态
 * @param forcePlay 强制播放
 * @param forcePause 强制暂停
 */
const controlBgm = (forcePlay?: boolean, forcePause?: boolean) => {
  // 如果没有实例或没有bgm，直接返回
  if (!bgmHowlerInstance.value || !agentMessage.value.bgm) return

  const shouldPlay = forcePlay ?? (userStore.userInformation.sound && userStore.userInformation.bgm && document.visibilityState === 'visible')
  const shouldPause = forcePause ?? !shouldPlay
  if (shouldPlay) {
    bgmHowlerInstance.value.play()
  } else if (shouldPause) {
    Howler.stop()
    bgmHowlerInstance.value.pause()
  }
}

/**
 * 切换BGM开关状态
 */
const setBgmStatus = () => {
  userStore.userInformation.sound = Boolean(userStore.userInformation.sound) ? 0 : 1
  controlBgm()
  if (!userStore.isLogin) return
  updateUserInfo({
    sound: Boolean(userStore.userInformation.sound) ? 1 : 0
  })
    .then((res) => {
      if (res.code === ResultEnum.SUCCESS) {
      }
    })
    .catch(() => {
      userStore.userInformation.sound = Boolean(userStore.userInformation.sound) ? 0 : 1
      controlBgm()
    })
}

const debounceSetBgmStatus = debounce(setBgmStatus, 500, {
  leading: true,
  trailing: false
})

const listenerControl = () => {
  if (phoneCall.value) return
  controlBgm()
}

// 监听页面可见性变化
document.addEventListener('visibilitychange', listenerControl)

defineExpose({
  init: initBgm,
  control: controlBgm
})

onBeforeUnmount(() => {
  document.removeEventListener('visibilitychange', listenerControl)
})
</script>

<template>
  <div
    class="switch-btn flex-center-center"
    @click="debounceSetBgmStatus"
  >
    <SvgIcon
      v-show="userStore.userInformation.sound"
      icon-class="music-on"
      class="fsize-20"
    />
    <SvgIcon
      v-show="!userStore.userInformation.sound"
      icon-class="music-off"
      class="fsize-20"
    />
  </div>
</template>

<style scoped lang="scss"></style>
