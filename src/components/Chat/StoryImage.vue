<script setup lang="ts">
import { useAdStore } from '@/stores/modules/ad.ts'

const { t } = useI18n()
import { RecordType } from '@/api/chat/types.ts'
const props = withDefaults(
  defineProps<{
    record: RecordType
  }>(),
  {}
)
const aiID = inject<Ref<number>>('aiID')
const adStore = useAdStore()
const emits = defineEmits(['goContentGame'])

const goContentGame = () => {
  console.log(props, 'contentGame')
  adStore
    .showCheckAdv('start_history', {
      aiID: aiID.value,
      trigger_position: '内容玩法跳页'
    })
    .then(() => {
      emits('goContentGame', Number(props.record.content_id))
    })
    .catch(() => {
      emits('goContentGame', Number(props.record.content_id))
    })
}
</script>

<template>
  <div
    class="story-img"
    :style="{ backgroundImage: `url('${props.record.content}')` }"
    @click="goContentGame"
  >
    <div class="story-flag">
      <SvgIcon
        icon-class="opening"
        class="fsize-14"
      />{{ t('story') }}
    </div>
    <div class="enter-story flex-center-center cg-4">
      {{ t('startNow') }}
      <SvgIcon
        icon-class="arrow-right-content"
        class="fsize-16"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.story-img {
  position: relative;
  width: 80%;
  aspect-ratio: 16 / 9;
  background-repeat: no-repeat;
  background-position: center;
  background-clip: padding-box;
  background-size: cover;
  border: 2px solid rgba(255, 255, 255, 30%);
  border-radius: 12px;

  .story-flag {
    position: absolute;
    top: 8px;
    left: 8px;
    display: flex;
    column-gap: 2px;
    align-items: center;
    justify-content: center;
    padding: 4px 8px 4px 6px;
    font-size: 12px;
    font-weight: bold;
    line-height: 14px;
    color: #fff;
    text-align: left;
    background: rgba(23, 23, 23, 70%);
    border-radius: 8px;
  }

  .enter-story {
    position: absolute;
    right: 0;
    bottom: 0;
    padding: 8px 16px 7px 14px;
    font-size: 12px;
    font-weight: 600;
    line-height: 14px;
    color: #fff;
    text-align: left;
    background: rgba(2, 2, 2, 80%);
    border-radius: 12px 0;
  }
}
</style>
