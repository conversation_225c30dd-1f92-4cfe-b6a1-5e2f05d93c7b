<script setup lang="ts">
import { getGift } from '@/api/agentChat'
import { useModal } from '@/hooks/useModal.ts'
import { GiftListResType, IGiftListType } from '@/api/agentChat/types.ts'
import { IPurchaseTypeEnum } from '@/enums'
import useUserStore from '@/stores/modules/user.ts'
import { Ref } from 'vue'
import SendGiftItem from '@/components/SkinGiftGroup/SendGiftItem.vue'
import { getAmountID, purchase } from '@/api/purchase'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { usePaymentError } from '@/hooks/useCommon.ts'
import { debounce } from 'lodash-es'
import { POPUP_TYPE_NAME } from '@/stores/modules/popupMemory.ts'
import { chat } from '@/api/chat'
import useAppStore from '@/stores/modules/app.ts'
const { t } = useI18n()

const emits = defineEmits(['triggerGiftMessage'])

// const popupMemoryStore = usePopupMemoryStore()
const showBottom = defineModel<boolean>()
const appStore = useAppStore()
const purchaseVisible = ref(false)
const userStore = useUserStore()
const giftList = ref<IGiftListType[]>([])
const purchaseGiftData = ref()
const route = useRoute()

const loading = ref(false)
const aiID = inject<Ref<number>>('aiID')
const live2dStatus = inject<Ref<boolean>>('live2dStatus')
const changeSelected = (item: IGiftListType) => {
  if (item.selected) {
    handleSend(item)
  }
  if ((item.is_weekly_limit_buy || item.is_day_limit_buy) && item.client_have_count === 0) {
    useModal({
      message: item.is_weekly_limit_buy ? t('weeklyPurchaseLimit') : t('dailyPurchaseLimit')
    })
    return
  }
  // if (item.client_have_count < 1) {
  //   openPurchaseGift(item)
  //   return
  // }
  giftList.value.forEach((gift: IGiftListType) => {
    gift.selected = gift.id === item.id
  })
}

const debounceChangeSelected = debounce(changeSelected, 200, {
  leading: true,
  trailing: false
})

const getGiftList = () => {
  loading.value = true
  getGift({
    limit: 9999,
    page: 1
  })
    .then((res) => {
      const { code, data } = res
      if (code === 200) {
        giftList.value = data.list.map((item: GiftListResType) => {
          return {
            ...item,
            selected: false
          }
        })
        nextTick(() => {
          giftList.value[0].selected = true
        })
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      loading.value = false
    })
}

const submitPurchase = async (giftItem: IGiftListType) => {
  // loading.value = true
  try {
    const { data, code, msg } = await getAmountID({
      currency_type: giftItem.currency_type,
      product_type: IPurchaseTypeEnum.Gift,
      product_id: giftItem.id,
      count: 1,
      front_address: route.path,
      ai_id: route.path.toLowerCase().includes('/agentchat') ? aiID.value : undefined
    })
    if (code === ResultEnum.SUCCESS) {
      const { code: code1, msg: msg1 } = await purchase({ amount_id: data.amount_id, currency_type: giftItem.currency_type })
      if (code1 === ResultEnum.SUCCESS) {
        const { close } = useModal({
          message: t('sendingGift'),
          loading: true,
          autoClose: false
        })
        const volume = await appStore.getSystemVolume()
        chat({
          ai_id: aiID.value,
          query: String(giftItem.id),
          pjh_query: giftItem.prompt,
          gift_id: giftItem.id,
          gift_count: 1,
          interactive_model: live2dStatus.value ? 2 : 1,
          response_way: '送礼',
          volume: volume && userStore.userInformation.sound ? 1 : 0
        })
          .then(({ data }) => {
            useModal({
              message: t('giftSent')
            })
            emits('triggerGiftMessage', { data, giftItem })
            showBottom.value = false
          })
          .finally(() => {
            close()
          })
        await userStore.getUserInfo()
      } else {
        useModal({
          message: msg1
        })
      }
    } else {
      usePaymentError(code, msg, POPUP_TYPE_NAME.SEND_GIFT, route.path)
    }
  } catch (e) {
    console.warn(e)
  }
  // loading.value = false
}

const handleSend = async (gift: IGiftListType) => {
  if (gift.client_have_count > 0) {
    const { close } = useModal({
      message: t('sendingGift'),
      loading: true,
      autoClose: false
    })
    const volume = await appStore.getSystemVolume()
    chat({
      ai_id: aiID.value,
      query: String(gift.id),
      pjh_query: gift.prompt,
      gift_id: gift.id,
      gift_count: 1,
      interactive_model: live2dStatus.value ? 2 : 1,
      response_way: '送礼',
      volume: volume && userStore.userInformation.sound ? 1 : 0
    })
      .then(({ data }) => {
        useModal({
          message: t('giftSent')
        })
        emits('triggerGiftMessage', { data, gift })
        showBottom.value = false
      })
      .finally(() => {
        close()
      })
  } else {
    submitPurchase(gift)
  }
}

watch(
  () => showBottom.value,
  (val) => {
    if (val) {
      userStore.getUserInfo()
      getGiftList()
    }
  }
)
</script>

<template>
  <van-popup
    round
    teleport="#app"
    :overlay-style="{ background: 'rgba(0, 0, 0, 0)' }"
    v-model:show="showBottom"
    position="bottom"
    :style="{ maxHeight: '65%' }"
  >
    <div class="popup">
      <div class="flex-between-center rg-8">
        <div>
          <div>{{ t('giftItems') }}</div>
          <div class="fsize-12 text mt-4">{{ t('giftBoostsIntimacy') }}</div>
        </div>
        <div class="flex-center-center cg-8">
          <CrystalBalance />
          <!--          <CoinBalance />-->
        </div>
      </div>
      <div
        class="load"
        v-show="loading"
      >
        <div class="gl-container">
          <div class="global-dot"></div>
          <div class="global-dot"></div>
          <div class="global-dot"></div>
          <div class="global-dot"></div>
          <div class="global-dot"></div>
          <div class="global-dot"></div>
        </div>
      </div>
      <div
        class="gift-list mt-8"
        v-show="!loading"
      >
        <SendGiftItem
          v-for="(item, index) in giftList"
          :key="index"
          :gift="item"
          class="gift-item"
          :class="{ 'git-item-selected': item.selected }"
          :style="
            (item.is_day_limit_buy || item.is_weekly_limit_buy) &&
            item.client_have_count === 0 && {
              opacity: 0.5
            }
          "
          @click="debounceChangeSelected(item)"
        />
      </div>
    </div>
    <PurchaseGiftPopup
      v-model="purchaseVisible"
      :gift-item="purchaseGiftData"
      @get-list="getGiftList"
    />
  </van-popup>
</template>

<style scoped lang="scss">
.popup {
  width: 100%;
  padding: 16px;

  .load {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }

  .text {
    color: #8b8889;
  }

  :deep(.balance) {
    background: #262626;
  }

  .gift-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
    max-width: 100%;
    max-height: 280px;
    padding-bottom: 32px;
    overflow: auto;
    mask-image: linear-gradient(to bottom, #000 0%, #000 260px, transparent 100%);

    .gift-item {
      user-select: none;
      border: 1px solid transparent;
      border-radius: 8px;
    }

    .git-item-selected {
      padding-bottom: 0;
      background: rgba(235, 223, 172, 10%);
      border: 1px solid rgba(255, 255, 255, 5%);
    }
  }
}
</style>
