<script setup lang="ts">
import { useIntersectionObserver } from '@vueuse/core'
import { Ref } from 'vue'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import useUserStore from '@/stores/modules/user.ts'
const { t } = useI18n()
const props = withDefaults(
  defineProps<{
    currentLevel: number
    currentValue: number
  }>(),
  {
    currentLevel: 4,
    currentValue: 55
  }
)
const router = useRouter()
const userStore = useUserStore()
const emits = defineEmits(['sendGift'])
const showBottom = defineModel<boolean>()
const levelItemArrayRef = ref(null)
const activeIndex = ref(0)

const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')

const intimacyInfo = computed(() => {
  return agentMessage.value?.intimacy_level_info.map((item, index) => {
    return {
      level: item?.level,
      rules: item.interest_names,
      minValue: item?.experience,
      maxValue: agentMessage.value.intimacy_level_info[index + 1]?.experience || -1
    }
  })
})

const handleLevelTitle = computed(() => {
  if (intimacyInfo.value[activeIndex.value].level === props.currentLevel) {
    return t('currentLevel')
  } else if (intimacyInfo.value[activeIndex.value].level > props.currentLevel) {
    return t('levelLocked')
  } else {
    return t('levelUnlocked')
  }
})

const handleCurrentValue = computed(() => {
  if (intimacyInfo.value[activeIndex.value].level === props.currentLevel) {
    return props.currentValue
  } else if (intimacyInfo.value[activeIndex.value].level > props.currentLevel) {
    return 0
  } else {
    return intimacyInfo.value[activeIndex.value].maxValue
  }
})

const handelPercent = computed(() => {
  if (activeIndex.value === intimacyInfo.value.length - 1) {
    console.log(122)
    if (intimacyInfo.value.length === props.currentLevel) {
      console.log(12121)
      return '100%'
    } else {
      return '0%'
    }
  } else {
    if (activeIndex.value !== 0) {
      return `${(((handleCurrentValue.value as number) - intimacyInfo.value[activeIndex.value - 1]?.maxValue) / ((intimacyInfo.value[activeIndex.value].maxValue as number) - intimacyInfo.value[activeIndex.value - 1]?.maxValue)) * 100}%`
    } else {
      return `${((handleCurrentValue.value as number) / (intimacyInfo.value[activeIndex.value].maxValue as number)) * 100}%`
    }
  }
})

watch(
  () => showBottom.value,
  (val) => {
    if (val) {
      nextTick(() => {
        levelItemArrayRef.value.forEach((item: HTMLElement, index: number) => {
          useIntersectionObserver(
            item,
            ([{ isIntersecting }]) => {
              // console.log(isIntersecting, index)
              if (isIntersecting) {
                activeIndex.value = index
              }
            },
            {
              threshold: 0.7
            }
          )
          if (intimacyInfo.value[index].level === props.currentLevel) {
            item.scrollIntoView({ behavior: 'smooth' })
          }
        })
      })
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <van-popup
    round
    teleport="#app"
    v-model:show="showBottom"
    position="bottom"
    :style="{ maxHeight: '85%' }"
  >
    <div class="popup">
      <SvgIcon
        icon-class="popup-close"
        class="close"
        @click="showBottom = false"
      />
      <div
        class="intimacy-bg"
        :class="intimacyInfo[activeIndex].level > props.currentLevel && 'intimacy-bg-black'"
      >
        <div class="level-container">
          <div class="level-current">
            <div class="level-title">{{ handleLevelTitle }}</div>
            <div
              class="level"
              :class="intimacyInfo[activeIndex].level > props.currentLevel && 'level-lock'"
            >
              LV.<span class="fsize-32">{{ activeIndex + 1 }}</span>
            </div>
            <div class="progress-container">
              <div class="bar">
                <div class="progress-bar">
                  <div
                    class="bar-value"
                    :style="{ width: handelPercent }"
                  >
                    <SvgIcon
                      v-if="intimacyInfo[activeIndex].level === props.currentLevel && intimacyInfo.length !== props.currentLevel"
                      icon-class="intimacy-value-heart"
                      class="heart-value"
                    />
                    <div
                      v-if="intimacyInfo[activeIndex].level === props.currentLevel && intimacyInfo.length !== props.currentLevel"
                      class="current-value flex-center-center"
                    >
                      {{ handleCurrentValue }}
                    </div>
                  </div>
                  <div
                    class="progress-text"
                    v-if="activeIndex < intimacyInfo.length - 1"
                  >
                    <span class="fsize-12">LV.{{ activeIndex + 2 }}</span>
                  </div>
                </div>
              </div>
              <div class="progress-value">{{ intimacyInfo[activeIndex].maxValue === -1 ? 'Max' : intimacyInfo[activeIndex].maxValue }}</div>
            </div>
          </div>
          <div class="avatar">
            <div
              class="avatar-my"
              :style="{ background: `url('${userStore.userInformation.avatar_url}') no-repeat center / cover` }"
            ></div>
            <div
              class="avatar-her"
              :style="{ background: `url('${agentMessage.avatar_url}') no-repeat center / cover` }"
            ></div>
            <div class="heart">
              <SvgIcon
                icon-class="intimacy-heart"
                class="fsize-24"
              />
            </div>
          </div>
        </div>
        <div class="all-level">
          <div
            ref="levelItemArrayRef"
            class="intimacy-rules"
            v-for="(infoItem, index) in intimacyInfo"
            :key="index"
          >
            <div class="mb-16 strategy">
              <div class="title">{{ t('increaseIntimacy') }}</div>
              <SvgIcon
                @click="router.push('/chat/intimacyStrategy')"
                icon-class="tips"
                class="fsize-22 detail"
              />
            </div>
            <div class="rules-gift">
              <div class="rules">
                <div
                  class="rule-item flex-start-center"
                  v-for="(ruleItem, ruleIndex) in infoItem.rules"
                  :style="index + 1 <= props.currentLevel && { color: '#EBDFAC' }"
                  :key="ruleIndex"
                >
                  <SvgIcon
                    :icon-class="index + 1 > props.currentLevel ? 'transparent-grey-lock' : 'checkbox-active'"
                    class="fsize-18"
                  />{{ ruleItem }}
                </div>
              </div>
              <div class="gift">
                <img
                  src="../../assets/images/agentChat/intimacy-gift.webp"
                  alt="gift"
                  style="width: 96px; height: 96px"
                />
                <van-button
                  class="btn"
                  size="small"
                  @click="emits('sendGift')"
                >
                  {{ t('sendGift') }}
                </van-button>
              </div>
            </div>
          </div>
        </div>
        <div class="level-point">
          <div
            class="point-item"
            v-for="(_, index) in intimacyInfo"
            :key="index"
            :class="activeIndex === index ? 'active' : ''"
          ></div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
@import '../../assets/styles/mixin';

.popup {
  position: relative;

  .close {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 20px;
  }

  .intimacy-bg {
    padding: 40px 0 18px;
    background: url('@/assets/images/agentChat/intimacy-bg.webp') center / cover no-repeat;
  }

  .intimacy-bg-black {
    padding: 40px 0 18px;
    background: url('@/assets/images/agentChat/intimacy-bg-black.webp') center / cover no-repeat;
  }

  .level-container {
    display: flex;
    column-gap: 32px;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    margin-bottom: 24px;

    .level-current {
      width: 100%;

      .level-title {
        font-size: 12px;
        color: rgba(255, 255, 255, 30%);
      }

      .level {
        font-size: 24px;
        font-style: italic;
        font-weight: bolder;
      }

      .level-lock {
        color: rgba(255, 255, 255, 30%);
      }

      .progress-container {
        width: 100%;

        .bar {
          display: flex;
          column-gap: 8px;
          align-items: center;
          width: 100%;
          font-size: 12px;
          font-style: italic;
          color: rgba(255, 255, 255, 60%);

          .progress-bar {
            position: relative;
            flex: 1;
            height: 4px;
            background: rgba(255, 255, 255, 20%);
            border-radius: 35px;

            .bar-value {
              position: relative;
              width: 0;
              height: 4px;
              background: $livCoThemeColor;
              border-radius: 35px;

              .heart-value {
                position: absolute;
                top: 0;
                right: 0;
                font-size: 18px;
                transform: translate(30%, -35%);
              }

              .current-value {
                position: absolute;
                right: 0;
                bottom: 0;
                padding: 1px 6px;
                font-size: 10px;
                font-style: normal;
                font-weight: 600;
                color: $livCoThemeColor;
                background: rgba(235, 223, 172, 15%);
                backdrop-filter: blur(16px);
                border: 1px solid $livCoThemeColor;
                border-radius: 8px;
                transform: translate(35%, 140%);
              }
            }

            .progress-text {
              position: absolute;
              top: -4px;
              right: 0;
              transform: translateY(-100%);
            }
          }
        }

        .progress-value {
          margin-top: 4px;
          font-size: 12px;
          color: rgba(255, 255, 255, 60%);
          text-align: right;
        }
      }
    }
  }

  .avatar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .avatar-my {
      z-index: 1;
      width: 64px;
      height: 64px;
      background: url('@/assets/images/normal-holder.png') no-repeat center / cover;
      background-clip: border-box;
      border: 1px solid rgba(255, 255, 255, 30%);
      border-radius: 50%;
    }

    .avatar-her {
      width: 64px;
      height: 64px;
      margin-left: -8px;
      background: url('@/assets/images/normal-holder.png') no-repeat center / cover;
      background-clip: border-box;
      border: 1px solid rgba(255, 255, 255, 30%);
      border-radius: 50%;
    }

    .heart {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 2;
      transform: translate(-50%, -50%);
    }
  }

  .all-level {
    display: flex;
    flex-wrap: nowrap;
    column-gap: 12px;
    align-items: center;
    width: 100%;
    padding: 2px;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
  }

  .intimacy-rules {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: calc(100% - 32px);
    height: 200px;
    padding: 18px 12px 0;
    background: rgba(55, 55, 55, 30%);
    border-radius: 16px;
    scroll-snap-align: start;
    scroll-margin-left: 18px;

    &:first-child {
      margin-left: 16px;
    }

    &:last-child {
      margin-right: 16px;
    }

    .strategy {
      position: relative;

      .detail {
        position: absolute;
        top: 25px;
        right: -5px;
      }
    }

    .title {
      font-size: 14px;
    }

    .rules-gift {
      display: flex;
      flex: 1;
      align-items: flex-start;
      justify-content: center;
      overflow: hidden;

      .rules {
        display: flex;
        flex: 1;
        flex-direction: column;
        row-gap: 10px;
        height: 100%;
        padding: 1px;
        overflow-y: auto;

        .rule-item {
          @include gradation-border-with-radius-and-transparent-bg(1px, linear-gradient(90deg, rgba(255, 255, 255, 0.16), rgba(255, 255, 255, 0)));

          column-gap: 8px;
          height: 40px;
          padding: 12px;
          font-size: 12px;
          color: rgba(255, 255, 255, 50%);
          word-break: break-word;
          background: linear-gradient(90deg, rgba(57, 56, 57, 20%) 0%, rgba(57, 56, 57, 0%) 100%);
          border-radius: 12px;

          svg {
            flex-shrink: 0;
          }
        }
      }

      .gift {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .img {
          font-size: 120px;
        }

        .btn {
          width: 86px;
          font-size: 12px;
          color: #2f2101;
          background: $livCoThemeColor;
          border: none;
          border-radius: 35px;
        }
      }
    }
  }

  .level-point {
    display: flex;
    column-gap: 8px;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-top: 16px;

    .point-item {
      width: 8px;
      height: 3px;
      background: #373737;
      border-radius: 0 4px;
    }

    .active {
      width: 16px;
      background: $livCoThemeColor;
    }
  }
}
</style>
