<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'

const props = withDefaults(
  defineProps<{
    record: RecordType
  }>(),
  {}
)
</script>

<template>
  <div
    class="line"
    style="height: 1px"
  ></div>
  <div class="content flex-center-center">
    {{ props.record.content }}
    <SvgIcon
      icon-class="intimacy-value-heart"
      class="fsize-12"
    />+{{ props.record.gift_info?.intimacy }}
  </div>
  <div
    class="line"
    style="height: 1px"
  ></div>
</template>

<style scoped lang="scss"></style>
