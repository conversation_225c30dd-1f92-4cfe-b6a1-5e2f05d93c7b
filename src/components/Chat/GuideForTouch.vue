<script setup lang="ts">
const { t } = useI18n()
const guideForClearModeVisible = defineModel<boolean>()
</script>

<template>
  <div
    class="overlay"
    @click="guideForClearModeVisible = false"
    @touchend="guideForClearModeVisible = false"
  >
    <div class="pic">
      <div class="point"></div>
      <SvgIcon
        icon-class="guide-hand-right"
        class="fsize-66 hand"
      />
    </div>

    <div class="guide-intro">
      <div class="guide-text">{{ t('triggerActions') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.overlay {
  position: absolute;
  z-index: 999999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 80%);

  .pic {
    position: relative;
    width: 100%;
    height: 100px;
  }

  .hand {
    position: absolute;
    top: 20%;
    left: 50%;
    animation: hand-animation 1s infinite;

    // 向左下角平移然后回来 往返无限运动动画
    @keyframes hand-animation {
      0% {
        transform: translate(-20%, -20%);
      }

      50% {
        transform: translate(0%, 0%);
      }

      100% {
        transform: translate(-20%, -20%);
      }
    }
  }

  .point {
    position: absolute;
    left: 50%;
    width: 20px;
    height: 20px;
    background: #bdbcbc;
    border-radius: 50%;
    animation: pulse 1s infinite;

    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 #7a7776;
        transform: scale(0.95) translate(-50%, 0);
      }

      70% {
        box-shadow: 0 0 0 20px transparent;
        transform: scale(1) translate(-50%, 0);
      }

      100% {
        box-shadow: 0 0 0 0 transparent;
        transform: scale(0.95) translate(-50%, 0);
      }
    }
  }

  .guide-intro {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 60px;

    .guide-text {
      width: 100%;
      padding: 12px;
      margin-top: -10px;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      word-break: break-word;
    }
  }
}
</style>
