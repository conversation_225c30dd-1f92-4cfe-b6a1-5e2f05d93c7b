<script setup lang="ts">
import useUserStore from '@/stores/modules/user.ts'
import { changeSecondToMMSS } from '@/utils'
import { getPhoneStoreList } from '@/api/agentChat'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { getAmountID, purchase } from '@/api/purchase'
import { ICurrencyTypeEnum, IPurchaseTypeEnum } from '@/enums'
import { useModal } from '@/hooks/useModal.ts'
import { usePaymentError } from '@/hooks/useCommon.ts'
import { PhoneCallTimeGoodsType } from '@/api/agentChat/types.ts'
import { Ref } from 'vue'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    shouldHangout?: boolean
  }>(),
  {
    shouldHangout: false
  }
)
const showBottom = defineModel<boolean>()

const aiID = inject<Ref<number>>('aiID')
const userStore = useUserStore()
const activeOption = ref()
const phoneTimeGoods = ref<PhoneCallTimeGoodsType[]>([])
const purchaseLoading = ref(false)
const emits = defineEmits(['reset', 'hangOut'])

const close = () => {
  if (userStore.userInformation.phone_time === 0 && props.shouldHangout) {
    emits('hangOut')
  }
  emits('reset')
  showBottom.value = false
}

const purchasePhoneTime = async () => {
  purchaseLoading.value = true
  try {
    const { data, code, msg } = await getAmountID({
      currency_type: ICurrencyTypeEnum.Crystal,
      product_type: IPurchaseTypeEnum.PhoneTime,
      product_id: activeOption.value.id,
      ai_id: aiID.value
    })
    if (code === ResultEnum.SUCCESS) {
      const { code: code1, msg: msg1 } = await purchase({ amount_id: data.amount_id, currency_type: ICurrencyTypeEnum.Crystal })
      if (code1 === ResultEnum.SUCCESS) {
        useModal({
          message: t('rechargeSuccessful')
        })
        await userStore.getUserInfo()
        showBottom.value = false
      } else {
        useModal({
          message: msg1
        })
      }
    } else {
      usePaymentError(code, msg)
    }
  } catch (e) {
    console.warn(e)
  }
  purchaseLoading.value = false
}

watch(showBottom, (val) => {
  if (val) {
    userStore.getUserInfo()
    getPhoneStoreList().then((res) => {
      const { code, data } = res
      if (code === ResultEnum.SUCCESS) {
        phoneTimeGoods.value = data.list
      }
    })
  }
})
</script>

<template>
  <van-popup
    round
    teleport="#app"
    v-model:show="showBottom"
    position="bottom"
    @close="close"
    :style="{ maxHeight: '85%' }"
  >
    <div class="popup">
      <div class="flex-between-center mb-16">
        <div>
          <div>{{ t('callDuration') }}</div>
        </div>
        <div @click="close">
          <van-icon
            name="cross"
            size="20"
          />
        </div>
      </div>
      <CrystalBalance class="mb-12" />
      <div class="call-time">
        <div class="call-title">{{ t('remainingDuration') }}:</div>
        <div>{{ changeSecondToMMSS(userStore.userInformation?.phone_time || 0) }}</div>
      </div>
      <div class="top-up-option">
        <div class="title">{{ t('exchangeDuration') }}</div>
        <div class="call-option">
          <div
            class="call-item"
            :class="activeOption?.id === item.id ? 'active' : ''"
            v-for="item in phoneTimeGoods"
            :key="item.id"
            @click="activeOption = item"
          >
            {{ (item.phone_time / 60).toFixed(0) }}{{ ' ' }}{{ t('minutes') }}
          </div>
        </div>
      </div>
      <div class="purchase">
        <div class="flex-center-start flex-column ml-20">
          <div class="num flex-start-center">
            <SvgIcon
              icon-class="crystal"
              class="mr-4 fsize-16"
            />{{ activeOption?.crystal_amount || '--' }}
          </div>
        </div>
        <van-button
          size="large"
          class="btn"
          :disabled="!Boolean(activeOption)"
          :loading="purchaseLoading"
          @click="purchasePhoneTime"
        >
          {{ t('confirmButton') }}
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
.popup {
  :deep(.balance) {
    background: #262626;
  }

  padding: 24px 16px 50px;

  .call-time {
    padding: 12px 16px;
    margin-top: 15px;
    background: #262626;
    border-radius: 16px;

    .call-title {
      margin-bottom: 4px;
      font-size: 13px;
      color: #8b8889;
    }
  }

  .top-up-option {
    margin-top: 24px;

    .title {
      font-size: 14px;
    }

    .tips {
      font-size: 11px;
      font-weight: 400;
      color: #fe1375;
    }

    .call-option {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      margin-top: 16px;

      .call-item {
        padding: 14px 0;
        font-size: 14px;
        line-height: 14px;
        text-align: center;
        background: #262626;
        border: 1px solid #262626;
        border-radius: 12px;
      }

      .active {
        color: $livCoThemeColor;
        background: rgba(235, 223, 172, 10%);
        border: 1px solid $livCoThemeColor;
      }
    }
  }

  .purchase {
    display: flex;
    justify-content: space-between;
    height: 54px;
    margin-top: 24px;
    background: #262626;
    border-radius: 32px;

    .text {
      color: #8b8889;
    }

    .num {
      font-weight: 600;
    }

    .btn {
      width: 124px;
      height: auto;
      color: #2f2101;
      background: $livCoThemeColor;
      border-radius: 40px;
    }
  }
}
</style>
