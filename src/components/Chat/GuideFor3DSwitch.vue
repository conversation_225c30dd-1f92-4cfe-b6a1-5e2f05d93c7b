<script setup lang="ts">
const emits = defineEmits(['switch'])
const guideForSwitchVisible = defineModel<boolean>()
const { t } = useI18n()

const handleSwitch = () => {
  guideForSwitchVisible.value = false
  emits('switch')
}
</script>

<template>
  <div>
    <div
      class="guide"
      @click="handleSwitch"
    ></div>
    <div
      class="guide-border border"
      @click="handleSwitch"
    >
      <img
        src="../../assets/images/agentChat/3d-switch.webp"
        alt="switch"
        class="switch-btn"
      />
      <SvgIcon
        icon-class="guide-hand"
        class="fsize-72 hand"
      />
      <div class="guide-intro">
        <SvgIcon
          icon-class="guide-line"
          class="fsize-40"
        />
        <div class="guide-text">{{ t('dynamicInteraction') }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '../../assets/styles/mixin';

.switch-btn {
  width: 60px;
  height: 60px;
}

.guide {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 9999;
  width: 69px;
  height: 69px;
  border-radius: 16px;
  box-shadow: 0 0 0 1200px rgba(0, 0, 0, 80%);
}

.guide-border {
  @include gradation-border-with-radius-and-transparent-bg(2px, linear-gradient(90deg, rgba(255, 53, 242, 1), rgba(153, 136, 255, 1)));
}

.border {
  position: absolute;
  top: 13px;
  right: 13px;
  z-index: 99999;
  display: flex;
  justify-content: center;
  width: 67px;
  height: 67px;
  padding-top: 2px;
  border-radius: 16px;

  .point {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .hand {
    position: absolute;
    top: 0;
    left: 0;
    transform: translate(-50%, 15px);
    animation: hand-animation 1s infinite;

    // 向左下角平移然后回来 往返无限运动动画
    @keyframes hand-animation {
      0% {
        transform: translate(-50%, 15px);
      }

      50% {
        transform: translate(-60%, 20px);
      }

      100% {
        transform: translate(-50%, 15px);
      }
    }
  }

  .guide-intro {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    transform: translate(0%, 101%);

    .guide-text {
      width: 205px;
      padding: 12px;
      font-size: 12px;
      font-weight: 600;
      background: linear-gradient(90deg, #ff35f2 0%, #98f 100%);
      border-radius: 16px;
    }
  }
}
</style>
