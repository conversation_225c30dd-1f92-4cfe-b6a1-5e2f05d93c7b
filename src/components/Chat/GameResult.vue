<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    record: RecordType
  }>(),
  {}
)
</script>

<template>
  <div class="game-result">
    <div class="game-result-title">
      <img
        v-if="props.record.revert_content?.game_status?.result === 'win'"
        src="@/assets/images/chat/game-result-win.png"
        alt=""
        width="32"
        height="32"
      />
      <img
        v-if="props.record.revert_content?.game_status?.result === 'lose'"
        src="@/assets/images/chat/game-result-lose.png"
        alt=""
        width="32"
        height="32"
      />
      <span class="text">{{ props.record.revert_content?.game_status?.result === 'win' ? t('gameWin') : t('gameLose') }}</span>
    </div>
    <div class="game-result-content">
      {{ props.record.content }}
    </div>
  </div>
</template>
<style scoped lang="scss">
.game-result {
  width: 100%;
  padding: 12px;
  color: white;
  background-color: #171717f2;
  border-radius: 10px;

  .game-result-title {
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 16px;
    font-weight: bold;

    .text {
      line-height: 100%;
    }
  }

  .game-result-content {
    margin-top: 8px;
    font-size: 14px;
    color: #ffffff82;
  }
}
</style>
