<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { Ref } from 'vue'

const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')
const props = withDefaults(
  defineProps<{
    record: RecordType
  }>(),
  {}
)
const videoRef = ref()
const videoUrl = computed(() => {
  return agentMessage.value?.video_list?.find((item) => String(item?.rank) === props.record.revert_content.video_backend_id?.content)?.path
})

const playVideo = () => {
  if (videoUrl.value) {
    videoRef.value?.play()
  }
}
</script>

<template>
  <div class="ads-video">
    <video
      ref="videoRef"
      preload="auto"
      :src="videoUrl"
      autoplay
      muted
      onload="document.getElementById('chatPage').scrollTo({ top: document.getElementById('chatPage').scrollHeight })"
    ></video>
    <div
      class="replay-btn"
      @click="playVideo"
    >
      <SvgIcon
        icon-class="replay"
        class="fsize-22"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.ads-video {
  position: relative;
  width: 80%;
  border-radius: 12px;

  video {
    width: 100%;
    border-radius: inherit;
  }

  .replay-btn {
    position: absolute;
    right: 11px;
    bottom: 11px;
    cursor: pointer;
  }
}
</style>
