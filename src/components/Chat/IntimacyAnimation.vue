<script setup lang="ts">
import lottie, { AnimationItem } from 'lottie-web'
import anime from 'animejs'
import circleFlash from '@/assets/lottie/intimacy-animation/circle-flash.json'
import intimacyHeart from '@/assets/lottie/intimacy-animation/intimacy-heart.json'
import { Ref } from 'vue'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
// const { t } = useI18n()
const show = defineModel<boolean>()

const circleFlashInstance = ref<AnimationItem | null>(null)
const intimacyHeartInstance = ref<AnimationItem | null>(null)

const circleFlashRef = ref(null)
const intimacyHeartRef = ref(null)
const equityBackgroundRef = ref(null)
const textRef = ref(null)

const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')

const newRules = computed(() => {
  return agentMessage.value.intimacy_level_info.find((item) => item.level === agentMessage.value.ai_chat_setting.intimacy_level).interest_names
})
onMounted(() => {
  circleFlashInstance.value = lottie.loadAnimation({
    container: circleFlashRef.value, // 容器
    renderer: 'svg', // 通过svg或canvas渲染
    loop: true, // 是否循环
    autoplay: true, // 是否自动播放
    animationData: circleFlash, // 动画文件
    assetsPath: (import.meta.env.VITE_APP_PUBLIC_PATH === '/' ? '' : import.meta.env.VITE_APP_PUBLIC_PATH) + '/intimacy-animation/circle-flash/' // 动画文件路径
  })
  intimacyHeartInstance.value = lottie.loadAnimation({
    container: intimacyHeartRef.value, // 容器
    renderer: 'svg', // 通过svg或canvas渲染
    loop: false, // 是否循环
    autoplay: true, // 是否自动播放
    animationData: intimacyHeart, // 动画文件
    assetsPath: (import.meta.env.VITE_APP_PUBLIC_PATH === '/' ? '' : import.meta.env.VITE_APP_PUBLIC_PATH) + '/intimacy-animation/intimacy-heart/' // 动画文件路径
  })

  // equityBackgroundInstance.value = lottie.loadAnimation({
  //   container: equityBackgroundRef.value, // 容器
  //   renderer: 'svg', // 通过svg或canvas渲染
  //   loop: false, // 是否循环
  //   autoplay: true, // 是否自动播放
  //   animationData: equityBackground, // 动画文件
  //   assetsPath: '/intimacy-animation/equity-background/' // 动画文件路径
  // })

  circleFlashInstance.value.play()
  intimacyHeartInstance.value.play()
  // equityBackgroundInstance.value.play()
  anime({
    targets: '.circle-flash',
    scale: [0, 1.5],
    translateX: ['-7%', '-7%'],
    translateY: ['-7%', '-7%'],
    duration: 800,
    delay: 200,
    easing: 'linear'
  })
  anime({
    targets: '.flash1',
    translateX: [500, '30%'],
    translateY: ['-50%', '-50%'],
    duration: 800,
    easing: 'linear'
  })
  anime({
    targets: '.flash2',
    translateX: [-500, '-30%'],
    translateY: ['50%', '50%'],
    duration: 800,
    easing: 'linear'
  })
  const tl = anime
    .timeline({
      autoplay: false,
      easing: 'easeOutCubic',
      duration: 200
    })
    .add({
      targets: '.title',
      translateX: [-500, 0],
      duration: 200,
      easing: 'easeOutCubic'
    })
  textRef.value.forEach((item: HTMLElement) => {
    tl.add({
      targets: item,
      translateX: [-500, 0],
      duration: 200,
      easing: 'easeOutCubic'
    })
  })
  setTimeout(() => {
    tl.play()
  }, 500)
})
</script>

<template>
  <van-overlay
    z-index="10"
    :show="show"
    @click="show = false"
  >
    <div
      class="flex-center-center flex-column w-full h-full"
      style="margin-top: -10%; overflow: hidden"
    >
      <div
        ref="intimacyHeartRef"
        class="intimacy-heart"
      >
        <div
          ref="circleFlashRef"
          class="circle-flash"
        ></div>
      </div>
      <div class="title">
        Intimacy Level Increase
        <span class="level">Lv{{ agentMessage.ai_chat_setting.intimacy_level }}</span>
      </div>
      <div
        ref="equityBackgroundRef"
        class="equity-background-intimacy-animation"
      >
        <div class="flash flash1"></div>
        <div class="flash flash2"></div>
        <div class="equity-text">
          <div
            ref="textRef"
            class="flex-start-center cg-4 text-item"
            v-for="(item, index) in newRules"
            :key="index"
          >
            <p>
              <SvgIcon
                icon-class="unlock"
                class="fsize-18 mr-4"
              />
              {{ item }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<style scoped lang="scss">
.circle-flash {
  position: absolute;
  top: 0;
  left: 0;
  width: 120%;
  transform: translate(-50%, -50%);
}

.intimacy-heart {
  position: relative;
  z-index: 11;
  width: 60%;
}

.title {
  z-index: 11;
  width: 100%;
  margin-top: -15%;
  font-family: 'AlibabaPuHuiTi Heavy', serif;
  font-size: 24px;
  color: #fff3b2;
  text-align: center;
  transform: translateX(-500px);

  .level {
    color: white;
  }
}

.equity-background-intimacy-animation {
  position: relative;
  width: calc(100vw - 100px);
  padding: 16px;
  margin: 2% 50px 0;
  background: linear-gradient(90deg, rgba(241, 81, 255, 0%) 0%, rgba(241, 81, 255, 25%) 50%, rgba(241, 81, 255, 0%) 100%);

  .equity-text {
    z-index: 12;
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
    min-width: calc(100% - 100px);
    font-size: 14px;
    color: rgba(255, 255, 255, 60%);
    word-break: break-all;

    .text-item {
      width: 100%;
      transform: translateX(-500px);
    }

    p {
      margin: 0;
    }
  }

  .flash {
    position: absolute;
    width: 118px;
    height: 16px;
    background: url('~@/assets/images/intimacy-flash.png') center / cover no-repeat;
  }

  .flash1 {
    top: 0;
    right: 0;
    transform: translateY(-50%);
  }

  .flash2 {
    bottom: 0;
    left: 0;
    transform: translateY(50%);
  }
}
</style>
