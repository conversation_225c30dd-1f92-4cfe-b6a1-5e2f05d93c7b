<script setup lang="ts">
import Recorder from 'recorder-core/recorder.mp3.min.js'

const myRecorder = ref(null)

// @ts-ignore
const recOpenInit = () => {
  if (!myRecorder.value) {
    myRecorder.value = new Recorder({
      type: 'mp3',
      sampleRate: 16000,
      bitRate: 16
    })
  }
  myRecorder.value.open(
    function () {
      //打开麦克风授权获得相关资源
      //rec.start() 此处可以立即开始录音，但不建议这样编写，因为open是一个延迟漫长的操作，通过两次用户操作来分别调用open和start是推荐的最佳流程]
    },
    function (msg: string, isUserNotAllow: boolean) {
      console.log((isUserNotAllow ? 'UserNotAllow，' : '') + '无法录音:' + msg)
    }
  )
}

import {
  type Hume,
  HumeClient,
  convertBlobToBase64,
  convertBase64ToBlob,
  ensureSingleValidAudioTrack,
  getAudioStream,
  getBrowserSupportedMimeType,
  MimeType
} from 'hume'
import { getCallInfo } from '@/api/agentChat'
import { Ref } from 'vue'
import { ResultEnum } from '@/enums/httpEnum.ts'
import useWs from '@/hooks/useWs.ts'
import useUserStore from '@/stores/modules/user.ts'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { changeSecondToMMSS, scrollToBottom } from '@/utils'
import { useModal } from '@/hooks/useModal.ts'
const { t } = useI18n()

const CALL_TYPE = {
  CALL: 1,
  HANG_OUT: 2,
  META: 3
}
const live2dStatus = inject<Ref<boolean>>('live2dStatus')
const userStore = useUserStore()
const aiID = inject<Ref<number>>('aiID')
const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')
const agentTextRef = ref()
const topUpVisible = ref(false)
const countdownVisible = ref(false)
const show = defineModel<boolean>()
const phoneCallID = ref('')
const inputRef = ref(null)
const question = ref('')
const agentText = ref('')
const volumeNum = ref(0)
const humeConfig = reactive({
  access_token: '',
  config_id: '',
  chat_group_id: '',
  chat_id: ''
})
const callStatus = reactive({
  connecting: true,
  opening_statement: true,
  micOpening: true,
  recording: false,
  thinking: false,
  talking: false,
  inputting: false,
  error: false
})
const topUpVisibleOutside = inject<Ref<boolean>>('topUpVisible')

const getHumeConfig = () => {
  console.log(aiID.value)
  getCallInfo({
    ai_id: aiID.value
  }).then(({ code, data }) => {
    if (code === ResultEnum.SUCCESS) {
      humeConfig.access_token = data.access_token
      humeConfig.config_id = data.config_id
      humeConfig.chat_group_id = data.chat_group_id
      connect()
    }
  })
}

const openKeyboard = () => {
  callStatus.inputting = true
  nextTick(() => {
    inputRef.value.focus()
  })
}

const handleDot = () => {
  if (callStatus.thinking) {
    return 'dot-circle'
  } else if (callStatus.talking) {
    return 'dot-sound'
  } else {
    return 'dot'
  }
}

const sendText = () => {
  socket?.sendUserInput(question.value)
  question.value = ''
}

const hangOut = () => {
  disconnect()
}

const handleTopUp = () => {
  topUpVisible.value = true
}

let balanceIntervalID: number | null = null

/**
 * the Hume Client, includes methods for connecting to EVI and managing the Web Socket connection
 */
let client: HumeClient | null = null

/**
 * the WebSocket instance
 */
let socket: Hume.empathicVoice.chat.ChatSocket | null = null

/**
 * the humeRecorder responsible for recording the audio stream to be prepared as the audio input
 */
let humeRecorder: MediaRecorder | null = null

let volumeCheckId: number | null = null

/**
 * the stream of audio captured from the user's microphone
 */
let audioStream: MediaStream | null = null

let audioContext: AudioContext | null = null

let analyser: AnalyserNode | null = null

let microphone: MediaStreamAudioSourceNode | null = null

/**
 * the current audio element to be played
 */
let currentAudio: HTMLAudioElement | null = null

/**
 * flag which denotes whether to utilize chat resumability (preserve context from one chat to the next)
 */
// let resumeChats = true

/**
 * audio playback queue
 */
const audioQueue: Blob[] = []

/**
 * mime type supported by the browser the application is running in
 */
const mimeType: MimeType = (() => {
  const result = getBrowserSupportedMimeType()
  return result.success ? result.mimeType : MimeType.WEBM
})()

const checkVolume = () => {
  const bufferLength = analyser.fftSize
  const dataArray = new Uint8Array(bufferLength)
  if (!audioStream) return

  analyser.getByteTimeDomainData(dataArray)

  let sum = 0
  for (let i = 0; i < bufferLength; i++) {
    const deviation = dataArray[i] - 128
    sum += deviation * deviation
  }
  const rms = Math.sqrt(sum / bufferLength)

  // 👉 调整灵敏度（放大倍数），加门限
  const amplified = rms * 2.5 // 可调倍数，2.0~4.0 之间尝试
  const normalized = Math.min(Math.round((amplified / 128) * 100), 100)

  // 👉 加一个门限，低于3就算0
  volumeNum.value = normalized < 3 ? 0 : normalized

  volumeCheckId = requestAnimationFrame(checkVolume)
}

/**
 * instantiates interface config and client, sets up Web Socket handlers, and establishes secure Web Socket connection
 */
async function connect(): Promise<void> {
  // instantiate the HumeClient with credentials to make authenticated requests
  client = new HumeClient({
    accessToken: humeConfig.access_token
  })
  // instantiates WebSocket and establishes an authenticated connection
  socket = client.empathicVoice.chat.connect({
    configId: humeConfig.config_id,
    resumedChatGroupId: humeConfig.chat_group_id || undefined
  })

  socket.on('open', handleWebSocketOpenEvent)
  socket.on('message', handleWebSocketMessageEvent)
  socket.on('error', handleWebSocketErrorEvent)
  socket.on('close', handleWebSocketCloseEvent)
}

/**
 * stops audio capture and playback, and closes the Web Socket connection
 */
function disconnect(): void {
  clearInterval(balanceIntervalID)
  stopAudio()

  if (volumeCheckId !== null) {
    cancelAnimationFrame(volumeCheckId)
    volumeCheckId = null
  }

  humeRecorder?.stop()
  humeRecorder = null
  audioStream?.getTracks().forEach((track) => track.stop())
  audioStream = null

  callStatus.connecting = false

  socket?.close()

  volumeNum.value = 0
  show.value = false
}

/**
 * captures and records audio stream, and sends audio stream through the socket
 *
 * API Reference:
 * - `audio_input`: https://dev.hume.ai/reference/empathic-voice-interface-evi/chat/chat#send.Audio%20Input.type
 */
async function captureAudio(): Promise<void> {
  audioStream = await getAudioStream()
  ensureSingleValidAudioTrack(audioStream)

  audioContext = new AudioContext()
  analyser = audioContext.createAnalyser()
  microphone = audioContext.createMediaStreamSource(audioStream)
  microphone.connect(analyser)

  analyser.fftSize = 256

  checkVolume()

  humeRecorder = new MediaRecorder(audioStream, { mimeType })
  // ... 其余现有代码保持不变

  // callback for when recorded chunk is available to be processed
  humeRecorder.ondataavailable = async ({ data }) => {
    // IF size of data is smaller than 1 byte then do nothing
    if (data.size < 1) return

    // base64 encode audio data
    const encodedAudioData = await convertBlobToBase64(data)
    // console.log(encodedAudioData.slice(0, 20))

    // define the audio_input message JSON
    const audioInput: Omit<Hume.empathicVoice.AudioInput, 'type'> = {
      data: encodedAudioData
    }

    // send audio_input message
    socket?.sendAudioInput(audioInput)
  }

  // capture audio input at a rate of 100ms (recommended)
  const timeSlice = 100
  humeRecorder.start(timeSlice)
  callStatus.recording = true
}

/**
 * play the audio within the playback queue, converting each Blob into playable HTMLAudioElements
 */
function playAudio(): void {
  // IF there is nothing in the audioQueue OR audio is currently playing then do nothing
  if (!audioQueue.length || callStatus.talking) return

  // update isPlaying state
  callStatus.talking = true

  // pull next audio output from the queue
  const audioBlob = audioQueue.shift()

  // IF audioBlob is unexpectedly undefined then do nothing
  if (!audioBlob) return

  // converts Blob to AudioElement for playback
  const audioUrl = URL.createObjectURL(audioBlob)
  currentAudio = new Audio(audioUrl)

  // play audio
  currentAudio.play()

  // callback for when audio finishes playing
  currentAudio.onended = () => {
    // update isPlaying state
    callStatus.talking = false

    // attempt to pull next audio output from queue
    if (audioQueue.length) playAudio()
  }
}

/**
 * stops audio playback, clears audio playback queue, and updates audio playback state
 */
function stopAudio(): void {
  // stop the audio playback
  currentAudio?.pause()
  currentAudio = null

  // update audio playback state
  callStatus.talking = false

  // clear the audioQueue
  audioQueue.length = 0
}

/**
 * callback function to handle a WebSocket opened event
 */
async function handleWebSocketOpenEvent(): Promise<void> {
  /* place logic here which you would like invoked when the socket opens */
  console.log('Web socket connection opened')

  // ensures socket will reconnect if disconnected unintentionally
  callStatus.connecting = false
  useWs.send({
    client_id: userStore.userInformation.client_id,
    ai_id: aiID.value,
    type: 'phone_call',
    interactive_model: live2dStatus.value ? 2 : 1,
    call_type: CALL_TYPE.CALL
  })

  //@ts-ignore
  balanceIntervalID = setInterval(() => {
    useWs.send({
      client_id: userStore.userInformation.client_id,
      ai_id: aiID.value,
      type: 'phone_call',
      call_type: CALL_TYPE.META,
      chat_group_id: humeConfig.chat_group_id,
      chat_id: humeConfig.chat_id,
      interactive_model: live2dStatus.value ? 2 : 1,
      phone_call_id: phoneCallID.value
    })
  }, 1000)

  await captureAudio()
}

/**
 * callback function to handle a WebSocket message event
 *
 * API Reference:
 * - `user_message`: https://dev.hume.ai/reference/empathic-voice-interface-evi/chat/chat#receive.User%20Message.type
 * - `assistant_message`: https://dev.hume.ai/reference/empathic-voice-interface-evi/chat/chat#receive.Assistant%20Message.type
 * - `audio_output`: https://dev.hume.ai/reference/empathic-voice-interface-evi/chat/chat#receive.Audio%20Output.type
 * - `user_interruption`: https://dev.hume.ai/reference/empathic-voice-interface-evi/chat/chat#receive.User%20Interruption.type
 */
async function handleWebSocketMessageEvent(message: Hume.empathicVoice.SubscribeEvent): Promise<void> {
  /* place logic here which you would like to invoke when receiving a message through the socket */

  // handle messages received through the WebSocket (messages are distinguished by their "type" field.)
  switch (message.type) {
    // save chat_group_id to resume chat if disconnected
    case 'chat_metadata':
      // chatGroupId = message.chatGroupId
      humeConfig.chat_group_id = message.chatGroupId
      humeConfig.chat_id = message.chatId
      useWs.send({
        client_id: userStore.userInformation.client_id,
        ai_id: aiID.value,
        type: 'phone_call',
        call_type: CALL_TYPE.META,
        chat_group_id: message.chatGroupId,
        interactive_model: live2dStatus.value ? 2 : 1,
        chat_id: message.chatId
      })

      break

    // append user and assistant messages to UI for chat visibility
    case 'user_message':
      callStatus.thinking = true
      useWs.send({
        client_id: userStore.userInformation.client_id,
        ai_id: aiID.value,
        type: 'phone_call',
        call_type: CALL_TYPE.META,
        chat_group_id: humeConfig.chat_group_id,
        chat_id: humeConfig.chat_id,
        interactive_model: live2dStatus.value ? 2 : 1,
        phone_call_id: phoneCallID.value
      })
      break
    case 'assistant_message':
      callStatus.thinking = false
      const { role, content } = message.message
      // const topThreeEmotions = extractTopThreeEmotions(message)
      console.log(content, role)
      agentText.value += content + '<br />'
      scrollToBottom(agentTextRef.value)
      break

    // add received audio to the playback queue, and play next audio output
    case 'audio_output':
      // convert base64 encoded audio to a Blob
      callStatus.thinking = false
      const audioOutput = message.data
      const blob = convertBase64ToBlob(audioOutput, mimeType)

      // add audio Blob to audioQueue
      audioQueue.push(blob)

      // play the next audio output
      if (audioQueue.length >= 1) playAudio()
      break

    // stop audio playback, clear audio playback queue, and update audio playback state on interrupt
    case 'user_interruption':
      stopAudio()
      break
    case 'error':
      useModal({
        message: message.message,
        duration: 1500
      })
      disconnect()
      break
  }
}

/**
 * callback function to handle a WebSocket error event
 */
function handleWebSocketErrorEvent(error: Error): void {
  /* place logic here which you would like invoked when receiving an error through the socket */
  console.error(error, 'handleWebSocketErrorEvent')
  callStatus.error = true
  if (error.message === 'core.ReconnectingWebSocket error') {
    useModal({
      message: t('unSupportRegion'),
      duration: 1500
    })
  } else {
    // useModal({
    //   message: error.message,
    //   duration: 1500
    // })
  }
}

/**
 * callback function to handle a WebSocket closed event
 */
async function handleWebSocketCloseEvent(): Promise<void> {
  /* place logic here which you would like invoked when the socket closes */
  // reconnect to the socket if disconnect was unintentional
  if (callStatus.connecting && !callStatus.error) await connect()

  useWs.send({
    client_id: userStore.userInformation.client_id,
    ai_id: aiID.value,
    type: 'phone_call',
    interactive_model: live2dStatus.value ? 2 : 1,
    call_type: CALL_TYPE.HANG_OUT
  })
  clearInterval(balanceIntervalID)
  stopAudio()

  if (volumeCheckId !== null) {
    cancelAnimationFrame(volumeCheckId)
    volumeCheckId = null
  }
  // 网络问题断开再次处理关闭
  humeRecorder?.stop()
  humeRecorder = null
  audioStream?.getTracks().forEach((track) => track.stop())
  audioStream = null

  callStatus.connecting = false

  volumeNum.value = 0
  show.value = false
  console.log('Web socket connection closed handleWebSocketCloseEvent')
}

const muteMic = () => {
  callStatus.micOpening = !callStatus.micOpening
  if (callStatus.micOpening) {
    humeRecorder?.start(100)
    // 启动音量检测
    if (volumeCheckId === null) {
      volumeCheckId = requestAnimationFrame(checkVolume)
    }
  } else {
    humeRecorder?.stop()
    if (volumeCheckId !== null) {
      cancelAnimationFrame(volumeCheckId)
      volumeCheckId = null
      volumeNum.value = 0
    }
  }
}

const balanceUnsubscribe = useWs.subscribe(
  (data) => data.hasOwnProperty('phone_time_balance'),
  (data) => {
    phoneCallID.value = data.phone_call_id
    userStore.userInformation.phone_time = data.phone_time_balance
    console.log(data.phone_time_balance, 'phone_time_balance')
    if (data.phone_time_balance <= 0) {
      console.log('phone_time_balance_0')
      topUpVisibleOutside.value = true
      console.log(topUpVisibleOutside.value, 'topUpVisibleOutside')
      disconnect()
    }
  }
)

getHumeConfig()

onBeforeUnmount(() => {
  balanceUnsubscribe()
  disconnect()
})
</script>

<template>
  <div
    class="phone-page"
    :style="
      agentMessage.bg_info?.bg_url && {
        background: `url('${agentMessage.bg_info?.bg_url}') center / cover no-repeat`
      }
    "
  >
    <div class="phone-background">
      <div class="top-bar">
        <div class="bar">
          <div
            class="left-arrow"
            @click="hangOut"
          >
            <SvgIcon icon-class="left-arrow" />
          </div>
          <div
            class="fsize-16"
            @click="countdownVisible = true"
          >
            {{ agentMessage.name }}
          </div>
        </div>
      </div>
      <div
        class="down-content"
        :style="callStatus.inputting && 'margin-top: -50%'"
      >
        <div class="sound-tips mb-16">{{ t('clearAudioEnvironment') }}</div>
        <div class="loading-phone-call mb-26">
          <div class="left-line"></div>
          <div class="dots">
            <div
              v-for="i in 7"
              :key="i"
              :class="handleDot()"
            ></div>
          </div>
          <div class="right-line"></div>
        </div>
        <div
          v-if="callStatus.connecting"
          class="mb-12"
        >
          连接中...
        </div>
        <div
          v-else
          class="text-container mb-12"
          ref="agentTextRef"
        >
          <div class="placeholder"></div>
          <span
            v-html="agentText"
            class="text"
          ></span>
          <div class="placeholder"></div>
        </div>
        <div
          class="time-container mb-32"
          v-show="!callStatus.inputting"
          :style="{
            opacity: userStore.userInformation?.phone_time > 30 ? 0 : 1
          }"
        >
          <div
            class="charge"
            @click="handleTopUp"
          >
            <div>{{ changeSecondToMMSS(userStore.userInformation?.phone_time) }}</div>
            <div>
              {{ t('rechargeToUnlock') }}<span class="purple-link">{{ t('moreCallTime') }}</span>
            </div>
          </div>
        </div>
        <div
          class="btn-container mb-8"
          v-show="!callStatus.inputting"
        >
          <div class="flex-center-center flex-column rg-12">
            <div
              class="hang-out"
              @click="hangOut"
            >
              <SvgIcon icon-class="hang-out" />
            </div>
            <div class="btn-text">{{ t('hangUp') }}</div>
          </div>
          <div class="flex-center-center flex-column rg-12">
            <div
              class="mic"
              :class="'mic-volume'"
              :style="{
                '--border-width': volumeNum > 10 ? `${volumeNum * 0.1}px` : '0px',
                opacity: !callStatus.recording ? 0.5 : 1
              }"
              @click="muteMic"
            >
              <SvgIcon :icon-class="callStatus.micOpening ? 'microphone-call' : 'microphone-close'" />
            </div>
            <div class="btn-text">{{ callStatus.micOpening ? t('microphoneOn') : t('microphoneOff') }}</div>
          </div>
        </div>
        <div
          class="keyboard"
          v-show="!callStatus.inputting"
          @click="openKeyboard"
        >
          <div class="w-full flex-center-center cg-4"><SvgIcon icon-class="keyboard-white" />{{ t('showKeyboard') }}</div>
        </div>
        <div
          class="input-container"
          v-show="callStatus.inputting"
        >
          <van-field
            ref="inputRef"
            type="textarea"
            class="flex-1 input"
            v-model="question"
            :autosize="{
              maxHeight: 100
            }"
            rows="1"
          />
          <SvgIcon
            v-if="question"
            icon-class="send"
            class="fsize-28"
            @click="sendText"
          />
          <SvgIcon
            v-else
            icon-class="microphone"
            class="fsize-28"
            @click="callStatus.inputting = false"
          />
        </div>
      </div>
    </div>
    <CountdownHangoutPopup
      v-model="countdownVisible"
      @hang-out="hangOut"
    />
    <CallTimeTopUpPopup
      v-model="topUpVisible"
      @hang-out="hangOut"
    />
  </div>
</template>

<style scoped lang="scss">
.phone-page {
  position: absolute;
  z-index: 6;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overscroll-behavior: none;
  background: url('/ugenie.png') center / 50% no-repeat;

  &::after {
    position: absolute;
    inset: 0;
    z-index: -1;
    content: '';
    background: #0006;
    backdrop-filter: blur(4px); /* 只模糊背景，不影响内容 */
  }
}

.phone-background {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(24, 24, 24, 50%) 0%, rgba(24, 24, 24, 0%) 30%, rgba(24, 24, 24, 0%) 60%, #181818 100%);
}

.down-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 0 32px;

  .sound-tips {
    padding: 4px 12px;
    font-size: 11px;
    font-weight: 400;
    line-height: 13px;
    color: rgba(255, 255, 255, 50%);
    background: rgba(21, 21, 21, 60%);
    border-radius: 28px;
  }

  .loading-phone-call {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 16px;

    .left-line {
      flex: 1;
      height: 1px;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0%) 0%, #fff 100%);
      border-radius: 7px;
    }

    .dots {
      display: flex;
      column-gap: 4px;
      align-items: center;
      margin: 0 8px;

      .dot-circle {
        width: 6px;
        height: 6px;
        background: #fff;
        border-radius: 50%;
        opacity: 0;
        animation: fadeInOut 1s linear infinite;

        // 通过循环自动生成 nth-child 延迟
        @for $i from 1 through 7 {
          &:nth-child(#{$i}) {
            animation-delay: 0.1s * ($i - 1);
          }
        }
      }

      .dot-sound {
        width: 4px;
        height: 6px;
        background: #fff;
        border-radius: 22px;
        animation: dotAnimation 600ms linear infinite;

        // 使用循环为每个 dot 元素添加不同的延迟时间
        @for $i from 1 through 7 {
          &:nth-child(#{$i}) {
            animation-delay: 0.1s * ($i - 1); // 动态计算延迟
          }
        }
      }

      .dot {
        width: 4px;
        height: 6px;
        background: #fff;
        border-radius: 22px;
      }

      /* HTML: <div class="loader"></div> */
      .loader {
        width: 60px;
        aspect-ratio: 4;
        clip-path: inset(0 100% 0 0);
        background: radial-gradient(circle closest-side, #000 90%, #0000) 0 / calc(100% / 3) 100% space;
        animation: l1 1s steps(4) infinite;
      }

      @keyframes l1 {
        to {
          clip-path: inset(0 -34% 0 0);
        }
      }
    }

    .right-line {
      flex: 1;
      height: 1px;
      background: linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0%) 100%);
      border-radius: 7px;
    }
  }

  .text-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 154px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 22px;
    word-break: break-all;
    mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0%) 0%, rgba(0, 0, 0, 100%) 66px, rgba(0, 0, 0, 100%) 88px, rgba(0, 0, 0, 0%) 100%);

    .placeholder {
      flex-shrink: 0;
      width: 100%;
      height: 66px;
    }

    .text {
      text-align: center;
    }
  }

  .time-container {
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    align-items: center;
    justify-content: center;
    padding: 8px 24px;
    background: rgba(24, 24, 24, 60%);
    backdrop-filter: blur(16px);
    border-radius: 28px;

    .time {
      font-weight: 500;
    }

    .charge {
      display: flex;
      flex-direction: column;
      row-gap: 4px;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      font-weight: 400;

      .purple-link {
        color: $livCoThemeColor;
        text-decoration: underline;
      }
    }
  }

  .btn-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 42px;
    font-size: 32px;

    .hang-out {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 62px;
      height: 62px;
      background: #ff5b4d;
      border: 1px solid rgba(255, 255, 255, 40%);
      border-radius: 24px;
    }

    .mic {
      box-sizing: content-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 62px;
      height: 62px;
      background: #4d4d4d99;
      backdrop-filter: blur(68px);
      border: 1px solid rgba(223, 219, 255, 20%);
      border-radius: 24px;
    }

    .mic-volume {
      outline: var(--border-width) solid $livCoThemeColor;
    }

    .btn-text {
      font-size: 12px;
      font-weight: 400;
      color: rgba(255, 255, 255, 50%);
    }
  }

  .keyboard {
    display: flex;
    justify-content: center;
    width: 100%;
    padding-bottom: 12px;
    margin-top: auto;
    font-size: 12px;
    font-weight: 400;
    color: #b7b7b7;
  }

  .input-container {
    position: fixed;
    bottom: 0;
    display: flex;
    column-gap: 12px;
    align-items: center;
    width: 100%;
    padding: 6px 12px 16px;
  }

  :deep(.van-field) {
    height: 42px;
    padding-top: 4px;
    padding-right: 8px;
    padding-bottom: 4px;
  }

  .input {
    height: 42px;
    background: rgba(255, 255, 255, 15%);
    border: 1px solid rgba(223, 219, 255, 20%);
    border-radius: 16px;
  }
}

.top-bar {
  flex: 1;
  width: 100%;

  .bar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;

    .left-arrow {
      position: absolute;
      left: 12px;
      height: 24px;
      font-size: 24px;
    }
  }
}

// 定义关键帧动画
@keyframes fadeInOut {
  0% {
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  30% {
    opacity: 0;
  }

  100% {
    opacity: 0;
  }
}
// 定义关键帧动画，模拟高度变化的效果
@keyframes dotAnimation {
  0% {
    height: 6px;
  }

  50% {
    height: 16px; // 中间时高度增加到 16px
  }

  100% {
    height: 6px; // 最后恢复到 6px
  }
}
</style>
