<script setup lang="ts">
import { BtnContentType } from '@/api/chat/types'
import useAppStore from '@/stores/modules/app.ts'
import { useAdStore } from '@/stores/modules/ad.ts'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { Ref } from 'vue'
import useUserStore from '@/stores/modules/user.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    options: BtnContentType[]
  }>(),
  {
    options: () => []
  }
)
const aiID = inject<Ref<number>>('aiID')

const { share } = useShare()
const appStore = useAppStore()
const adStore = useAdStore()
const userStore = useUserStore()
const shareVisible = ref(false)
const show = defineModel<boolean>()
const emits = defineEmits(['submit', 'restart'])

const submit = async (query: BtnContentType) => {
  if (query.function) {
    switch (query.function) {
      case 'restart':
        emits('restart')
        break
      case 'share':
        if (appStore.isAndroid) {
          shareVisible.value = true
        } else {
          share({
            title: t('shareContent'),
            text: 'Livco',
            url: window.location.href
          })
        }
        break
    }
    return
  }
  if (query.ad) {
    adStore
      .showCheckAdv('input_short_option', {
        aiID: aiID.value,
        trigger_position: '遮挡聊天框的按钮',
        recall_content: query.callback
      })
      .then(() => {
        emits('submit', { text: query, responseWay: '遮挡聊天框的按钮' })
      })
      .catch(() => {
        emits('submit', { text: query, responseWay: '遮挡聊天框的按钮' })
      })
  } else {
    emits('submit', { text: query, responseWay: '遮挡聊天框的按钮' })
  }
  if (!userStore.isLogin) {
    return
  }
  show.value = false
}

onMounted(() => {
  if (props.options.filter((item) => item.ad) && appStore.isAndroid) {
    eventReport({
      event_type: EventTypeEnum.SHOW_AD_BUTTON,
      ai_id: aiID?.value,
      adv_id: adStore.incentiveAdID,
      trigger_position: '输入框位置短选项',
      recall_content: props.options.filter((item) => item.ad)[0].callback
    })
  }
})
</script>

<template>
  <div class="w-full">
    <div class="global-short">
      <div
        v-for="item in props.options"
        :key="item.content"
        class="short-btn"
        @click="submit(item)"
      >
        <SvgIcon
          v-android-only
          v-if="item.ad"
          icon-class="ad"
          class="fsize-20"
        />
        <div class="btn-text">
          {{ item.content }}
        </div>
      </div>
    </div>
    <Teleport to="body">
      <ShareCard v-model="shareVisible" />
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.global-short {
  z-index: 4;
  display: flex;
  column-gap: 12px;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px 16px 24px;
  background: rgba(24, 24, 24, 40%);
  backdrop-filter: blur(88px);
  border-radius: 8px 8px 0 0;

  .short-btn {
    display: flex;
    flex: 1;
    column-gap: 4px;
    align-items: center;
    align-self: stretch;
    justify-content: center;
    height: 48px;
    padding: 8px 12px;
    text-align: center;
    background-color: #ebdfac;
    border-radius: 12px;

    .btn-text {
      color: #2f2101;
    }
  }
}
</style>
