<script setup lang="ts">
const { t } = useI18n()
const guideForClearModeVisible = defineModel<boolean>()
</script>

<template>
  <div
    class="overlay"
    @click="guideForClearModeVisible = false"
    @touchend="guideForClearModeVisible = false"
  >
    <div class="pic">
      <img
        src="../../assets/images/guide-arrow.png"
        alt="arrow"
        class="arrow"
      />
      <SvgIcon
        icon-class="guide-hand-right"
        class="fsize-66 hand"
      />
    </div>
    <div class="guide-intro">
      <div class="guide-text">{{ t('swipeToClear') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.overlay {
  position: absolute;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 80%);

  .pic {
    position: relative;
    width: 100%;
    height: 100px;
  }

  .hand {
    position: absolute;
    top: 20%;
    left: 50%;
    animation: hand-animation 2s linear infinite;

    @keyframes hand-animation {
      0% {
        opacity: 1;
        transform: translateX(50%);
      }

      60% {
        opacity: 1;
      }

      80% {
        opacity: 0;
        transform: translateX(-70%);
      }

      100% {
        opacity: 0;
        transform: translateX(-70%);
      }
    }
  }

  .arrow {
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0) scale(0.5);
  }

  .guide-intro {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 60px;

    .guide-text {
      width: 100%;
      padding: 12px;
      //margin-top: -10px;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      word-break: break-word;
    }
  }
}
</style>
