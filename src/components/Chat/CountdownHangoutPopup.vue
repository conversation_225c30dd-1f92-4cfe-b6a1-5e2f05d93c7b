<script setup lang="ts">
import anime from 'animejs'
const { t } = useI18n()

const show = defineModel<boolean>()
const countdown = reactive({
  time: 15
})
const emits = defineEmits(['hangOut', 'reset'])
const countdownAnime = ref(
  anime({
    targets: countdown,
    round: 1,
    time: 0,
    easing: 'linear',
    duration: 15000,
    autoplay: false,
    complete: () => {
      hangOut()
    }
  })
)

const hangOut = () => {
  show.value = false
  emits('hangOut')
}

const keepOn = () => {
  countdownAnime.value.pause()
  emits('reset')
}

watch(
  show,
  (value) => {
    if (value) {
      countdownAnime.value.restart()
    } else {
      countdown.time = 15
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <van-dialog
    v-model:show="show"
    show-cancel-button
  >
    <div class="padding-16">{{ t('continueConversation') }}</div>
    <template #footer>
      <div class="w-full flex-around-center padding-16 cg-16">
        <van-button
          class="flex-1"
          @click="keepOn"
        >
          {{ t('exitOption') }}
        </van-button>
        <van-button
          type="primary"
          class="flex-1"
          @click="hangOut"
        >
          {{ t('exit') }}({{ countdown.time }}s)
        </van-button>
      </div>
    </template>
  </van-dialog>
</template>

<style scoped lang="scss"></style>
