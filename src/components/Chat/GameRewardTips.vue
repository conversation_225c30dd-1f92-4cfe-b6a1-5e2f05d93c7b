<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'
import { getCrystal, getCrystalCode, updateRecord } from '@/api/chat'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { useModal } from '@/hooks/useModal.ts'
import { useAdStore } from '@/stores/modules/ad.ts'
import useUserStore from '@/stores/modules/user.ts'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { Ref } from 'vue'
import useAppStore from '@/stores/modules/app.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    record: RecordType
  }>(),
  {}
)
const aiID = inject<Ref<number>>('aiID')
const resultReward = inject<Ref<number>>('resultReward')
const appStore = useAppStore()
const adStore = useAdStore()
const userStore = useUserStore()

const updateDoubleStatus = () => {
  updateRecord({
    record_id: props.record.record_id,
    output_text: {
      game_status: {
        ...props.record.revert_content.game_status,
        isShow: false
      }
    }
  }).then(() => {
    props.record.revert_content.game_status.isShow = false
  })
}

const adDoubleReward = async () => {
  let crystalCode = ''
  const { code, data } = await getCrystalCode({
    tag: 'double_get_crystal'
  })
  if (code === ResultEnum.SUCCESS) {
    console.log(data)
    crystalCode = data
  }
  adStore
    .showCheckAdv('double_get_crystal', {
      aiID: aiID.value,
      trigger_position: '玩法成功双倍领取'
    })
    .then(async () => {
      const { code } = await getCrystal({ code: crystalCode })
      if (code === ResultEnum.SUCCESS) {
        updateDoubleStatus()
        useModal({
          message: t('getSuccessfully'),
          duration: 3000
        })
        userStore.getUserInfo()
      }
    })
}
onMounted(() => {
  if (appStore.isAndroid) {
    eventReport({
      event_type: EventTypeEnum.SHOW_AD_BUTTON,
      ai_id: aiID?.value,
      adv_id: adStore.incentiveAdID,
      trigger_position: '玩法成功双倍领取水晶'
    })
  }
})
</script>

<template>
  <div
    class="chat-tips"
    v-if="props.record.revert_content.game_status.isShow"
  >
    <div
      class="line"
      style="height: 1px"
    ></div>
    <div class="content flex-center-center flex-column">
      <div>{{ t('rewardTip') }}</div>
      <div class="flex-center-center">
        <SvgIcon
          icon-class="crystal"
          class="fsize-16"
        />{{ t('crystal') }}*{{ resultReward }}
        <span
          v-android-only
          class="double"
          @click="adDoubleReward"
        >
          {{ t('doubleRewards') }}>
        </span>
      </div>
    </div>
    <div
      class="line"
      style="height: 1px"
    ></div>
  </div>
</template>

<style scoped lang="scss">
.chat-tips {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;

  .content {
    flex-shrink: 0;
    width: auto;
    max-width: 90%;
    padding: 4px 16px;
    font-size: 11px;
    font-weight: 400;
    line-height: 13px;
    color: rgba(255, 255, 255, 70%);
    text-align: center;
    background: rgba(32, 32, 32, 60%);
    backdrop-filter: blur(16px);
    border-radius: 28px;

    .double {
      margin-left: 2px;
      font-size: 11px;
      color: #ebdfac;
      text-decoration-line: underline;
    }
  }

  .line {
    width: 100%;
    background: rgba(255, 255, 255, 30%);
    border-radius: 5px;
  }
}
</style>
