<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'

const props = withDefaults(
  defineProps<{
    record: RecordType
  }>(),
  {}
)
</script>

<template>
  <div class="chat-synopsis">
    <div class="synopsis">
      <SvgIcon
        icon-class="opening"
        class="fsize-18"
      />
      <div>{{ props.record.content }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.chat-synopsis {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;

  svg {
    flex-shrink: 0;
  }

  .synopsis {
    display: flex;
    column-gap: 4px;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    padding: 12px 16px;
    white-space: pre-wrap;
    background: rgba(17, 17, 17, 90%);
    border-radius: 16px;
  }
}
</style>
