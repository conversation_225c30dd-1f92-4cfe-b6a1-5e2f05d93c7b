<script setup lang="ts">
import { BtnContentType } from '@/api/chat/types.ts'
import useAppStore from '@/stores/modules/app.ts'
import { useAdStore } from '@/stores/modules/ad.ts'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { Ref } from 'vue'

const props = withDefaults(
  defineProps<{
    options: BtnContentType[]
  }>(),
  {}
)
const aiID = inject<Ref<number>>('aiID')

// const show = defineModel<boolean>()
const loading = defineModel<boolean>('loading')
const emits = defineEmits(['submit'])

const appStore = useAppStore()
const adStore = useAdStore()
const submit = async (query: BtnContentType) => {
  if (query.ad) {
    adStore
      .showCheckAdv('long_option', {
        aiID: aiID?.value,
        trigger_position: '长选项',
        recall_content: query.callback
      })
      .then(() => {
        emits('submit', { text: query, responseWay: '不遮挡聊天框的长按钮' })
      })
      .catch(() => {
        emits('submit', { text: query, responseWay: '不遮挡聊天框的长按钮' })
      })
  } else {
    emits('submit', { text: query, responseWay: '不遮挡聊天框的长按钮' })
  }
}

onMounted(() => {
  if (props.options.filter((item) => item.ad).length && appStore.isAndroid) {
    eventReport({
      event_type: EventTypeEnum.SHOW_AD_BUTTON,
      ai_id: aiID?.value,
      adv_id: adStore.incentiveAdID,
      trigger_position: '长选项',
      recall_content: props.options.filter((item) => item.ad)?.[0]?.callback
    })
  }
})
</script>

<template>
  <div class="container">
    <div
      class="attach-long"
      v-show="!loading"
    >
      <div
        v-for="item in props.options"
        :key="item.content"
        class="long-btn"
        @click="submit(item)"
      >
        <img
          v-if="item.ad"
          v-android-only
          src="@/assets/images/options-shine.png"
          alt=""
          class="moving-img"
        />
        <SvgIcon
          v-if="item.ad && appStore.isAndroid"
          icon-class="options-ads"
          class="fsize-20"
        />
        <SvgIcon
          v-else
          icon-class="ai-options"
          class="fsize-20"
        />
        <div
          class="btn-text"
          :class="item.ad && appStore.isAndroid && 'ad-btn'"
        >
          {{ item.content }}
        </div>
      </div>
    </div>
    <div
      class="load-container"
      v-show="loading"
    >
      <OptionsLoading />
    </div>
  </div>
</template>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  min-height: 155px;
  max-height: 220px;
  padding: 0 16px 12px 0;
  margin-top: 12px;
  overflow: auto;
}

.load-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.attach-long {
  display: flex;
  flex-direction: column;
  row-gap: 12px;
  align-items: center;
  justify-content: flex-start;
  width: 100%;

  .long-btn {
    position: relative;
    display: flex;
    flex-shrink: 0;
    column-gap: 8px;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    padding: 14px 12px;
    overflow: hidden;
    background: #ffffff1a;
    border-radius: 12px;

    svg {
      flex-shrink: 0;
    }

    .moving-img {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      animation: moveRight 1.5s linear infinite; /* 应用动画 */
    }

    .btn-text {
      flex: 1;
      flex-shrink: 0;
      font-size: 14px;
      color: white;
      word-break: break-word;
      white-space: pre-wrap;
    }

    .ad-btn {
      flex-shrink: 0;
      background: linear-gradient(90deg, #f2d87b 0%, #d3ff62 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

/* 定义从左到右移动的动画 */
@keyframes moveRight {
  0% {
    transform: translateX(-100%); /* 从左侧开始 */
  }

  100% {
    transform: translateX(300%); /* 移动到右侧 */
  }
}
</style>
