<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'

const props = withDefaults(
  defineProps<{
    record: RecordType
  }>(),
  {}
)
</script>

<template>
  <div class="chat-tips">
    <div
      class="line"
      style="height: 1px"
    ></div>
    <div class="content">{{ props.record.content }}</div>
    <div
      class="line"
      style="height: 1px"
    ></div>
  </div>
</template>

<style scoped lang="scss">
.chat-tips {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;

  .content {
    flex-shrink: 0;
    width: auto;
    max-width: 90%;
    padding: 4px 16px;
    font-size: 11px;
    font-weight: 400;
    line-height: 13px;
    color: rgba(255, 255, 255, 70%);
    text-align: center;
    background: rgba(32, 32, 32, 60%);
    backdrop-filter: blur(16px);
    border-radius: 28px;
  }

  .line {
    width: 100%;
    max-width: 70px;
    background: rgba(255, 255, 255, 30%);
    border-radius: 5px;
  }
}
</style>
