<script setup lang="ts">
import { vOnLongPress } from '@vueuse/components'
import { RecordType } from '@/api/chat/types.ts'
import { RecordEnum } from '@/enums'
import { copyText, markdownToHtml, replaceBracketsWithAsterisks } from '@/utils'
import { PopoverPlacement } from 'vant'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    record: RecordType
    index: number
  }>(),
  {}
)
const showPopover = inject<Ref<Record<string, boolean>>>('showPopover')

const onLongPressCallbackDirective = (item: RecordType, index: number) => {
  if (item.type === RecordEnum.AI_COMMENT) {
    showPopover.value[index] = true
  }
}

const dividePlacement = (type: RecordEnum): PopoverPlacement => {
  switch (type) {
    case RecordEnum.USER_COMMENT:
      return 'top-end'
    case RecordEnum.AI_COMMENT:
      return 'top-start'
    default:
      return 'top-end'
  }
}
</script>

<template>
  <div class="user-comment">
    <div
      v-on-long-press.prevent="() => onLongPressCallbackDirective(props.record, index)"
      class="chat-record"
    >
      <ChatLoading
        v-if="props.record.userTTS"
        :style="props.record.type === RecordEnum.USER_COMMENT && { '--uib-color': 'white' }"
      />
      <van-popover
        v-model:show="showPopover[index]"
        teleport="#app"
        trigger="manual"
        :placement="dividePlacement(props.record.type)"
      >
        <div class="flex-center-center pb-14 pt-14 pl-14 pr-14 cg-44">
          <div
            class="flex-center-center flex-column fsize-12 rg-8"
            @click="copyText(props.record.content)"
          >
            <SvgIcon
              icon-class="copy-option"
              class="fsize-20"
            />{{ t('copy') }}
          </div>
        </div>

        <template #reference>
          <div
            class="no-selected"
            v-html="markdownToHtml(replaceBracketsWithAsterisks(props.record.content))"
          ></div>
        </template>
      </van-popover>
    </div>
  </div>
</template>

<style scoped lang="scss">
.user-comment {
  display: flex;
  flex-direction: row-reverse;
  column-gap: 8px;
  align-items: center;
  align-self: end;
  width: fit-content;
  min-width: 48px;
  max-width: 95%;
  height: fit-content;
  background: #282827e5;
  backdrop-filter: blur(32px);
  border-radius: 12px;

  .chat-record {
    flex: 1;
    width: 100%;
    padding: 12px;
    line-height: normal;
    overflow-wrap: break-word;

    :deep(.van-popover__content) {
      border-radius: 12px;
    }

    :deep(img) {
      width: 100%;
      min-width: 300px;
      min-height: 200px;
    }
  }
}
</style>
