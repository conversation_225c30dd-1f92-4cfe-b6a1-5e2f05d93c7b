<script setup lang="ts">
import { vOnLongPress } from '@vueuse/components'
import { RecordType } from '@/api/chat/types.ts'
import { markdownToHtml, replaceBracketsWithAsterisks } from '@/utils'
// import { RecordEnum } from '@/enums'
// import { PopoverPlacement } from 'vant'
// import { likeOrUnlikeAgentText } from '@/api/agentChat'
// import { BOOL_NUMBER } from '@/constant'
// import { useModal } from '@/hooks/useModal.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    record: RecordType
    index: number
  }>(),
  {}
)
const feedbackShow = ref(false)
const showPopover = inject<Ref<Record<string, boolean>>>('showPopover')

const onLongPressCallbackDirective = (index: number) => {
  // if (item.type === RecordEnum.AI_COMMENT) {
  showPopover.value[index] = true
  // }
}

const unlikeAgentText = () => {
  feedbackShow.value = true
}
</script>

<template>
  <van-popover
    v-model:show="showPopover[props.index]"
    teleport="#app"
    trigger="manual"
    placement="top"
  >
    <div class="flex-center-center pb-14 pt-14 pl-14 pr-14 cg-44">
      <div
        class="flex-center-center flex-column fsize-12 rg-8"
        @click="unlikeAgentText()"
      >
        <SvgIcon
          :icon-class="props.record.is_bad ? 'unlike-hand-active' : 'unlike-hand'"
          class="fsize-20"
        />{{ t('report') }}
      </div>
    </div>

    <template #reference>
      <div
        v-on-long-press.prevent="() => onLongPressCallbackDirective(index)"
        v-html="markdownToHtml(replaceBracketsWithAsterisks(props.record.content))"
      ></div>

      <Teleport to="body">
        <FeedbackDrawer v-model="feedbackShow" />
      </Teleport>
    </template>
  </van-popover>
</template>
<style>
.markdown-image {
  display: none;
  min-width: 20%;
  max-width: 75%;
  max-height: 40vh;
  border-radius: 12px;
}

.image-placeholder {
  width: 75%;
  aspect-ratio: 2/1;
  border-radius: 12px;
}
</style>
<style scoped lang="scss"></style>
