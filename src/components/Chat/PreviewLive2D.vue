<script setup lang="ts">
// @ts-ignore
import { ThreeDSourceType } from '@/api/agentChat/types.ts'
import { getDressDetail, getThreeDSource } from '@/api/agentChat'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { ICurrencyTypeEnum, IPurchaseTypeEnum } from '@/enums'
import { getAmountID, purchase } from '@/api/purchase'
import { useModal } from '@/hooks/useModal.ts'
import { usePaymentError } from '@/hooks/useCommon.ts'
import useUserStore from '@/stores/modules/user.ts'
import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'
const { t } = useI18n()
const userStore = useUserStore()
const props = withDefaults(
  defineProps<{
    skinID: number
  }>(),
  {}
)

const emits = defineEmits(['switch'])
const showModel = defineModel<boolean>()
const showBottom = ref(false)
const loading = ref(false)
const skinItem = ref<DressType>({} as DressType)
const live2dResource = ref<ThreeDSourceType>({
  model_json: undefined,
  action_list: [],
  rotate: 0
})
const l2dRef = ref()

const showPopup = () => {
  showBottom.value = true
}

const getThreeDResource = () => {
  getThreeDSource({
    id: props.skinID
  }).then((res) => {
    const { data, code } = res
    if (code === ResultEnum.SUCCESS) {
      live2dResource.value = data
      nextTick(() => {
        l2dRef.value.createNewModel()
      })
    }
  })
}

const getDressInfo = () => {
  loading.value = true
  getDressDetail({
    id: props.skinID,
    type: SkinTypeEnum.ThreeD
  })
    .then((res) => {
      const { data, code } = res
      if (code === ResultEnum.SUCCESS) {
        skinItem.value = data
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      loading.value = false
    })
}

const submitPurchase = async () => {
  loading.value = true
  try {
    const { data, code, msg } = await getAmountID({
      currency_type: skinItem.value.currency_type,
      product_type: IPurchaseTypeEnum.ThreeDSkin,
      product_id: skinItem.value.id
    })
    if (code === ResultEnum.SUCCESS) {
      console.log(data)
      const { code: code1, msg: msg1 } = await purchase({ amount_id: data.amount_id, currency_type: skinItem.value.currency_type })
      if (code1 === ResultEnum.SUCCESS) {
        useModal({
          message: t('purchaseSuccessful')
        })
        loading.value = false
        showModel.value = false
        showBottom.value = false
        emits('switch', true)
        await userStore.getUserInfo()
      } else {
        useModal({
          message: msg1
        })
      }
    } else {
      usePaymentError(code, msg)
    }
  } catch (e) {
    console.warn(e)
  }
  loading.value = false
}

getThreeDResource()
getDressInfo()
</script>

<template>
  <div
    id="modelContainer"
    class="w-full h-full preview"
  >
    <div
      class="return"
      @click="showModel = false"
    >
      <SvgIcon
        icon-class="close"
        class="fsize-22"
      />
    </div>
    <Live2D
      v-if="live2dResource.model_json"
      ref="l2dRef"
      :asset-url="live2dResource.model_json"
      :change-url="live2dResource.change_model_json"
      :action-list="live2dResource.action_list"
      :back-pic="live2dResource.back_pic"
      :params="{
        autoInteract: false,
        scale: live2dResource.scale,
        rotate: live2dResource.rotate / 360,
        anchor: {
          x: live2dResource.vertical,
          y: live2dResource.horizontal
        }
      }"
      @func="showPopup"
    />
    <van-popup
      :overlay="false"
      round
      teleport="#modelContainer"
      v-model:show="showBottom"
      position="bottom"
    >
      <div class="unlock">
        <div class="title">
          <div>{{ t('unlock3DOutfit') }}</div>
          <CrystalBalance />
        </div>
        <van-button
          size="large"
          class="btn"
          :loading="loading"
          :disabled="loading"
          @click="submitPurchase"
        >
          <div class="flex-center-center">
            <SvgIcon
              v-if="skinItem.value"
              :icon-class="skinItem.currency_type === ICurrencyTypeEnum.Coin ? 'coin' : 'crystal'"
              class="mr-4 fsize-22"
            />
            <div>{{ skinItem.value || t('free') }}</div>
            <div class="num">{{ t('unlock') }}</div>
          </div>
        </van-button>
      </div>
    </van-popup>
  </div>
</template>

<style scoped lang="scss">
:deep(.balance) {
  background: #262626;
}

.preview {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  background: black;
}

.return {
  position: absolute;
  top: 8px;
  right: 16px;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(24, 24, 24, 60%);
  backdrop-filter: blur(14px);
  border-radius: 10px;
}

.unlock {
  padding: 16px;

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .num {
    margin-left: 4px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(47, 33, 1, 70%);
  }

  .btn {
    width: 100%;
    height: 48px;
    margin-top: 20px;
    font-size: 18px;
    font-weight: 600;
    line-height: 21px;
    color: $livCoTextColor;
    background: $livCoThemeColor;
    border-radius: 40px;
  }
}
</style>
