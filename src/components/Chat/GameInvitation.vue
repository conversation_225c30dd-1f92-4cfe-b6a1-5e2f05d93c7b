<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'
import { updateRecord } from '@/api/chat'
import { useAdStore } from '@/stores/modules/ad.ts'
import { Ref } from 'vue'

const props = withDefaults(
  defineProps<{
    record: RecordType
    index: number
  }>(),
  {}
)
const aiID = inject<Ref<number>>('aiID')
const adStore = useAdStore()
const emits = defineEmits(['submit'])
const done = ref(false)

const submit = () => {
  if (done.value) return
  done.value = true
  emits('submit', {
    text: {
      content: props.record.revert_content.game_invitation.button_content,
      callback: props.record.revert_content.game_invitation.callback
    },
    responseWay: '玩法邀请'
  })
  updateRecord({
    record_id: props.record.record_id,
    output_text: {
      game_invitation: {
        ...props.record.revert_content.game_invitation,
        isShow: false
      }
    }
  }).then(() => {
    props.record.revert_content.game_invitation.isShow = false
  })
}

const ad = () => {
  adStore
    .showCheckAdv('sure_content_invite', {
      aiID: aiID.value,
      trigger_position: '玩法邀请',
      recall_content: props.record.revert_content.game_invitation.callback
    })
    .then(submit)
    .catch(submit)
}
</script>

<template>
  <div
    class="game-invitation"
    v-if="props.record.revert_content.game_invitation.isShow"
  >
    <div class="black-bg">
      <div class="inside">
        <div class="text">{{ props.record.revert_content.game_invitation.invitation_content }}</div>
        <div
          class="start"
          @click="ad"
        >
          {{ props.record.revert_content.game_invitation.button_content }} <SvgIcon icon-class="invite-arrow" />
        </div>
        <img
          src="@/assets/images/chat/game-invitation-bg.png"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.game-invitation {
  width: 80%;
  padding: 1px;
  background: linear-gradient(132deg, #fff4db 0%, rgba(23, 23, 23, 0%) 19.63%, rgba(255, 244, 219, 60%) 49.92%, rgba(23, 23, 23, 0%) 79.63%, #fff4db 100%);
  border-radius: 12px;

  .black-bg {
    background: #111111f2;
    border-radius: inherit;
  }

  .inside {
    position: relative;
    width: 100%;
    padding: 16px 16px 20px;
    background: linear-gradient(155.41deg, rgba(23, 23, 23, 0%) 42.47%, rgba(235, 223, 172, 22%) 100%);
    border-radius: inherit;

    img {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 142px;
    }
  }

  .text {
    display: -webkit-box;
    width: 100%;
    overflow: hidden;
    font-size: 14px;
    color: #fffc;
    word-break: break-word;
    white-space: pre-wrap;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .start {
    display: flex;
    column-gap: 2px;
    align-items: center;
    justify-content: center;
    width: fit-content;
    padding: 8px 8px 8px 12px;
    margin-top: 12px;
    line-height: 100%;
    color: #000;
    background: linear-gradient(90deg, #feeb96 0%, #ebdfac 100%);
    border-radius: 18px;
  }
}
</style>
