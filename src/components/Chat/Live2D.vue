<script setup lang="ts">
import lottie, { AnimationItem } from 'lottie-web'
import { Live2dCreator } from '@/utils/live2d.ts'
import { MotionPriority } from 'pixi-live2d-display/cubism4'
import { ActionType } from '@/api/agentChat/types.ts'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { Ref } from 'vue'
import switchModel from '@/assets/lottie/switch-model.json'
import { isEmpty } from 'lodash-es'
import Loading from '@/assets/lottie/loading.json'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { playChatAudio } from '@/utils'

interface ModelParams {
  autoInteract?: boolean
  scale?: number
  rotate: number
  anchor?: {
    x: number
    y: number
  }
}

const switchInstance = ref<AnimationItem | null>(null)
const switchRef = ref(null)

const isChange = computed(() => {
  return activeAssetUrl.value && activeAssetUrl.value === props.changeUrl
})
const activeAssetUrl = ref('')

const props = withDefaults(
  defineProps<{
    params?: ModelParams
    isClearMode: boolean
    assetUrl: string
    configParameter?: Record<string, number>
    changeUrl?: string
    actionList: ActionType[]
    backPic?: string
    emo?: string
  }>(),
  {
    isClearMode: false
  }
)
const aiID = inject<Ref<number>>('aiID')
const showInterface = inject<Ref<boolean>>('showInterface')
const isNoRecord = inject<Ref<boolean>>('isNoRecord')
const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')
const live2dOpacity = inject<Ref<number>>('live2dOpacity')
const isDragging = inject<Ref<boolean>>('isDragging')
const emits = defineEmits(['func'])
const loading = ref(true)
const erosModel = ref()
const loadingInstance = ref<AnimationItem | null>(null)
const loadingRef = ref(null)

const createNewModel = async (changeUrl?: string) => {
  console.log('create')
  activeAssetUrl.value = changeUrl || props.assetUrl
  deleteModel()
  loading.value = true
  erosModel.value = new Live2dCreator({
    modelJson: activeAssetUrl.value,
    canvasID: 'live2d',
    containerID: 'modelContainer',
    autoInteract: props.params.autoInteract,
    params: {
      scale: props.params.scale,
      rotate: props.params.rotate,
      anchor: props.params.anchor
    }
  })
  await erosModel.value.initModel(() => {
    if (props.backPic) {
      const img = new Image()
      img.src = props.backPic
      img.onload = () => {
        loading.value = false
        setTimeout(() => {
          loading.value = false
          showInterface.value = true
          isNoRecord.value && playChatAudio(agentMessage.value.opening_statement_voice)
        }, 400)
      }
      img.onerror = () => {
        loading.value = false
        setTimeout(() => {
          loading.value = false
          showInterface.value = true
          isNoRecord.value && playChatAudio(agentMessage.value.opening_statement_voice)
        }, 400)
      }
    } else {
      loading.value = false
      setTimeout(() => {
        showInterface.value = true
      }, 400)
    }
    erosModel.value.motionTrigger('蝴蝶结')
    emits('func')
    eventReport({
      event_type: EventTypeEnum.L2D_LOADED,
      ai_id: aiID.value
    })
  })

  if (aiID?.value) {
    if (!isEmpty(props.configParameter)) {
      Object.keys(props.configParameter).forEach((key) => {
        erosModel.value?.setParameterValueById(key, props.configParameter[key])
      })
    }
    erosModel.value.registerHitEvent((hitAreaNames: string[]) => {
      const motionName = hitAreaNames[0]
      const actionItem = props.actionList.find((item) => item.motion === motionName)
      console.log(actionItem, motionName)
      if (motionName.toLowerCase() === 'tychange') {
        changeModel()
        return
      }

      erosModel.value.motionTrigger(motionName, undefined, MotionPriority.FORCE)
      eventReport({
        event_type: EventTypeEnum.TOUCH_INTERACTION,
        ai_id: aiID.value,
        content: motionName
      }).catch((err) => {
        console.warn(err)
      })
    })
  }
}

const deleteModel = () => {
  ;(window as any).erosModel?.destroy(true, true, true)
  delete (window as any).erosModel
  erosModel.value = undefined
}

const changeModel = () => {
  switchInstance.value.play()
  createNewModel(activeAssetUrl.value === props.changeUrl ? props.assetUrl : props.changeUrl)
}

onMounted(async () => {
  // await createNewModel()
  switchInstance.value = lottie.loadAnimation({
    container: switchRef.value, // 容器
    renderer: 'svg', // 通过svg或canvas渲染
    loop: false, // 是否循环
    autoplay: false, // 是否自动播放
    animationData: switchModel, // 动画文件
    assetsPath: (import.meta.env.VITE_APP_PUBLIC_PATH === '/' ? '' : import.meta.env.VITE_APP_PUBLIC_PATH) + '/switch/' // 动画文件路径
  })
  switchInstance.value.addEventListener('complete', () => {
    const flag = activeAssetUrl.value === props.changeUrl ? -1 : 1
    switchInstance.value.setDirection(flag)
  })
})

onBeforeUnmount(() => {
  delete (window as any).erosModel
})

defineExpose({
  createNewModel,
  deleteModel,
  erosModel
})

provide('erosModel', erosModel)
provide('isChange', isChange)

watch(
  () => props.emo,
  (val) => {
    if (val) {
      const motion = props.actionList.find((item) => item.emotion_name === val)
      erosModel.value?.motionTrigger(motion?.motion)
    }
  },
  {
    deep: true
  }
)

onMounted(() => {
  loadingInstance.value = lottie.loadAnimation({
    container: loadingRef.value, // 容器
    renderer: 'svg', // 通过svg或canvas渲染
    loop: true, // 是否循环
    autoplay: true, // 是否自动播放
    animationData: Loading // 动画文件
  })
})
</script>

<template>
  <div
    id="live2d-container"
    :style="{
      background: props.backPic && !loading ? `url('${props.backPic}') center / cover no-repeat` : 'transparent'
    }"
  >
    <canvas
      id="live2d"
      :style="{ opacity: loading ? 0 : 1 }"
    ></canvas>
    <L2DSpeedial
      v-if="props.actionList.length"
      v-show="props.isClearMode"
      :action-list="props.actionList"
      :style="{ opacity: props.isClearMode && !isDragging ? 1 : live2dOpacity - 0.4 }"
    />
    <div
      ref="switchRef"
      class="switch-btn"
      v-show="props.isClearMode && props.changeUrl"
      @click="changeModel"
    >
      <img
        src="../../assets/images/3d/switch.png"
        alt=""
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.load {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  font-size: 14px;
  text-align: center;
  background: black;

  img {
    width: 232px;
    //animation: rotate 3s linear infinite;
  }
}

//@keyframes rotate {
//  0% {
//    transform: rotate(0deg);
//  }
//  100% {
//    transform: rotate(360deg);
//  }
//}

#live2d-container {
  position: absolute;
  z-index: 0;
  width: 100%;
  height: 100dvh;
  overscroll-behavior: none;

  .switch-btn {
    position: fixed;
    bottom: 38px;
    left: 24px;
    width: 58px;
    height: 58px;
    transition: all 0.5s;

    img {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 1;
      width: 58px;
    }
  }

  .anima-before {
    transform: rotate3d(0, 1, 0, 180deg);
  }

  .switch-btn-after {
    position: fixed;
    bottom: 38px;
    left: 24px;
    width: 58px;
    height: 58px;
    background: url('@/assets/images/3d/switch-after.webp') no-repeat center center;
    background-size: contain;
    transition: all 0.5s;
    transform: rotate3d(0, 1, 0, -180deg);
    transform-style: preserve-3d;
    backface-visibility: hidden;
  }

  .anima-after {
    transform: rotate3d(0, 1, 0, 0deg);
  }

  @keyframes circleRound {
    0% {
    }

    100% {
      transform: rotate3d(0, 1, 0, 90deg);
    }
  }

  @keyframes circleRoundAfter {
    0% {
      transform: rotate3d(0, 1, 0, 90deg);
    }

    100% {
      transform: rotate3d(0, 1, 0, 0deg);
    }
  }

  &::-webkit-scrollbar {
    display: none;
  }
}

#live2d {
  width: 100%;
  height: 100dvh;
  transition: all 0.5s;
}
</style>
