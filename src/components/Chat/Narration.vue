<script setup lang="ts">
import { vOnLongPress } from '@vueuse/components'
import { RecordType } from '@/api/chat/types.ts'
import { copyText } from '@/utils'
import { likeOrUnlikeAgentText } from '@/api/agentChat'
import { BOOL_NUMBER } from '@/constant'
import { useModal } from '@/hooks/useModal.ts'
const { t } = useI18n()

const feedbackShow = ref(false)
const showPopover = inject<Ref<Record<string, boolean>>>('showPopover')

const props = withDefaults(
  defineProps<{
    record: RecordType
    index: number
  }>(),
  {}
)

const onLongPressCallbackDirective = (index: number) => {
  // if (item.type === RecordEnum.AI_COMMENT) {
  showPopover.value[index] = true
  // }
}

const likeAgentText = (data: RecordType) => {
  likeOrUnlikeAgentText({
    message_id: data.record_id,
    is_like: data.is_like ? BOOL_NUMBER.NO : BOOL_NUMBER.YES,
    is_bad: data.is_like ? undefined : BOOL_NUMBER.NO
  })
    .then((res) => {
      if (res.code === 200) {
        useModal({
          message: data.is_like ? t('likeCancelled') : t('likeSuccessful')
        })
        if (!data.is_like) {
          data.is_bad = BOOL_NUMBER.NO
        }
        data.is_like = data.is_like ? BOOL_NUMBER.NO : BOOL_NUMBER.YES
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {})
}
const unlikeAgentText = () => {
  feedbackShow.value = true
  // likeOrUnlikeAgentText({
  //   message_id: data.record_id,
  //   is_bad: data.is_bad ? BOOL_NUMBER.NO : BOOL_NUMBER.YES,
  //   is_like: data.is_bad ? undefined : BOOL_NUMBER.NO
  // })
  //   .then((res) => {
  //     if (res.code === 200) {
  //       useModal({
  //         message: data.is_bad ? t('dislikeCancelled') : t('dislikeSuccessful')
  //       })
  //       if (!data.is_bad) {
  //         data.is_like = BOOL_NUMBER.NO
  //       }
  //       data.is_bad = data.is_bad ? BOOL_NUMBER.NO : BOOL_NUMBER.YES
  //     }
  //   })
  //   .catch((err) => {
  //     console.warn(err)
  //   })
  //   .finally(() => {})
}
</script>

<template>
  <van-popover
    v-model:show="showPopover[index]"
    teleport="#app"
    trigger="manual"
    placement="top"
  >
    <div class="flex-center-center pb-14 pt-14 pl-14 pr-14 cg-44">
      <div
        class="flex-center-center flex-column fsize-12 rg-8"
        @click="likeAgentText(props.record)"
      >
        <SvgIcon
          :icon-class="props.record.is_like ? 'like-hand-active' : 'like-hand'"
          class="fsize-20"
        />{{ t('like') }}
      </div>
      <div
        class="flex-center-center flex-column fsize-12 rg-8"
        @click="unlikeAgentText()"
      >
        <SvgIcon
          :icon-class="props.record.is_bad ? 'unlike-hand-active' : 'unlike-hand'"
          class="fsize-20"
        />{{ t('report') }}
      </div>
      <div
        class="flex-center-center flex-column fsize-12 rg-8"
        @click="copyText(props.record.content)"
      >
        <SvgIcon
          icon-class="copy-option"
          class="fsize-20"
        />{{ t('copy') }}
      </div>
    </div>

    <template #reference>
      <div
        class="narration-normal"
        v-on-long-press.prevent="() => onLongPressCallbackDirective(index)"
      >
        <img
          src="@/assets/images/chat/normal-narraion-decoration-top.png"
          alt=""
        />
        <img
          src="@/assets/images/chat/normal-narraion-decoration-bottom.png"
          alt=""
        />{{ props.record.content }}
      </div>
      <Teleport to="body">
        <FeedbackDrawer v-model="feedbackShow" />
      </Teleport>
    </template>
  </van-popover>
</template>

<style scoped lang="scss">
.narration-normal {
  position: relative;
  padding: 10px 16px;
  margin: 8px 0;
  font-size: 12px;
  line-height: 16px;
  color: rgba(255, 255, 255, 92%);
  text-align: center;
  white-space: pre-wrap;
  background: linear-gradient(
    90deg,
    rgba(13, 13, 13, 10%) 0%,
    rgba(13, 13, 13, 60%) 5%,
    rgba(13, 13, 13, 90%) 90%,
    rgba(13, 13, 13, 60%) 95%,
    rgba(13, 13, 13, 10%) 100%
  );
  border: 1px solid;
  border-image: linear-gradient(90deg, rgba(255, 255, 255, 0%), rgba(255, 255, 255, 50%), rgba(255, 255, 255, 0%)) 1 1;
}

img {
  position: absolute;
  left: 50%;
  height: 12px;

  &:first-child {
    top: 0;
    transform: translate(-50%, -70%);
  }

  &:last-child {
    bottom: 0;
    transform: translate(-50%, 70%);
  }
}
</style>
