<script setup lang="ts">
import { transformAspectRadio } from '@/utils'
import { ActionType } from '@/api/agentChat/types.ts'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { Ref } from 'vue'

const ACTION_TYPE = {
  BEFORE: 1,
  AFTER: 2
}

const props = withDefaults(
  defineProps<{
    actionList: ActionType[]
  }>(),
  {}
)
const aiID = inject<Ref<number>>('aiID')

const handledAction = () => {
  if (props.actionList.length < 4) {
    // 补充到4个
    const diff = 4 - props.actionList.length
    for (let i = 0; i < diff; i++) {
      props.actionList.push({
        id: 0,
        motion: 'null',
        pic_url: null,
        rank: Infinity,
        type: 1
      })
    }
  } else if (props.actionList.length < 8) {
    // 补充到8个
    const diff = 8 - props.actionList.length
    for (let i = 0; i < diff; i++) {
      props.actionList.push({
        id: 0,
        motion: 'null',
        pic_url: null,
        rank: Infinity,
        type: 1
      })
    }
  }
}

const beforeActionList = computed(() => {
  const arr = props.actionList.filter((item) => item.type === ACTION_TYPE.BEFORE)
  // 根据rank排序
  arr.sort((a, b) => a.rank - b.rank)
  if (arr.length < 8) {
    // 补充到8个
    const diff = 8 - arr.length
    for (let i = 0; i < diff; i++) {
      arr.push({
        id: 0,
        motion: 'null',
        pic_url: null,
        rank: Infinity,
        type: 1
      })
    }
  }
  return rearrangeArray(arr)
})

const afterActionList = computed(() => {
  const arr = props.actionList.filter((item) => item.type === ACTION_TYPE.AFTER)
  // 根据rank排序
  arr.sort((a, b) => a.rank - b.rank)
  if (arr.length < 8) {
    const diff = 8 - arr.length
    for (let i = 0; i < diff; i++) {
      arr.push({
        id: 0,
        motion: 'null',
        pic_url: null,
        rank: Infinity,
        type: 1
      })
    }
  }
  return rearrangeArray(arr)
})

const erosModel = inject<Ref<any>>('erosModel')
const isChange = inject<Ref<boolean>>('isChange')

const menuVisible = ref(false)
const radius = ref(transformAspectRadio(110)) // 圆形展开半径
const startAngle = ref(0)
const currentAngle = ref(0)
const dragActive = ref(false)
const activeAngle = ref(0)

const container = ref<HTMLElement | null>(null)
const itemsContainer = ref<HTMLElement | null>(null)
const speedDialItem = ref<HTMLElement[] | null>(null)

const rearrangeArray = (arr: ActionType[]) => {
  const order = [3, 2, 1, 0, 7, 6, 5, 4] // 目标顺序
  return order.map((index) => arr[index]) // 使用目标顺序重新排列数组
}

const toggleMenu = () => {
  menuVisible.value = !menuVisible.value
  if (!menuVisible.value) {
    currentAngle.value = 0
    if (itemsContainer.value) {
      itemsContainer.value.style.transform = 'rotate(0rad)'
    }
  } else {
    if (itemsContainer.value) {
      itemsContainer.value.style.transition = 'opacity 0.3s linear'
    }
  }
}

const btnClick = (item: ActionType) => {
  if (dragActive.value) return
  navigator?.vibrate?.(30)
  console.log(item)
  if (item.id) {
    eventReport({
      event_type: EventTypeEnum.TOUCH_INTERACTION,
      ai_id: aiID.value,
      action_id: item.id,
      action_name: item.motion
    }).catch((err) => {
      console.warn(err)
    })
    erosModel.value.motionTrigger(item.motion)
  }
}

const getElementCenter = () => {
  if (!container.value) return { x: 0, y: 0 }
  const rect = container.value.getBoundingClientRect()
  return {
    x: rect.left + rect.width / 2,
    y: rect.top + rect.height / 2
  }
}

function getImageUrl() {
  return new URL(`../../assets/images/3d/lock-motion.png`, import.meta.url).href
}

const getItemStyle = (index: number, item: ActionType) => {
  let angle = 0
  if (index < 4) {
    angle = ((index * 33) / 360) * 2 * Math.PI - (190 * Math.PI) / 180
  } else {
    angle = (((index - 5) * 33) / 360) * 2 * Math.PI + (25 * Math.PI) / 180
  }

  const x = radius.value * Math.cos(angle) - transformAspectRadio(32.5) // 调整X轴偏移量
  const y = radius.value * Math.sin(angle) - transformAspectRadio(32.5) // 调整Y轴偏移量

  return {
    transform: `translate(${x}px, ${y}px) rotate(${-currentAngle.value}rad)`,
    background: `url('${item.pic_url || getImageUrl()}') center/cover no-repeat`
  }
}

const onTouchStart = (event: TouchEvent) => {
  dragActive.value = true
  const touch = event.touches[0]
  const center = getElementCenter()
  startAngle.value = Math.atan2(touch.clientY - center.y, touch.clientX - center.x)
  activeAngle.value = startAngle.value
}

const onTouchMove = (event: TouchEvent) => {
  if (!dragActive.value || !container.value || !itemsContainer.value) return

  speedDialItem.value.forEach((item) => {
    item.style.transition = 'opacity 0.3s linear'
  })
  const touch = event.touches[0]
  const center = getElementCenter()
  const angle = Math.atan2(touch.clientY - center.y, touch.clientX - center.x)
  const angleDiff = angle - startAngle.value

  currentAngle.value += angleDiff
  itemsContainer.value.style.transform = `rotate(${currentAngle.value}rad)`
  itemsContainer.value.style.transition = 'opacity 0.3s linear'
  startAngle.value = angle
}

const onTouchEnd = () => {
  if (!dragActive.value || !itemsContainer.value) return

  const totalAngle = currentAngle.value * (180 / Math.PI)
  const angleDiff = (startAngle.value - activeAngle.value) * (180 / Math.PI)

  if (Math.abs(angleDiff) > 40) {
    speedDialItem.value.forEach((item) => {
      item.style.transition = 'transform 0.3s ease-in-out'
    })
    const snappedAngle = Math.floor(totalAngle / 180) * 180
    currentAngle.value = (snappedAngle * Math.PI) / 180
    itemsContainer.value.style.transition = 'transform 0.3s ease-in-out'
  } else {
    speedDialItem.value.forEach((item) => {
      item.style.transition = 'transform 0.3s ease-in-out'
    })
    const snappedAngle = Math.ceil(totalAngle / 180) * 180
    currentAngle.value = (snappedAngle * Math.PI) / 180
    itemsContainer.value.style.transition = 'transform 0.3s ease-in-out'
  }
  itemsContainer.value.style.transform = `rotate(${currentAngle.value}rad)`
  dragActive.value = false
}
handledAction()
onMounted(() => {
  if (props.actionList.length > 4) {
    if (itemsContainer.value) {
      itemsContainer.value.addEventListener('touchstart', onTouchStart)
    }
    window.addEventListener('touchmove', onTouchMove)
    window.addEventListener('touchend', onTouchEnd)
  }
})

onBeforeUnmount(() => {
  if (props.actionList.length > 4) {
    if (itemsContainer.value) {
      itemsContainer.value.removeEventListener('touchstart', onTouchStart)
    }
    window.removeEventListener('touchmove', onTouchMove)
    window.removeEventListener('touchend', () => {
      nextTick(() => {
        onTouchEnd()
      })
    })
  }
})
</script>

<template>
  <div
    class="speed-dial-container"
    ref="container"
  >
    <div
      class="back"
      :class="{ active: menuVisible, 'back-change': isChange }"
    ></div>
    <div
      class="main-button"
      :class="{ 'main-button-change': isChange }"
      @click="toggleMenu"
    ></div>
    <div
      class="items-container"
      ref="itemsContainer"
      :class="{ active: menuVisible }"
      @mousedown.stop
    >
      <div
        ref="speedDialItem"
        v-show="!isChange"
        v-for="(item, index) in beforeActionList"
        :key="index"
        class="speed-dial-item"
        :style="getItemStyle(index, item)"
        @click.prevent="btnClick(item)"
        style="color: black"
      ></div>
      <div
        ref="speedDialItem"
        v-show="isChange"
        v-for="(item, index) in afterActionList"
        :key="index"
        class="speed-dial-item"
        :style="getItemStyle(index, item)"
        @click.prevent="btnClick(item)"
        style="color: black"
      ></div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.speed-dial-container {
  position: fixed;
  right: 16px;
  bottom: 32px;
  width: 74px;
  height: 74px;

  .back {
    position: absolute;
    top: -63px;
    left: -63px;
    width: 200px;
    height: 200px;
    pointer-events: none;
    background: url('@/assets/images/3d/main-circle.png') center/cover no-repeat;
    opacity: 0;
    transition: opacity 0.3s linear;

    &.active {
      opacity: 0.6;
    }
  }

  .back-change {
    background: url('@/assets/images/3d/main-circle-change.png') center/cover no-repeat;
  }

  .main-button {
    position: absolute;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 74px;
    height: 74px;
    font-size: 24px;
    color: white;
    cursor: pointer;
    user-select: none;
    background: url('@/assets/images/3d/main-action.png') center/cover no-repeat;
    border-radius: 50%;
  }

  .main-button-change {
    background: url('@/assets/images/3d/main-action-change.png') center/cover no-repeat;
  }

  .items-container {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s linear;
    transform-origin: center center;

    &.active {
      pointer-events: auto;
      opacity: 1;
    }

    .speed-dial-item {
      position: absolute;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 65px;
      height: 65px;
      font-size: 10px;
      color: white;
      cursor: pointer;
      user-select: none;
      background: url('https://eros-dev.wujialin.top/api/uploads/admin/2024-09-13/e99d887d-df48-6c9a-8543-59fb8a741dd/image/item.png') center/cover no-repeat;
      border-radius: 50%;
    }
  }
}
</style>
