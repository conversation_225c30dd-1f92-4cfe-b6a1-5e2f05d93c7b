<script setup lang="ts">
import { vOnLongPress } from '@vueuse/components'
import { RecordType } from '@/api/chat/types.ts'
import { RecordEnum } from '@/enums'
import { copyText, markdownToHtml, replaceBracketsWithAsterisks } from '@/utils'
import { PopoverPlacement } from 'vant'
import { likeOrUnlikeAgentText } from '@/api/agentChat'
import { BOOL_NUMBER } from '@/constant'
import { useModal } from '@/hooks/useModal.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    record: RecordType
    index: number
  }>(),
  {}
)
const feedbackShow = ref(false)
const showPopover = inject<Ref<Record<string, boolean>>>('showPopover')

const onLongPressCallbackDirective = (item: RecordType, index: number) => {
  if (item.type === RecordEnum.AI_COMMENT) {
    showPopover.value[index] = true
  }
}

const dividePlacement = (type: RecordEnum): PopoverPlacement => {
  switch (type) {
    case RecordEnum.USER_COMMENT:
      return 'top-end'
    case RecordEnum.AI_COMMENT:
      return 'top-start'
    default:
      return 'top-end'
  }
}

const likeAgentText = (data: RecordType) => {
  likeOrUnlikeAgentText({
    message_id: data.record_id,
    is_like: data.is_like ? BOOL_NUMBER.NO : BOOL_NUMBER.YES,
    is_bad: data.is_like ? undefined : BOOL_NUMBER.NO
  })
    .then((res) => {
      if (res.code === 200) {
        useModal({
          message: data.is_like ? t('likeCancelled') : t('likeSuccessful')
        })
        if (!data.is_like) {
          data.is_bad = BOOL_NUMBER.NO
        }
        data.is_like = data.is_like ? BOOL_NUMBER.NO : BOOL_NUMBER.YES
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {})
}
const unlikeAgentText = () => {
  feedbackShow.value = true
  // likeOrUnlikeAgentText({
  //   message_id: data.record_id,
  //   is_bad: data.is_bad ? BOOL_NUMBER.NO : BOOL_NUMBER.YES,
  //   is_like: data.is_bad ? undefined : BOOL_NUMBER.NO
  // })
  //   .then((res) => {
  //     if (res.code === 200) {
  //       useModal({
  //         message: data.is_bad ? t('dislikeCancelled') : t('dislikeSuccessful')
  //       })
  //       if (!data.is_bad) {
  //         data.is_like = BOOL_NUMBER.NO
  //       }
  //       data.is_bad = data.is_bad ? BOOL_NUMBER.NO : BOOL_NUMBER.YES
  //     }
  //   })
  //   .catch((err) => {
  //     console.warn(err)
  //   })
  //   .finally(() => {})
}
</script>

<template>
  <div class="ai-comment">
    <div
      v-on-long-press.prevent="() => onLongPressCallbackDirective(props.record, index)"
      class="chat-record"
    >
      <div
        class="name"
        v-if="props.record?.revert_content?.dialogue?.name"
      >
        <div class="text">{{ props.record.revert_content.dialogue.name }}</div>
      </div>
      <ChatLoading v-if="props.record.userTTS" />
      <van-popover
        v-model:show="showPopover[index]"
        teleport="#app"
        trigger="manual"
        :placement="dividePlacement(props.record.type)"
      >
        <div class="flex-center-center pb-14 pt-14 pl-14 pr-14 cg-44">
          <div
            class="flex-center-center flex-column fsize-12 rg-8"
            @click="likeAgentText(props.record)"
          >
            <SvgIcon
              :icon-class="props.record.is_like ? 'like-hand-active' : 'like-hand'"
              class="fsize-20"
            />{{ t('like') }}
          </div>
          <div
            class="flex-center-center flex-column fsize-12 rg-8"
            @click="unlikeAgentText()"
          >
            <SvgIcon
              :icon-class="props.record.is_bad ? 'unlike-hand-active' : 'unlike-hand'"
              class="fsize-20"
            />{{ t('report') }}
          </div>
          <div
            class="flex-center-center flex-column fsize-12 rg-8"
            @click="copyText(props.record.content)"
          >
            <SvgIcon
              icon-class="copy-option"
              class="fsize-20"
            />{{ t('copy') }}
          </div>
        </div>

        <template #reference>
          <div
            class="no-selected"
            v-html="markdownToHtml(replaceBracketsWithAsterisks(props.record.content))"
          ></div>
        </template>
      </van-popover>
    </div>
    <Teleport to="body">
      <FeedbackDrawer v-model="feedbackShow" />
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.ai-comment {
  display: flex;
  flex-direction: column;
  column-gap: 8px;
  align-items: flex-start;
  align-self: start;
  justify-content: center;
  width: fit-content;
  min-width: 48px;
  max-width: 95%;
  height: fit-content;
  color: #2d2d2d;
  background: rgba(255, 255, 255, 95%);
  backdrop-filter: blur(32px);
  border-radius: 12px;

  .chat-record {
    flex: 1;
    width: 100%;
    padding: 12px;
    line-height: normal;
    overflow-wrap: break-word;

    :deep(.van-popover__content) {
      border-radius: 12px;
    }

    :deep(img) {
      display: block;
      width: 100%;
      min-width: 300px;
      min-height: 200px;
      border-radius: 12px;
    }
  }

  .name {
    position: absolute;
    top: -9px;
    left: 0;
    z-index: 1;
    display: flex;
    column-gap: 2px;
    align-items: center;
    justify-content: center;
    height: 18px;
    padding: 0 10px;
    background: linear-gradient(270deg, #4c484b 0%, #5d5e61 100%);
    border-radius: 12px 12px 12px 0;

    .text {
      font-size: 12px;
      line-height: 18px;
      color: #fff;
      white-space: nowrap;
    }
  }
}
</style>
