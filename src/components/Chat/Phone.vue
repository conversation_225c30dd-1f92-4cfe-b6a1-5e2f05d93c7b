<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'

const props = withDefaults(
  defineProps<{
    record: RecordType
    index: number
  }>(),
  {}
)
</script>

<template>
  <div
    class="line"
    style="height: 1px"
  ></div>
  <div class="content">{{ props.record.content }}</div>
  <div
    class="line"
    style="height: 1px"
  ></div>
</template>

<style scoped lang="scss"></style>
