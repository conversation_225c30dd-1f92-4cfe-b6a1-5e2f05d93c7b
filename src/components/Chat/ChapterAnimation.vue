<script setup lang="ts">
import { ChapterObj } from '@/api/chat/types.ts'
const show = defineModel<boolean>()
const props = withDefaults(
  defineProps<{
    chapter: ChapterObj
  }>(),
  {}
)
const handleClose = () => {
  // 触发关闭事件
  window.dispatchEvent(new Event('chapter-animation-close'))
  show.value = false
}
</script>

<template>
  <div
    class="chapter-animation"
    @click="handleClose"
  >
    <div class="content">
      <img
        src="@/assets/images/chat/chapter-bg.png"
        alt=""
        class="bg"
      />
      <div class="text-content">
        <div class="title">
          <img
            src="@/assets/images/chat/chapter-star-left.png"
            alt=""
          />
          {{ props.chapter.content }}
          <img
            src="@/assets/images/chat/chapter-star-right.png"
            alt=""
          />
        </div>
        <img
          src="@/assets/images/chat/chapter-divide.png"
          alt=""
          class="w-full"
        />
        <div class="tips">{{ props.chapter.tips }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.chapter-animation {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  width: 100vw;
  height: 100vh;
  backdrop-filter: blur(4px);

  .content {
    position: relative;
    width: 100%;
    animation: move 800ms ease-in-out;

    .bg {
      width: 100%;
    }

    .text-content {
      position: absolute;
      top: 50%;
      left: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-size: 20px;
      text-shadow: #fffbdb 1px 0 10px;
      transform: translate(-50%, -50%);

      .title {
        font-family: 'AlibabaPuHuiTi Heavy', serif;
      }

      .tips {
        font-size: 10px;
      }
    }
  }
}

@keyframes move {
  0% {
    opacity: 0;
    transform: translateX(-100vw);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
