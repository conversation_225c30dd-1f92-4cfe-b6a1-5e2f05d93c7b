<script setup lang="ts"></script>

<template>
  <div class="container"></div>
</template>

<style scoped lang="scss">
.container {
  --uib-size: 55px;
  --uib-color: #ebdfac;
  --uib-speed: 1.5s;
  --uib-dot-size: calc(var(--uib-size) * 0.4);

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--uib-size);
  height: var(--uib-size);
  animation: rotate calc(var(--uib-speed) * 1.667) infinite linear;
}

.container::before,
.container::after {
  position: absolute;
  flex-shrink: 0;
  width: var(--uib-dot-size);
  height: var(--uib-dot-size);
  content: '';
  background-color: var(--uib-color);
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.container::before {
  animation: orbit var(--uib-speed) linear infinite;
}

.container::after {
  animation: orbit var(--uib-speed) linear calc(var(--uib-speed) / -2) infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes orbit {
  0% {
    opacity: 0.65;
    transform: translateX(calc(var(--uib-size) * 0.251)) scale(0.7368);
  }

  5% {
    opacity: 0.58;
    transform: translateX(calc(var(--uib-size) * 0.235)) scale(0.6842);
  }

  10% {
    opacity: 0.51;
    transform: translateX(calc(var(--uib-size) * 0.182)) scale(0.6315);
  }

  15% {
    opacity: 0.44;
    transform: translateX(calc(var(--uib-size) * 0.129)) scale(0.5789);
  }

  20% {
    opacity: 0.37;
    transform: translateX(calc(var(--uib-size) * 0.076)) scale(0.5263);
  }

  25% {
    opacity: 0.3;
    transform: translateX(0) scale(0.4736);
  }

  30% {
    opacity: 0.37;
    transform: translateX(calc(var(--uib-size) * -0.076)) scale(0.5263);
  }

  35% {
    opacity: 0.44;
    transform: translateX(calc(var(--uib-size) * -0.129)) scale(0.5789);
  }

  40% {
    opacity: 0.51;
    transform: translateX(calc(var(--uib-size) * -0.182)) scale(0.6315);
  }

  45% {
    opacity: 0.58;
    transform: translateX(calc(var(--uib-size) * -0.235)) scale(0.6842);
  }

  50% {
    opacity: 0.65;
    transform: translateX(calc(var(--uib-size) * -0.25)) scale(0.7368);
  }

  55% {
    opacity: 0.72;
    transform: translateX(calc(var(--uib-size) * -0.235)) scale(0.7894);
  }

  60% {
    opacity: 0.79;
    transform: translateX(calc(var(--uib-size) * -0.182)) scale(0.8421);
  }

  65% {
    opacity: 0.86;
    transform: translateX(calc(var(--uib-size) * -0.129)) scale(0.8947);
  }

  70% {
    opacity: 0.93;
    transform: translateX(calc(var(--uib-size) * -0.076)) scale(0.9473);
  }

  75% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }

  80% {
    opacity: 0.93;
    transform: translateX(calc(var(--uib-size) * 0.076)) scale(0.9473);
  }

  85% {
    opacity: 0.86;
    transform: translateX(calc(var(--uib-size) * 0.129)) scale(0.8947);
  }

  90% {
    opacity: 0.79;
    transform: translateX(calc(var(--uib-size) * 0.182)) scale(0.8421);
  }

  95% {
    opacity: 0.72;
    transform: translateX(calc(var(--uib-size) * 0.235)) scale(0.7894);
  }

  100% {
    opacity: 0.65;
    transform: translateX(calc(var(--uib-size) * 0.25)) scale(0.7368);
  }
}
</style>
