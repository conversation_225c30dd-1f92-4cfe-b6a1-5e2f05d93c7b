<script setup lang="ts">
const { t } = useI18n()
const showBottom = defineModel<boolean>()
</script>

<template>
  <van-popup
    round
    teleport="#app"
    v-model:show="showBottom"
    position="bottom"
    :style="{ maxHeight: '85%' }"
  >
    <div class="popup">
      <div class="flex-between-center mb-16">
        <div>
          <div class="fsize-18">{{ t('story') }}</div>
        </div>
        <SvgIcon
          @click="showBottom = false"
          icon-class="close"
          class="fsize-24 mr-16"
        />
      </div>
      <div class="story">
        <Story />
      </div>
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
.popup {
  display: flex;
  flex-direction: column;
  max-height: 85vh;
  padding: 20px 12px 16px;
  overflow: hidden;
  border-radius: 24px 24px 0 0;

  .story {
    height: 100%;
    overflow: auto;
  }
}
</style>
