<script setup lang="ts">
const { t } = useI18n()
interface IEmits {
  (e: 'search', val: string): void
  (e: 'clear'): void
  (e: 'cancel'): void
  (e: 'update:modelValue', val: string): void
}
interface IProps {
  modelValue: string
  placeholder?: string
}
const props = withDefaults(defineProps<IProps>(), {
  modelValue: '',
  placeholder: '搜索角色或创作者'
})
const emit = defineEmits<IEmits>()
const onSearch = (val: string) => emit('search', val)
const onClickButton = () => emit('search', props.modelValue)
const onClear = () => {
  emit('clear')
}
// const clickSearchHandel = () => {
//   emit('search', props.modelValue)
// }
</script>

<template>
  <div class="search-container">
    <form
      action="javascript:;"
      class="flex-start-center"
    >
      <SvgIcon
        icon-class="back"
        class="fsize-24 mr-12"
        @click="emit('cancel')"
      />
      <van-search
        :model-value="props.modelValue"
        show-action
        :clearable="false"
        :placeholder="props.placeholder"
        @update:model-value="emit('update:modelValue', $event)"
        @search="onSearch"
      >
        <template #action>
          <div
            class="search-cancel"
            @click="onClickButton"
          >
            {{ t('search') }}
          </div>
        </template>
        <template #left-icon>
          <div class="search-icon-left flex-center-center">
            <SvgIcon
              icon-class="search-icon"
              class="fsize-16"
            />
          </div>
        </template>
        <template #right-icon>
          <van-icon
            v-show="props.modelValue"
            name="clear"
            color="#474747"
            @click="onClear"
            class="search-icon-right"
          />
        </template>
      </van-search>
    </form>
  </div>
</template>

<style scoped lang="scss">
.search-container {
  .search-icon-left {
    width: 28px;
    height: 16px;
    padding-right: 12px;
    margin-right: 4px;
    border-right: 1px solid rgba(255, 255, 255, 30%);
  }

  :deep(.van-search) {
    flex: 1;
    height: 40px;
    padding: 0 !important;
    background-color: transparent;

    .van-search__content {
      display: flex;
      align-items: center;
      height: 100%;
      margin-right: 16px;
      background: #262626;
      border: 1px solid rgba(255, 255, 255, 10%);
      border-radius: 52px;
    }

    .van-search__action {
      padding-left: 0;
      color: rgba(255, 255, 255, 50%);
    }

    .van-icon {
      .van-icon-clear::before {
        background-color: #fff;
      }
    }

    .van-field__right-icon {
      line-height: 100%;
    }
  }

  .search-icon {
    width: 16px;
    height: 16px;
    // margin-left: 2px;
    background: url('@/assets/images/search/search-icon.png') no-repeat;
    background-size: contain;
  }

  .search-icon-right {
    line-height: 100%;
  }
}
</style>
