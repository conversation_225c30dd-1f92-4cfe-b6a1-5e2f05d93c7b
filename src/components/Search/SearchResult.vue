<script setup lang="ts">
import { queryAiList } from '@/api/home'
import { getCreaterList, setFollow } from '@/api/search'
import { IAiListParams, IAiListItem } from '@/api/home/<USER>'
import { ICreaterListItem, ICreaterParams } from '@/api/search/types'
import { eventReport, ForwardAddressEnum, EventTypeEnum } from '@/api/eventReport'
import router from '@/router'
import useUserStore from '@/stores/modules/user'
import useAppStore from '@/stores/modules/app'
import { useModal } from '@/hooks/useModal'
import useSearchStore from '@/stores/modules/search'
interface IProps {
  searchValue: string
}
const props = withDefaults(defineProps<IProps>(), {
  searchValue: ''
})
const { t } = useI18n()
const appStore = useAppStore()
const userStore = useUserStore()
const searchStore = useSearchStore()
const active = ref<number>(0)
active.value = 0
const searchResultAIList = ref<IAiListItem[]>([])
const searchResultCreaterList = ref<ICreaterListItem[]>([])
const loading = ref(false)
const finished = ref(false)
const aiListParams = ref<IAiListParams>({
  page: 1,
  limit: 10
})
const createrListParams = ref<ICreaterParams>({
  page: 1,
  limit: 10
})

function tabClickHandle() {
  resetParams()
}

function navToAiMsgPage(id: number, type: string) {
  if (!userStore.isLogin) return (appStore.showLogin = true)
  if (type === 'role') {
    sessionStorage.setItem(
      'searchValue',
      JSON.stringify({
        searchValue: props.searchValue
      })
    )
    eventReport({
      event_type: EventTypeEnum.OPEN_AI_PROFILE,
      front_address: ForwardAddressEnum.SEARCH_PAGE,
      ai_id: id
    }).catch((err) => {
      console.warn(err)
    })
    router.push(`/agentHomePage/${id}`)
  } else {
    router.push({ name: 'others', query: { id } })
  }
}

function resetParams() {
  aiListParams.value.page = 1
  createrListParams.value.page = 1
  searchResultAIList.value = []
  searchResultCreaterList.value = []
  loading.value = false
  finished.value = false
}

function getAiListHandle() {
  if (searchStore.loading) return
  searchStore.loading = true
  const params = { ...aiListParams.value, keyword: props.searchValue }
  // const params = { ...aiListParams.value }
  queryAiList(params)
    .then((res) => {
      if (res.code === 200) {
        searchResultAIList.value.push(...res.data.list)
        // 加载状态结束
        aiListParams.value.page++
      }
      if (searchResultAIList.value.length >= res.data.count) {
        finished.value = true
      }
    })
    .finally(() => {
      loading.value = false
      searchStore.loading = false
    })
}

function getCreaterListHandle() {
  if (searchStore.loading) return
  searchStore.loading = true
  const params = { ...createrListParams.value, keyword: props.searchValue }
  // const params = { ...createrListParams.value }
  getCreaterList(params)
    .then((res) => {
      if (res.code === 200) {
        searchResultCreaterList.value.push(...res.data.list)
        // 加载状态结束
        createrListParams.value.page++
      }
      if (searchResultCreaterList.value.length >= res.data.count) {
        finished.value = true
      }
    })
    .finally(() => {
      loading.value = false
      searchStore.loading = false
    })
}

const onLoad = () => {
  // 异步更新数据
  switch (active.value) {
    case 0:
      getAiListHandle()
      break
    case 1:
      getCreaterListHandle()
      break
    default:
      break
  }
}

const isShowEmptyIcon = computed(() => {
  return (active.value === 0 && searchResultAIList.value.length === 0) || (active.value === 1 && searchResultCreaterList.value.length === 0)
})

function followHandle(item: ICreaterListItem) {
  if (!userStore.isLogin) return (appStore.showLogin = true)
  setFollow(item.client_id).then((res) => {
    if (res.code !== 200) return
    // item.is_follow = item.is_follow === 0 ? 1 : 0
    if (item.is_follow === 0) {
      item.is_follow = 1
      useModal({
        message: t('focusonSuccess'),
        duration: 1500
      })
    } else {
      useModal({
        message: t('unfollowed'),
        duration: 1500
      })
      item.is_follow = 0
    }
    userStore.getUserInfo()
  })
}
function chatHandle(item: IAiListItem) {
  if (!userStore.isLogin) return (appStore.showLogin = true)
  sessionStorage.setItem(
    'searchValue',
    JSON.stringify({
      searchValue: props.searchValue
    })
  )
  eventReport({
    event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
    front_address: ForwardAddressEnum.SEARCH_PAGE,
    ai_id: item.id
  }).catch((err) => {
    console.warn(err)
  })
  router.push(`/chat/${item.id}`)
}

defineExpose({
  searchResultAIList,
  searchResultCreaterList,
  active,
  getAiListHandle,
  getCreaterListHandle,
  resetParams
})
</script>

<template>
  <div class="search-result-container">
    <van-tabs
      v-model:active="active"
      line-width="50%"
      title-active-color="#FFF"
      title-inactive-color="rgba(255,255,255,0.5)"
      background="#181818"
      @change="tabClickHandle"
    >
      <van-tab
        :title="t('characterOption')"
        title-class="tab1"
      >
        <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="''"
          @load="onLoad"
          offset="400"
        >
          <template #loading>
            <img
              class="loading-icon"
              src="../../assets/images/index/loading-icon.png"
              alt="loading"
            />
          </template>
          <div class="search-result-list">
            <div
              class="search-result-item flex-between-center"
              v-for="item in searchResultAIList"
              :key="item.id"
            >
              <div
                class="search-result-img"
                @click="navToAiMsgPage(item.id, 'role')"
              >
                <van-image
                  :src="item.avatar_url"
                  alt="avatar"
                />
              </div>
              <div class="search-result-info">
                <div class="search-result-info__title">
                  <div v-if="String(item.name).includes(props.searchValue)">
                    <van-highlight
                      :keywords="props.searchValue"
                      :source-string="item.name"
                    />
                  </div>

                  <div v-else>{{ item.name }}</div>
                </div>
                <div class="search-result-info__desc flex-start-center">
                  <div
                    class="flex-start-center"
                    v-for="(role, index) in item.tag_list.slice(0, 3)"
                    :key="role.id"
                  >
                    <van-highlight
                      :keywords="props.searchValue"
                      :source-string="role.name"
                    />
                    <div
                      class="tag-item-line"
                      v-if="index < item.tag_list.length - 1 && index < 2"
                    ></div>
                  </div>
                </div>
                <div class="info-popularity flex-start-center">
                  <div class="view flex-between-center">
                    <SvgIcon
                      icon-class="chat-times"
                      class="fsize-14 mr-2"
                    />
                    <div class="view-num">{{ item.chat_times }}</div>
                  </div>
                  <div class="love flex-between-center">
                    <SvgIcon
                      :icon-class="item.like_status === 1 ? 'love-active' : 'love'"
                      class="fsize-14 mr-2"
                    />
                    <div class="love-num">{{ item.follow }}</div>
                  </div>
                </div>
              </div>
              <div class="search-result-btn">
                <div
                  @click="chatHandle(item)"
                  class="chat-btn flex-center-center"
                >
                  {{ t('chatNow') }}
                </div>
              </div>
            </div>
          </div>
        </van-list>
        <Feedback v-if="searchResultAIList.length === 0" />
      </van-tab>

      <van-tab
        :title="t('creatorSelection')"
        title-class="tab2"
        v-if="false"
      >
        <!-- :finished-text="searchResultCreaterList.length === 0 ? '' : '没有更多了' -->
        <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="''"
          @load="onLoad"
          class="search-list"
        >
          <template #loading>
            <img
              class="loading-icon"
              src="../../assets/images/index/loading-icon.png"
              alt="loading"
            />
          </template>
          <div
            class="search-result-list"
            v-for="item in searchResultCreaterList"
            :key="item.client_id"
          >
            <div class="search-result-item flex-between-center">
              <div
                class="search-result-img"
                @click="navToAiMsgPage(item.client_id, 'creater')"
              >
                <van-image
                  :src="item.avatar_url"
                  alt="avatar"
                />
              </div>
              <div class="search-result-info">
                <div class="search-result-info__title mb-8">
                  <van-highlight
                    :keywords="props.searchValue"
                    :source-string="item.name"
                  />
                </div>
                <div class="info-popularity flex-start-center">
                  <div class="view flex-between-center">
                    <div class="view-num">
                      <span>{{ t('fansAmount').replace(/xxx/g, String(item.fans_count)) }}</span>
                    </div>
                  </div>
                  <div class="love">
                    <span>{{ t('rolesAmount').replace(/xxx/g, String(item.show_ai_count)) }}</span>
                  </div>
                </div>
              </div>
              <div
                class="search-result-btn"
                v-if="item.is_me === 0"
              >
                <div
                  @click="followHandle(item)"
                  class="chat-btn flex-center-center"
                  :class="item.is_follow === 0 ? '' : 'follow-btn'"
                >
                  {{ item.is_follow === 0 ? t('followOption') : t('followedNotice') }}
                </div>
              </div>
            </div>
          </div>
        </van-list>
        <Feedback v-if="searchResultCreaterList.length === 0" />
      </van-tab>
    </van-tabs>
    <div
      class="search-result-nodata"
      v-if="isShowEmptyIcon && !loading"
    >
      <EmptyIcon
        :text="t('emptySpaceNotice') + t('tryUsingDifferentKeyword')"
        url="search"
        :width="196"
        :height="114"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
$mainColor: #ff35f2;

:deep(.van-tabs__content) {
  height: calc(100vh - 52px) !important;
  overflow-y: scroll;
}

:deep(.van-image__error) {
  border-radius: 74px;
}

:deep(.van-image__loading) {
  border-radius: 74px;
}

:deep(.van-tabs) {
  margin-bottom: 8px;

  .van-tabs__wrap {
    display: none;
  }

  .van-tabs__line {
    width: 200% !important;
    background: linear-gradient(90deg, $mainColor 0%, #8459ff 100%);

    --van-tabs-bottom-bar-height: 2px;
  }

  .van-tab--line.tab1 {
    padding-right: 8px !important;
  }

  .van-tab--line.tab2 {
    padding-left: 8px !important;
  }
}

.chat-btn {
  min-width: 74px;
  padding: 9px 12px;
  font-size: 12px;
  line-height: 12px;
  color: $livCoTextColor;
  background: $livCoThemeColor;
  border-radius: 21px;
}

.follow-btn {
  color: #fff;
  background: $livCoTextColor;
}

.search-result-container {
  .search-result-item {
    padding: 14px 0;
  }

  .search-result-img {
    width: 48px;
    height: 48px;

    :deep(.van-image) {
      width: 100%;
      height: 100%;

      img {
        border-radius: 74px;
      }
    }
  }

  .search-result-info {
    flex: 1;
    margin: 0 16px 0 12px;

    .search-result-info__title {
      margin-bottom: 4px;
      font-weight: 600;
    }

    .search-result-info__desc {
      min-width: 150px;
      max-width: 190px;
      margin-bottom: 8px;
      overflow: hidden;
      font-size: 12px;
      color: rgba(255, 255, 255, 80%);
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .tag-item-line {
      width: 2px;
      height: 12px;
      margin: 0 6px;
      background-color: rgba(255, 255, 255, 80%);
    }
  }

  .info-popularity {
    font-size: 12px;
    color: rgba(255, 255, 255, 50%);

    .view-num {
      margin-right: 8px;
      vertical-align: middle;
    }

    .love {
      margin-left: 6px;
    }
  }

  .search-result-nodata {
    position: absolute;
    top: 40%;
    left: 50%;
    text-align: center;
    transform: translate(-50%, -50%);

    img {
      width: 196px;
      height: 140px;
    }

    .nodata-text {
      margin-top: -24px;
      font-size: 13px;
      line-height: 20px;
      color: #4d4d4d;
    }
  }

  .mb-8 {
    margin-bottom: 8px !important;
  }

  .loading-icon {
    width: 24px;
    height: 24px;
    animation: loading 1s linear infinite;
  }

  @keyframes loading {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .no-result {
    position: fixed;
    bottom: 0;
    left: 50%;
    padding-bottom: 36px;
    text-align: center;
    transform: translateX(-50%);

    span {
      font-size: 14px;
      color: rgba(255, 255, 255, 50%);
    }

    .link {
      margin-left: 4px;
      color: #ff35f2;
    }
  }
}
</style>
