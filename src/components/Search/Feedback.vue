<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    positionWay?: string
  }>(),
  {
    positionWay: 'fixed'
  }
)
const { t } = useI18n()
</script>
<template>
  <div
    class="feedback-container"
    :style="{ position: props.positionWay === 'fixed' ? 'fixed' : 'absolute' }"
  >
    <span>{{ t('searchWantTips') }}</span>
    <a
      href="https://www.facebook.com/profile.php?id=61570151682130"
      target="_blank"
      ><span class="link">{{ t('clickFeedback') }}</span></a
    >
  </div>
</template>

<style lang="scss" scoped>
.feedback-container {
  position: fixed;
  bottom: 0;
  left: 50%;
  width: 100%;
  padding: 0 24px;
  padding-bottom: 36px;
  text-align: center;
  transform: translateX(-50%);

  span {
    font-size: 12px;
    color: rgba(255, 255, 255, 50%);
  }

  .link {
    margin-left: 4px;
    color: $livCoThemeColor;
  }
}
</style>
