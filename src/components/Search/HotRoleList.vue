<script setup lang="ts">
import { getHotSearch } from '@/api/search'
import { IHotSearchItem } from '@/api/search/types'
import useAppStore from '@/stores/modules/app'
import useUserStore from '@/stores/modules/user'
import { eventReport, ForwardAddressEnum, EventTypeEnum } from '@/api/eventReport'

const userStore = useUserStore()
const appStore = useAppStore()
const router = useRouter()
const hotRoleList = ref<IHotSearchItem[]>([])
const hotloading = ref(false)
const hotfinished = ref(false)
const { t } = useI18n()
function onLoad() {
  getHotSearch().then((res) => {
    if (res.code !== 200) return
    if (!res.data.list) return (hotfinished.value = true)
    hotRoleList.value.push(...res.data.list)
    hotfinished.value = false
    if (hotRoleList.value.length >= res.data.count) {
      hotfinished.value = true
    }
  })
}
function navToChat(ai_id: number) {
  if (!userStore.isLogin) return (appStore.showLogin = true)
  eventReport({
    event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
    front_address: ForwardAddressEnum.SEARCH_PAGE,
    ai_id
  }).catch((err) => {
    console.warn(err)
  })
  router.push(`/chat/${ai_id}`)
}
</script>

<template>
  <div class="hot-role-container">
    <div class="hot-role-title">{{ t('popularRoles') }}</div>
    <div class="hot-role-list-wrap">
      <van-list
        v-model:loading="hotloading"
        :finished="hotfinished"
        finished-text=""
        @load="onLoad"
        class="search-list"
      >
        <template #loading>
          <img
            class="loading-icon"
            src="../../assets/images/index/loading-icon.png"
            alt="loading"
          />
        </template>
        <div
          class="hot-role-item flex-between-center"
          v-for="(item, index) in hotRoleList"
          :key="item.ai_info.id"
          @click="navToChat(item.ai_info.id)"
        >
          <van-image
            class="hot-role-img"
            :src="item.ai_info.avatar_url"
            alt="hot-role-img"
            fit="cover"
            position="top"
          />
          <div class="hot-role-content">
            <div class="hot-role-content__title">{{ item.ai_info.name }}</div>
            <div class="hot-role-content__text multi-ellipsis">{{ item.ai_info.synopsis }}</div>
            <div class="hot-role-content__info flex-between-center">
              <div class="info-author"></div>
              <div class="info-popularity flex-between-center">
                <div class="view flex-between-center">
                  <SvgIcon
                    icon-class="chat-times"
                    class="fsize-14 ml-6"
                  />
                  <div class="ml-2">{{ item.ai_info.chat_times }}</div>
                </div>
                <div class="love flex-between-center">
                  <SvgIcon
                    :icon-class="item.ai_info.like_status === 1 ? 'like-mini-active' : 'like-mini-normal'"
                    class="fsize-14 ml-6"
                  />
                  <div class="ml-2">{{ item.ai_info.follow }}</div>
                </div>
              </div>
            </div>
          </div>
          <div :class="['hot-role-subscript flex-center-center', index < 3 ? `active${index + 1} br0` : '']">
            {{ index < 3 ? '' : index + 1 }}
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.hot-role-container {
  .hot-role-title {
    margin-bottom: 16px;
  }

  .info-author {
    display: flex;
    align-items: center;
    line-height: 14px;
    vertical-align: middle;
  }

  .hot-role-item {
    position: relative;
    padding: 16px 12px;
    margin-bottom: 12px;
    background: #232222;
    border-radius: 16px;

    :deep(.van-image) {
      img {
        width: 68px;
        height: 68px;
        margin-right: 12px;
        border-radius: 12px;
      }
    }
  }

  .hot-role-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 68px;
  }

  .hot-role-content__title {
    font-weight: 500;
    line-height: 16px;
  }

  .hot-role-content__text {
    flex: 1;
    margin: 4px 0 6px;
    font-size: 12px;
    line-height: 14px;
    color: rgba(255, 255, 255, 80%);
  }

  .hot-role-content__info {
    font-size: 12px;
    color: rgba(255, 255, 255, 50%);
  }

  .multi-ellipsis {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }

  .hot-role-subscript {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 24px;
    font-size: 14px;
    font-weight: 900;
    color: #cbcbcb;
    background: #363535;
    border-radius: 12px 0;

    &.active1 {
      background: url('~/images/search/hot-icon-one.png') center / contain no-repeat;
    }

    &.active2 {
      background: url('~/images/search/hot-icon-two.png') center / contain no-repeat;
    }

    &.active3 {
      background: url('~/images/search/hot-icon-three.png') center / contain no-repeat;
    }

    &.br0 {
      border-radius: 0;
    }
  }
}

.loading-icon {
  width: 24px;
  height: 24px;
  animation: loading 1s linear infinite;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
