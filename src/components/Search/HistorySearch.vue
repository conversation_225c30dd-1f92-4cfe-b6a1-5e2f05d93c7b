<script setup lang="ts">
import { getSearchRecord, cleanSearchRecord } from '@/api/search'
import { ISearchRecordItem } from '@/api/search/types'

defineExpose({
  getSearchRecordHandle
})
const historyList = ref<ISearchRecordItem[]>([])
function onClearHistory() {
  cleanSearchRecord().then((res) => {
    if (res.code === 200) {
      getSearchRecordHandle()
    }
  })
}
function onClickHistoryItem(item: ISearchRecordItem) {
  emit('tab-click', item)
}
const emit = defineEmits<{
  (e: 'tab-click', val: ISearchRecordItem): void
}>()
const { t } = useI18n()
function getSearchRecordHandle() {
  getSearchRecord().then((res) => {
    if (res.code === 200) {
      historyList.value = res.data.list
    }
  })
}
getSearchRecordHandle()
</script>
<template>
  <div
    class="histery-search-container"
    v-if="historyList.length && historyList.length > 0"
  >
    <div class="histery-search__wrap flex-between-center">
      <div class="histery-search__title">{{ t('searchHistory') }}</div>
      <img
        class="clear-icon"
        src="../../assets/images/search/delete-icon.png"
        alt="delete-icon"
        @click="onClearHistory"
      />
    </div>
    <div class="histery-search-list flex-start-center">
      <div
        class="histery-search-item ellipsis"
        v-for="item in historyList"
        :key="item.id"
        @click="onClickHistoryItem(item)"
      >
        <span class="record-text">{{ item.content }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.histery-search-container {
  .histery-search__wrap {
    margin-bottom: 14px;
  }

  .clear-icon {
    width: 20px;
    height: 20px;
    vertical-align: middle;
  }

  .histery-search-list {
    flex-wrap: wrap;
  }

  .histery-search-item {
    height: 38px;
    padding: 10px 16px;
    margin: 0 12px 12px 0;
    font-size: 14px;
    background: #232222;
    border-radius: 32px;
  }

  .ellipsis {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
