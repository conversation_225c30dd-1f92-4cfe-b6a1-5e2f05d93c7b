<script setup lang="ts">
import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    skinItem: DressType
  }>(),
  {}
)
</script>

<template>
  <div class="container">
    <div
      class="skin"
      :class="{ 'skin-active': props.skinItem.isSelected, 'skin-had': props.skinItem.is_buy }"
      :style="{ background: `url('${props.skinItem.pic_url}') no-repeat center / cover` }"
    >
      <div
        class="flag-3d"
        v-if="props.skinItem.type === SkinTypeEnum.ThreeD"
      >
        <img
          src="@/assets/images/agentChat/3d.png"
          alt=""
        />
      </div>
    </div>
    <div
      style="opacity: 1"
      class="overlay"
      v-if="!props.skinItem.is_buy"
    >
      <div
        class="lock"
        style="opacity: 1"
      ></div>
      <div
        class="unlock-text"
        style="opacity: 1"
      >
        {{ t('notOwned') }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '../../assets/styles/mixin';

.container {
  position: relative;
}

.skin {
  width: 90px;
  height: 160px;
  border: 2px solid #666;
  border-radius: 12px;
  opacity: 0.6;

  .flag-3d {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    padding: 3px 8px 1px;
    font-size: 12px;
    background: $livCoThemeColor;
    border-radius: 0 10px 0 8px;

    img {
      width: 16px;
      height: 12px;
    }
  }
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgba(24, 24, 24, 70%);
  border-radius: 10px;
  opacity: 1;

  .lock {
    width: 24px;
    height: 24px;
    margin-bottom: 4px;
    background: url('@/assets/images/grey-lock.png') center / contain no-repeat;
  }

  .unlock-text {
    font-size: 11px;
    color: #8b8889;
  }
}

.skin-active {
  border: 2px solid $livCoThemeColor;
}

.skin-had {
  background-color: rgba(0, 0, 0, 50%); /* 背景颜色半透明 */
  opacity: 1;
}
</style>
