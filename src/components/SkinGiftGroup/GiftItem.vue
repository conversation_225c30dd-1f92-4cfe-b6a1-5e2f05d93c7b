<script setup lang="ts">
import { GiftListResType } from '@/api/agentChat/types.ts'
import { ICurrencyTypeEnum } from '@/enums'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    gift: GiftListResType
  }>(),
  {}
)
</script>

<template>
  <div class="gift">
    <div class="gift-img">
      <img
        class="img"
        :src="props.gift.icon"
        alt=""
      />
    </div>
    <div class="gift-name">{{ props.gift.name }}</div>
    <div class="gift-intimacy">
      <SvgIcon
        icon-class="intimacy-heart-store"
        class="fsize-14"
      />+{{ props.gift.intimacy }}
    </div>
    <div
      v-if="props.gift.price"
      class="gift-price"
    >
      <SvgIcon :icon-class="props.gift.currency_type === ICurrencyTypeEnum.Coin ? 'coin' : 'crystal'" />

      {{ props.gift.price }}
    </div>
    <div
      v-else
      class="gift-price"
    >
      {{ t('free') }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.gift {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 143px;
  padding-top: 8px;
  background: #1a1a1a;
  border-radius: 12px;

  .gift-img {
    width: 62px;
    height: 62px;

    .img {
      width: 100%;
    }
  }

  .gift-name {
    margin-top: 4px;
    font-size: 11px;
    font-weight: 400;
  }

  .gift-intimacy {
    display: flex;
    column-gap: 2px;
    align-items: center;
    margin-top: 4px;
    font-size: 11px;
    font-weight: 400;
    color: rgba(255, 255, 255, 50%);
  }

  .gift-price {
    display: flex;
    column-gap: 4px;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 8px 0;
    margin-top: 8px;
    font-size: 14px;
    font-weight: bold;
    background: #2d2d2d;
    border-radius: 0 0 12px 12px;
  }
}
</style>
