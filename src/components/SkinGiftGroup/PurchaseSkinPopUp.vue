<script setup lang="ts">
import { DressType } from '@/api/propertyStore/types.ts'
import { getAmountID, purchase } from '@/api/purchase'
import { ICurrencyTypeEnum, IPurchaseTypeEnum } from '@/enums'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { useModal } from '@/hooks/useModal.ts'
import { getPictureInfo } from '@/api/agentHomePage'
import useUserStore from '@/stores/modules/user.ts'
import { usePaymentError } from '@/hooks/useCommon.ts'
import { POPUP_TYPE_NAME } from '@/stores/modules/popupMemory.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    skinItem: DressType | undefined
  }>(),
  {}
)
const aiID = inject<Ref<number>>('aiID')
const route = useRoute()
const userStore = useUserStore()
const showBottom = defineModel<boolean>()
const loading = ref(false)
const emits = defineEmits(['getList'])

const submitPurchase = async () => {
  loading.value = true
  try {
    const { data, code, msg } = await getAmountID({
      currency_type: props.skinItem.currency_type,
      product_type: IPurchaseTypeEnum.NormalSkin,
      product_id: props.skinItem.id,
      front_address: route.path,
      ai_id: route.path.toLowerCase().includes('/agentchat') ? aiID.value : undefined
    })
    if (code === ResultEnum.SUCCESS) {
      console.log(data)
      const { code: code1, msg: msg1 } = await purchase({ amount_id: data.amount_id, currency_type: props.skinItem.currency_type })
      if (code1 === ResultEnum.SUCCESS) {
        useModal({
          message: t('purchaseSuccessful')
        })
        showBottom.value = false
        loading.value = false
        await userStore.getUserInfo()
        getPictureInfo({ pic_id: props.skinItem.id }).then((res) => {
          const { code } = res
          if (code === ResultEnum.SUCCESS) {
            emits('getList')
            showBottom.value = false
          }
        })
      } else {
        useModal({
          message: msg1
        })
      }
    } else {
      usePaymentError(code, msg, POPUP_TYPE_NAME.BUY_SKIN, route.path, props.skinItem.id)
    }
  } catch (e) {
    console.warn(e)
  }
  loading.value = false
}
</script>

<template>
  <van-popup
    round
    teleport="#app"
    v-model:show="showBottom"
    position="bottom"
    :style="{ height: '95%' }"
  >
    <div class="popup">
      <div class="flex-between-center mb-16">
        <div>
          <div>Costume Purchase</div>
        </div>
        <div @click="showBottom = false">
          <van-icon
            name="cross"
            size="20"
          />
        </div>
      </div>
      <div class="flex-start-center mb-26">
        <CrystalBalance />
      </div>
      <div class="skin">
        <div
          class="skin-img"
          :style="{ background: `url('${props.skinItem.pic_url}') no-repeat center / cover` }"
        ></div>
        <!--        <div class="flex-center-center skin-name">{{ props.skinItem.name }}</div>-->
      </div>
      <div class="purchase">
        <div class="flex-center-start flex-column ml-24">
          <div class="num flex-start-center cg-2">
            <SvgIcon
              v-if="props.skinItem.value && props.skinItem.currency_type === ICurrencyTypeEnum.Coin"
              icon-class="coin"
              class="fsize-18"
            />
            <img
              v-if="props.skinItem.value && props.skinItem.currency_type === ICurrencyTypeEnum.Crystal"
              width="18"
              src="@/assets/images/crystal.png"
              alt=""
            />{{ props.skinItem.value || t('free') }}
          </div>
        </div>
        <van-button
          size="large"
          class="btn flex-center-center"
          @click="submitPurchase"
          :loading="loading"
        >
          {{ t('confirmButton') }}
        </van-button>
      </div>
      <!--      <PhotoPreviewDialog v-model="previewVisible" />-->
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
@import '../../assets/styles/mixin';

.popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px 16px 16px;

  :deep(.balance) {
    background: #262626;
  }

  .skin {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    row-gap: 24px;
    align-items: center;
    justify-content: center;

    .skin-img {
      position: relative;
      height: 100%;
      aspect-ratio: 0.56;
      border-collapse: collapse;
      border: 3px solid $livCoThemeColor;
      border-radius: 12px;
    }

    .skin-name {
      margin: 0 48px;
      text-align: center;
    }

    .skin-background {
      position: absolute;
      top: 0;
      left: 50%;
      z-index: -1;
      width: 100vw;
      height: 100%;
      background: url('@/assets/images/common/skin-background.webp') center / cover no-repeat;
      transform: translateX(-50%);
    }
  }

  .purchase {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 42px;
    background: #302f2f;
    border-radius: 32px;

    .text {
      color: #8b8889;
    }

    .num {
      font-weight: 600;
    }

    .btn {
      width: 124px;
      height: 54px;
      font-weight: 600;
      color: $livCoTextColor;
      background: $livCoThemeColor;
      border-radius: 40px;
    }
  }
}
</style>
