<script setup lang="ts">
import { IUserGiftListType } from '@/api/propertyStore/types.ts'

const props = withDefaults(
  defineProps<{
    gift: IUserGiftListType
  }>(),
  {}
)
</script>

<template>
  <div class="gift">
    <div class="gift-img">
      <img
        class="img"
        :src="props.gift.icon"
        alt=""
      />
    </div>
    <div class="gift-name">{{ props.gift.goods_name }}</div>
    <div class="gift-intimacy">
      <SvgIcon
        icon-class="intimacy-value-heart"
        class="fsize-14"
      />+{{ props.gift.intimacy }}
    </div>
    <div class="gift-price">
      {{ props.gift.balance_count }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.gift {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 143px;
  padding-top: 16px;
  background: #232323;
  border-radius: 12px;

  .gift-img {
    width: 62px;
    height: 62px;

    .img {
      width: 100%;
    }
  }

  .gift-name {
    margin-top: 4px;
    font-size: 11px;
    font-weight: 400;
  }

  .gift-intimacy {
    display: flex;
    column-gap: 2px;
    align-items: center;
    margin-top: 4px;
    font-size: 11px;
    font-weight: 400;
    color: rgba(255, 255, 255, 50%);
  }

  .gift-price {
    display: flex;
    column-gap: 4px;
    align-items: center;
    align-self: flex-end;
    justify-content: center;
    height: 24px;
    padding: 8px;
    margin-top: 6px;
    font-size: 12px;
    font-weight: bold;
    background: #363434;
    border-radius: 12px 0;
  }
}
</style>
