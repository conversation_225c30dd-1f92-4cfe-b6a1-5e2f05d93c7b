<script setup lang="ts">
import { IGiftListType } from '@/api/agentChat/types.ts'
import { ICurrencyTypeEnum } from '@/enums'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    gift: IGiftListType
  }>(),
  {}
)
</script>

<template>
  <div class="gift">
    <div
      class="gift-num"
      v-if="props.gift.selected"
    >
      {{ props.gift.client_have_count }}
    </div>
    <div class="gift-img">
      <img
        class="img"
        :src="props.gift.icon"
        alt=""
      />
    </div>
    <div
      class="gift-intimacy"
      v-show="!props.gift.selected"
    >
      <SvgIcon
        icon-class="intimacy-heart-store"
        class="fsize-14"
      />+{{ props.gift.intimacy }}
    </div>
    <div
      v-if="props.gift.price"
      class="gift-price"
      :style="!props.gift.selected && { padding: '7px 0' }"
    >
      <SvgIcon :icon-class="props.gift.currency_type === ICurrencyTypeEnum.Coin ? 'coin' : 'crystal'" />

      {{ props.gift.price }}
    </div>
    <div
      v-else
      class="gift-price"
    >
      {{ t('free') }}
    </div>
    <div
      class="send flex-center-center"
      v-show="props.gift.selected"
    >
      {{ t('sendGift') }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.gift {
  position: relative;
  box-sizing: content-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 108px;
  border-radius: 8px;

  .gift-num {
    position: absolute;
    top: 0;
    right: 0;
    padding: 2px 4px;
    font-size: 11px;
    font-weight: 600;
    line-height: 13px;
    color: rgba(255, 255, 255, 80%);
    background: rgba(255, 255, 255, 10%);
    border-radius: 0 6px 0 7px;
  }

  .gift-img {
    width: 54px;
    height: 54px;
    margin-top: 8px;
    margin-bottom: 4px;

    .img {
      width: 100%;
    }
  }

  .gift-name {
    margin-top: 4px;
    font-size: 11px;
    font-weight: 400;
  }

  .gift-intimacy {
    display: flex;
    column-gap: 2px;
    align-items: center;
    padding: 1px 8px 1px 6px;
    font-size: 11px;
    font-weight: 400;
    line-height: 10px;
    background: rgba(38, 38, 38, 100%);
    border-radius: 40px;
  }

  .gift-price {
    display: flex;
    column-gap: 4px;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding-top: 2px;
    margin-bottom: 4px;
    font-size: 12px;
    line-height: 12px;
    color: rgba(255, 255, 255, 50%);
  }

  .send {
    width: 100%;
    padding: 7px 0;
    font-size: 12px;
    font-weight: 600;
    line-height: 12px;
    color: $livCoTextColor;
    background: $livCoThemeColor;
    border-radius: 0 0 8px 8px;
  }
}
</style>
