<script setup lang="ts">
import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'
import { ICurrencyTypeEnum } from '@/enums'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    skinItem: DressType
  }>(),
  {}
)
</script>

<template>
  <div
    class="skin"
    :class="props.skinItem.is_buy && 'skin-had'"
    :style="{ background: `url('${props.skinItem.pic_url}') no-repeat center / cover` }"
  >
    <div
      class="flag-3d"
      v-if="props.skinItem.type === SkinTypeEnum.ThreeD"
    >
      <SvgIcon
        icon-class="3D-flag-skin"
        class="fsize-20"
      />
    </div>
    <div class="overlay">
      <div
        class="flex-center-center cg-2"
        v-if="!props.skinItem.is_buy"
      >
        <SvgIcon
          v-if="props.skinItem.value && props.skinItem.currency_type === ICurrencyTypeEnum.Coin"
          icon-class="coin"
          class="fsize-16"
        />
        <img
          v-if="props.skinItem.value && props.skinItem.currency_type === ICurrencyTypeEnum.Crystal"
          width="16"
          src="@/assets/images/crystal.png"
          alt=""
        />
        <div class="price-text">{{ props.skinItem.value || t('free') }}</div>
      </div>
      <div
        v-else
        class="own-text"
      >
        {{ t('owned') }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '../../assets/styles/mixin';

.skin {
  position: relative;
  width: 90px;
  height: 160px;
  margin: 2px;
  border: 2px solid $livCoThemeColor;
  border-radius: 8px;

  .flag-3d {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    padding: 2px 6px 0;
    font-size: 12px;
    background: $livCoThemeColor;
    border-radius: 0 7px 0 8px;

    svg {
      width: 20px;
      height: 13px;
    }
  }

  .overlay {
    position: absolute;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: 100%;
    padding-bottom: 12px;
    background: linear-gradient(180deg, rgba(24, 24, 24, 0%) 70%, #181818 100%);
    border-radius: 6px;

    .own-text {
      font-size: 12px;
      color: #dcdcdc;
    }

    .price-text {
      font-size: 12px;
      color: #ffebb9;
    }
  }
}

.skin-had {
  border: 2px solid #4d4d4d;
}
</style>
