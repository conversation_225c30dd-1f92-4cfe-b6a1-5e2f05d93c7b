<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    cancel: boolean
  }>(),
  {
    cancel: false
  }
)
</script>

<template>
  <div class="music">
    <div
      v-for="i in 30"
      :key="i"
      class="wave-bar"
      :style="{ background: props.cancel ? '#fff' : '#2f2101' }"
    ></div>
  </div>
</template>

<style scoped lang="scss">
.music {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 50%;
}
</style>
