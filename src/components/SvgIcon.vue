<template>
  <component :is="svgVNode" />
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    iconClass: string
  }>(),
  {}
)
const svgVNode = ref()

const getPath = async () => {
  const { default: svgContent } = await import(`../assets/icons/svg/${props.iconClass}.svg?raw`)
  // 使用 DOMParser 将 SVG 字符串解析为真实 DOM 元素
  const parser = new DOMParser()
  const svgDocument = parser.parseFromString(svgContent, 'image/svg+xml')
  const svgElement = svgDocument.querySelector('svg')
  svgElement.classList.add('icon-style-for-svg')
  // 将 DOM 节点转换为 Vue 虚拟 DOM (VNode)
  svgVNode.value = h(svgElement.tagName, {
    // 将 SVG 的属性（如 width, height, fill 等）保留
    innerHTML: svgElement.innerHTML,
    ...Object.fromEntries([...svgElement.attributes].map((attr) => [attr.name, attr.value]))
  })
}

watch(
  () => props.iconClass,
  (val) => {
    if (val) {
      getPath()
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss">
.icon-style-for-svg {
  position: relative;
  width: 1em;
  height: 1em;
}
</style>
