<script setup lang="ts">
const route = useRoute()
const router = useRouter()

const props = withDefaults(
  defineProps<{
    langTitle?: string
  }>(),
  {}
)

function onBack() {
  if (window.history.state.back) history.back()
  else router.replace('/')
}

const { t } = useI18n()

const title = computed(() => {
  if (!route.meta) return ''
  return route.meta.i18n ? t(route.meta.i18n) : route.meta.title || ''
})
</script>

<template>
  <VanNavBar
    v-if="title"
    :title="title"
    :fixed="true"
    clickable
    left-arrow
    z-index="9999"
    @click-left="onBack"
    :border="false"
    :placeholder="false"
    :safe-area-inset-top="true"
  >
    <template #left>
      <SvgIcon
        icon-class="back"
        class="fsize-24"
      />
    </template>
  </VanNavBar>

  <VanNavBar
    v-else-if="props.langTitle"
    :title="props.langTitle"
    :fixed="true"
    clickable
    left-arrow
    @click-left="onBack"
    :border="false"
    :placeholder="false"
    :safe-area-inset-top="true"
  >
    <template #left>
      <SvgIcon
        icon-class="back"
        class="fsize-24"
      />
    </template>
  </VanNavBar>
</template>
<style></style>
