<script setup lang="ts">
interface Props {
  safeAreaInsetTop?: boolean
  safeAreaInsetBottom?: boolean
}
withDefaults(defineProps<Props>(), {
  safeAreaInsetTop: true,
  safeAreaInsetBottom: true
})
const show = ref(false)
const showPopup = () => {
  show.value = true
}
const closePopup = () => {
  show.value = false
}

defineExpose({
  showPopup,
  closePopup
})
</script>
<template>
  <van-popup
    v-model:show="show"
    :safe-area-inset-top="true"
    :safe-area-inset-bottom="true"
  >
    <slot></slot>
  </van-popup>
</template>

<style lang="scss" scoped></style>
