<script setup lang="ts">
// 使用 withDefaults 设置默认值
const props = withDefaults(
  defineProps<{
    title?: string
    message?: string
    placeholder?: string
    confirmText: string
    cancelText: string
    onClose?: () => void
    onConfirm?: (value: string) => void
    destroy?: () => void
  }>(),
  {
    title: '',
    message: '',
    placeholder: '',
    confirmText: '确定',
    cancelText: '取消',
    onClose: () => {},
    onConfirm: () => {},
    destroy: () => {}
  }
)

// 控制模态框的可见性
const visible = ref(false)
const inputValue = ref('')

// 显示模态框的方法
const show = () => {
  visible.value = true
  inputValue.value = ''
}

// 关闭模态框的方法
const close = () => {
  visible.value = false
  props.destroy()
  if (props.onClose) {
    props.onClose()
  }
}

const confirm = () => {
  visible.value = false
  if (props.onConfirm) {
    props.onConfirm(inputValue.value)
  }
}

// 将 show 和 close 方法暴露给父组件
defineExpose({ show, close })
</script>

<template>
  <van-dialog
    v-model:show="visible"
    show-cancel-button
    :title="props.title"
  >
    <div class="prompt-content">
      <div
        v-if="props.message"
        class="message"
      >
        {{ props.message }}
      </div>
      <van-field
        v-model="inputValue"
        :placeholder="props.placeholder"
        clearable
        class="input-field"
      />
    </div>
    <template #footer>
      <div class="w-full flex-around-center padding-16 cg-16">
        <van-button
          class="flex-1 btn cancel"
          @click="close"
        >
          {{ props.cancelText }}
        </van-button>
        <van-button
          type="primary"
          class="flex-1 btn"
          @click="confirm"
        >
          {{ props.confirmText }}
        </van-button>
      </div>
    </template>
  </van-dialog>
</template>

<style scoped lang="scss">
.prompt-content {
  padding: 16px 24px;
}

.message {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 400;
  color: #c1c1c1;
  text-align: center;
}

.input-field {
  margin-bottom: 8px;
}

.cancel {
  color: #c1c1c1;
  background: #3d3c3c;
}

.btn {
  height: 48px;
  font-size: 16px;
}
</style>
