<script lang="ts" setup>
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
const router = useRouter()
const { t } = useI18n()
const options = [
  {
    title: t('itemStore'),
    path: '/PropertyStore',
    className: 'mine-props'
  },
  {
    title: t('task'),
    path: '/Task',
    className: 'mine-task'
  },
  {
    title: t('backpack'),
    path: '/propertyStore/backPack',
    className: 'mine-bag'
  },
  {
    title: t('userCharacterOption'),
    path: '/Mine/SelfImage',
    className: 'mine-image'
  }
]
function navToMyImage(path: string) {
  if (path == '/Mine/SelfImage') {
    eventReport({
      front_address: ForwardAddressEnum.MY_PAGE,
      event_type: EventTypeEnum.OPEN_MY_AVATAR_PAGE
    }).catch((err) => {
      console.warn(err)
    })
  }
  router.push(path)
}
</script>
<template>
  <div class="nav-banner flex-between-center mt-20">
    <div
      class="nav-item"
      v-for="item in options"
      :key="item.title"
      @click="navToMyImage(item.path)"
    >
      <SvgIcon
        :icon-class="item.className"
        class="fsize-24"
      />
      <div class="nav-text">{{ item.title }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../assets/styles/mixin';

.nav-banner {
  margin-bottom: 24px;

  .nav-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    height: 60px;

    .nav-text {
      @include multiLine(2);

      max-height: 30px;
      padding: 0 16px;
      margin-top: 9px;
      font-size: 12px;
      font-weight: 400;
      word-break: break-all;
    }
  }
}
</style>
