<template>
  <div
    class="wraps"
    id="wraps"
  >
    <van-skeleton :loading="firstLoading">
      <template #template>
        <CardSkeleton
          v-if="firstLoading"
          :loading="firstLoading"
          :firstHeight="250"
        />
      </template>
      <div
        v-for="(item, index) in state.homeCardList"
        :key="index"
        class="items"
        @click="cardClickHandle(item)"
      >
        <van-image
          class="card-img"
          :src="item.image_url"
          alt="image-bg"
          fit="cover"
          :lazy-load="true"
          position="top"
        >
          <template #loading>
            <div class="image-slot"></div>
          </template>
          <template #error>
            <div class="image-slot"></div>
          </template>
        </van-image>
        <div class="view-banner flex-start-center">
          <div
            class="view flex-center-center mr-4"
            v-if="item.is_repeat === 1"
          >
            <SvgIcon
              icon-class="orignal-icon"
              class="fsize-12 mr-2"
            />
            <div class="view-num">Orignal</div>
          </div>
          <div class="view flex-center-center">
            <div class="view-icon"></div>
            <div class="view-num">{{ item.chat_times }}</div>
          </div>
        </div>
        <div class="card-content">
          <SoundPlay
            :soundUrl="item.opening_statement_voice"
            v-if="item.opening_statement_voice"
          >
            <template #default="slotProps">
              <div
                class="play-icon mb-12 flex-center-center"
                @click.stop="playSoundHandle(slotProps)"
              >
                <SoundLoading
                  class="h-full"
                  v-if="slotProps.isPlaying"
                />
                <SvgIcon
                  v-else
                  icon-class="sound-loding-icon"
                  class="fsize-16"
                />
                <div class="sound-time-length">{{ slotProps.duration }}“</div>
              </div>
            </template>
          </SoundPlay>
          <div class="title mb-6">
            {{ item.name }}
          </div>
          <!-- <div
            class="author mb-8"
            :class="item.tag_list.length > 0 ? '' : 'no-bottom'"
          >
            @{{ item.client_info?.name }}
          </div> -->
          <div class="flex-between-center">
            <div
              class="label"
              @click.stop="tabClickHandle(item.tag_list[0].name)"
              v-if="item.tag_list.length > 0"
            >
              {{ item.tag_list[0].name }}
            </div>
            <SvgIcon
              icon-class="3d-flag"
              class="fsize-24"
              v-if="item.has_3d === 1"
            />
          </div>
        </div>
      </div>
    </van-skeleton>

    <div
      id="myaiLoadings"
      class="loading flex-center-center"
      v-show="!isRequestAll && state.homeCardList.length > 0"
    >
      <img
        class="loading-icon"
        src="../../assets/images/index/loading-icon.png"
        alt="loading"
      />
    </div>
    <div
      class="no-data"
      v-if="state.homeCardList.length === 0 && isRequestAll"
    >
      <EmptyIcon
        :text="props.type === 'mine' ? t('noAIsNotice') : t('noHaveAi')"
        :bottom="props.type === 'mine' ? -20 : 0"
      />
      <div
        class="add-ai-btn flex-center-center"
        @click="addAiHandle"
        v-if="props.type === 'mine'"
      >
        <SvgIcon
          icon-class="mine-add-white"
          class="fsize-16"
        />
        <span class="btn-text">{{ t('addOption') }}</span>
      </div>
    </div>
    <!-- <div class="loading end">没有更多了</div> -->
  </div>
</template>

<script setup lang="ts">
import router from '@/router'
import useAgentCreate from '@/stores/modules/agentCreate'
import { IAiListItem } from '@/api/home/<USER>'
import { getMyAiList } from '@/api/mine'
import { IAiListParams } from '@/api/mine/type'
import { eventReport, ForwardAddressEnum, EventTypeEnum } from '@/api/eventReport'
const agentCreateStore = useAgentCreate()
const route = useRoute()
const clientId = ref<string>(route.query.id as string)
interface IState {
  homeCardList: IAiListItem[]
}
const props = withDefaults(
  defineProps<{
    type?: 'mine' | 'others'
  }>(),
  {
    type: 'mine'
  }
)
interface IEmits {
  (e: 'tab-click', value: string): void
}
const emit = defineEmits<IEmits>()
const { t } = useI18n()
const isRequestAll = ref(false)
const swiperObserver = ref<IntersectionObserver | null>(null)
const state = reactive<IState>({
  homeCardList: []
})
const loading = ref(false)
const firstLoading = ref(false)

const aiListParams = ref<IAiListParams>({
  page: 1,
  limit: 10,
  other_client_id: clientId.value ? Number(clientId.value) : undefined
})

function getMyAiListHandle() {
  loading.value = true
  firstLoading.value = aiListParams.value.page === 1
  getMyAiList(aiListParams.value)
    .then((res) => {
      if (res.code === 200) {
        state.homeCardList.push(...res.data.list)
        if (state.homeCardList.length >= res.data.count) {
          isRequestAll.value = true
        }
        if (state.homeCardList.length === 0) return (isRequestAll.value = true)
      }
    })
    .catch(() => {
      isRequestAll.value = true
    })
    .finally(() => {
      loading.value = false
      firstLoading.value = false
    })
}
function cardClickHandle(item: IAiListItem) {
  eventReport({
    event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
    front_address: ForwardAddressEnum.MY_CHARACTER_PAGE,
    ai_id: item.id
  }).catch((err) => {
    console.warn(err)
  })
  router.push(`/chat/${item.id}`)
}
function playSoundHandle(slotProps: any) {
  !slotProps.isPlaying ? slotProps.play() : slotProps.stop()
}
function tabClickHandle(label: string) {
  emit('tab-click', label)
}
function addAiHandle() {
  agentCreateStore.showAgentCreateModePopup = true
}
function observerHandle() {
  if (!document.querySelector('#myaiLoadings')) return
  swiperObserver.value = new IntersectionObserver(
    (entries) => {
      // console.log(entries[0].isIntersecting, 'hhhhhh')
      if (state.homeCardList.length === 0) return
      if (entries[0].isIntersecting) {
        aiListParams.value.page++
        getMyAiListHandle()
      }
    },
    {
      root: null,
      threshold: 0
    }
  )
  swiperObserver.value.observe(document.querySelector('#myaiLoadings'))
}

onMounted(() => {
  getMyAiListHandle()
  observerHandle()
})

onUnmounted(() => {
  if (swiperObserver.value) {
    // swiperObserver.value.unobserve(document.querySelector('#myaiLoadings'))
    swiperObserver.value.disconnect()
    swiperObserver.value = null
  }
})
</script>

<style scoped lang="scss">
:deep(.van-skeleton) {
  width: 100%;
  padding: 0;
}

:deep(.bar) {
  background-color: #fff !important;
}

:deep(.van-image) {
  img {
    border-radius: 16px !important;
  }
}

:deep(.van-image__loading) {
  background: #242424;
  border-radius: 16px !important;
}

:deep(.van-image__error) {
  background: #242424;
  border-radius: 16px !important;
}

.wraps {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  min-height: 50vh;

  .items {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    width: calc((100% - 8px) / 2);
    height: 250px;
    padding-top: 12px;
    margin-bottom: 8px;
    overflow: hidden;
    background: linear-gradient(180deg, rgba(36, 36, 36, 0%) 0%, rgba(36, 36, 36, 0%) 50%, rgba(36, 36, 36, 60%) 70%, #242424 100%);
    border-radius: 16px;

    .card-img {
      position: absolute;
      top: 0;
      right: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
    }

    .view-banner {
      position: absolute;
      top: 8px;
      left: 8px;
    }

    .view {
      width: fit-content;
      padding: 4px 8px;
      background: rgba(23, 23, 23, 50%);
      backdrop-filter: blur(8px);
      border-radius: 16px;
    }

    .view-icon {
      width: 12px;
      height: 12px;
      margin-right: 2px;
      background: url('~@/assets/images/index/view-icon.png') no-repeat;
      background-size: contain;
    }

    .view-num {
      font-size: 12px;
      font-weight: 500;
    }

    .card-content {
      padding: 0 8px 12px;
      background: linear-gradient(180deg, rgba(36, 36, 36, 0%) 0%, rgba(36, 36, 36, 80%) 100%);
      border-radius: 16px;

      .author {
        font-size: 11px;
        color: rgba(255, 255, 255, 60%);
      }

      .play-icon {
        width: 50px;
        height: 24px;
        padding: 5px 8px;
        background: rgba(193, 41, 246, 70%);
        backdrop-filter: blur(3px);
        border: 1px solid rgba(255, 255, 255, 15%);
        border-radius: 16px;
      }

      .title {
        font-size: 14px;
        font-weight: 800;
        line-height: 14px;
        word-break: break-all;
        overflow-wrap: break-word;
      }

      .label {
        display: flex;
        width: fit-content;
        padding: 4px 6px;
        font-size: 10px;
        color: #ffefcf;
        background: rgba(24, 24, 24, 70%);
        backdrop-filter: blur(4px);
        border-radius: 6px;
      }
    }
  }

  .loading {
    width: 100%;

    .loading-icon {
      width: 24px;
      height: 24px;
      animation: loading 1s linear infinite;
    }
  }

  @keyframes loading {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .no-bottom {
    margin-bottom: 0 !important;
  }

  .sound-time-length {
    margin-left: 4px;
    font-size: 14px;
  }

  .no-data {
    position: absolute;
    top: 120px;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .add-ai-btn {
    width: fit-content;
    padding: 8px 19px;
    margin: auto;
    margin-top: 32px;
    font-size: 12px;
    font-weight: 600;
    background: linear-gradient(90deg, #ff35f2 0%, #98f 100%);
    border-radius: 40px;
    box-shadow: 0 4px 12px 0 rgba(193, 41, 246, 30%);
  }

  .btn-text {
    margin-left: 4px;
  }

  .image-slot {
    width: 110px;
    height: 45px;
    // background: url('~@/assets/images/eros-black2.png') no-repeat center center / contain;
  }
}
</style>
