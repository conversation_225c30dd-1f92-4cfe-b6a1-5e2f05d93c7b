<script setup lang="ts">
import useUserStore from '@/stores/modules/user'
import router from '@/router'

interface IMember {
  vip_name?: string
  vip_expiration_time?: string
  vip_type?: number
}
const userStore = useUserStore()
const { userInformation } = toRefs(userStore)
// console.log(userInformation, 'kkkkkkk')
const isHighLevelMenber = ref<IMember>({})

if (userInformation.value.vip_count > 0) {
  isHighLevelMenber.value = findHighestVipType(userInformation.value.vip_info)
}
function findHighestVipType(vipArray: any) {
  let highestVip = null
  let highestVipType = -1

  for (const vip of vipArray) {
    if (vip.vip_type > highestVipType) {
      highestVip = vip
      highestVipType = vip.vip_type
    }
  }

  return highestVip
}
const { t } = useI18n()
function openMemberHandle() {
  router.push({ name: 'Member' })
}
</script>
<template>
  <div class="member-container">
    <div
      class="advertise-banner flex-start-center"
      :class="userInformation.vip_count === 2 ? 'super' : ''"
    >
      <div class="banner-icon"></div>
      <div class="banner-text-wrap mr-2">
        <div class="title">{{ userInformation.vip_count !== 0 ? isHighLevelMenber.vip_name : t('upgradePlanBenefits') }}</div>
        <div
          class="validity"
          v-if="userInformation.vip_count !== 0"
        >
          {{ isHighLevelMenber.vip_expiration_time.split(' ')[0] }}
        </div>
      </div>
      <div
        class="banner-btn flex-center-center"
        @click="openMemberHandle"
      >
        {{ userInformation.vip_count === 0 ? t('activateNow') : t('purchaseNow') }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../assets/styles/mixin';

.advertise-banner {
  height: 60px;
  padding: 16px;
  background: url('~/images/mine/premium-member-newbg.png') no-repeat;
  background-size: contain;
  border-radius: 16px;

  &.super {
    background: url('~/images/mine/superMember-newbg.png') no-repeat;
    background-size: contain;
  }

  .banner-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    background: url('~/images/mine/upgradePlan-diamond-icon.png') no-repeat;
    background-size: contain;
  }

  .banner-text-wrap {
    flex: 1;

    .title {
      font-size: 14px;
      font-weight: 600;

      @include multiLine(2);
    }

    .validity {
      margin-top: 4px;
      font-size: 12px;
      color: rgba(255, 255, 255, 50%);
    }
  }

  .banner-btn {
    width: max-content;
    min-width: 60px;
    height: 28px;
    padding: 8px;
    margin-left: auto;
    font-size: 12px;
    font-weight: 600;
    color: #ff35f2;
    background-color: #fff;
    border-radius: 15px;
  }
}
</style>
