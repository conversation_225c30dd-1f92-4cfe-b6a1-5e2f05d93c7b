<script setup lang="ts">
import type { IMemberInfo, IMember } from '@/api/login/types'
const props = withDefaults(
  defineProps<{
    userInformation: IMemberInfo
  }>(),
  {
    userInformation: () => {
      return {
        vip_info: [],
        vip_count: 0
      }
    }
  }
)
const router = useRouter()
const isHighLevelMenber = ref<IMember>({
  vip_name: '',
  vip_expiration_time: '',
  vip_type: 0
})
function findHighestVipTypeHandle() {
  if (props.userInformation.vip_count > 0) {
    isHighLevelMenber.value = findHighestVipType(props.userInformation.vip_info)
  }
}
findHighestVipTypeHandle()
function findHighestVipType(vipArray: IMember[]) {
  let highestVip = null
  let highestVipType = -1

  for (const vip of vipArray) {
    if (vip.vip_type > highestVipType) {
      highestVip = vip
      highestVipType = vip.vip_type
    }
  }

  return highestVip
}
const { t } = useI18n()
function openMemberHandle() {
  router.push({ name: 'Member' })
}
defineExpose({ findHighestVipTypeHandle })
</script>
<template>
  <div class="membership-container flex-between-center">
    <div class="name">
      <div class="member-name flex-start-center">
        <IconSvgCrownIcon class="fsize-20 mr-4" />
        <span class="title">{{ props.userInformation.vip_count !== 0 ? isHighLevelMenber.vip_name : t('upgradePlanBenefits') }}</span>
      </div>
      <div
        class="expires-time mt-2 ml-24"
        v-if="props.userInformation.vip_count !== 0"
      >
        {{ isHighLevelMenber?.vip_expiration_time?.split(' ')[0] }}
      </div>
    </div>
    <div
      class="member-btn"
      @click="openMemberHandle"
    >
      {{ props.userInformation.vip_count === 0 ? t('activateNow') : t('purchaseNow') }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.membership-container {
  height: 62px;
  padding: 13px 12px 13px 16px;
  background: url('@/assets/images/mine/membership-bg.png') no-repeat top / cover;

  .member-btn {
    width: fit-content;
    min-width: 60px;
    padding: 9px 12px;
    font-size: 11px;
    font-weight: 600;
    line-height: 13px;
    color: #fff;
    text-align: center;
    background: #2f2101;
    border-radius: 8px;
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    color: $livCoTextColor;
  }

  .expires-time {
    font-size: 12px;
    line-height: 14px;
    color: rgba(47, 33, 1, 50%);
  }
}
</style>
