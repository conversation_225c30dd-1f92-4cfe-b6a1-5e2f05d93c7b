<script setup lang="ts">
import { IUserInfo } from '@/api/mine/type'
import router from '@/router'
import { LocationQueryRaw } from 'vue-router'
import { ForwardAddressEnum } from '@/api/eventReport/index'
// import avatar from '@/assets/images/avatar.png'

interface IProps {
  type?: string
  mineInfo: IUserInfo
}
const props = withDefaults(defineProps<IProps>(), {
  type: 'mine',
  mineInfo: () => ({})
})

const emit = defineEmits<{
  (e: 'setFollow', client_id: number): void
  (e: 'openExchangeGold'): void
}>()

const { t } = useI18n()
// const memberInfo = ref<IUserInfo>({})
// const memberFields = ['vip_name', 'vip_goods_id', 'vip_expiration_time', 'vip_type']
// memberInfo.value = JSON.parse(JSON.stringify(props.mineInfo, memberFields))
function goToFollow(active: number) {
  let params: LocationQueryRaw = { name: props.mineInfo.name, active }
  if (props.type === 'others') {
    params = { ...params, cid: props.mineInfo.client_id }
  }
  router.push({ name: 'follow', query: params })
}
function focusHandle() {
  emit('setFollow', props.mineInfo.client_id)
}
function btnClickHandle(type: string) {
  switch (type) {
    case 'gold':
      emit('openExchangeGold')
      break
    case 'crystal':
      sessionStorage.setItem('crystal_front_address', ForwardAddressEnum.MY_PAGE)
      router.push({ name: 'crystal' })
      break
  }
}
</script>

<template>
  <div class="mine-container">
    <div class="mine-container-bg"></div>
    <div class="avatar-banner flex-start-center">
      <div class="avatar-container">
        <van-image :src="props.mineInfo.avatar_url ? props.mineInfo.avatar_url : ''" />
      </div>
      <div
        class="info-wrap"
        :class="{ 'hidden-wrap': props.type === 'others' }"
      >
        <div class="info-wrap-name">
          {{ props.mineInfo.name }}
        </div>
        <div class="info-wrap-id">ID:{{ props.mineInfo.client_id }}</div>
      </div>
      <div
        class="follow-btn"
        :class="{ active: props.mineInfo.is_follow === 1 }"
        v-if="props.type === 'others'"
        @click="focusHandle"
      >
        {{ props.mineInfo.is_follow === 1 ? t('unfollowOption') : t('followOption') }}
      </div>
    </div>
    <div class="info-banner flex-center-center">
      <div
        class="info-banner-item item-fans"
        @click="goToFollow(0)"
      >
        <div class="info-banner-item-number">{{ props.mineInfo.fans_count }}</div>
        <div class="info-banner-item-label">{{ t('fansOption') }}</div>
      </div>
      <div
        class="info-banner-item item-love"
        @click="goToFollow(1)"
      >
        <div class="info-banner-item-number">{{ props.mineInfo.like_ai_count }}</div>
        <div class="info-banner-item-label">{{ t('likesOption') }}</div>
      </div>
      <div class="info-banner-item">
        <div
          class="info-banner-item-number"
          @click="goToFollow(2)"
        >
          {{ props.mineInfo.follow_count }}
        </div>
        <div class="info-banner-item-label">{{ t('followOption') }}</div>
      </div>
    </div>
    <Member v-if="props.type === 'mine'" />
    <div
      class="flex-between-center mt-12"
      v-if="props.type === 'mine'"
    >
      <div class="btn-item flex-start-center">
        <div class="species"></div>
        <div class="btn-item-icon"></div>
        <div class="btn-item-text">{{ props.mineInfo.gold_amount }}</div>
        <SvgIcon
          icon-class="mine-add"
          class="fsize-24 add-icon"
          @click="btnClickHandle('gold')"
        />
      </div>
      <div class="btn-item flex-start-center">
        <div class="diamond"></div>
        <div class="btn-item-icon diamond-btn"></div>
        <div class="btn-item-text">{{ props.mineInfo.crystal_amount }}</div>
        <SvgIcon
          icon-class="mine-add"
          class="fsize-24 add-icon"
          @click="btnClickHandle('crystal')"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.van-image__loading) {
  border: none;
  border-radius: 74px;
}

.mine-container {
  .mine-container-bg {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 300px;
    background: linear-gradient(to bottom, #311b39, #251e2d 33%, #1a191b 66%, #181818);
  }

  .avatar-banner {
    margin-bottom: 12px;

    .avatar-container {
      width: 52px;
      height: 52px;
      margin-right: 16px;
      background-color: #fff;
      border-radius: 50%;

      :deep(.van-image) {
        width: 100%;
        height: 100%;

        img {
          border-radius: 50%;
        }
      }
    }

    .info-wrap {
      &.hidden-wrap {
        max-width: calc(100% - 150px);
        // overflow: hidden;
        word-break: break-all;
      }

      .info-wrap-name {
        font-size: 20px;
        font-weight: 600;
        line-height: 28px;
        color: #fff;
      }

      .info-wrap-id {
        font-size: 14px;
        color: rgba(255, 255, 255, 50%);
      }
    }

    .follow-btn {
      padding: 8px 16px;
      margin-left: auto;
      font-size: 12px;
      font-weight: 600;
      color: #fff;
      background: #ff35f2;
      border: 1px solid #ff35f2;
      border-radius: 21px;

      &.active {
        padding: 8px 12px;
        color: #ff35f2;
        background: rgba(255, 53, 242, 10%);
      }
    }
  }

  .info-banner {
    margin-bottom: 12px;

    .info-banner-item {
      position: relative;
      flex: 1;
      height: 46px;
      text-align: center;

      .info-banner-item-number {
        margin-bottom: 7px;
        font-size: 18px;
        font-weight: 800;
        color: #fff;
      }

      .info-banner-item-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 50%);
      }
    }

    .item-love::after,
    .item-fans::after {
      position: absolute;
      top: 50%;
      right: 0;
      width: 1px;
      height: 16px;
      content: '';
      background: rgba(255, 255, 255, 10%);
      transform: translateY(-50%);
    }
  }

  .btn-item {
    position: relative;
    width: calc(50% - 5px);
    height: 48px;
    padding: 12px;
    background: #252525;
    border-radius: 16px;

    .species,
    .diamond {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 52px;
      height: 43px;
      content: '';
      background: url('~/images/mine/lightning-bg.png') no-repeat;
      background-size: contain;
    }

    .diamond {
      width: 58px;
      height: 39px;
      background: url('~/images/mine/diamond-bg.png') no-repeat;
      background-size: contain;
    }

    .btn-item-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
      background: url('~/images/mine/gold-icon.png') no-repeat;
      background-size: contain;
    }

    .btn-item-text {
      font-size: 16px;
      font-weight: 600;
      color: #fff;
    }

    .add-icon {
      margin-left: auto;
    }

    .diamond-btn.btn-item-icon {
      background: url('~/images/mine/diamond-icon.png') no-repeat;
      background-size: contain;
    }
  }
}
</style>
