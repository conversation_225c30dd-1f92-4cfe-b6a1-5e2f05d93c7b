<script setup lang="ts"></script>

<template>
  <div class="screen-root"></div>
</template>

<style scoped lang="scss">
.screen-root {
  padding: 20px;
  background-image: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 37%, #2a2a2a 63%);
  width: 100%;
  height: 0.6rem;
  background-size: 400% 100%;
  margin-top: 0.6rem;
  background-position: 100% 50%;
  animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0 50%;
  }
}
</style>
