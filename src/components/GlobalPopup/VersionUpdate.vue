<script lang="ts" setup>
import useAppStore from '@/stores/modules/app'
import useVersionStore, { UpdateTypeEnum } from '@/stores/modules/version'

const versionStore = useVersionStore()
const { t } = useI18n()
const appStore = useAppStore()

function updateNowHandle() {
  window.location.reload()
}

function updateLaterHandle() {
  if (versionStore.isShowEntryApp) {
    localStorage.setItem('laterUpdateCode', `${versionStore.versionUpdateInfo.version_code}`)
    versionStore.isShowEntryApp = false
  }
  appStore.showVersionPopup = false
}
</script>

<template>
  <van-overlay
    v-model:show="appStore.showVersionPopup"
    class="flex-center-center"
    z-index="9999"
    :close-on-click-overlay="false"
    :lock-scroll="false"
  >
    <div class="version-popup">
      <img
        src="@/assets/images/version/version-mask.png"
        alt="mask"
        class="mask"
      />
      <img
        src="@/assets/images/version/version-mascot.png"
        alt="mascot"
        class="mascot"
      />
      <div class="title mb-8">{{ t('discoverUpdate') }}</div>
      <div class="gray-text">V{{ versionStore.versionUpdateInfo.version_name }}</div>
      <div class="content gray-text">
        {{ versionStore.versionUpdateInfo.update_content }}
      </div>
      <div class="btn-wrap">
        <div
          class="btn-now flex-center-center"
          @click="updateNowHandle"
          :class="{ 'mb-16': versionStore.versionUpdateInfo.update_type === UpdateTypeEnum.FORCE }"
        >
          {{ t('updateNow') }}
        </div>
        <div
          class="btn-later flex-center-center"
          @click="updateLaterHandle"
          v-show="versionStore.versionUpdateInfo.update_type !== UpdateTypeEnum.FORCE"
        >
          {{ t('updateLater') }}
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<style lang="scss" scoped>
.version-popup {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: calc(100% - 68px);
  height: 390px;
  padding: 32px 20px 20px;
  background: url('~@/assets/images/version/version-bg.png') no-repeat center / cover;
  border-radius: 16px;

  .title {
    z-index: 2;
    width: 50%;
    font-size: 20px;
    font-weight: 600;
    line-height: 22px;
    color: #fff;
  }

  .content {
    z-index: 2;
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    padding-bottom: 30px;
    margin-top: 20px;
    overflow: auto;
    font-size: 12px;
    line-height: 16px;
    white-space: pre-line;
    mask-image: linear-gradient(
      to bottom,
      rgba(69, 66, 69, 100%) 0%,
      rgba(69, 66, 69, 100%) calc(100% - 30px),
      rgba(69, 66, 69, 0%) calc(100% - 15px),
      rgba(69, 66, 69, 0%) 100%
    );

    &::-webkit-scrollbar {
      display: block;
      width: 3px;
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 30%);
      border-radius: 15px;
    }
  }

  .btn-wrap {
    z-index: 2;
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
    margin-top: auto;
  }

  .btn-now,
  .btn-later {
    width: 100%;
    font-size: 16px;
    line-height: 16px;
    border-radius: 56px;
  }

  .btn-later {
    font-size: 12px;
    color: rgba(255, 255, 255, 50%);
  }

  .btn-now {
    height: 48px;
    font-weight: 600;
    color: $livCoTextColor;
    background: $livCoThemeColor;
  }

  .gray-text {
    color: #ffffffb2;
  }

  .mask {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
    display: block;
    width: 100%;
    border-radius: 16px;
  }

  .mascot {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    display: block;
    width: 184px;
    transform: translateY(-30%);
  }
}
</style>
