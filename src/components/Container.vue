<script setup lang="ts">
defineProps({
  paddingX: {
    type: Number,
    default: 16
  }
})
</script>

<template>
  <main
    class="main-container"
    :style="`padding-left: ${paddingX}px; padding-right: ${paddingX}px`"
  >
    <slot></slot>
  </main>
</template>
<style lang="scss" scoped>
.main-container {
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  padding-top: 46px;
  overflow-y: auto;
}
</style>
