<script setup lang="ts">
import defEmpty from '@/assets/images/index/empty-icon.png'
import followEmpty from '@/assets/images/mine/follow-empty.png'
import messageEmpty from '@/assets/images/AgentMessage/nodata.png'
import searchEmpty from '@/assets/images/search/nodata.png'
const props = withDefaults(
  defineProps<{
    width?: number
    height?: number
    text?: string
    bottom?: number
    url?: string
  }>(),
  {
    width: 196,
    height: 114,
    text: '智能体还在路上～',
    bottom: 6,
    url: 'def'
  }
)
const imgUrl = ref<string>(defEmpty)
switch (props.url) {
  case 'def':
    imgUrl.value = defEmpty
    break
  case 'follow':
    imgUrl.value = followEmpty
    break
  case 'msg':
    imgUrl.value = messageEmpty
    break
  case 'search':
    imgUrl.value = searchEmpty
    break
}
</script>
<template>
  <div class="empty-container flex-center-center">
    <img
      class="empty-icon"
      :src="imgUrl"
      alt="empty-icon"
      :style="{ width: props.width + 'px', height: props.height + 'px' }"
    />
    <div
      class="empty-text"
      v-html="props.text"
    ></div>
  </div>
</template>

<style lang="scss" scoped>
.empty-container {
  flex-direction: column;
  min-width: 292px;
  text-align: center;

  .empty-icon {
    width: 100%;
    height: 100%;
  }

  .empty-text {
    width: 100%;
    font-size: 13px;
    line-height: 15px;
    color: #4d4d4d;
    text-align: center;
  }
}
</style>
