<script setup lang="ts"></script>
<template>
  <div class="loading-overlay">
    <div class="loading-box">
      <img
        src="@/assets/images/agentCreate/loading.png"
        class="loading"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: green;
  background-color: transparent;
}

.loading-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 104px;
  height: 104px;
  background: #120b0f;
  border-radius: 16px;
  opacity: 0.8;
}

.loading {
  width: 56px;
  height: 56px;
  animation: run 1s infinite linear;
}

@keyframes run {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
