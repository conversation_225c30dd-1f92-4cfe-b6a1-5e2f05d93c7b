<script setup lang="ts">
import { SelfieType } from '@/api/agentChat/types.ts'
import { ICurrencyTypeEnum, IPurchaseTypeEnum } from '@/enums'
import { getAmountID, purchase } from '@/api/purchase'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { useModal } from '@/hooks/useModal.ts'
import useUserStore from '@/stores/modules/user.ts'
import { getPictureInfo } from '@/api/agentHomePage'
import { usePaymentError } from '@/hooks/useCommon.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    photoItem: SelfieType | undefined
  }>(),
  {}
)
const userStore = useUserStore()
const showBottom = defineModel<boolean>()
const loading = ref(false)
const emits = defineEmits(['refresh', 'getList'])

const submitPurchase = async () => {
  loading.value = true
  try {
    const { data, code, msg } = await getAmountID({
      currency_type: props.photoItem.currency_type,
      product_type: IPurchaseTypeEnum.SELFIE,
      product_id: props.photoItem.id
    })
    if (code === ResultEnum.SUCCESS) {
      console.log(data)
      const { code: code1, msg: msg1 } = await purchase({ amount_id: data.amount_id, currency_type: props.photoItem.currency_type })
      if (code1 === ResultEnum.SUCCESS) {
        useModal({
          message: t('purchaseSuccessful')
        })
        // showBottom.value = false
        loading.value = false
        // emits('getList')
        await userStore.getUserInfo()
        getPictureInfo({ pic_id: props.photoItem.id }).then((res) => {
          const { data, code } = res
          if (code === ResultEnum.SUCCESS) {
            console.log(data.pic_url)
            emits('refresh', data.pic_url)
            emits('getList')
            showBottom.value = false
          }
        })
      } else {
        useModal({
          message: msg1
        })
      }
    } else {
      usePaymentError(code, msg)
    }
  } catch (e) {
    console.warn(e)
  }
  loading.value = false
}
</script>

<template>
  <van-popup
    round
    teleport="#app"
    v-model:show="showBottom"
    position="bottom"
    :style="{ maxHeight: '85%' }"
  >
    <div class="popup">
      <div class="flex-between-center mb-16">
        <div>
          <div class="fsize-18">{{ t('unlockSelfie') }}</div>
        </div>
        <div @click="showBottom = false">
          <van-icon name="cross" />
        </div>
      </div>
      <CrystalBalance
        class="mb-12"
        v-if="props.photoItem.currency_type === ICurrencyTypeEnum.Crystal"
      />
      <!--      <CoinBalance-->
      <!--        class="mb-12"-->
      <!--        v-if="props.photoItem.currency_type === ICurrencyTypeEnum.Coin"-->
      <!--      />-->
      <div class="photo">
        <div
          class="photo-img"
          :style="{ background: `url('${props.photoItem.vague_pic_url}') no-repeat center / cover` }"
        >
          <div class="question-mark">
            <SvgIcon icon-class="question" />
          </div>
        </div>
        <div class="flex-center-center photo-text">Use crystals to unlock or become a member to unlock beautiful photos for free every day</div>
      </div>
      <div class="purchase">
        <div class="flex-center-start flex-column ml-20">
          <div class="num flex-start-center">
            <SvgIcon
              v-if="props.photoItem.value"
              :icon-class="props.photoItem.currency_type === ICurrencyTypeEnum.Coin ? 'coin' : 'crystal'"
              class="mr-4"
            />{{ props.photoItem.value }}
          </div>
        </div>
        <van-button
          size="large"
          class="btn"
          :loading="loading"
          @click="submitPurchase"
        >
          <div class="flex-between-center cg-4">
            <div>{{ t('confirmButton') }}</div>
          </div>
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
.popup {
  padding: 24px 16px 50px;

  :deep(.balance) {
    background: #262626;
  }

  .photo {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    align-items: center;
    justify-content: center;

    .photo-text {
      margin: 0 48px;
      font-size: 14px;
      text-align: center;
    }

    .photo-img {
      position: relative;
      width: 104px;
      height: 124px;
      border: 2px solid white;
      border-radius: 16px;

      .question-mark {
        position: absolute;
        top: 50%;
        left: 50%;
        font-size: 54px;
        transform: translate(-50%, -50%);
      }

      &::before {
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: -1;
        width: 84%;
        height: 84%;
        content: '';
        background: rgba(110, 110, 110, 20%);
        border-radius: 16px;
        transform: translate(40%, -5%) rotate(13deg);
      }
    }
  }

  .purchase {
    display: flex;
    justify-content: space-between;
    height: 54px;
    margin-top: 40px;
    background: #302f2f;
    border-radius: 32px;

    .text {
      color: #8b8889;
    }

    .num {
      font-weight: 600;
    }

    .btn {
      width: 124px;
      height: auto;
      color: $livCoTextColor;
      background: $livCoThemeColor;
      border: none;
      border-radius: 40px;
    }
  }
}
</style>
