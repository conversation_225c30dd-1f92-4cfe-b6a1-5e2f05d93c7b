<script setup lang="ts">
import { SelfieType } from '@/api/agentChat/types.ts'
import { downloadFile } from '@/utils'
import { useModal } from '@/hooks/useModal.ts'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { DressType } from '@/api/propertyStore/types.ts'
import { setDress } from '@/api/agentChat'
const { t } = useI18n()

const route = useRoute()
const aiID = computed(() => {
  if ('id' in route.params) {
    return Number(route.params.id)
  }
})

const props = withDefaults(
  defineProps<{
    photoItem: SelfieType | DressType | undefined
    isBackground?: boolean
  }>(),
  {
    isBackground: false
  }
)
const show = defineModel<boolean>()

const setBackGround = () => {
  const { close } = useModal({
    message: t('settingUp'),
    loading: true,
    autoClose: false
  })
  setDress({
    set_id: (props.photoItem as DressType).set_id,
    ai_id: aiID.value || (props.photoItem as DressType).ai_id
  })
    .then((res) => {
      if (res.code === ResultEnum.SUCCESS) {
        useModal({
          message: t('setupSuccessful')
        })
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      close()
    })
}
</script>

<template>
  <van-overlay
    :show="show"
    class="overlay"
    z-index="999"
  >
    <div
      class="wrapper"
      @click.stop
    >
      <div class="img-back">
        <div
          class="img"
          :style="{ background: `url('${props.photoItem.pic_url}') no-repeat center / cover` }"
        ></div>
        <div
          class="close"
          @click="show = false"
        >
          <SvgIcon icon-class="close" />
        </div>
      </div>
      <div class="btn">
        <van-button
          class="download-btn"
          @click="downloadFile(props.photoItem.pic_url)"
        >
          {{ t('download') }}
        </van-button>
        <van-button
          class="back-btn"
          v-if="props.isBackground"
          @click="setBackGround"
        >
          {{ t('setAsChatBackground') }}
        </van-button>
      </div>
    </div>
  </van-overlay>
</template>

<style scoped lang="scss">
@import 'src/assets/styles/mixin';

.overlay {
  --van-overlay-background: rgba(0, 0, 0, 80%);
}

.wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 64px 32px 60px;

  .img-back {
    position: relative;
    flex: 1;
    width: 100%;
    background: $livCoThemeColor;
    border-radius: 16px;

    .img {
      position: absolute;
      top: 3px;
      left: 3px;
      width: calc(100% - 6px);
      height: calc(100% - 6px);
      background: url('@/assets/images/image.png') center / cover no-repeat;
      border-radius: 16px;
    }

    .close {
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 28px;
    }
  }

  .btn {
    display: flex;
    column-gap: 15px;
    justify-content: space-between;
    width: 100%;
    margin-top: 48px;
    font-size: 32px;

    .download-btn {
      flex: 1;
      backdrop-filter: blur(8px);
      border: 2px solid rgba(255, 255, 255, 50%);
      border-radius: 32px;
    }

    .back-btn {
      flex: 1;
      color: $livCoTextColor;
      background: $livCoThemeColor;
      border: 2px solid rgba(255, 255, 255, 50%);
      border-radius: 32px;
    }
  }
}
</style>
