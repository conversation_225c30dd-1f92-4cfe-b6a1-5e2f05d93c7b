<script setup lang="ts">
import PhotoHover from '@/components/AgentHomePage/PhotoHover.vue'
import { Ref } from 'vue'
import { getPictureList } from '@/api/agentHomePage'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { AlbumTypeEnum } from '@/api/agentHomePage/types.ts'
import { ICurrencyTypeEnum } from '@/enums'
import { SelfieType } from '@/api/agentChat/types.ts'
const { t } = useI18n()

const route = useRoute()
const albumVisible = defineModel<boolean>()
const firstIn = ref(true)
const unlockVisible = ref(false)
const previewVisible = ref(false)
const tempPhotoData = ref()

const normalAlbumList = ref([])

const aiID = computed(() => {
  if ('id' in route.params) {
    return Number(route.params.id)
  }
})

// const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')
const activeName = inject<Ref<string>>('activeName')

const handlePhoto = (item: SelfieType) => {
  tempPhotoData.value = item
  if (item.pic_url) {
    previewVisible.value = true
  } else {
    unlockVisible.value = true
  }
}

// function scrollToCenter() {
//   const container = document.getElementsByClassName('likability-photo')[0]
//   const parent = document.getElementsByClassName('album')[0]
//   const element = document.getElementById('center-item')
//
//   const containerWidth = container.clientWidth
//   const elementWidth = element.clientWidth
//
//   // 计算父元素的 paddingLeft
//   const parentPaddingLeft = parent ? parseInt(window.getComputedStyle(parent).paddingLeft) : 0
//
//   const elementLeft = element.offsetLeft - parentPaddingLeft // 考虑父元素 padding
//   const scrollPosition = elementLeft + container.scrollLeft - containerWidth / 2 + elementWidth / 2
//
//   container.scrollTo({
//     left: scrollPosition,
//     behavior: 'smooth'
//   })
// }

// const handleLevelNumber = () => {
//   const maxFlag = agentMessage.value.ai_chat_setting.intimacy_level === agentMessage.value.intimacy_level_info.length
//   const minFlag = agentMessage.value.ai_chat_setting.intimacy_level === 1
//   const front = maxFlag ? agentMessage.value.ai_chat_setting.intimacy_level - 2 : agentMessage.value.ai_chat_setting.intimacy_level - 1
//   const now = maxFlag ? agentMessage.value.ai_chat_setting.intimacy_level - 1 : agentMessage.value.ai_chat_setting.intimacy_level
//   const next = maxFlag ? agentMessage.value.ai_chat_setting.intimacy_level : agentMessage.value.ai_chat_setting.intimacy_level + 1
//   const nextNext = maxFlag ? agentMessage.value.ai_chat_setting.intimacy_level + 1 : agentMessage.value.ai_chat_setting.intimacy_level + 2
//   const percent = maxFlag ? 100 : (agentMessage.value.ai_chat_setting.intimacy_now / agentMessage.value.ai_chat_setting.intimacy_upgrade) * 100
//   return {
//     front,
//     now,
//     next,
//     nextNext,
//     minFlag,
//     maxFlag,
//     percent
//   }
// }

const getAlbumList = () => {
  getPictureList({
    ai_id: aiID.value,
    type: AlbumTypeEnum.NORMAL
  }).then((res) => {
    const { data, code } = res
    if (code === ResultEnum.SUCCESS) {
      if (data.pic_list?.length) {
        albumVisible.value = true
      }
      normalAlbumList.value = data.pic_list
    }
  })
}

// const getIntimacyList = () => {
//   getPictureList({
//     ai_id: aiID.value,
//     type: AlbumTypeEnum.INTIMACY
//   }).then((res) => {
//     const { data, code } = res
//     if (code === ResultEnum.SUCCESS) {
//       console.log(data)
//     }
//   })
// }

getAlbumList()
// getIntimacyList()

watch(
  () => activeName.value,
  (val) => {
    if (val === 'b') {
      if (firstIn.value) {
        firstIn.value = false
        // nextTick(() => {
        //   scrollToCenter()
        // })
      }
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <div class="album">
    <!--    <div class="tips">—— 🩷提升好感度，解锁专属自拍🩷 ——</div>-->
    <!--    <div class="likability-photo flex-start-center cg-16">-->
    <!--      <div-->
    <!--        v-for="item in agentMessage.intimacy_level_info"-->
    <!--        class="photo-container"-->
    <!--        :key="item.level"-->
    <!--        :id="item.level === agentMessage.ai_chat_setting.intimacy_level && 'center-item'"-->
    <!--        @click="unlockVisible = true"-->
    <!--      >-->
    <!--        <PhotoHover>-->
    <!--          <template #hover>-->
    <!--            <div class="w-full h-full flex-center-center flex-column hover-content">-->
    <!--              <SvgIcon-->
    <!--                icon-class="heart"-->
    <!--                class="fsize-44"-->
    <!--              />-->
    <!--              <div>-->
    <!--                好感度-->
    <!--                <SvgIcon-->
    <!--                  :icon-class="`${item.level}`"-->
    <!--                  class="num-left"-->
    <!--                />-->
    <!--              </div>-->
    <!--              <div>解锁</div>-->
    <!--            </div>-->
    <!--          </template>-->
    <!--        </PhotoHover>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div class="progress-container">-->
    <!--      <div-->
    <!--        class="left"-->
    <!--        :style="-->
    <!--          handleLevelNumber().minFlag && {-->
    <!--            opacity: 0-->
    <!--          }-->
    <!--        "-->
    <!--      >-->
    <!--        <SvgIcon-->
    <!--          :icon-class="`${handleLevelNumber().front}`"-->
    <!--          class="num-left transparent-icon"-->
    <!--        />-->
    <!--      </div>-->
    <!--      <div-->
    <!--        class="center"-->
    <!--        :style="{-->
    <!--          background: `linear-gradient(to right, #ff35f2 ${handleLevelNumber().percent - 30}%, #ff7af6 ${handleLevelNumber().percent}%, #606060 ${handleLevelNumber().percent}%)`-->
    <!--        }"-->
    <!--      >-->
    <!--        <SvgIcon-->
    <!--          :icon-class="`${handleLevelNumber().now}`"-->
    <!--          class="num-left"-->
    <!--        />-->
    <!--        <SvgIcon-->
    <!--          :icon-class="`${handleLevelNumber().next}`"-->
    <!--          class="num-right"-->
    <!--        />-->
    <!--      </div>-->
    <!--      <div-->
    <!--        class="right"-->
    <!--        :style="-->
    <!--          handleLevelNumber().maxFlag && {-->
    <!--            opacity: 0-->
    <!--          }-->
    <!--        "-->
    <!--      >-->
    <!--        <SvgIcon-->
    <!--          :icon-class="`${handleLevelNumber().nextNext}`"-->
    <!--          class="num-right transparent-icon"-->
    <!--        />-->
    <!--      </div>-->
    <!--    </div>-->

    <div class="tips">{{ t('receiveSelfiesHere') }}</div>
    <div class="self-photo">
      <div
        v-for="item in normalAlbumList"
        class="self-photo-item"
        :key="item"
        @click="handlePhoto(item)"
      >
        <PhotoHover :img-url="item.pic_url || item.vague_pic_url">
          <template
            #hover
            v-if="!item.pic_url"
          >
            <div class="w-full h-full flex-center-center flex-column hover-content">
              <SvgIcon
                icon-class="lock"
                class="fsize-44"
              />
              <div class="flex-center-center">
                <SvgIcon
                  :icon-class="item.currency_type === ICurrencyTypeEnum.Coin ? 'coin' : 'crystal'"
                  class="num-left"
                />×{{ item.value }}
              </div>
            </div>
          </template>
        </PhotoHover>
      </div>
    </div>
    <UnlockPhotoPopup
      v-model="unlockVisible"
      :photo-item="tempPhotoData"
      @get-list="getAlbumList"
    />
    <PhotoPreviewDialog
      v-model="previewVisible"
      :photo-item="tempPhotoData"
    />
  </div>
</template>

<style scoped lang="scss">
@import 'src/assets/styles/variables';

.album {
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  align-items: center;
  justify-content: flex-start;

  .tips {
    font-size: 12px;
    font-weight: 200;
  }

  .likability-photo {
    width: 100%;
    height: 185px;
    overflow-x: auto;
    white-space: nowrap;

    .photo-container {
      flex-shrink: 0;
      width: 140px;
      height: 100%;
      border-radius: 20px;
    }
  }

  .self-photo {
    display: flex;
    flex-wrap: wrap;
    gap: 13px 13px;
    width: 100%;

    .self-photo-item {
      width: 104px;
      height: 139px;
      border-radius: 8px;
    }
  }
}

.progress-container {
  display: flex;
  align-items: center;
  width: 80%;
  height: 5px;

  .transparent-icon {
    opacity: 0.3;
  }

  .num-left {
    position: absolute;
    transform: translate(-50%, 130%);
  }

  .num-right {
    position: absolute;
    right: 0;
    transform: translate(50%, 130%);
  }

  .left {
    position: relative;
    flex: 2;
    height: 100%;
    background: #2a2527;

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 18px;
      height: 18px;
      content: '';
      background: grey;
      border-radius: 50%;
      transform: translate(-50%, -6px);
    }
  }

  .center {
    position: relative;
    box-sizing: content-box;
    flex: 4;
    height: 100%;
    border: 3px solid white;

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      box-sizing: content-box;
      width: 10px;
      height: 10px;
      content: '';
      background: $themeColor;
      border: 4px solid white;
      border-radius: 50%;
      transform: translate(-50%, -6px);
    }

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      width: 18px;
      height: 18px;
      content: '';
      background: white;
      border-radius: 50%;
      transform: translate(50%, -6px);
    }
  }

  .right {
    position: relative;
    z-index: 0;
    flex: 2;
    height: 100%;
    background: #2a2527;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      width: 18px;
      height: 18px;
      content: '';
      background: grey;
      border-radius: 50%;
      transform: translate(50%, -6px);
    }
  }
}
</style>
