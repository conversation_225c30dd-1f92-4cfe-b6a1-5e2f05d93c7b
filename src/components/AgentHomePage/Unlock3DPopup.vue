<script setup lang="ts">
import { DressType } from '@/api/propertyStore/types.ts'
import { getAmountID, purchase } from '@/api/purchase'
import { ICurrencyTypeEnum, IPurchaseTypeEnum } from '@/enums'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { useModal } from '@/hooks/useModal.ts'
import useUserStore from '@/stores/modules/user.ts'
import { usePaymentError } from '@/hooks/useCommon.ts'
const { t } = useI18n()

const props = withDefaults(
  defineProps<{
    skinItem: DressType | undefined
  }>(),
  {}
)
const userStore = useUserStore()
const loading = ref(false)
const showBottom = defineModel<boolean>()
const emits = defineEmits(['getList'])

const submitPurchase = async () => {
  loading.value = true
  try {
    const { data, code, msg } = await getAmountID({
      currency_type: props.skinItem.currency_type,
      product_type: IPurchaseTypeEnum.ThreeDSkin,
      product_id: props.skinItem.id
    })
    if (code === ResultEnum.SUCCESS) {
      console.log(data)
      const { code: code1, msg: msg1 } = await purchase({ amount_id: data.amount_id, currency_type: props.skinItem.currency_type })
      if (code1 === ResultEnum.SUCCESS) {
        useModal({
          message: t('purchaseSuccessful')
        })
        emits('getList')
        loading.value = false
        showBottom.value = false
        await userStore.getUserInfo()
      } else {
        useModal({
          message: msg1
        })
      }
    } else {
      usePaymentError(code, msg)
    }
  } catch (e) {
    console.warn(e)
  }
  loading.value = false
}
</script>

<template>
  <van-popup
    round
    teleport="#app"
    v-model:show="showBottom"
    position="bottom"
    :style="{ maxHeight: '85%' }"
  >
    <div class="popup">
      <div class="flex-between-start mb-16">
        <div class="title fsize-18">{{ t('unlock3DOutfit') }}</div>
        <div @click="showBottom = false"><van-icon name="cross" /></div>
      </div>
      <CrystalBalance />
      <div class="touch-space">
        <img
          :src="props.skinItem.show_pic"
          alt=""
          class="three-img"
        />
      </div>
      <div class="purchase">
        <div class="flex-center-start flex-column ml-20">
          <div class="num flex-start-center">
            <SvgIcon
              v-if="props.skinItem.value"
              :icon-class="props.skinItem.currency_type === ICurrencyTypeEnum.Coin ? 'coin' : 'crystal'"
              class="mr-4 fsize-16"
            />{{ props.skinItem.value }}
          </div>
        </div>
        <van-button
          size="large"
          class="btn"
          :loading="loading"
          @click="submitPurchase"
        >
          {{ t('confirmButton') }}
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
.popup {
  padding: 24px 16px;

  :deep(.balance) {
    background: #262626;
  }

  :deep(.van-button--default) {
    border: inherit;
  }

  .touch-space {
    position: relative;
    width: 100%;
    height: 200px;
    margin-top: 60px;
    margin-bottom: 48px;
    border: 1px solid $livCoThemeColor;
    border-radius: 20px;

    .three-img {
      position: absolute;
      bottom: 2px;
      width: 100%;
    }
  }

  .purchase {
    display: flex;
    justify-content: space-between;
    height: 54px;
    background: #302f2f;
    border-radius: 32px;

    .text {
      color: #8b8889;
    }

    .num {
      font-weight: 600;
    }

    .btn {
      width: 124px;
      height: auto;
      font-weight: 600;
      color: $livCoTextColor;
      background: $livCoThemeColor;
      border-radius: 40px;
    }
  }
}
</style>
