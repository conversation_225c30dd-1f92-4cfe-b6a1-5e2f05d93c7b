<script setup lang="ts">
import { useConfirm } from '@/hooks/useConfirm.ts'
import { useModal } from '@/hooks/useModal.ts'
import { Ref } from 'vue'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import useUserStore from '@/stores/modules/user.ts'
import { deleteAgent } from '@/api/agentHomePage'
import { ResultEnum } from '@/enums/httpEnum.ts'
const { t } = useI18n()

const showBottom = defineModel<boolean>()
const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')
const userStore = useUserStore()
const router = useRouter()

const deleteAgentByID = async () => {
  const { close } = useModal({ message: t('deleting'), autoClose: false, loading: true })
  deleteAgent({ ai_id: agentMessage.value.id })
    .then((res) => {
      console.log(res)
      if (res.code === ResultEnum.SUCCESS) {
        useModal({ message: t('deleteSuccessful'), duration: 1000 })
        router.push('/')
      }
    })
    .catch((err) => {
      console.log(err)
    })
    .finally(() => {
      close()
    })
}

const navToHandle = () => {
  // 跳转
  window.open('https://www.facebook.com/ugenie.aichat', '_blank')
}
</script>

<template>
  <van-popup
    round
    teleport="#app"
    v-model:show="showBottom"
    position="bottom"
    :style="{ maxHeight: '85%' }"
  >
    <div class="popup">
      <div
        class="w-full"
        @click="navToHandle"
      >
        <van-button class="w-full">{{ t('reportFeedback') }}</van-button>
      </div>
      <div
        v-if="agentMessage.client_info?.client_id === userStore.userInformation?.client_id"
        class="w-full mt-12"
      >
        <van-button
          class="w-full"
          type="danger"
          @click="
            () => {
              useConfirm({
                content: t('confirmDeleteCharacter'),
                title: t('deleteRole'),
                cancelText: t('cancelButton'),
                confirmText: t('confirmButton'),
                onConfirm: () => {
                  deleteAgentByID()
                }
              })
            }
          "
        >
          {{ t('deleteRole') }}
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
.popup {
  width: 100%;
  padding: 24px 16px 40px;
}
</style>
