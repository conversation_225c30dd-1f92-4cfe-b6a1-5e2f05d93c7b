<script setup lang="ts">
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { Ref } from 'vue'
// import { Howl } from 'howler'
// import Recorder from 'recorder-core/recorder.mp3.min'
// import 'recorder-core/src/extensions/buffer_stream.player.js'
// import { useHandleStream } from '@/hooks/useHandleStream.ts'
// import { ResultEnum } from '@/enums/httpEnum.ts'
// import { useModal } from '@/hooks/useModal.ts'
// import { getOpeningVoice } from '@/api/agentHomePage'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import useUserStore from '@/stores/modules/user.ts'
import useAgentCreateStore from '@/stores/modules/agentCreate'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')
const agentCreateStore = useAgentCreateStore()

const aiID = computed(() => {
  if ('id' in route.params) {
    return Number(route.params.id)
  }
})

const streamVoiceData = ref({
  loading: false,
  duration: 0,
  playing: false
})

// const howlInstance = ref<Howl | null>()
// const myStream = ref(null)

const goEdit = () => {
  eventReport({ event_type: EventTypeEnum.EDIT_ADVANCED_AI, ai_id: aiID.value })
  agentCreateStore.type = 'edit'
  router.push({ name: 'AdvancedCreate', params: { id: aiID.value } })
}

// 播放音频初始化
// const ttsTrans = () => {
//   if (agentMessage.value.opening_statement_voice) return
//   if (myStream.value) {
//     myStream.value.stop()
//   }
//   if (howlInstance.value) {
//     howlInstance.value.stop()
//     howlInstance.value = null
//   }
//   myStream.value = Recorder.BufferStreamPlayer({
//     play: true,
//     onPlayEnd: () => {
//       streamVoiceData.value.playing = false
//       myStream.value = null
//     },
//     realtime: false,
//     decode: true
//   })
//   myStream.value.start(
//     () => {
//       streamVoiceData.value.loading = true
//       getOpeningVoice({
//         ai_id: aiID.value
//       }).then(async (response) => {
//         useHandleStream(response, {
//           onAudioData: (audioData) => {
//             const hexString = audioData.data.audio
//             if (audioData.data.status === 1) {
//               if (hexString) {
//                 const arrayBuffer = new ArrayBuffer(hexString.length / 2)
//                 // 创建一个新的Uint8Array视图，以便我们可以将数据写入ArrayBuffer
//                 const uint8Array = new Uint8Array(arrayBuffer)
//
//                 // 遍历十六进制字符串，每两个字符转换为一个字节
//                 for (let i = 0; i < hexString.length; i += 2) {
//                   uint8Array[i / 2] = parseInt(hexString.slice(i, i + 2), 16)
//                 }
//                 myStream.value.input(arrayBuffer)
//                 streamVoiceData.value.loading = false
//                 streamVoiceData.value.playing = true
//               }
//             } else if (audioData.data.status === 2) {
//               streamVoiceData.value.duration = Number((audioData.extra_info.audio_length / 1000).toFixed(0))
//             }
//           },
//           onError: (e) => {
//             console.warn(e)
//           },
//           onNormalResponse: (code, normalObject, msg) => {
//             if (code === ResultEnum.AGENT_REMOVED) {
//               useModal({
//                 message: msg,
//                 duration: 1500,
//                 onClose: () => {
//                   router.push('/')
//                 }
//               })
//               return
//             }
//             if (code === ResultEnum.SUCCESS) {
//               howlInstance.value = new Howl({
//                 src: normalObject,
//                 onplay: () => {
//                   streamVoiceData.value.loading = false
//                   streamVoiceData.value.playing = true
//                 },
//                 onstop: () => {
//                   streamVoiceData.value.loading = false
//                   streamVoiceData.value.playing = false
//                 },
//                 onload: () => {
//                   howlInstance.value.play()
//                 },
//                 onend: () => {
//                   streamVoiceData.value.loading = false
//                   streamVoiceData.value.playing = false
//                   howlInstance.value = null
//                 }
//               })
//             } else {
//               streamVoiceData.value.loading = false
//               streamVoiceData.value.playing = false
//             }
//           }
//         })
//       })
//     },
//     (errMsg: string) => {
//       // ttsLoading.value[index] = false
//       useModal({
//         message: errMsg,
//         duration: 1000
//       })
//     }
//   )
// }

watch(
  () => agentMessage.value.opening_statement,
  (val) => {
    if (val) {
      streamVoiceData.value.duration = Number((agentMessage.value?.opening_statement?.length * 0.15).toFixed(0))
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <div class="about-ta">
    <!--    <div class="top-tab">-->
    <!--      <div-->
    <!--        class="tab-item"-->
    <!--        @click="router.push('/mine/selfImage')"-->
    <!--      >-->
    <!--        <div class="item-name">-->
    <!--          <SvgIcon icon-class="body" />-->
    <!--          形象设定-->
    <!--        </div>-->
    <!--        <div class="item-jump">点击设置 ></div>-->
    <!--      </div>-->
    <!--    </div>-->
    <div class="agent-detail">
      <div class="title flex-between-center">
        <div>{{ t('introduction') }}</div>
        <div
          v-if="agentMessage.client_info?.client_id === userStore.userInformation?.client_id && agentMessage.is_quick === 0"
          class="edit"
          @click="goEdit"
        >
          {{ t('editOption') }}
        </div>
      </div>
      <div
        class="tag-container"
        v-if="agentMessage.tag_list?.length"
      >
        <div
          class="tag"
          v-for="tag in agentMessage.tag_list"
          :key="tag.id"
        >
          {{ tag.name }}
        </div>
      </div>
      <div class="content">
        {{ agentMessage?.synopsis || t('none') }}
        <!--        <van-text-ellipsis-->
        <!--          v-if="agentMessage.synopsis"-->
        <!--          rows="3"-->
        <!--          :content="agentMessage?.synopsis"-->
        <!--        >-->
        <!--          <template #action="{ expanded }">-->
        <!--            <van-icon-->
        <!--              v-if="expanded"-->
        <!--              color="white"-->
        <!--              name="arrow-up"-->
        <!--            />-->
        <!--            <van-icon-->
        <!--              color="white"-->
        <!--              name="arrow-down"-->
        <!--              v-else-->
        <!--            />-->
        <!--          </template>-->
        <!--        </van-text-ellipsis>-->
        <!--        <template v-else>{{ t('none') }}</template>-->
      </div>
      <!--      <div class="title flex-start-center cg-8 h-full">-->
      <!--        <div>{{ t('openingLine') }}</div>-->
      <!--        <SoundPlay-->
      <!--          v-if="agentMessage.opening_statement_voice"-->
      <!--          :sound-url="agentMessage.opening_statement_voice"-->
      <!--        >-->
      <!--          <template #default="{ play, duration, isPlaying, loading, stop }">-->
      <!--            <div-->
      <!--              class="sound-btn"-->
      <!--              @click="-->
      <!--                () => {-->
      <!--                  if (isPlaying) {-->
      <!--                    stop()-->
      <!--                  } else {-->
      <!--                    play()-->
      <!--                  }-->
      <!--                }-->
      <!--              "-->
      <!--            >-->
      <!--              <van-loading-->
      <!--                v-if="loading"-->
      <!--                size="18"-->
      <!--                color="#8d005d"-->
      <!--              />-->
      <!--              <template v-else>-->
      <!--                <SoundLoading v-if="isPlaying" />-->
      <!--                <SvgIcon-->
      <!--                  v-else-->
      <!--                  icon-class="play"-->
      <!--                />-->
      <!--                <div class="ml-4 second flex-center-center">{{ duration }}”</div></template-->
      <!--              >-->
      <!--            </div>-->
      <!--          </template>-->
      <!--        </SoundPlay>-->
      <!--        <div-->
      <!--          v-else-->
      <!--          class="sound-btn"-->
      <!--          @click="ttsTrans"-->
      <!--        >-->
      <!--          <van-loading-->
      <!--            v-if="streamVoiceData.loading"-->
      <!--            size="18"-->
      <!--            color="#8d005d"-->
      <!--          />-->
      <!--          <template v-else>-->
      <!--            <SoundLoading v-if="streamVoiceData.playing" />-->
      <!--            <SvgIcon-->
      <!--              v-else-->
      <!--              icon-class="play"-->
      <!--            />-->
      <!--            <div class="ml-4 second flex-center-center">{{ streamVoiceData.duration }}”</div></template-->
      <!--          >-->
      <!--        </div>-->
      <!--      </div>-->
      <!--      <div class="content">-->
      <!--        <van-text-ellipsis-->
      <!--          v-if="agentMessage.opening_statement"-->
      <!--          rows="3"-->
      <!--          :content="agentMessage?.opening_statement"-->
      <!--          expand-text="展开"-->
      <!--          collapse-text="收起"-->
      <!--        >-->
      <!--          <template #action="{ expanded }">-->
      <!--            <van-icon-->
      <!--              v-if="expanded"-->
      <!--              color="white"-->
      <!--              name="arrow-up"-->
      <!--            />-->
      <!--            <van-icon-->
      <!--              color="white"-->
      <!--              name="arrow-down"-->
      <!--              v-else-->
      <!--            />-->
      <!--          </template>-->
      <!--        </van-text-ellipsis>-->
      <!--        <template v-else>{{ t('none') }}</template>-->
      <!--      </div>-->
    </div>
  </div>
</template>

<style scoped lang="scss">
@import 'src/assets/styles/variables';

.about-ta {
  :deep(.van-button--default) {
    border: inherit;
  }

  .top-tab {
    margin-bottom: 16px;

    .tab-item {
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      width: 120px;
      padding: 12px;
      background: #232222;
      border-radius: 16px;

      .item-jump {
        font-size: 10px;
        color: rgba(255, 255, 255, 50%);
      }
    }
  }

  .agent-detail {
    display: flex;
    flex-direction: column;
    row-gap: 16px;
    width: 100%;
    padding: 16px;
    background: #232222;
    border-radius: 16px;

    .edit {
      font-size: 12px;
      color: rgba(255, 255, 255, 50%);
    }

    .sound-btn {
      display: flex;
      align-items: stretch;
      justify-content: center;
      height: 100%;
      padding: 4px 8px;
      color: #fff;
      background: $livCoThemeColor;
      border-radius: 16px;

      :deep(.loading-bar) {
        background: #fff;
      }

      .second {
        font-size: 12px;
        font-weight: 500;
      }
    }

    .tag-container {
      display: flex;
      flex-wrap: wrap;
      gap: 6px 8px;

      .tag {
        padding: 5px 8px;
        font-size: 11px;
        background: rgba(90, 90, 90, 60%);
        border-radius: 8px;
      }
    }

    .content {
      font-size: 14px;
      font-weight: 200;
      white-space: pre-wrap;
    }
  }
}
</style>
