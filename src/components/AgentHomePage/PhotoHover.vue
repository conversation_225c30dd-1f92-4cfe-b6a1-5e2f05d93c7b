<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    imgUrl?: string
  }>(),
  {
    imgUrl: ''
  }
)
// function getImageUrl(name) {
//   console.log(name)
//   return new URL(`../../assets/images/${name}`, import.meta.url).href
// }
</script>

<template>
  <div
    class="photo"
    :style="{ background: `url('${props.imgUrl}') no-repeat top / cover` }"
  >
    <div class="hover">
      <slot name="hover"> </slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.photo {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: inherit;

  .hover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
  }
}
</style>
