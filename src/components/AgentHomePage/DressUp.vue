<script setup lang="ts">
// import SkinItem from '@/components/SkinGiftGroup/SkinItem.vue'
import PurchaseSkinPopUp from '@/components/SkinGiftGroup/PurchaseSkinPopUp.vue'
import { useModal } from '@/hooks/useModal.ts'
import { getDressByID } from '@/api/agentChat'
import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'
const { t } = useI18n()

const route = useRoute()
const aiID = computed(() => {
  if ('id' in route.params) {
    return Number(route.params.id)
  }
})
const skinList = ref([])
const threeDSkin = ref()
const unlockVisible = ref(false)
const purchaseVisible = ref(false)
const previewVisible = ref(false)
const purchaseSkinData = ref()
const previewSkinData = ref()

const handleOpen = (item: DressType) => {
  if (item.is_buy) {
    previewVisible.value = true
    previewSkinData.value = item
  } else {
    purchaseSkinData.value = item
    purchaseVisible.value = true
  }
}

const getDressList = () => {
  const { close } = useModal({
    message: t('loading'),
    loading: true,
    autoClose: false
  })
  getDressByID({
    ai_id: aiID.value
  })
    .then((res) => {
      const { code, data } = res
      if (code === 200) {
        console.log(data)
        skinList.value = data.filter((item) => item.type === SkinTypeEnum.Normal)
        threeDSkin.value = data
          .sort((a, b) => {
            return new Date(b.create_time).getTime() - new Date(a.create_time).getTime()
          })
          .find((item) => item.type === SkinTypeEnum.ThreeD)
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      close()
    })
}

getDressList()
provide('aiID', aiID)
</script>

<template>
  <div class="flex-center-start flex-column rg-20">
    <div
      class="three-d flex-center-start flex-column rg-20"
      v-if="threeDSkin"
    >
      <div class="title"><div class="text">3D</div></div>
      <div class="touch-space">
        <img
          :src="threeDSkin.show_pic"
          alt=""
          class="three-img"
        />
      </div>
      <van-button
        v-if="!threeDSkin?.is_buy"
        size="large"
        class="btn"
        @click="unlockVisible = true"
      >
        <div class="flex-center-center cg-4">
          <SvgIcon
            icon-class="lock"
            class="fsize-20"
          />
          <div>{{ t('unlock3DOutfit') }}</div>
        </div>
      </van-button>
    </div>
    <div class="static flex-center-start flex-column rg-20 w-full">
      <div class="title">{{ t('staticOutfit') }}</div>
      <div class="static-photo">
        <SkinItem
          v-for="item in skinList"
          :key="item.id"
          :skin-item="item"
          @click="handleOpen(item)"
          class="self-photo-item"
        />
      </div>
    </div>
    <Unlock3DPopup
      v-model="unlockVisible"
      :skin-item="threeDSkin"
      @get-list="getDressList"
    />
    <PurchaseSkinPopUp
      v-model="purchaseVisible"
      :skin-item="purchaseSkinData"
      @get-list="getDressList"
    />
    <PhotoPreviewDialog
      v-model="previewVisible"
      :photo-item="previewSkinData"
      is-background
    />
  </div>
</template>

<style scoped lang="scss">
.three-d {
  width: 100%;

  .title {
    position: relative;
    padding: 3px 0;
    font-size: 16px;

    .text {
      position: relative;
    }
  }

  .touch-space {
    position: relative;
    width: 100%;
    height: 200px;
    margin-top: 30px;
    border: 1px solid $livCoThemeColor;
    border-radius: 20px;

    .three-img {
      position: absolute;
      bottom: 2px;
      width: 100%;
    }
  }

  .btn {
    width: 100%;
    color: $livCoTextColor;
    background: $livCoThemeColor;
    background-clip: padding-box;
    border-radius: 37px;
  }
}

.static-photo {
  display: flex;
  gap: 13px 13px;
  width: 100%;
  padding: 2px;
  overflow-x: auto;

  .self-photo-item {
    flex-shrink: 0;
  }
}
</style>
