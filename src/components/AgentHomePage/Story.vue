<script setup lang="ts">
import { getAgentContentGameList } from '@/api/contentGame'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { ContentGameStatusEnum, IContentListRes, IContentRes } from '@/api/contentGame/types.ts'
import { useConfirm } from '@/hooks/useConfirm.ts'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const aiID = computed(() => {
  if ('id' in route.params) {
    //@ts-ignore
    return Number(route.params.id)
  } else if ('ai_id' in route.params) {
    return Number(route.params.ai_id)
  }
})
const loading = ref(false)

const storyVisible = defineModel<boolean>()
const contentGameList = ref<IContentListRes>([])

const goContentGame = (item: IContentRes) => {
  if (item.lock_status === ContentGameStatusEnum.LOCKED) {
    useConfirm({
      content: t('moreIntimacyToUnlock'),
      confirmText: t('goToChat'),
      cancelText: t('closeButton'),
      onConfirm: () => {
        if (route.query.path.includes('chat')) {
          router.push(`/chat/${aiID.value}`)
          eventReport({
            event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
            front_address: ForwardAddressEnum.STORY_PAGE,
            ai_id: aiID.value
          }).catch((err) => {
            console.warn(err)
          })
        }
      }
    })
  } else {
    eventReport({
      event_type: EventTypeEnum.ENTER_STORY,
      content_id: item.id,
      front_address: ForwardAddressEnum.STORY_PAGE,
      ai_id: aiID.value
    }).catch((err) => {
      console.warn(err)
    })
    router.push(`/chat/${item.api_key}`)
  }
}

const getContentGameList = () => {
  loading.value = true
  getAgentContentGameList({
    ai_id: aiID.value
  })
    .then(({ code, data }) => {
      if (code === ResultEnum.SUCCESS) {
        console.log(data)
        if (!data.length) {
          storyVisible.value = false
        }
        contentGameList.value = data
      }
    })
    .finally(() => {
      loading.value = false
    })
}

getContentGameList()
</script>

<template>
  <div class="story">
    <div
      class="load"
      v-show="loading"
    >
      <div class="gl-container">
        <div class="global-dot"></div>
        <div class="global-dot"></div>
        <div class="global-dot"></div>
        <div class="global-dot"></div>
        <div class="global-dot"></div>
        <div class="global-dot"></div>
      </div>
    </div>
    <div
      class="story-item"
      v-for="contentItem in contentGameList"
      v-show="!loading"
      :key="contentItem.id"
      @click="goContentGame(contentItem)"
    >
      <div
        :style="{ backgroundImage: `url('${contentItem.prefix_url + contentItem.cover_url_vertical}')` }"
        class="story-image"
      >
        <div
          v-if="contentItem.lock_status === ContentGameStatusEnum.LOCKED"
          class="story-lock"
        >
          <SvgIcon
            icon-class="story-lock"
            class="fsize-24"
          />
        </div>
      </div>
      <div class="story-content">
        <div class="story-title">{{ contentItem.ai_info.name }}</div>
        <div class="story-describe">{{ contentItem.ai_info.synopsis }}</div>
        <div
          class="intimacy-level"
          :style="!contentItem.level && { opacity: 0 }"
        >
          <SvgIcon
            icon-class="intimacy-heart-store"
            class="fsize-16"
          />LV{{ contentItem.level }}
        </div>
      </div>
      <div
        v-if="contentItem.lock_status === ContentGameStatusEnum.LOCKED"
        class="story-status-lock"
      >
        <SvgIcon icon-class="story-lock-grey" />
        {{ t('unlockedFeatures') }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.story {
  display: flex;
  flex-direction: column;
  row-gap: 16px;
  width: 100%;

  .load {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 200px;
  }

  .story-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 110px;
    background: #262626;
    border-radius: 12px;

    .story-image {
      height: 100%;
      aspect-ratio: 84 / 110;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      border-radius: 12px;

      .story-lock {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 50%);
        border-radius: 12px;
      }
    }

    .story-content {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 8px;
      height: 100%;
      padding: 16px;
      overflow: hidden;
    }

    .story-title {
      overflow: hidden;
      font-size: 15px;
      font-weight: bolder;
      line-height: 16px;
      color: #fff;
      text-align: left;
    }

    .story-describe {
      display: -webkit-box; // 将对象作为弹性伸缩盒子模型显示。
      overflow: hidden;
      font-size: 11px;
      font-weight: 400;
      line-height: 14px;
      color: rgba(255, 255, 255, 60%);
      text-align: left;
      text-overflow: ellipsis; // 溢出用省略号显示
      -webkit-line-clamp: 2; // 这个属性不是css的规范属性，需要组合上面两个属性，表示显示的行数。
      -webkit-box-orient: vertical; // 从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
    }

    .intimacy-level {
      display: flex;
      column-gap: 2px;
      align-items: center;
      width: fit-content;
      padding: 2px 0;
      font-size: 10px;
      font-weight: bold;
      line-height: 10px;
      color: #fff;
      text-align: left;
      background: #262626;
      border-radius: 12px;
    }

    .story-status-completed {
      position: absolute;
      right: 0;
      bottom: 0;
      padding: 4px 8px;
      font-size: 12px;
      font-weight: 600;
      line-height: 14px;
      color: #8b3612;
      text-align: left;
      background: linear-gradient(161deg, #ffc378 0%, #ffdfb7 100%);
      border: 1px solid white;
      border-radius: 12px 0;
    }

    .story-status-lock {
      position: absolute;
      right: 0;
      bottom: 0;
      display: flex;
      column-gap: 4px;
      align-items: center;
      justify-content: center;
      padding: 2px 8px;
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      color: rgba(255, 255, 255, 50%);
      text-align: left;
      background: #3b3d41;
      border-radius: 12px 0;
    }
  }
}
</style>
