<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    giftProgress: number
    seriesTitle: string
    seriesNum: number
  }>(),
  {
    giftProgress: 0
  }
)
</script>

<template>
  <div class="series-container">
    <div class="flex-between-center mb-16">
      <div class="series-title">
        {{ props.seriesTitle }}
        <span class="series-num">({{ props.seriesNum * props.giftProgress }}/{{ props.seriesNum }})</span>
      </div>
      <div
        class="gift"
        :style="{ '--gift-progress': props.giftProgress }"
      >
        <div class="gift-inside">
          <IconSvgSkinGift class="fsize-12" />
        </div>
      </div>
    </div>
    <div class="series-skins">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '@/assets/styles/mixin';
.series-container {
  .series-num {
    font-family: 'DIN Light', serif;
  }

  .gift {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    padding: 2px;
    background: conic-gradient(from 180deg, #ebdfac 0 calc(var(--gift-progress) * 360deg), #303030 calc(var(--gift-progress) * 360deg));
    border-radius: 50%;
    //box-shadow: 0 0 16px 0 #ebdfac;

    .gift-inside {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #000;
      border-radius: 50%;
    }
  }

  .series-skins {
    padding-bottom: 52px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
}
</style>
