<script setup lang="ts">
const show = defineModel<boolean>()
</script>

<template>
  <van-overlay
    v-model:show="show"
    z-index="9999"
    :close-on-click-overlay="false"
    :lock-scroll="false"
    class="skin-detail-overlay"
  >
    <div class="skin-scroll">
      <div class="skin-detail">1</div>
    </div>
  </van-overlay>
</template>

<style scoped lang="scss">
.skin-detail-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
