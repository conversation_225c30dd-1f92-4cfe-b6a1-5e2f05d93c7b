<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    skin: any
  }>(),
  {}
)
</script>

<template>
  <div class="relative">
    <div class="series-skin">
      <!--    :class="item === 3 && 'lock'"-->
      <img
        src="@/assets/images/mine/49b66793ce076fe5a88ddf758359650563fc73b7.png"
        alt=""
        class="skin"
      />
      <img
        src="@/assets/images/skin/skin-top-mask.png"
        alt="mask"
        class="mask-top"
      />
      <div class="mask-text"></div>
      <img
        src="@/assets/images/skin/skin-bottom-mask.png"
        alt="mask"
        class="mask-bottom"
      />
      <div class="skin-describe">This is skin describe {{ props.skin }} 2 但是多少度是的是的</div>
      <div class="overlay-radius"></div>
    </div>
    <div
      class="lock"
      v-if="false"
    >
      <IconSvgStoryLock class="fsize-24" />
      <div>未获得</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '@/assets/styles/mixin';
.lock {
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #fff4db;
  opacity: 0.5;
  transform: translate(-50%, -50%);
}

.series-skin {
  position: relative;
  aspect-ratio: 114 / 196;
  overflow: hidden;
  border-radius: 24px 0;

  @include gradation-border-with-radius-and-transparent-bg(1px, linear-gradient(161.44deg, #fff4db -0.56%, #ffdd91 100%));

  .overlay-radius {
    @include gradation-border-with-radius-and-transparent-bg(1px, linear-gradient(161.44deg, #fff4db -0.56%, #ffdd91 100%));

    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    border-radius: 24px 16px;
  }

  &.lock {
    opacity: 0.3;
  }

  .mask-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }

  .mask-bottom {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
  }

  .mask-text {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 24%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0%) 0%, rgba(0, 0, 0, 60%) 29.85%, #000 100%);
    border-radius: 0 20px;
  }

  .skin-describe {
    position: absolute;
    bottom: 8px;
    left: 0;
    width: 100%;
    padding: 0 8px;
    font-size: 10px;
    color: #fff;
  }

  .skin {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
