<script setup lang="ts">
const emits = defineEmits<{
  (e: 'close', errorStr?: string): void
  (e: 'confirm', type: string): void
}>()
const props = withDefaults(
  defineProps<{
    email: string
    code: string
    type?: string
  }>(),
  {
    email: '',
    code: '',
    type: 'login'
  }
)
const { t } = useI18n()
const show = ref(true)
const countdown = ref(3)
const netError = ref(false)
const errorStr = ref('')
function close() {
  emits('close')
}
window.addEventListener('offline', () => {
  netError.value = true
})
function submit() {
  if (countdown.value > 0) {
    return
  }
  if (netError.value) {
    errorStr.value = t('networkErrorRetry')
    emits('close', errorStr.value)
    return
  }
  emits('confirm', props.type)
}

let timer = setInterval(() => {
  if (countdown.value > 0) {
    countdown.value--
  } else {
    clearInterval(timer)
  }
}, 1000)
</script>
<template>
  <van-dialog
    v-model:show="show"
    show-cancel-button
    class="logoffDialog"
  >
    <div class="flex-start-center flex-column content">
      <div class="title">{{ t('deactivationConfirmation') }}</div>
      <div class="tip">{{ t('deactivationNotice1') }}</div>
      <div class="tip">{{ t('deactivationNotice2') }}</div>
      <div class="btnbox flex-between-center pt-40">
        <div
          class="btn flex-center-center btn1"
          @click="close"
        >
          {{ t('cancelButton') }}
        </div>
        <div
          :class="{ btn: true, 'flex-center-center': true, btn2: countdown <= 0, btn3: countdown > 0 }"
          @click="submit"
        >
          {{ t('confirmButton') }}
          <span v-if="countdown > 0">({{ countdown }})</span>
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<style lang="scss">
.logoffDialog {
  padding: 24px 16px;

  .van-dialog__footer {
    display: none !important;
  }

  .content {
    gap: 18px;

    .title {
      width: 100%;
      font-size: 18px;
      font-weight: 500;
      color: #fff;
      text-align: left;
    }

    .tip {
      width: 100%;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: #c1c1c1;
      text-align: left;
    }

    .btnbox {
      width: 100%;

      .btn {
        width: 134px;
        height: 48px;
        border-radius: 12px;
      }

      .btn1 {
        color: #c1c1c1;
        background: #3d3c3c;
      }

      .btn2 {
        color: $livCoTextColor;
        background: $livCoThemeColor;
      }

      .btn3 {
        color: $livCoTextColor;
        background: rgba(118, 112, 86);
      }
    }
  }
}
</style>
