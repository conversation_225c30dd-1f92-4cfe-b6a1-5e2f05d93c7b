<script setup lang="ts">
import { getRecommendPreference, set_preference } from '@/api/login'
import { IRecommendPreference, ITag_ids } from '@/api/login/types'
import useAppStore from '@/stores/modules/app'
import useNotifyStore from '@/stores/modules/notification.ts'
const appStore = useAppStore()
const notifyStore = useNotifyStore()
interface setPreferenceParams {
  sexs: number[]
  interest: number[]
}
interface genderListItem {
  name: string
  id: number
  light?: boolean
}
const { t } = useI18n()
const datas = reactive<{
  preferenceList: IRecommendPreference[]
  genderList: genderListItem[]
  genderCurrent: number
  loading: boolean
  sex: number
}>({
  preferenceList: [],
  genderList: [
    { id: 0, name: t('allOptions'), light: true },
    { id: 1, name: t('maleOption'), light: false },
    { id: 2, name: t('femaleOption'), light: false },
    { id: 3, name: t('nonBinaryOption'), light: false }
  ],
  genderCurrent: 0,
  loading: false,
  sex: 0
})
// function chooseTag(item: genderListItem) {
//   item.light = !item.light
//   dataChange(datas.genderList, item)
// }
function choosePreference(pItem: IRecommendPreference, item: ITag_ids) {
  item.light = !item.light
  dataChange(pItem.tag_list, item)
}
function dataChange(sourceList: ITag_ids[], item: ITag_ids) {
  const list = sourceList.filter((f) => f.light).map((item) => item.id)
  const listAll = sourceList.filter((_, pindex) => pindex !== 0).map((item) => item.light)
  if (list.length > 1) {
    sourceList[0].light = false
  }
  if (listAll.every((e) => e)) {
    sourceList.forEach((item, index) => {
      item.light = index === 0
    })
  }
  sourceList.forEach((c, cIndex) => {
    if (c.id === item.id && cIndex === 0) {
      c.light = true
      sourceList.forEach((k, v) => {
        if (v !== 0) {
          k.light = false
        }
      })
    }
  })
}
function getList() {
  getRecommendPreference().then((res) => {
    datas.preferenceList = res.data
    datas.preferenceList.forEach((item: IRecommendPreference) => {
      item.tag_list = item.tag_list.map((item: ITag_ids) => {
        return { ...item, light: false }
      })
      item.tag_list.unshift({
        id: 999999,
        name: t('allOptions'),
        light: true
      })
    })
  })
}
function submit() {
  let sexs: number[] = []
  let interest: number[] = paramsFormatter(datas.preferenceList)
  console.log('interest', interest)
  let data: setPreferenceParams = {
    sexs,
    interest
  }
  setPreferenceHandle(data)
}
function setPreferenceHandle(data: setPreferenceParams) {
  datas.loading = true
  set_preference(data)
    .then(() => {
      datas.loading = false
      close()
    })
    .catch(() => {
      datas.loading = false
    })
    .finally(() => {
      datas.loading = false
    })
}
function skipHandle() {
  let data: setPreferenceParams = {
    sexs: [],
    interest: []
  }
  setPreferenceHandle(data)
}
function paramsFormatter(sourceList: IRecommendPreference[]) {
  let result: number[] = []
  sourceList.forEach((item: IRecommendPreference) => {
    if (item.tag_list[0].light) {
      result = [...result, ...item.tag_list.filter((_, pindex: number) => pindex !== 0).map((item: ITag_ids) => item.id)]
    } else {
      result = [...result, ...item.tag_list.filter((f: ITag_ids) => f.light).map((item: ITag_ids) => item.id)]
    }
  })
  result = [...new Set(result)]
  return result
}

function close() {
  appStore.showPreference = false
  notifyStore.initNoticeAndPoster(true)
}
watch(
  () => appStore.showPreference,
  (val) => {
    if (val) getList()
  }
)
// appStore.showPreference = true
// getList()
</script>
<template>
  <van-popup
    v-model:show="appStore.showPreference"
    position="bottom"
    :style="{
      background: '#232222',
      'border-radius': '24px 24px 0px 0px',
      padding: '24px 16px 80px',
      maxHeight: '80vh'
    }"
    :close-on-click-overlay="false"
  >
    <div class="mb-16 flex-between-center title-banner">
      <div class="title">{{ t('choosePreference') }}</div>
      <div
        class="skip"
        @click="skipHandle"
      >
        {{ t('skipOption') }}
      </div>
    </div>
    <!-- <div class="tag-banner"> -->
    <!-- <div class="label">{{ t('genderOption') }}</div>
    <div class="tagBox">
      <div
        :class="{ tag: true, 'flex-center-center': true, lightTag: item.light }"
        v-for="(item, i) in datas.genderList"
        :key="i"
        @click="chooseTag(item)"
      >
        {{ item.name }}
      </div>
    </div> -->
    <div
      class="tag-list"
      v-for="(pitem, pindex) in datas.preferenceList"
      :key="pitem.id"
    >
      <div
        class="label"
        :style="{ paddingTop: pindex === 0 ? '' : '22px' }"
      >
        {{ pitem.name }}
      </div>
      <div class="tagBox">
        <div
          :class="{ tag: true, 'flex-center-center': true, lightTag: item.light }"
          v-for="(item, i) in pitem.tag_list"
          :key="i"
          @click="choosePreference(pitem, item)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>

    <!-- </div> -->
    <div class="btn-box flex-center-center">
      <div
        class="btn flex-center-center"
        @click="submit"
      >
        {{ t('confirmSelection') }}
        <van-loading
          v-if="datas.loading"
          type="spinner"
          color="white"
          size="24px"
        />
      </div>
    </div>
  </van-popup>
</template>
<style lang="scss" scoped>
@import 'src/assets/styles/variables';

// .tag-banner {
//   height: calc(100% - 96px);
//   overflow-y: scroll;
// }

.title {
  font-size: 18px;
  font-weight: 500;
  color: #fff;
}

.skip {
  font-size: 16px;
  font-weight: 500;
  color: $themeColor;
}

.label {
  padding: 16px 0;
  font-size: 14px;
  font-weight: 500;
  color: #8b8889;
}

.tagBox {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;

  .tag {
    min-width: 80px;
    height: 38px;
    padding: 0 16px;
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 52px;
  }

  .lightTag {
    background: rgba(255, 53, 242, 30%);
    border: 1px solid $themeColor;
  }
}

.btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  padding: 12px 0;
  background: #232222;
}

.btn {
  width: 327px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background: linear-gradient(90deg, $themeColor 0%, $helpColorB 100%);
  border-radius: 40px;
}
</style>
