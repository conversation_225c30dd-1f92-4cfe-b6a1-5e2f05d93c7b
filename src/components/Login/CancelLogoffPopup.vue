<script setup lang="ts">
import useAppStore from '@/stores/modules/app'
import useUserStore from '@/stores/modules/user'
import { logout, cancelLogoff } from '@/api/login'
import { useModal } from '@/hooks/useModal'

const appStore = useAppStore()
const userStore = useUserStore()
const { userInformation } = toRefs(userStore)
const { t } = useI18n()
function useOtherWayLogin() {
  logout().then(() => {
    userStore.removeAllUserInfo()
    appStore.showCancelLogoff = false
    appStore.showLogin = true
  })
}

function cancelLogoffHandle() {
  const { close } = useModal({
    message: t('loading'),
    loading: true,
    autoClose: false
  })
  cancelLogoff()
    .then((res) => {
      if (res.code !== 200) return
      appStore.showCancelLogoff = false
    })
    .finally(() => {
      close()
    })
}

const tipsTextHandle = (text: string) => {
  return text.replace(/\[时间]/g, userInformation.value?.delete_date)
}
</script>

<template>
  <van-dialog
    v-model:show="appStore.showCancelLogoff"
    show-cancel-button
    class="loginDialog flex-center-center"
  >
    <div class="dialog-content">
      <div class="title mb-16">{{ t('abandonAccountCancellation') }}</div>
      <div class="text mb-32">{{ tipsTextHandle(t('discardLogoutPrompt')) }}</div>
      <div
        class="cancel-btn mb-16"
        @click="cancelLogoffHandle"
      >
        {{ t('abandonCancellation') }}
      </div>
      <div class="other-way">
        <span @click="useOtherWayLogin">{{ t('otherLoginWay') }}</span>
      </div>
    </div>
  </van-dialog>
</template>

<style lang="scss" scoped>
.dialog-content {
  width: 312px;
  padding: 24px 16px;
  text-align: center;
  background: #232222;
  border-radius: 16px;

  .title {
    font-size: 18px;
    font-weight: 500;
  }

  .text {
    font-size: 14px;
    line-height: 20px;
    color: #c1c1c1;
  }

  .cancel-btn {
    padding: 17px 0;
    color: $livCoTextColor;
    background: $livCoThemeColor;
    border-radius: 12px;
  }

  .other-way {
    font-size: 12px;
    color: rgba(255, 255, 255, 50%);
  }
}
</style>
