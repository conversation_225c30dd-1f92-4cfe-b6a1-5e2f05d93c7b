<script setup lang="ts">
import googleIcon from '~@/assets/images/login/google-btn.png'
import type { ICallBackParams } from '@/api/login/types'
import { useModal } from '@/hooks/useModal'

interface ISubmitParams {
  type: string
  origin: ICallBackParams
  value: ICallBackParams
}
interface ILoginGoogleParams {
  clientId: string
  client_id: string
  credential: string
  select_by: string
}
interface ILoginFacebookParams {
  authResponse: {
    accessToken: string
    data_access_expiration_time: number
    expiresIn: number
    graphDomain: string
    signedRequest: string
    userID: string
  }
  status: string
}
interface IProps {
  disabled?: {
    google: boolean
    facebook: boolean
    apple: boolean
  }
  show?: {
    google: boolean
    facebook: boolean
    apple: boolean
  }
  type?: string
}
interface IEmit {
  (e: 'submit', value: ISubmitParams): void
  (e: 'update:disabled', value: { type: string; value: boolean }): void
}
const props = withDefaults(defineProps<IProps>(), {
  disabled: () => {
    return {
      google: false,
      facebook: false,
      apple: false
    }
  },
  show: () => {
    return {
      google: true,
      facebook: true,
      apple: true
    }
  },
  type: 'login'
})
const emits = defineEmits<IEmit>()
interface ILoadOptionItem {
  url: string
  id: string
  timer: null
  status: null
}
interface ILoadOption {
  google: ILoadOptionItem
  facebook: ILoadOptionItem
  apple: ILoadOptionItem
}
const { t } = useI18n()
const loadOption = ref<ILoadOption>({
  google: {
    url: 'https://accounts.google.com/gsi/client',
    id: 'eros_googleSDK',
    timer: null,
    status: null
  },
  facebook: {
    url: 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v15.0&appId=****************&autoLogAppEvents=1',
    id: 'eros_facebookSDK',
    timer: null,
    status: null
  },
  apple: {
    url: 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js',
    id: 'eros_appleSDK',
    timer: null,
    status: null
  }
})
const webButton = reactive({
  apple: {
    val: 0,
    text: 'Apple',
    icon: 'apple',
    id: 'apple-btn',
    click: () => handleAppleLogin()
  },
  facebook: {
    val: 1,
    text: 'Facebook',
    icon: 'facebook',
    id: 'facebook-btn',
    click: () => handleFbLogin()
  },
  google: {
    val: 2,
    text: 'Google',
    icon: 'google',
    id: 'google-btn',
    click: () => handleGeLogin()
  }
})
async function handleAppleLogin() {
  if (!loadOption.value.apple.status) {
    return
  }
  if (props.type === 'cancel') localStorage.setItem('loginType', 'cancel')
  //   const type = 'apple'
  try {
    // handleUpdate(type, true)
    // @ts-ignore
    await AppleID.auth.signIn()
    // handleUpdate(type, false)
  } catch (e) {
    // handleUpdate(type, false)
  }
}
function handleFbLogin() {
  try {
    handleUpdate('facebook', true)
    // @ts-ignore
    FB.login(facebookCallback)
  } catch (error) {
    handleUpdate('facebook', false)
    // sdk 加载失败
    // Toast(t('m.login.text44'))
    useModal({
      message: t('loading'),
      duration: 2000
    })
  }
}
function facebookCallback(response: ILoginFacebookParams) {
  const type = 'facebook'
  const { authResponse } = response
  if (authResponse) {
    handleSubmit(type, authResponse)
  } else {
    handleUpdate(type, false)
  }
}
function googleCallback(response: ILoginGoogleParams) {
  const type = 'google'
  handleUpdate(type, true)
  handleSubmit(type, response)
}

function handleUpdate(type: string, value: boolean) {
  emits('update:disabled', { type, value })
}
function handleGeLogin() {
  console.log('handleGeLogin')
  if (loadOption.value.google.status !== 1) {
    useModal({
      message: t('loading'),
      duration: 1500
    })
  }
}
function handleSubmit(type: string, value: ICallBackParams) {
  let query: ICallBackParams
  switch (type) {
    case 'google':
      query = {
        credential: value.credential
      }
      break
    case 'facebook':
      query = {
        access_token: value.accessToken
      }
      break
    case 'apple':
      query = {}
      break
    default:
      break
  }
  // value-根据登录接口需要的参数名处理过的参数对象  origin-第三方登录后返回的参数对象
  emits('submit', { type, value: query, origin: value })
}
function loadScript(url: string, id: string, callback: Function) {
  let script: any = document.getElementById(id)
  if (script) {
    callback(1)
    return
  }
  script = document.createElement('script')
  script.setAttribute('id', id)
  script.setAttribute('type', 'text/javascript')
  script.async = true
  script.defer = true
  if (script.readyState) {
    script.onreadystatechange = function () {
      if (script.readyState == 'loaded' || script.readyState == 'complete') {
        script.onreadystatechange = null
        callback && callback(1)
      } else {
        callback && callback(0)
        document.getElementsByTagName('head')[0].removeChild(script)
      }
    }
  } else {
    //其他浏览器
    script.onload = function () {
      callback && callback(1)
    }
    script.onerror = function () {
      callback && callback(0)
      document.getElementsByTagName('head')[0].removeChild(script)
    }
  }
  script.setAttribute('src', url)
  document.getElementsByTagName('head')[0].appendChild(script)
}

function googleInit() {
  try {
    // @ts-ignore
    google?.accounts?.id.initialize({
      client_id: '************-6r2d10k2n9r586h5toh5leucug0hsnqo.apps.googleusercontent.com',
      callback: googleCallback
    })
    // @ts-ignore
    google?.accounts.id?.renderButton(document.getElementById('google-btn'), {
      type: 'standard',
      shape: 'pill',
      logo_alignment: 'center',
      text: 'continue_with',
      // theme: "outline",
      // size: "large",
      width: '48px'
      // locale: "fr" // 语言
    })
    // google?.accounts?.id.prompt(); // also display the One Tap dialog
  } catch (error) {
    // console.log(error)
  }
}

function appleInit() {
  setTimeout(() => {
    // @ts-ignore
    AppleID?.auth.init({
      clientId: 'me.livco',
      // redirectURI: 'https://livco-dev.wujialin.top/api/apiv1/login/apple_login',
      redirectURI: 'https://livco.me/api/apiv1/login/apple_login', // 苹果后台只能配置一个重定向链接  只有该接口有用
      scope: 'name email',
      state: 'web_login'
    })
  }, 100)
}
function loadSDK() {
  console.log('loadSDK')
  if (props.show?.google) {
    // googleInit()
    loadScript(loadOption.value.google.url, loadOption.value.google.id, (status: number) => {
      console.log('googlesdk', status)
      loadOption.value.google.status = status
      if (status === 1) {
        googleInit()
      }
    })
  }
  if (props.show?.facebook) {
    loadScript(loadOption.value.facebook.url, loadOption.value.facebook.id, (status: number) => {
      console.log('facebooksdk', status)
      loadOption.value.facebook.status = status
      // if(status === 1) {
      // }
    })
  }
  if (props.show?.apple) {
    loadScript(loadOption.value.apple.url, loadOption.value.apple.id, (status: number) => {
      console.log('apple', status)
      loadOption.value.apple.status = status
      if (status === 1) {
        appleInit()
      }
    })
  }
}

loadSDK()
onMounted(() => {})
</script>
<template>
  <div class="thirdBtn-container">
    <div class="thirdBtn flex-center-center svgbox">
      <!-- :disabled="disabled[key]" -->
      <van-button
        v-for="item in webButton"
        @click="item.click"
        :key="item.val"
        :icon="item.icon === 'google' ? googleIcon : ''"
        :round="true"
      >
        <template #default>
          <div
            :class="item.icon"
            v-if="item.icon !== 'google'"
            class="fsize-42"
          ></div>
          <div
            id="google-btn"
            v-else
          ></div>
        </template>
      </van-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.van-button__icon) {
  font-size: 42px;
}

:deep(.van-button--round) {
  width: 42px;
  background: #1a1a1a;
}

.svgbox {
  gap: 24px;
  font-size: 42px;
}

.thirdBtn {
  position: relative;
}

#google-btn {
  position: absolute;
  inset: 0;
  cursor: pointer;
  opacity: 0;
}

.apple,
.facebook {
  width: 42px;
  height: 42px;
  background: url('~@/assets/images/login/apple-btn.png') no-repeat;
  background-size: contain;
}

.facebook {
  background: url('~@/assets/images/login/facebook-btn.png') no-repeat;
  background-size: contain;
}
</style>
