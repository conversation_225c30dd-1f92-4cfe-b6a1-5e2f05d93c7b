<script setup lang="ts">
import useAppStore from '@/stores/modules/app'
import { languageColumns, locale } from '@/utils/i18n'
import useHomeStore from '@/stores/modules/home'

const appStore = useAppStore()
const showPopup = ref(true)
const homeStore = useHomeStore()

function close() {
  appStore.showLangPopup = false
}
function onLanguageConfirm(value: string, text: string) {
  if (appStore.languageType === 'audio') {
    appStore.audioLanguage = value
    appStore.audioLanguageText = text
    localStorage.setItem('audioLanguage', value)
    localStorage.setItem('audioLanguageText', text)
  } else {
    locale.value = value
    appStore.language = value
    appStore.languageText = text
  }
  close()
}
watch(
  () => appStore.language,
  (val) => {
    if (val) {
      // localStorage.setItem('language', val)
      console.log(val, '变化了')
      homeStore.setUpdateData(true)
      homeStore.setUpdateAlbum(true)
    }
  },
  {
    immediate: true
  }
)
</script>
<template>
  <van-popup
    v-model:show="showPopup"
    position="bottom"
    :style="{
      background: '#232222',
      'border-radius': '24px 24px 0px 0px',
      padding: '24px 16px'
    }"
    @click-overlay="close"
    :close-on-click-overlay="false"
  >
    <div class="mb-16 flex-between-center">
      <div class="title">{{ appStore.languagePopupTitle }}</div>
    </div>

    <van-cell-group class="langCell">
      <van-cell
        v-for="item in languageColumns"
        :key="item.value"
        :title="item.text"
        @click="onLanguageConfirm(item.value as string, item.text as string)"
        :class="{ lightCell: item.value === (appStore.languageType === 'audio' ? appStore.audioLanguage : appStore.language) }"
      >
        <template
          #right-icon
          v-if="item.value === (appStore.languageType === 'audio' ? appStore.audioLanguage : appStore.language)"
        >
          <SvgIcon
            icon-class="selected-round"
            class="fsize-20"
          />
        </template>
      </van-cell>
    </van-cell-group>
  </van-popup>
</template>
<style lang="scss" scoped>
@import 'src/assets/styles/variables';

:deep(.van-cell-group) {
  border-radius: 16px;
}

:deep(.van-hairline--top-bottom) {
  position: static !important;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.skip {
  font-size: 16px;
  font-weight: 500;
  color: $themeColor;
}

.label {
  padding: 16px 0;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

.tagBox {
  display: flex;
  flex-wrap: wrap;
  gap: 9px;
  justify-content: flex-start;

  .tag {
    min-width: 80px;
    height: 32px;
    padding: 0 16px;
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 41px;
  }

  .lightTag {
    background: rgba(255, 53, 242, 30%);
    border: 1px solid $themeColor;
  }
}

.btn {
  width: 327px;
  height: 48px;
  margin: 60px auto 12px;
  font-size: 16px;
  font-weight: 600;
  line-height: 48px;
  color: white;
  text-align: center;
  background: linear-gradient(90deg, $themeColor 0%, $helpColorB 100%);
  border-radius: 40px;
}
</style>
<style scoped lang="scss">
.langCell {
  .van-cell {
    background: #2f2d2d;

    &:nth-of-type(1) {
      border-radius: 16px 16px 0 0 !important;
    }
  }

  .lightCell {
    display: flex;
    align-items: center;
    color: $livCoThemeColor;
  }
}
</style>
