<script setup lang="ts">
import { send_auth_code, login } from '@/api/login'
import useAppStore from '@/stores/modules/app'
import useUserStore from '@/stores/modules/user'
import { showToast } from 'vant'
import useLoginStore from '@/stores/modules/login'
import useHomeStore from '@/stores/modules/home'
import { useModal } from '@/hooks/useModal'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import useNotifyStore from '@/stores/modules/notification.ts'

const router = useRouter()
const route = useRoute()
const loginStore = useLoginStore()
const appStore = useAppStore()
const userStore = useUserStore()
const notifyStore = useNotifyStore()
const homeStore = useHomeStore()
const emits = defineEmits<{
  (e: 'go-preference'): void
}>()
const { t } = useI18n()
const show = computed(() => {
  return appStore.showLogin
})

const datas = reactive<{
  check1: boolean
  check2: boolean
  check3: boolean
  countdown: number
  email: string
  code: string
  loading: boolean
  codeLoading: boolean
  [key: string]: any
}>({
  check1: true,
  check2: true,
  check3: true,
  countdown: 0,
  email: '',
  code: '',
  loading: false,
  codeLoading: false
})

// function changeCheck(type: string) {
//   datas['check' + type] = !datas['check' + type]
// }
// function loginRegister() {
//   if (!datas.check1 || !datas.check2 || !datas.check3) {
//     showToast(t('CheckTerms'))
//     return
//   }
// }

function getCode() {
  if (datas.email === '') return showToast(t('enterEmail'))
  const emailRegex =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  if (!emailRegex.test(datas.email)) {
    return showToast(t('invalidEmailFormat'))
  }
  if (datas.countdown > 0) {
    return // 如果倒计时未结束，则不执行
  }
  let data = {
    action: 'login-register',
    type: 'email',
    email: datas.email
  }
  datas.codeLoading = true
  send_auth_code(data)
    .then(() => {
      datas.codeLoading = false
      datas.countdown = 60
      const intervalId = setInterval(() => {
        if (datas.countdown > 0) {
          datas.countdown--
        } else {
          clearInterval(intervalId)
        }
      }, 1000)
    })
    .catch(() => {
      datas.codeLoading = false
    })
}
// function appleLoginPopup() {
//   loginStore.btnLoading = true
//   datas.showEros = false
// }
function loginFn() {
  const verify = verifyHandle()
  if (!verify) return
  loginStore.btnLoading = true
  let data = {
    code: datas.code,
    email: datas.email,
    register_channel: sessionStorage.getItem('is_ugphone') || undefined,
    invite_code: localStorage.getItem('invite_code') || undefined
  }
  login(data)
    .then((res) => {
      sessionStorage.removeItem('is_phone')
      loginStore.btnLoading = false
      if (appStore.loginRedirectPath) {
        router.push(appStore.loginRedirectPath)
        appStore.loginRedirectPath = ''
      }
      notifyStore.triggerChatPageEvent('loginSendChat', {
        data: {},
        // @ts-ignore
        aiId: route.params.ai_id
      })
      userStore.getUserInfo()
      userStore.saveLoginInfo(res.data)
      close()
      localStorage.getItem('invite_code') ? localStorage.removeItem('invite_code') : ''
      homeStore.setUpdateBanner(true)
      datas.code = ''
      datas.email = ''
      datas.countdown = 0
    })
    .catch(() => {
      loginStore.btnLoading = false
    })
}
function verifyHandle() {
  if (!datas.email) {
    useModal({
      message: t('enterEmail'),
      duration: 1500
    })
    return false
  }
  if (!datas.code) {
    useModal({
      message: t('enterCaptcha'),
      duration: 1500
    })
    return false
  }
  return true
}

function close() {
  appStore.showLogin = false
  if (homeStore.isUpdateData) {
    router.go(0)
  }
}

function changeLang() {
  appStore.showLangPopup = true
}
function navToHandle() {
  window.open('https://www.facebook.com/ugenie.aichat', '_blank')
}
function openAgreement(type: 'privacy' | 'user') {
  sessionStorage.setItem('agreementType', type)
  appStore.showAgreement = true
}
function openHandle() {
  eventReport({
    event_type: EventTypeEnum.SHOW_LOGIN_PAGE,
    front_address: sessionStorage.getItem('login_front_address') || ''
  }).catch((err) => {
    console.warn(err)
  })
}
</script>
<template>
  <van-popup
    v-model:show="show"
    show-cancel-button
    class="loginDialog flex-center-center"
    :close-on-click-overlay="false"
    @open="openHandle"
  >
    <div class="outbox pt-16 pb-24">
      <!-- <div class="outbox-bg"></div> -->
      <img
        class="outbox-bg"
        src="@/assets/images/login/bgc.png"
        alt=""
      />
      <div
        class="lang pb-16 pr-16 flex-end-center"
        @click="changeLang"
      >
        {{ appStore.languageText }}
        <svg-icon
          icon-class="down-arrow"
          font-size="16px"
        />
      </div>
      <div class="title">{{ t('welcomeToUgenie') }}</div>
      <!--      <template v-if="datas.showEros">-->
      <!--        <div class="padding-16 msg mt-24 mb-16">{{ t('termsConfirmation') }}</div>-->
      <!--        &lt;!&ndash; <div class="pb-16 pl-16 check">-->
      <!--          <svg-icon-->
      <!--            v-show="datas.check1"-->
      <!--            icon-class="checkbox-active"-->
      <!--            @click="changeCheck('1')"-->
      <!--            class="fsize-18 check-icon"-->
      <!--          />-->
      <!--          <svg-icon-->
      <!--            v-show="!datas.check1"-->
      <!--            icon-class="checkbox"-->
      <!--            @click="changeCheck('1')"-->
      <!--            class="fsize-18 check-icon"-->
      <!--          />-->
      <!--          <span class="ml-8 check-text">{{ t('ageConfirmation') }}</span>-->
      <!--        </div> &ndash;&gt;-->
      <!--        <div class="pb-16 pl-16 check">-->
      <!--          <svg-icon-->
      <!--            v-show="datas.check2"-->
      <!--            icon-class="checkbox-active"-->
      <!--            @click="changeCheck('2')"-->
      <!--            class="fsize-18 check-icon"-->
      <!--          />-->
      <!--          <svg-icon-->
      <!--            v-show="!datas.check2"-->
      <!--            icon-class="checkbox"-->
      <!--            @click="changeCheck('2')"-->
      <!--            class="fsize-18 check-icon"-->
      <!--          />-->
      <!--          <span class="ml-8 check-text">{{ t('aiContentNotice') }}</span>-->
      <!--        </div>-->
      <!--        <div class="pb-16 pl-16 check">-->
      <!--          <svg-icon-->
      <!--            v-show="datas.check3"-->
      <!--            icon-class="checkbox-active"-->
      <!--            @click="changeCheck('3')"-->
      <!--            class="fsize-18 check-icon"-->
      <!--          />-->
      <!--          <svg-icon-->
      <!--            v-show="!datas.check3"-->
      <!--            icon-class="checkbox"-->
      <!--            @click="changeCheck('3')"-->
      <!--            class="fsize-18 check-icon"-->
      <!--          />-->
      <!--          <span class="ml-8 check-text"-->
      <!--            >{{ t('readAndConfirmed') }}-->
      <!--            <span-->
      <!--              class="contract"-->
      <!--              @click="openAgreement('privacy')"-->
      <!--              >《{{ t('privacyAgreement') }}》</span-->
      <!--            >、-->
      <!--            <span-->
      <!--              class="contract"-->
      <!--              @click="openAgreement('user')"-->
      <!--              >《{{ t('userAgreement') }}》</span-->
      <!--            >-->
      <!--          </span>-->
      <!--        </div>-->
      <!--        <div-->
      <!--          class="btn flex-center-center"-->
      <!--          @click="loginRegister"-->
      <!--        >-->
      <!--          {{ t('registrationLogin') }}-->
      <!--        </div>-->
      <!--        <div-->
      <!--          class="textBtn"-->
      <!--          @click="tourLoginFn"-->
      <!--        >-->
      <!--          {{ t('guestLogin') }}-->
      <!--        </div>-->
      <!--      </template>-->
      <!--      <template>-->
      <div class="login-divider flex-center-center">
        <span class="mr-12 ml-12">{{ t('emailLogin') }}</span>
      </div>

      <van-field
        v-model="datas.email"
        :placeholder="t('email')"
        bind:change="onChange"
        :border="false"
      />
      <div class="inputCode">
        <van-field
          v-model="datas.code"
          :placeholder="t('verificationCode')"
          bind:change="onChange"
          type="text"
          :border="false"
        />
        <span
          class="getcode"
          @click="getCode"
        >
          <van-loading
            v-if="datas.codeLoading"
            type="spinner"
            color="white"
            size="24px"
          />
          <span v-else>{{ datas.countdown > 0 ? `${datas.countdown}s` : t('getCaptcha') }}</span>
        </span>
      </div>
      <div
        class="btn flex-center-center"
        @click="loginFn"
      >
        <span
          v-if="loginStore.btnLoading"
          class="flex-center-center"
        >
          <span class="mr-8">{{ t('emailLoginOption') }}</span>
          <van-loading
            type="spinner"
            color="white"
            size="16px"
          />
        </span>
        <span v-else>{{ t('emailLoginOption') }}</span>
      </div>
      <div class="msg agree">
        <div class="mb-4">{{ t('loginAndAgree') }}</div>
        <span
          class="contract"
          @click="openAgreement('privacy')"
        >
          {{ t('privacyAgreement') }}
        </span>
        <span
          class="contract ml-8"
          @click="openAgreement('user')"
        >
          {{ t('userAgreement') }}
        </span>
      </div>
      <div class="mb-24 mt-32">
        <ThirdBtn
          :disabled="loginStore.loginBtnDisabled"
          @update:disabled="loginStore.changeDisabled"
          @submit="loginStore.handleLogin"
        />
      </div>
      <div class="txt-banner">
        <span
          class="txt1 mr-2"
          @click="navToHandle"
          >{{ t('unableLogin') }}</span
        >
        <span
          class="txt2"
          @click="navToHandle"
          >{{ t('contactCustomer') }}</span
        >
      </div>
      <!--      </template>-->
    </div>
    <div class="flex-center-center mt-24">
      <div
        class="dialog-close"
        @click="close"
      ></div>
    </div>
  </van-popup>
</template>
<style scoped lang="scss"></style>
<style lang="scss">
@import 'src/assets/styles/variables';

.loginDialog {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;

  .login-divider {
    margin: 20px 0 10px;
    font-size: 14px;
    font-weight: 400;
    line-height: 14px;
    color: rgba(255, 255, 255, 50%);
    text-align: center;

    &::before,
    &::after {
      box-sizing: border-box;
      display: block;
      width: 42px;
      height: 1px;
      content: '';
      background: rgba(255, 255, 255, 10%);
    }
  }

  .van-dialog__footer {
    display: none !important;
  }

  .van-field__control {
    width: 100%;
    height: 42px;
    padding: 0 16px;
    background: #262626;
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 52px;
  }

  .van-cell {
    padding: 6px 24px;
    background-color: transparent;
  }

  .outbox {
    position: relative;
    width: 327px;
    background: #1a1a1a;
    // background-image: linear-gradient(to right, #1a1a1a, #1a1a1a), linear-gradient(220deg, #4b3657, rgba(54, 46, 65, 20%));
    // background-clip: padding-box, border-box;
    // background-origin: padding-box, border-box;
    // border: 2px solid transparent;
    border-radius: 12px;

    .outbox-bg {
      position: absolute;
      top: -1px;
      right: -1px;
      pointer-events: none;
      border-radius: 12px;
    }

    .lang {
      font-size: 12px;
      font-weight: 400;
      color: rgba(255, 255, 255, 50%);
      text-align: end;
    }

    .title {
      font-size: 18px;
      font-weight: 600;
      text-align: center;
    }

    .contract {
      color: $livCoThemeColor;
      text-decoration: underline;
    }

    .msg {
      padding: 0 24px;
      font-size: 12px;
      line-height: 12px;
      color: rgba(255, 255, 255, 50%);
    }

    .check {
      display: flex;
      align-items: center;
      font-size: 12px;
    }

    .btn {
      width: 279px;
      height: 48px;
      margin: 18px auto 12px;
      font-size: 16px;
      font-weight: 600;
      color: $livCoTextColor;
      text-align: center;
      background: $livCoThemeColor;
      border-radius: 40px;
    }

    .textBtn {
      width: 259px;
      margin: 0 auto;
      font-size: 14px;
      font-weight: 400;
      color: rgba(255, 255, 255, 50%);
      text-align: center;
    }

    .inputCode {
      position: relative;

      .getcode {
        position: absolute;
        top: 50%;
        right: 35px;
        font-size: 14px;
        font-weight: 500;
        color: $livCoThemeColor;
        transform: translateY(-50%);
      }
    }

    .agree {
      line-height: 120%;
      text-align: center;
    }

    .txt-banner {
      padding: 0 24px;
      line-height: 12px;
      text-align: center;
      word-break: keep-all;
    }

    .txt1 {
      font-size: 12px;
      font-weight: 400;
      color: #8b8889;
    }

    .txt2 {
      font-size: 12px;
      font-weight: 500;
      color: $livCoThemeColor;
    }
  }

  .dialog-close {
    width: 32px;
    height: 32px;
    background: url('~@/assets/images/login/close-btn.png') no-repeat;
    background-size: contain;
  }

  .check-icon {
    min-width: 18px;
  }
}
</style>
