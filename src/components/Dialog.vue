<script setup lang="ts">
const show = ref(false)
const showDialog = () => {
  show.value = true
}
const closeDiaog = () => {
  show.value = false
}
defineExpose({
  showDialog,
  closeDiaog
})
</script>
<template>
  <van-dialog
    v-model:show="show"
    title="标题"
    show-cancel-button
  >
    <template #default>
      <slot name="default"></slot>
    </template>
    <template #title>
      <slot name="title"></slot>
    </template>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </van-dialog>
</template>
<style lang="scss" scoped></style>
