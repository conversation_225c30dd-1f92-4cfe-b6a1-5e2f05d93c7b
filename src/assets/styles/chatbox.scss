.whole-story-item {
  display: flex;
  column-gap: 8px;
  user-select: none;

  .avatar {
    flex-shrink: 0;
    width: 42px;
    height: 42px;
    margin-top: 10px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border: 2px solid #444457;
    border-radius: 50%;
  }

  .pink {
    border: 2px solid $livCoThemeColor;
  }

  &:first-child {
    margin-top: auto;
  }
}

.story-item-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding-top: 9px;
  overflow-x: auto;

  .story-item {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;
    font-size: 14px;
  }

  &::-webkit-scrollbar {
    display: none;
  }
}

%story-item-common {
  display: flex;
  column-gap: 8px;
  width: fit-content;
  max-width: 95%;
  backdrop-filter: blur(32px);
  border-radius: 12px;

  .story-record {
    flex: 1;
    width: 100%;
    padding: 12px;
    overflow-wrap: break-word;
  }
}

.story-item-agent {
  @extend %story-item-common;

  flex-direction: column;
  align-items: flex-start;
  align-self: start;
  color: #2d2d2d;
  background: rgba(255, 251, 235, 95%);
}

.story-item-user {
  @extend %story-item-common;

  flex-direction: row-reverse;
  align-items: center;
  align-self: end;
  background: #282827e5;
}

.story-item-narration {
  position: relative;
  padding: 10px 16px;
  margin: 18px 0;
  line-height: 16px;
  color: rgba(255, 255, 255, 70%);
  text-align: center;
  background: linear-gradient(
    90deg,
    rgba(26, 26, 26, 0%) 0%,
    rgba(26, 26, 26, 70%) 25%,
    rgba(26, 26, 26, 90%) 50%,
    rgba(26, 26, 26, 70%) 75%,
    rgba(26, 26, 26, 0%) 100%
  );
  border: 1px solid;
  border-image: linear-gradient(90deg, rgba(255, 255, 255, 0%), rgba(255, 255, 255, 50%), rgba(255, 255, 255, 0%)) 1 1;
}

.story-item-background {
  position: relative;
  padding: 10px 16px;
  font-family: 'AlibabaPuHuiTi Heavy', serif;
  font-size: 12px;
  line-height: 16px;
  color: #fff4db;
  text-align: center;
  background: linear-gradient(
    90deg,
    rgba(26, 26, 26, 0%) 0%,
    rgba(26, 26, 26, 70%) 25%,
    rgba(26, 26, 26, 90%) 50%,
    rgba(26, 26, 26, 70%) 75%,
    rgba(26, 26, 26, 0%) 100%
  );
  border: 1px solid;
  border-image: linear-gradient(90deg, rgba(255, 223, 184, 0%), rgba(255, 223, 184, 100%), rgba(255, 223, 184, 0%)) 1 1;
}

.story-item-video {
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 30%);
  border-radius: 12px;

  video {
    object-fit: cover;
    display: block;
    width: 100%;
    aspect-ratio: 16 / 9;
  }

  .replay-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px;
    background: rgba(34, 34, 34, 80%);
    border-radius: 9px;
  }
}

.story-item-image {
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 30%);
  border-radius: 12px;

  img {
    display: block;
    width: 100%;
    aspect-ratio: 16 / 9;
  }
}

.story-item-reward {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;

  .reward-line {
    flex: 1;
    height: 1px;
    background: rgba(255, 255, 255, 30%);
    border-radius: 5px;
  }

  .reward-content {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 400;
    line-height: 12px;
    color: rgba(255, 255, 255, 80%);
    text-align: center;
    background: rgba(32, 32, 32, 60%);
    border-radius: 28px;
  }
}

.story-item-end {
  padding: 16px 12px;
  background: rgba(40, 41, 42, 80%);
  border-radius: 16px;

  .end-content {
    display: flex;
    column-gap: 4px;
    align-items: flex-start;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: rgba(255, 255, 255, 70%);
    text-align: left;
  }
}

.story-skip {
  display: flex;
  column-gap: 4px;
  align-items: center;
  justify-content: center;
  width: fit-content;
  padding: 6px 12px;
  margin-top: 8px;
  background: rgba(54, 54, 54, 60%);
  border-radius: 12px;
}

.whole-chat-item {
  user-select: none;

  &:first-child {
    margin-top: auto;
  }
}

.chat-item-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  overflow-x: auto;
  scroll-snap-type: x mandatory;

  &::-webkit-scrollbar {
    display: none;
  }

  .chat-item {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;
    padding: 0 12px;
    font-size: 14px;
    scroll-snap-align: start;

    &:has(.narration-normal) {
      padding: 0;
    }
  }

  &:has(.ai-comment) {
    padding-top: 12px;
  }
}

%chat-item-common {
  display: flex;
  column-gap: 8px;
  width: fit-content;
  max-width: 95%;
  backdrop-filter: blur(32px);
  border-radius: 12px;

  .chat-record {
    flex: 1;
    width: 100%;
    padding: 12px;
    overflow-wrap: break-word;

    :deep(.van-popover__content) {
      border-radius: 12px;
    }

    :deep(img) {
      width: 100%;
      min-width: 300px;
      min-height: 200px;
    }
  }
}

.chat-selfie-image {
  position: relative;
  width: 138px;
  height: 168px;
  overflow: hidden;
  border-radius: 16px;

  .van-img {
    width: 138px;
    height: 168px;
  }

  img {
    width: 138px;
    height: 168px;
    object-fit: contain;
  }

  .selfie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    row-gap: 6px;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}

.chat-item-left {
  @extend %chat-item-common;

  flex-direction: column;
  align-items: flex-start;
  align-self: start;
  color: #2d2d2d;
  background: rgba(255, 251, 235, 95%);
}

.chat-item-right {
  @extend %chat-item-common;

  flex-direction: row-reverse;
  align-items: center;
  align-self: end;
  background: #282827e5;
}

.chat-item-ai-tips {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;

  .tips {
    width: auto;
    padding: 5px 32px;
    font-size: 11px;
    font-weight: 400;
    color: rgba(255, 255, 255, 50%);
    background: rgba(21, 21, 21, 60%);
    backdrop-filter: blur(16px);
    border-radius: 28px;
  }
}

.chat-item-phone {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;

  .phone {
    flex-shrink: 0;
    width: auto;
    padding: 5px 32px;
    font-size: 11px;
    font-weight: 400;
    background: rgba(100, 100, 100, 60%);
    backdrop-filter: blur(16px);
    border-radius: 28px;
  }

  .line {
    width: 100%;
    border-radius: 5px;

    &:first-child {
      background: linear-gradient(90deg, rgba(255, 255, 255, 0%) 0%, #fff 100%);
    }

    &:last-child {
      background: linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0%) 100%);
    }
  }
}

.chat-item-synopsis {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;

  svg {
    flex-shrink: 0;
  }

  .synopsis {
    display: flex;
    column-gap: 4px;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    padding: 12px 16px;
    background: rgba(17, 17, 17, 90%);
    border-radius: 16px;
  }
}

.chat-item-story {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;
  width: 90%;
  padding: 0 12px;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  color: rgba(255, 255, 255, 50%);
  text-align: left;
  background: rgba(17, 17, 17, 90%);
  border-radius: 16px;

  svg {
    flex-shrink: 0;
  }

  .story {
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    padding: 12px 0;

    .keep-talk {
      display: flex;
      column-gap: 8px;
      align-items: center;
      justify-content: center;
      padding: 6px 8px;
      font-size: 12px;
      font-weight: 600;
      line-height: 12px;
      color: #fff4db;
      text-align: center;
      background: rgba(255, 255, 255, 8%);
      border-radius: 8px;
    }
  }
}

.chat-item-tips {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;

  .content {
    flex-shrink: 0;
    width: auto;
    max-width: 90%;
    padding: 4px 16px;
    font-size: 11px;
    font-weight: 400;
    line-height: 13px;
    color: rgba(255, 255, 255, 70%);
    text-align: center;
    background: rgba(32, 32, 32, 60%);
    backdrop-filter: blur(16px);
    border-radius: 28px;
  }

  .line {
    width: 100%;
    background: rgba(255, 255, 255, 30%);
    border-radius: 5px;
  }
}

.chat-item-story-img {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
  background-repeat: no-repeat;
  background-position: center;
  background-clip: padding-box;
  background-size: cover;
  border: 2px solid rgba(255, 255, 255, 30%);
  border-radius: 12px;

  .story-flag {
    position: absolute;
    top: 8px;
    left: 8px;
    display: flex;
    column-gap: 2px;
    align-items: center;
    justify-content: center;
    padding: 4px 8px 4px 6px;
    font-size: 12px;
    font-weight: bold;
    line-height: 14px;
    color: #fff;
    text-align: left;
    background: rgba(23, 23, 23, 70%);
    border-radius: 8px;
  }

  .enter-story {
    position: absolute;
    right: 0;
    bottom: 0;
    padding: 8px 16px 7px 14px;
    font-size: 12px;
    font-weight: 600;
    line-height: 14px;
    color: #fff;
    text-align: left;
    background: rgba(2, 2, 2, 80%);
    border-radius: 12px 0;
  }
}

.chat-item-unlock {
  display: flex;
  column-gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;

  .unlock {
    width: auto;
    padding: 8px 16px;
    font-size: 11px;
    line-height: 12px;
    color: #fff;
    text-align: center;
    background: rgba(21, 21, 21, 60%);
    backdrop-filter: blur(16px);
    border-radius: 28px;
  }

  .go {
    font-weight: 600;
    color: #ff35f2;
  }
}

.web-search {
  display: flex;
  column-gap: 4px;
  align-items: center;
  justify-content: center;
  width: fit-content;
  padding: 6px 12px;
  margin-top: 8px;
  background: rgba(54, 54, 54);
  border-radius: 12px;
}
