@use 'sass:list';
@use 'sass:math';

@function generate-px-values($start, $end) {
  $list: ();

  @for $i from $start through $end {
    $list: list.append($list, $i);
  }

  @return $list;
}

@function generate-random-list($count, $range) {
  $list: (); // 初始化空列表

  @for $i from 1 through $count {
    $list: list.append($list, math.random($range), comma); // 生成随机数并添加到列表
  }

  @return $list; // 返回生成的列表
}
