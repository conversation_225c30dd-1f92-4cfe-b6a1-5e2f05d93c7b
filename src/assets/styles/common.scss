@use 'sass:list';

@import 'variables';

@each $value in $px-values {
  @each $abbr, $dir in $directions {
    .m#{$abbr}-#{$value} {
      margin-#{$dir}: #{$value}px;
    }
  }

  @each $abbr, $dir in $directions {
    .p#{$abbr}-#{$value} {
      padding-#{$dir}: #{$value}px;
    }
  }

  .fsize-#{$value} {
    font-size: #{$value}px;
  }

  .margin-#{$value} {
    margin: #{$value}px;
  }

  .padding-#{$value} {
    padding: #{$value}px;
  }

  .cg-#{$value} {
    column-gap: #{$value}px;
  }

  .rg-#{$value} {
    row-gap: #{$value}px;
  }
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.pointer {
  cursor: pointer;
}

.overflow-hidden {
  overflow: hidden;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-column {
  flex-direction: column;
}

.flex-center-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-start-center {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end-center {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-center-start {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.flex-center-end {
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.flex-between-center {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around-center {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-start-end {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
}

.flex-between-start {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.flex-around-start {
  display: flex;
  align-items: flex-start;
  justify-content: space-around;
}

.flex-between-end {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.flex-around-end {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
}

.flex-start-start {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex-1 {
  flex: 1;
}

.center-table {
  td {
    text-align: center !important;
  }

  th {
    text-align: center !important;
  }
}

.bg-white {
  background: #fff;
}

.btn-disabled {
  filter: brightness(50%);
}

.gradient-bg {
  height: 100%;
  overflow: auto;
  background: url('../images/common/page-bg.png') no-repeat;
  background-size: 100% 100%;
}

.overflow-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pt-navbar {
  padding-top: var(--van-nav-bar-height);
}

.mt-navbar {
  margin-top: var(--van-nav-bar-height);
}

.translateY-navbar {
  transform: translateY(var(--van-nav-bar-height));
}

.wave-bar {
  width: 3px;
  height: 4px;
  background: white;
  border-radius: 16px;
  animation: movement 0.8s ease-in-out infinite;
  $bar-count: 40;

  @for $i from 1 through $bar-count {
    $duration-value: list.nth(generate-random-list($bar-count, 15), $i); // 从 $random-list-15 中取值
    $delay-value: list.nth(generate-random-list($bar-count, 15), $i); // 从 $random-list-10 中取值

    &:nth-child(#{$i}) {
      animation-duration: #{calc($duration-value / 10) + 0.5}s; // 0.5s 至 2.0s 的随机持续时间
      animation-delay: #{calc($delay-value / 10)}s; // 0s 至 1.0s 的随机延迟
    }
  }

  @keyframes movement {
    0%,
    100% {
      height: 4px;
    }

    50% {
      height: 100%;
    }
  }
}
