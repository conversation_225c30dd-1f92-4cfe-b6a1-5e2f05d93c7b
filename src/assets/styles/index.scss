@import 'common';
@import 'mixin';
@import 'vant';
@import 'animation';
@import 'chatbox';

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  color-scheme: light;
}

html.dark {
  color-scheme: dark;
  background: #000;
}

body {
  overflow: hidden;
  font-family: <PERSON><PERSON>, 'Microsoft YaHei UI', sans-serif !important;
}

#app {
  position: relative;
  height: 100vh;
  // Dynamic Viewport Units
  height: 100dvh;
  overflow-x: hidden;
  clip-path: inset(0 0 0 0);
}

p {
  margin: 0;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  background: transparent;
}

.slide-fadein-left-enter-active,
.slide-fadein-left-leave-active,
.slide-fadein-right-enter-active,
.slide-fadein-right-leave-active {
  transition:
    opacity 0.3s,
    transform 0.4s,
    -webkit-transform 0.4s;
}

.slide-fadein-left-enter-from,
.slide-fadein-right-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.slide-fadein-left-leave-to,
.slide-fadein-right-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.app-tips-enter-active,
.app-tips-leave-active {
  transition: transform 0.3s ease-out;
}

.app-tips-enter-from,
.app-tips-leave-to {
  transform: translateY(-100%);
}
