// vant输入框没有边框，使用该样式添加默认边框
@mixin field-border {
  &:deep(.van-cell) {
    flex-direction: column;
    height: 100px;
    padding: 0 10px !important;
    overflow: hidden;
  }

  &:deep(.van-field__body) {
    box-sizing: border-box;
    height: 40px;
    padding: 0 16px;
    border: 2px solid #333;
    border-radius: 10px;
  }

  &:deep(.van-field__right-icon) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &:deep(.van-cell__title) {
    font-size: 15px;
    line-height: 2;
    color: #333;
  }

  &:deep(.van-cell__value) {
    user-select: none;
  }
}

@mixin gradation-border-with-radius-and-transparent-bg(
  $borderWidth: 1px,
  $borderColor: linear-gradient(90deg, rgba(57, 56, 57, 0.2) 0%, rgba(57, 56, 57, 0) 100%)
) {
  position: relative;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
    padding: $borderWidth; /* 边框宽度 */
    content: '';
    background: $borderColor;
    border-radius: inherit; /* 继承父元素的圆角 */
    mask:
      linear-gradient(#fff 0 100%) content-box,
      linear-gradient(#fff 0 100%);
    mask-composite: xor;
    mask-composite: exclude; /* 使用 exclude 来实现 XOR 效果 */

    /* 兼容 WebKit 浏览器 */
  }
}

/**
 * 单行文本超出省略
 * @param {Number} line
 */
@mixin singleLine {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  lines: 1;
}

/**
 * 多行文本超出省略
 * @param {Number} line
 */
@mixin multiLine($line) {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: $line;
  line-clamp: $line;
  lines: $line;
  -webkit-box-orient: vertical;
}

@mixin skeleton {
  background-image: linear-gradient(110deg, #2a2a2a 8%, #3a3a3a 18%, #2a2a2a 33%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}