<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 1000006484" clip-path="url(#clip0_929_3947)">
<g id="Vector" filter="url(#filter0_i_929_3947)">
<path d="M8.99998 3.58704C4.6516 -1.66734 0.0604093 2.06612 0 6.09071C0 12.0822 7.2793 17 8.99998 17C10.7207 17 18 12.0822 18 6.09071C17.9396 2.06612 13.3484 -1.66734 8.99998 3.58704Z" fill="#F535AC"/>
</g>
</g>
<defs>
<filter id="filter0_i_929_3947" x="0" y="1" width="18" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.516683 0 0 0 0 0.79322 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_929_3947"/>
</filter>
<clipPath id="clip0_929_3947">
<rect width="18" height="18" fill="white"/>
</clipPath>
</defs>
</svg>
