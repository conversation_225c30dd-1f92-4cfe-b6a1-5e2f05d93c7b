<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 1000006976" clip-path="url(#clip0_3354_11449)">
<g id="uYAcL5_2_">
<g id="Group">
<path id="Vector" d="M9.99644 11.6516C10.078 11.5496 10.1596 11.4476 10.2344 11.3593C10.6762 10.8426 11.2473 10.5843 11.9203 10.5231C12.6408 10.4551 13.341 10.6115 14.0412 10.7542C14.5783 10.863 15.1085 10.9718 15.6523 11.0601C15.9311 11.1077 16.2166 11.0873 16.4953 11.033C16.706 10.9922 16.7604 11.033 16.7604 11.2505C16.7604 11.5972 16.7604 11.9439 16.7604 12.2906C16.7604 12.3925 16.774 12.4945 16.808 12.5897C17.2091 13.7453 17.6102 14.901 18.0112 16.0567C18.0316 16.1042 18.0384 16.1586 18.0656 16.2334C17.9841 16.2402 17.9161 16.247 17.8481 16.247C15.7815 16.247 13.7149 16.247 11.6415 16.247C11.39 16.247 11.1317 16.247 10.8802 16.247C10.7918 16.247 10.7238 16.2742 10.649 16.3286C10.5403 16.4101 10.4111 16.4849 10.2752 16.5121C9.92166 16.5869 9.57496 16.5733 9.28945 16.3014C9.25546 16.2742 9.20788 16.2538 9.16029 16.247C9.1127 16.2402 9.06512 16.247 9.01753 16.247C6.76061 16.247 4.50368 16.247 2.24676 16.247C2.17878 16.247 2.1108 16.2402 2.01562 16.2402C2.03602 16.1654 2.04961 16.111 2.06321 16.0567C2.4303 14.9486 2.81779 13.8541 3.19847 12.7528C3.22566 12.6713 3.23926 12.5761 3.23926 12.4809C3.24606 12.0866 3.23926 11.6924 3.23926 11.2981C3.23926 11.2505 3.23926 11.2029 3.24606 11.1553C3.26645 11.0398 3.32763 10.9922 3.4432 11.0194C3.97344 11.1417 4.49688 11.0669 5.02033 10.9582C5.57776 10.8494 6.1284 10.7135 6.68583 10.6115C6.99854 10.5571 7.31804 10.5095 7.63754 10.5027C8.33094 10.4823 8.98354 10.6387 9.52058 11.1213C9.66334 11.2437 9.7789 11.4 9.90807 11.536C9.92846 11.5632 9.95565 11.604 9.99644 11.6516ZM2.85178 15.8663C3.23246 15.7236 3.57236 15.5808 3.92585 15.4584C4.65324 15.2137 5.39422 15.0574 6.16239 14.9962C6.60425 14.9622 7.05292 14.9622 7.48799 15.0166C8.21537 15.0981 8.90876 15.2885 9.56817 15.608C9.67694 15.6624 9.68373 15.6556 9.76531 15.5332C9.75171 15.5264 9.73812 15.5128 9.72452 15.506C9.60895 15.4516 9.48659 15.3973 9.37103 15.3429C8.20178 14.8126 6.97814 14.6155 5.70012 14.799C4.95914 14.9078 4.25895 15.1321 3.57916 15.438C3.30044 15.5672 3.23246 15.5196 3.23246 15.2137C3.23246 14.9894 3.23246 14.765 3.23246 14.5407C3.22566 14.5407 3.21887 14.5407 3.21207 14.5407C3.0965 14.9758 2.97414 15.4108 2.85178 15.8663ZM10.1732 15.506C10.248 15.6148 10.3159 15.6284 10.4179 15.5808C10.6219 15.4856 10.8326 15.3973 11.0501 15.3225C12.1242 14.9486 13.2255 14.8738 14.3471 15.0166C15.2717 15.1321 16.1554 15.3973 17.0051 15.7711C17.0391 15.7847 17.0663 15.7915 17.1207 15.8119C16.9983 15.3769 16.8828 14.9622 16.774 14.5475C16.7536 14.7786 16.7536 15.0098 16.7536 15.2341C16.7536 15.2817 16.7536 15.3293 16.7468 15.3769C16.7196 15.4924 16.6584 15.5332 16.5429 15.4992C16.5089 15.4856 16.4749 15.4652 16.4409 15.4516C16.1486 15.3361 15.8631 15.2069 15.564 15.1117C14.6395 14.799 13.6945 14.6699 12.7224 14.7582C12.0562 14.8194 11.4104 14.9826 10.7918 15.2341C10.5811 15.3225 10.3839 15.4176 10.1732 15.506ZM15.8767 15.6556C14.1092 15.0981 12.3485 15.0302 10.6015 15.7915C10.6558 15.9139 10.7306 15.8731 10.8054 15.8459C11.5872 15.54 12.4029 15.4312 13.2391 15.4312C13.5382 15.4312 13.8305 15.4448 14.1296 15.4652C14.4287 15.4856 14.721 15.5128 15.0201 15.5468C15.2989 15.574 15.5912 15.6148 15.8767 15.6556ZM4.36092 15.6216C4.39491 15.6148 4.4357 15.6148 4.46969 15.608C5.08831 15.5128 5.71372 15.4516 6.33913 15.4312C6.66544 15.4176 6.99854 15.4312 7.32484 15.4584C7.63754 15.4788 7.95025 15.5128 8.25616 15.5808C8.56887 15.6488 8.86798 15.7575 9.17389 15.8459C9.23507 15.8663 9.29625 15.9071 9.34383 15.7983C7.70552 15.0709 6.04002 15.1253 4.36092 15.6216ZM9.29625 14.9962C8.18138 14.194 6.50228 14.2076 5.46899 14.5543C6.7878 14.3232 8.05902 14.4863 9.29625 14.9962ZM14.6598 14.5815C13.2663 14.1328 11.6415 14.296 10.7646 14.9554C12.0222 14.4591 13.3206 14.33 14.6598 14.5815Z" fill="white"/>
<g id="Vector_2" filter="url(#filter0_d_3354_11449)">
<path d="M10.2767 8.84527C10.1673 8.62635 10.0852 8.38918 9.93924 8.19762C9.79329 8.00607 9.58349 7.86924 9.38281 7.69593C9.57437 7.51349 9.78417 7.35842 9.93012 7.16686C10.0669 6.97531 10.149 6.73814 10.2494 6.5101C10.2676 6.5101 10.2859 6.5101 10.3041 6.50098C10.4136 6.71078 10.4957 6.94794 10.6416 7.1395C10.7876 7.33106 10.9974 7.477 11.198 7.65944C11.1159 7.74154 11.043 7.83275 10.9518 7.89661C10.7511 8.04255 10.6142 8.22499 10.523 8.45303C10.4683 8.58986 10.4044 8.70844 10.3406 8.84527C10.3132 8.84527 10.295 8.84527 10.2767 8.84527Z" fill="#FFF4DB"/>
</g>
<g id="Vector_3" filter="url(#filter1_d_3354_11449)">
<path d="M8.10503 3.10471C7.89297 3.26376 7.73392 3.40955 7.54836 3.52884C7.37606 3.63487 7.28328 3.78066 7.21701 3.96622C7.12424 4.21804 7.0182 4.46987 6.91217 4.70844C6.88566 4.70844 6.85916 4.70844 6.83265 4.70844C6.75312 4.53614 6.66035 4.37709 6.60733 4.20479C6.5013 3.87344 6.31574 3.63487 6.02415 3.46257C5.87836 3.38304 5.75908 3.26376 5.61328 3.15772C5.89161 2.91915 6.19646 2.7336 6.38201 2.46852C6.56757 2.20344 6.66035 1.85883 6.80614 1.50098C6.87241 1.60701 6.91217 1.66002 6.93868 1.71304C6.95193 1.72629 6.95193 1.7528 6.96519 1.76606C7.11098 2.32272 7.42908 2.72034 7.93273 2.97217C7.98574 2.99868 8.02551 3.03844 8.10503 3.10471Z" fill="#FFF4DB"/>
</g>
<g id="Vector_4" filter="url(#filter2_d_3354_11449)">
<path d="M15.0129 7.66757C14.9511 7.42047 14.8687 7.18367 14.8276 6.93657C14.7967 6.73066 14.6937 6.6174 14.4981 6.56593C14.3334 6.52474 14.1789 6.45267 14.0039 6.33942C14.1172 6.28794 14.2201 6.20558 14.3334 6.17469C14.6731 6.08203 14.8585 5.8967 14.879 5.52606C14.8893 5.35103 14.9511 5.1863 14.9923 5.01127C15.0129 5.01127 15.0335 5.01127 15.0541 5.00098C15.0747 5.08334 15.1055 5.15541 15.1261 5.23778C15.1776 5.4334 15.2291 5.62901 15.2703 5.83493C15.2909 5.95848 15.3526 6.02025 15.4659 6.06144C15.6615 6.13351 15.8674 6.20557 16.1248 6.29824C15.9807 6.37031 15.8983 6.44238 15.7954 6.46297C15.3835 6.55563 15.1982 6.81302 15.1879 7.22485C15.1776 7.3484 15.1364 7.46165 15.1158 7.5852C15.1055 7.60579 15.0953 7.62638 15.0747 7.64698C15.0438 7.66757 15.0335 7.66757 15.0129 7.66757Z" fill="#FFF4DB"/>
</g>
<g id="Vector_5" filter="url(#filter3_d_3354_11449)">
<path d="M11.9879 2.87979C11.9166 2.91545 11.8453 2.95111 11.774 2.97251C11.6385 3.00817 11.5886 3.10802 11.5743 3.22926C11.56 3.32911 11.5315 3.42182 11.5101 3.52167C11.4958 3.52167 11.4816 3.52167 11.4673 3.5288C11.4388 3.42896 11.4031 3.33624 11.3889 3.23639C11.3675 3.08662 11.2961 2.9939 11.1392 2.95824C11.075 2.94398 11.018 2.90832 10.9609 2.88692C10.9609 2.87266 10.9609 2.8584 10.9609 2.85126C11.0537 2.80847 11.1392 2.75855 11.232 2.73002C11.3175 2.70149 11.3532 2.64444 11.3746 2.55885C11.396 2.43048 11.4245 2.30923 11.453 2.18799C11.4673 2.18799 11.4816 2.18799 11.4958 2.18799C11.5244 2.3021 11.5672 2.40908 11.5814 2.52319C11.6028 2.6587 11.667 2.73715 11.8025 2.76568C11.8738 2.77994 11.938 2.8156 12.0022 2.84413C11.9879 2.85126 11.9879 2.86553 11.9879 2.87979Z" fill="#FFF4DB"/>
</g>
<g id="Vector_6" filter="url(#filter4_d_3354_11449)">
<path d="M4.00391 7.27633C4.02215 7.23984 4.02215 7.23072 4.02215 7.23072C4.35965 7.14862 4.48736 6.91146 4.5056 6.58307C4.5056 6.56483 4.53297 6.55571 4.56945 6.50098C4.60594 6.6378 4.65155 6.75639 4.66979 6.86585C4.69716 7.00267 4.76101 7.08477 4.89784 7.12126C4.97081 7.1395 5.04379 7.18511 5.16237 7.23984C5.04379 7.29457 4.97081 7.34018 4.88872 7.36754C4.76101 7.41315 4.67892 7.48613 4.66979 7.63207C4.66067 7.74154 4.61506 7.851 4.5877 7.96046C4.56946 7.96046 4.55121 7.96046 4.54209 7.96958C4.51472 7.86924 4.46912 7.77802 4.45999 7.66856C4.43263 7.50437 4.35965 7.38579 4.18634 7.35842C4.12249 7.34018 4.06776 7.30369 4.00391 7.27633Z" fill="#FFF4DB"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_3354_11449" x="7.38281" y="4.50098" width="5.81641" height="6.34424" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.889985 0 0 0 0 0.633285 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3354_11449"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3354_11449" result="shape"/>
</filter>
<filter id="filter1_d_3354_11449" x="3.61328" y="-0.499023" width="6.49219" height="7.20752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.889985 0 0 0 0 0.633285 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3354_11449"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3354_11449" result="shape"/>
</filter>
<filter id="filter2_d_3354_11449" x="12.0039" y="3.00098" width="6.12109" height="6.6665" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.889985 0 0 0 0 0.633285 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3354_11449"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3354_11449" result="shape"/>
</filter>
<filter id="filter3_d_3354_11449" x="8.96094" y="0.187988" width="5.04297" height="5.34082" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.889985 0 0 0 0 0.633285 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3354_11449"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3354_11449" result="shape"/>
</filter>
<filter id="filter4_d_3354_11449" x="2.00391" y="4.50098" width="5.16016" height="5.46875" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.889985 0 0 0 0 0.633285 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3354_11449"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3354_11449" result="shape"/>
</filter>
<clipPath id="clip0_3354_11449">
<rect width="20" height="20" fill="white"/>
</clipPath>
</defs>
</svg>
