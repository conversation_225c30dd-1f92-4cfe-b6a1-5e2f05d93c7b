import { ChatRecordTypeEnum, RecordEnum, StoryRecordTypeEnum, StoryRewardGiftType } from '@/enums'

export const BOOL_NUMBER = {
  YES: 1,
  NO: 0
}

export const BOOL_STRING = {
  YES: '1',
  NO: '0'
}

export const MQTT_LOCALSTORAGE_KEY: string = 'EROS_MQTT'

export const STORY_BOX_STYLE_CLASS = {
  [StoryRecordTypeEnum.USER]: 'story-item-user',
  [StoryRecordTypeEnum.AGENT]: 'story-item-agent',
  [StoryRecordTypeEnum.NPC]: 'story-item-agent',
  [StoryRecordTypeEnum.BACKGROUND]: 'story-item-background',
  [StoryRecordTypeEnum.VIDEO]: 'story-item-video',
  [StoryRecordTypeEnum.IMAGE]: 'story-item-image',
  [StoryRecordTypeEnum.NARRATION]: 'story-item-narration',
  [StoryRecordTypeEnum.REWARD]: 'story-item-reward',
  [StoryRecordTypeEnum.END]: 'story-item-end'
}

export const CHAT_BOX_STYLE_CLASS = {
  [ChatRecordTypeEnum.USER]: 'chat-item-right',
  [ChatRecordTypeEnum.AI]: 'chat-item-left',
  [ChatRecordTypeEnum.IMAGE]: 'chat-selfie-image',
  [ChatRecordTypeEnum.PHONE]: 'chat-item-tips',
  [ChatRecordTypeEnum.STORY_IMAGE]: 'chat-item-story-img',
  [ChatRecordTypeEnum.SYNOPSIS]: 'chat-item-synopsis',
  [ChatRecordTypeEnum.TIPS]: 'chat-item-tips',
  [ChatRecordTypeEnum.GIFT]: 'chat-item-tips',
  [ChatRecordTypeEnum.WARN]: 'chat-item-ai-tips'
}

export const STORY_REWARD_ICON_TYPE = {
  [StoryRewardGiftType.GIFT]: 'reward-gift',
  [StoryRewardGiftType.COIN]: 'coin',
  [StoryRewardGiftType.CRYSTAL]: 'crystal',
  [StoryRewardGiftType.INTIMACY]: 'intimacy-value-heart'
}

export const CHAT_BOX_STYLE = {
  [RecordEnum.GAME_INVITATION]: 'chat-item-tips',
  [RecordEnum.STORY_IMAGE]: 'chat-item-story-img',
  [RecordEnum.VIDEO_ADS]: 'story-item-video',
  [RecordEnum.PICTURE_ADS]: 'story-item-image',
  [RecordEnum.GIFT]: 'story-item-reward'
}

// 随便传什么，触发内容玩法
export const INIT_QUERY = 'init'
