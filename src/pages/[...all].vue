<script setup lang="ts">
const router = useRouter()

function onBack() {
  if (window.history.state.back) history.back()
  else router.replace('/')
}
</script>

<template>
  <Container>
    <div class="w-full h-full flex-center-center flex-column">
      <van-icon
        name="warn-o"
        size="3em"
      />
      <div>Not found</div>

      <div class="mt-16">
        <VanButton @click="onBack"> Back </VanButton>
      </div>
    </div>
  </Container>
</template>
