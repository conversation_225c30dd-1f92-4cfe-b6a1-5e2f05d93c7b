<script setup lang="ts">
import lottie, { AnimationItem } from 'lottie-web'
import type { ImsgParams } from '@/api/msg/type'
import { getMsgList, msgSetTop, deleteTalk } from '@/api/msg/index'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
import { setLike } from '@/api/mine'
import { useModal } from '@/hooks/useModal'
import useAppStore from '@/stores/modules/app'
import useAgentCreate from '@/stores/modules/agentCreate'
import useUserStore from '@/stores/modules/user'
import { IDialogueRecord } from '@/api/chat/types.ts'
import Loading from '@/assets/lottie/loading.json'

definePage({
  name: 'agentMessage',
  meta: {
    level: 1
  }
})
const { t } = useI18n()
const userStore = useUserStore()
const agentCreateStore = useAgentCreate()
const appStore = useAppStore()
const router = useRouter()
const loadingRef = ref(null)
const loadingInstance = ref<AnimationItem | null>(null)
// const tabList = ref<Array<string>>([t('previousChats'), t('likesOption'), t('myCreations')])
const current = ref(0)
const reportTypeList = [ForwardAddressEnum.MESSAGE_CHAT_HISTORY, ForwardAddressEnum.MESSAGE_LIKE_PAGE, ForwardAddressEnum.MESSAGE_MY_CREATION_PAGE]
const msgList = ref<any>([])
const loading = ref(false)
const isRequestAll = ref(false)
// const enumList = [EventTypeEnum.OPEN_MESSAGE_CHAT_HISTORY, EventTypeEnum.OPEN_MESSAGE_LIKE_PAGE, EventTypeEnum.OPEN_MESSAGE_MY_CREATION_PAGE]
const msgParams = ref<ImsgParams>({
  page: 1,
  limit: 10,
  type: 1
})
const swiperObserver = ref<IntersectionObserver | null>(null)
// function chooseTag(i: number) {
//   current.value = i
//   resetDataHandle()
//   msgParams.value.type = i + 1
//   if (loading.value) return
//   if (!userStore.token) return
//   eventReport({
//     event_type: enumList[i] // 打开消息-聊过
//   }).catch((err) => {
//     console.warn(err)
//   })
//   getMsgListHandle()
// }

function resetDataHandle() {
  msgList.value = []
  msgParams.value.page = 1
  isRequestAll.value = false
}

function getMsgListHandle() {
  if (msgParams.value.page === 1) {
    loading.value = true
  }
  getMsgList(msgParams.value)
    .then((res) => {
      if (res.code !== 200) return
      msgList.value.push(...res.data.list)
      if (msgList.value.length >= res.data.count) {
        isRequestAll.value = true
      }
      if (msgList.value.length === 0) return (isRequestAll.value = true)
    })
    .finally(() => {
      loading.value = false
    })
}

function handleTouchStart(event: any, item: { startX: number }) {
  item.startX = event.touches[0].clientX
}

function handleTouchEnd(event: any, item: { startX: number; endX: number; id: string }) {
  item.endX = event.changedTouches[0].clientX
  let leftClassName = current.value === 2 ? 'slide-left-half' : 'slide-left'
  let rightClassName = current.value === 2 ? 'slide-right-half' : 'slide-right'
  if (item.startX - item.endX > 50) {
    // 50是滑动的阈值，可以根据需要调整
    document.getElementById(`outbox${item.id}`).classList.remove(rightClassName)
    document.getElementById(`outbox${item.id}`).classList.add(leftClassName)
  }
  if (item.endX - item.startX > 50) {
    document.getElementById(`outbox${item.id}`).classList.add(rightClassName)
    document.getElementById(`outbox${item.id}`).classList.remove(leftClassName)
  }
}
function goHome() {
  appStore.showLogin = true
}
if (localStorage.getItem('access_token')) {
  getMsgListHandle()
}
function formatDate(dateStr: string) {
  const parts = dateStr.split(' ')
  const dateParts = parts[0].split('-')
  const timeParts = parts[1].split(':')
  const date = new Date(
    parseInt(dateParts[0], 10), // 年
    parseInt(dateParts[1], 10) - 1, // 月（从0开始）
    parseInt(dateParts[2], 10), // 日
    parseInt(timeParts[0], 10), // 小时
    parseInt(timeParts[1], 10), // 分钟
    parseInt(timeParts[2], 10) // 秒
  )
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  let formattedDate = `${month}-${day} ${hours}:${minutes}`
  if (new Date().getFullYear() !== year) {
    formattedDate = `${year}-${formattedDate}`
  }
  return formattedDate
}
function deleteHandle(id: number) {
  switch (current.value) {
    case 0:
      deleteTalkHandle(id)
      break
    case 1:
      setLikeHandle(id)
      break
    default:
      break
  }
}
function setLikeHandle(ai_id: number) {
  const { close } = useModal({
    message: t('loading'),
    autoClose: false,
    loading: true
  })
  resetDataHandle()
  setLike({ ai_id })
    .then((res) => {
      if (res.code === 200) {
        getMsgListHandle()
      }
    })
    .finally(() => {
      close()
    })
}
function deleteTalkHandle(ai_id: number) {
  const { close } = useModal({
    message: t('loading'),
    autoClose: false,
    loading: true
  })
  resetDataHandle()
  deleteTalk({ ai_id })
    .then((res) => {
      if (res.code === 200) {
        getMsgListHandle()
      }
    })
    .finally(() => {
      close()
    })
}
function msgSetTopHandle(ai_id: number, is_top: number) {
  const { close } = useModal({
    message: t('loading'),
    autoClose: false,
    loading: true
  })
  const msgParams = { ai_id, is_top: is_top === 1 ? 0 : 1 }
  resetDataHandle()
  msgSetTop(msgParams)
    .then((res) => {
      if (res.code === 200) {
        getMsgListHandle()
      }
    })
    .finally(() => {
      close()
    })
}
function goDetail(id: number) {
  eventReport({
    event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
    front_address: reportTypeList[current.value],
    ai_id: id
  }).catch((err) => {
    console.warn(err)
  })
  router.push('/chat/' + id)
}
function navToHandle() {
  current.value === 2 ? (agentCreateStore.showAgentCreateModePopup = true) : router.push('/')
}
function observerHandle() {
  if (!document.querySelector('#msgload')) return
  swiperObserver.value = new IntersectionObserver(
    (entries) => {
      if (msgList.value.length === 0) return
      if (entries[0].isIntersecting) {
        msgParams.value.page++
        if (isRequestAll.value) return
        getMsgListHandle()
      }
    },
    {
      root: null,
      threshold: 0
    }
  )
  swiperObserver.value.observe(document.querySelector('#msgload'))
}
watch(
  () => loading.value,
  (val) => {
    if (val) {
      nextTick(() => {
        loadingInstance.value = lottie.loadAnimation({
          container: loadingRef.value, // 容器
          renderer: 'svg', // 通过svg或canvas渲染
          loop: true, // 是否循环
          autoplay: true, // 是否自动播放
          animationData: Loading // 动画文件
        })
      })
    }
  },
  {
    immediate: true
  }
)
watch(
  () => userStore.isLogin,
  (val) => {
    if (val) {
      getMsgListHandle()
    }
  }
)
const lastChatText = computed(
  () =>
    function (item: any) {
      let { output_text = '' } = item.latest_chat_record || {}
      if (output_text) {
        if (Array.isArray(output_text)) {
          console.log(output_text, 'output_text', item.ai_info.name)
          let realText = ''
          const recursionFormat = (text: IDialogueRecord[]) => {
            let lastText = text[text.length - 1]
            const keyName = Object.getOwnPropertyNames(lastText)[0]
            switch (keyName) {
              case 'dialogue':
                lastText[keyName].content.map((item) => {
                  realText = item
                })
                break
              case 'narration_normal':
                realText = lastText[keyName].content
                break
              case 'picture_url':
                realText = `【${t('picture')}】`
                break
              case 'game_status':
                realText = lastText[keyName].explanation
                break
              case 'global_score':
                text.pop()
                recursionFormat(text)
                break
              case 'short_tips':
                realText = lastText[keyName].content
                break
              case 'narration_chapter_title':
                realText = lastText[keyName].content
                break
              case 'chat_lock':
                text.pop()
                recursionFormat(text)
                break
              case 'game_invitation':
                realText = lastText[keyName].invitation_content
                break
              case 'picture_backend_id':
                realText = `【${t('picture')}】`
                break
              case 'video_backend_id':
                realText = `【${t('picture')}】`
                break
              case 'story_image':
                text.pop()
                recursionFormat(text)
                break
            }
          }
          recursionFormat(output_text)
          return realText
        } else {
          console.log(output_text, 'output_text')
          return output_text
        }
      } else {
        return item.ai_info.opening_statement
      }
    }
)
onMounted(() => {
  observerHandle()
})
onUnmounted(() => {
  if (swiperObserver.value) {
    swiperObserver.value.disconnect()
    swiperObserver.value = null
  }
})
</script>

<template>
  <div class="msg-container">
    <div class="head">
      <div class="title">{{ t('message') }}</div>
      <!-- <div class="tagBox">
        <div
          :class="{ tag: true, 'flex-center-center': true, lightTag: current === i }"
          v-for="(item, i) in tabList"
          :key="i"
          @click="chooseTag(i)"
        >
          {{ item }}
        </div>
      </div> -->
    </div>
    <div
      class="nodata flex-center-center flex-column"
      v-if="!userStore.isLogin"
    >
      <img
        src="@/assets/images/AgentMessage/nodata.png"
        alt=""
        style="width: 196px; height: 140px"
      />
      <div class="btn flex-center-center">
        <span @click="goHome">{{ t('loginToView') }}</span>
      </div>
    </div>
    <div
      class="pb-74 pt-52 content-container"
      v-else
    >
      <div
        ref="loadingRef"
        class="loading-global"
        v-if="loading"
      ></div>

      <template v-else>
        <div
          class="nodata flex-center-center flex-column"
          v-if="msgList && msgList.length === 0 && isRequestAll"
        >
          <EmptyIcon
            :text="current === 2 ? t('createLover') : t('emptyDiscover')"
            url="msg"
            :width="196"
            :height="114"
          />
          <div
            class="btn mt-32 flex-center-center"
            @click="navToHandle"
          >
            <span>{{ current === 2 ? t('goCreate') : t('goDiscover') }}</span>
          </div>
        </div>
        <template v-else>
          <div
            v-for="item in msgList"
            :key="item.id"
            :id="`outbox${item.id}`"
            @touchstart="handleTouchStart($event, item)"
            @touchend="handleTouchEnd($event, item)"
            @click="goDetail(item.ai_id)"
          >
            <div
              class="flex-between-center itembox"
              :class="item.is_top === 1 ? 'top' : ''"
            >
              <div class="flex-start-center itembox-left">
                <van-image
                  :src="item.ai_info.avatar_url"
                  class="mr-8"
                />
                <div class="flex-center-start flex-column lefbox-right">
                  <div
                    class="flex-between-center"
                    style="width: 100%"
                  >
                    <div class="name overflow-text">{{ item.ai_info.name }}</div>
                    <div
                      class="date"
                      v-if="item?.latest_chat_record?.create_time || item.last_record_time"
                    >
                      {{ formatDate(item?.latest_chat_record?.create_time || item.last_record_time) }}
                    </div>
                  </div>
                  <div class="text flex-between-center">
                    <div class="overflow-text">{{ lastChatText(item) }}</div>
                    <div
                      class="talk-num ml-10"
                      v-if="item.no_read_count > 0"
                    >
                      {{ item.no_read_count > 99 ? '99+' : item.no_read_count }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="slidebtn flex-center-center">
              <SvgIcon
                :icon-class="item.is_top === 1 ? 'cancel-set-top' : 'set-top'"
                class="fsize-24"
                @click.stop="msgSetTopHandle(item.ai_id, item.is_top)"
              />
            </div>
            <div
              class="slidebtn2 flex-center-center"
              v-if="current !== 2"
            >
              <SvgIcon
                icon-class="delete-msg"
                class="fsize-24"
                @click.stop="deleteHandle(item.ai_id)"
              />
            </div>
          </div>
        </template>
      </template>
      <div
        id="msgload"
        class="msgloading flex-center-center"
        v-show="!isRequestAll && msgList.length > 0"
      >
        <img
          class="loading-icon"
          src="../../assets/images/index/loading-icon.png"
          alt="loading"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import 'src/assets/styles/variables';

.loading-global {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
}

:deep(.van-image__error) {
  border-radius: 74px;
}

:deep(.van-image__loading) {
  border-radius: 74px;
}

.msg-container {
  height: 100%;
}

.content-container {
  height: 100%;
  overflow: hidden;
  overflow-y: scroll;
  background-color: #000;
}

.head {
  position: fixed;
  top: 0;
  z-index: 999;
  width: 100vw;
  background-color: #000;
}

.title {
  padding: 14px 16px;
  font-size: 20px;
  font-weight: 600;
}

.tagBox {
  display: flex;
  gap: 12px;
  justify-content: flex-start;
  padding: 8px 16px;
  font-size: 14px;

  .tag {
    width: fit-content;
    min-width: 80px;
    height: 32px;
    padding: 0 15px;
    background: #252525;
    border-radius: 16px;
  }

  .lightTag {
    background: rgba(193, 41, 246, 50%);
    border: 1px solid rgba(255, 255, 255, 15%);
  }
}

.nodata {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);

  .text {
    font-size: 13px;
    font-weight: 400;
    color: rgba(255, 255, 255, 50%);
  }

  .btn {
    width: fit-content;
    height: 42px;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    color: $livCoTextColor;
    background: $livCoThemeColor;
    border-radius: 21px;

    span {
      word-break: keep-all;
    }
  }
}

div[id^='outbox'] {
  display: flex;

  .itembox {
    position: relative;
    flex-shrink: 0;
    width: 100%;
    padding: 12px 16px 16px;
    background: #000;

    &.top {
      background-color: #141414;
    }

    .itembox-left {
      flex: 1;

      :deep(.van-image) {
        width: 48px;
        height: 48px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .lefbox-right {
        flex: 1;
        gap: 8px;

        .name {
          max-width: 208px;
          margin-right: 16px;
          font-size: 16px;
          font-weight: bold;
          color: #fff;
        }

        .text {
          width: 288px;
          font-size: 12px;
          font-weight: 400;
          color: #8b8889;

          .talk-num {
            padding: 1px 4px;
            font-size: 12px;
            font-weight: 500;
            line-height: 14px;
            color: #fff;
            background-color: #ff482b;
            border-radius: 41px;
          }
        }
      }
    }

    .date {
      width: fit-content;
      font-size: 12px;
      font-weight: 400;
      color: #8b8889;
      word-break: keep-all;
    }
  }

  .slidebtn {
    flex-shrink: 0;
    width: 60px;
    height: 72px;
    background: #1a1a1a;
  }

  .slidebtn2 {
    flex-shrink: 0;
    width: 60px;
    height: 72px;
    background: #ff482b;
  }
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes moveLeft {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-118px);
  }
}

@keyframes moveRight {
  0% {
    transform: translateX(-118px);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes moveLeft1 {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-59px);
  }
}

@keyframes moveRight1 {
  0% {
    transform: translateX(-59px);
  }

  100% {
    transform: translateX(0);
  }
}

.slide-left {
  animation: moveLeft 0.1s linear 1 forwards;
}

.slide-right {
  animation: moveRight 0.1s linear 1 forwards;
}

.slide-left-half {
  animation: moveLeft1 0.1s linear 1 forwards;
}

.slide-right-half {
  animation: moveRight1 0.1s linear 1 forwards;
}

.msgloading {
  width: 100%;
  margin-top: 8px;

  .loading-icon {
    width: 24px;
    height: 24px;
    animation: loading 1s linear infinite;
  }
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
