<script setup lang="ts">
import useAppStore from '@/stores/modules/app'
// import { queryAlbumList } from '@/api/home'
import type { IAlbumListItem } from '@/api/home/<USER>'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import WaterFall from '@/components/Home/WaterFall.vue'
import Swiper from '@/components/Home/Swiper.vue'
// import TabList from '@/components/Home/tabsList.vue'
import useLoginStore from '@/stores/modules/login'
import useHomeStore from '@/stores/modules/home'

// defineOptions({
//   name: 'Home'
// })
// definePage({
//   name: 'Home',
//   meta: {
//     level: 1,
//     keepAlive: true
//   }
// })
const loginStore = useLoginStore()
const route = useRoute()
const appStore = useAppStore()
const homeStore = useHomeStore()
const router = useRouter()
let routeType = route.query
const tabsList = ref<IAlbumListItem[]>([])
const showHomeTypeSelectPopup = ref(false)
// const tablistLoading = ref(false)
const swiperObserver = ref<IntersectionObserver | null>(null)
const showSearch = ref(false)
const tabActive = ref(0)
function searchClick() {
  eventReport({
    event_type: EventTypeEnum.CLICK_SEARCH_BOX
  }).catch((err) => {
    console.warn(err)
  })
  router.push('/search')
}

// const { t } = useI18n()

function tabLabelClick(e: string) {
  router.push({ name: 'search', query: { searchLabel: e } })
}
const cardListRef = ref<InstanceType<typeof WaterFall> | null>(null)
// const tabListRef = ref<InstanceType<typeof TabList> | null>(null)
const swiperRef = ref<InstanceType<typeof Swiper> | null>(null)

function comfirmHandle(e: { sort_type: number; tag_id: number; name: string }) {
  eventReport({
    event_type: EventTypeEnum.CLICK_TAG,
    tag_name: e.name
  }).catch((err) => {
    console.warn(err)
  })
  tabActive.value = e.tag_id
  cardListRef.value?.clearData()
  cardListRef.value?.queryAiListHandle({
    album_id: e.tag_id,
    sort_type: e.sort_type
  })
}

if (routeType?.invite_code) {
  localStorage.setItem('invite_code', routeType.invite_code as string)
}

function observerHandle() {
  if (!document.querySelector('#swiperRef')) return
  swiperObserver.value = new IntersectionObserver(
    (entries) => {
      // console.log(entries[0].isIntersecting, 'hhhhhh')
      if (!entries[0].isIntersecting) {
        showSearch.value = true
      } else {
        showSearch.value = false
      }
    },
    {
      root: document.querySelector('#home-conyainer'),
      threshold: 0
    }
  )
  swiperObserver.value.observe(document.querySelector('#swiperRef'))
}

// queryAlbumListHandle()
onActivated(() => {
  eventReport({
    event_type: EventTypeEnum.SHOW_HOME_PAGE
  }).catch((err) => {
    console.warn(err)
  })
  // const container = document.querySelector('#home-conyainer')
  // container.scrollTop = scrollTop.value
  noLoginJumpAndPreference()
  observerHandle()
  // 事件重新上报
  swiperRef.value.isEnd = false
  if (homeStore.isUpdateData) {
    swiperRef.value.queryBannerHandle()
    // queryAlbumListHandle()
    homeStore.setUpdateData(false)
  }
  routeType = route.query
  if (routeType?.action === 'apple_login') {
    appStore.showLogin = false
    const params = {
      code: routeType.code as string,
      state: routeType.state as string,
      id_token: routeType.id_token as string
    }
    if (localStorage.getItem('loginType') === 'cancel') {
      router.push({ name: 'Logoff', query: { ...params, action: 'apple_cancel' } })
      return
    }
    loginStore.handleLogin({ type: 'apple', value: params })
  }
  if (localStorage.getItem('access_token')) {
    appStore.showLogin = false
  }
})

onDeactivated(() => {
  if (swiperObserver.value) {
    swiperObserver.value.disconnect()
    swiperObserver.value = null
  }
  if (routeType) {
    routeType = null
  }
})

watch(
  () => homeStore.isUpdateBanner,
  (value) => {
    if (value) {
      swiperRef.value.isEnd = false
      swiperRef.value.queryBannerHandle()
      // queryAlbumListHandle()
      homeStore.setUpdateBanner(false)
    }
  }
)

const noLoginJumpAndPreference = () => {
  if (route.redirectedFrom?.path && route.redirectedFrom?.path !== '/') {
    nextTick(() => {
      appStore.showLogin = true
    })
  }
  console.log(appStore.backHomeShouldShowPreference, '1')
  appStore.showPreference = appStore.backHomeShouldShowPreference
  nextTick(() => {
    appStore.backHomeShouldShowPreference = false
  })
}
</script>

<template>
  <div
    class="index-conyainer"
    id="home-conyainer"
  >
    <Swiper ref="swiperRef" />
    <SearchRowButton
      :showSearch="true"
      v-if="swiperRef && swiperRef.imgList.length === 0 && !swiperRef.firstLoading"
    />
    <SearchButton
      @search-click="searchClick"
      v-if="swiperRef && swiperRef.imgList.length > 0"
    />
    <div class="content-banner">
      <div
        id="tablist"
        class="tablist-observer"
      >
        <!-- <TabsList
          ref="tabListRef"
          :tabsList="tabsList"
          :loading="tablistLoading"
          :tabActive="tabActive"
          @tab-click="tabLabelClickHandle"
          @more-click="showHomeTypeSelectPopup = true"
        /> -->
      </div>
      <div
        id="swiperRef"
        class="target"
      ></div>
      <WaterFall
        ref="cardListRef"
        @tab-click="tabLabelClick"
      />
    </div>
    <SearchRowButton
      :showSearch="showSearch"
      :is-fixed="true"
    />
    <TypeSelectPopup
      v-model="showHomeTypeSelectPopup"
      :tabsList="tabsList"
      @comfirm="comfirmHandle"
    />
  </div>
</template>

<style scoped lang="scss">
.index-conyainer {
  position: relative;
  height: 100%;
  overflow-y: scroll;

  .content-banner {
    position: relative;
    padding: 0 16px;
    padding-bottom: 62px;
  }
}
</style>
