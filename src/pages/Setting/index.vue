<script setup lang="ts">
import useAppStore from '@/stores/modules/app'
import { getFaq } from '@/api/mine'
import { copyText } from '@/utils'
import useUserStore from '@/stores/modules/user.ts'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { updateUserInfo } from '@/api/chat'

definePage({
  name: 'Set',
  meta: {
    level: 2
  }
})
const { t } = useI18n()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const token = ref(localStorage.getItem('access_token'))
// const tourist = ref(localStorage.getItem('tourist'))
const showLogout = ref(false)
const feedbackVisible = ref(false)
let faqCount = sessionStorage.getItem('faqCount') || 0
const showEmail = ref(false)
function logoutFn() {
  showLogout.value = true
}
function changeLang(type: Number) {
  appStore.languagePopupTitle = type === 1 ? t('language') : t('defaultVoicePlayback')
  appStore.languageType = type === 1 ? undefined : 'audio'
  appStore.showLangPopup = true
}

function handleClose() {
  showLogout.value = false
}
function handleSet(type: any) {
  router.push({
    name: type
  })
}
function navToHandle() {
  // 跳转
  // window.open('https://www.facebook.com/profile.php?id=61570151682130', '_blank')
  showEmail.value = true
}
function getFaqHandle() {
  getFaq().then((res) => {
    if (res.code !== 200) return
    if (sessionStorage.getItem('faqCount') == String(res.data.count)) return
    faqCount = String(res.data.count)
    sessionStorage.setItem('faqCount', faqCount)
  })
}

const changBgm = (value: number) => {
  updateUserInfo({
    bgm: value
  })
    .then((res) => {
      if (res.code === ResultEnum.SUCCESS) {
        // 只有在请求成功时才更新状态
      }
      // 如果请求失败，保持原来的状态不变
    })
    .catch(() => {
      userStore.userInformation.bgm = Boolean(userStore.userInformation.bgm) ? 0 : 1
      // 发生错误时，保持原来的状态不变
      console.error('BGM setting failed')
    })
}

const changeGlobalSound = (value: number) => {
  updateUserInfo({
    sound: value
  })
    .then((res) => {
      if (res.code === ResultEnum.SUCCESS) {
        // 只有在请求成功时才更新状态
      }
      // 如果请求失败，保持原来的状态不变
    })
    .catch(() => {
      userStore.userInformation.sound = Boolean(userStore.userInformation.sound) ? 0 : 1
      // 发生错误时，保持原来的状态不变
      console.error('Sound setting failed')
    })
}

function openAgreement(type: 'privacy' | 'user') {
  sessionStorage.setItem('agreementType', type)
  appStore.showAgreement = true
}

getFaqHandle()
</script>
<template>
  <div class="setting-container">
    <van-nav-bar
      :title="t('setup')"
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-22"
          @click="router.push({ name: 'mine' })"
        />
      </template>
    </van-nav-bar>
    <div class="content h-full">
      <van-cell-group class="langCell">
        <van-cell
          :title="t('basicInfo')"
          is-link
          @click="handleSet('Information')"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <van-cell
          :title="t('language')"
          is-link
          :value="appStore.languageText"
          @click="changeLang(1)"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18 ml-4"
            />
          </template>
        </van-cell>
        <van-cell :title="t('globalSoundSwitch')">
          <template #value>
            <van-switch
              size="20"
              v-model="userStore.userInformation.sound"
              :active-value="1"
              :inactive-value="0"
              @change="changeGlobalSound"
            />
          </template>
        </van-cell>
        <van-cell :title="t('bgmSwitch')">
          <template #value>
            <van-switch
              size="20"
              v-model="userStore.userInformation.bgm"
              :active-value="1"
              :inactive-value="0"
              :disabled="!userStore.userInformation.sound"
              @change="changBgm"
            />
          </template>
        </van-cell>
        <van-cell
          :title="t('helpFAQ')"
          is-link
          @click="handleSet('FAQ')"
          v-if="Number(faqCount) > 0"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <van-cell
          :title="t('checkForUpdates')"
          is-link
          @click="handleSet('Version')"
        />
        <van-cell
          :title="t('feedbackOption')"
          is-link
          @click="navToHandle"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <van-cell
          :title="t('inviteCode')"
          is-link
          @click="handleSet('Invitation')"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <van-cell
          :title="t('accountDeactivation')"
          is-link
          @click="handleSet('Logoff')"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <van-cell
          :title="t('report')"
          is-link
          @click="feedbackVisible = true"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
      </van-cell-group>
      <div
        v-if="token"
        class="logout flex-center-center mt-16"
        @click="logoutFn"
      >
        {{ t('logout') }}
      </div>
      <FeedbackDrawer v-model="feedbackVisible" />
    </div>
    <ConfirmLogout
      v-if="showLogout"
      @close="handleClose"
    />
    <van-dialog
      v-model:show="showEmail"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <div class="flex-center-center email flex-column">
        <SvgIcon
          icon-class="close"
          class="fsize-24 close-icon"
          @click="showEmail = false"
        />
        <div>
          <span class="mr-4">{{ t('email') }}: </span>
          <span><EMAIL></span>
          <SvgIcon
            icon-class="copy"
            class="fsize-16 ml-4"
            @click="copyText('<EMAIL>')"
          />
        </div>
        <div class="mt-12">
          <span
            class="contract"
            @click="openAgreement('privacy')"
          >
            {{ t('privacyAgreement') }}
          </span>
          |
          <span
            class="contract"
            @click="openAgreement('user')"
          >
            {{ t('userAgreement') }}
          </span>
        </div>
      </div>
    </van-dialog>
  </div>
</template>
<style lang="scss" scoped>
:deep(.van-hairline--top-bottom::after) {
  border: none !important;
}

:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed !important;
  top: 0;
  width: 100vw;
  background: transparent;
}

.setting-container {
  height: 100%;
  padding-top: 48px;
  overflow: hidden;
}

.email {
  position: relative;
  height: 150px;

  .close-icon {
    position: absolute;
    top: 16px;
    right: 16px;
  }

  .contract {
    font-size: 12px;
    text-decoration: underline;
  }
}

.content {
  height: calc(100vh - 48px);
  padding: 8px 16px;
  overflow-y: scroll;

  .langCell {
    overflow: hidden;
    background: #1a1a1a;
    border-radius: 16px;

    .van-cell {
      display: flex;
      align-items: center;
      padding: 14px 16px;
      background: #1a1a1a;
    }
  }

  :deep(.van-cell__title) {
    font-size: 14px;
    color: #fff;
  }

  .logout {
    width: 343px;
    height: 52px;
    font-size: 14px;
    font-weight: 500;
    color: #ff3f3f;
    background: #1a1a1a;
    border-radius: 16px;
  }
}
</style>
