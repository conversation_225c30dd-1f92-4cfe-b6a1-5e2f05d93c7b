<script setup lang="ts">
import { showToast } from 'vant'
import { delete_account, confirmDeletet } from '@/api/mine/index'
import { send_auth_code, login } from '@/api/login'
// import { ICallBackParams } from '@/api/login/types.ts'
import useUserStore from '@/stores/modules/user'
import useLoginStore from '@/stores/modules/login'
import { useModal } from '@/hooks/useModal'
definePage({
  name: 'Logoff',
  meta: {
    level: 2
  }
})
// interface ITempCallBackParams extends ICallBackParams {
//   is_cancel?: number
//   client_id?: number
// }
const { t } = useI18n()
const loginStore = useLoginStore()
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const routeType = route.query
const check = ref(false)
const showAccount = ref(false)
const email = ref('')
const code = ref('')
const codeLoading = ref(false)
const countdown = ref(0)
const showLogoff = ref(false)
const errorStr = ref('')
const cancelType = ref('')
const btnType = ref('')
const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
const loginBtnDisabled = reactive<{ google: boolean; apple: boolean; facebook: boolean; [key: string]: any }>({
  google: false,
  apple: false,
  facebook: false
})
// const callback = ref<Function>()
// const callbackParams = ref<ITempCallBackParams>({})

// function changeDisabled(val: { type: string; value: boolean }) {
//   loginBtnDisabled[val.type] = val.value
// }
function thirdPartyLogin(_: Function, type: string) {
  showLogoff.value = true
  if (type === 'apple') {
    localStorage.removeItem('loginType')
  }
}
function changeCheck() {
  check.value = !check.value
}
function next() {
  if (!check.value) {
    showToast(t('CheckTerms'))
  } else {
    showAccount.value = true
  }
}

function getCode() {
  if (countdown.value > 0 || !email.value.trim()) {
    return
  }
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
  if (!emailRegex.test(email.value)) {
    errorStr.value = '11'
    return
  }

  let data = {
    action: 'login-register',
    type: 'email',
    email: email.value
  }
  codeLoading.value = true
  send_auth_code(data)
    .then(() => {
      codeLoading.value = false
      countdown.value = 60
      const intervalId = setInterval(() => {
        if (countdown.value > 0) {
          countdown.value--
        } else {
          clearInterval(intervalId)
        }
      }, 1000)
    })
    .catch((err) => {
      errorStr.value = err.msg

      console.log(err)

      codeLoading.value = false
    })
}
function submit() {
  if (code.value.length === 6 && email.value.trim() && emailRegex.test(email.value)) {
    let params = {
      code: code.value,
      email: email.value
    }
    login(params).then((res: any) => {
      if (res.code !== 200) return
      if (res.data.client_id !== userStore.userInformation.client_id) {
        useModal({
          message: t('inconsistentAccounts'),
          duration: 2000
        })
        return
      }
      showLogoff.value = true
      cancelType.value = 'login'
    })
  }
}
function handleClose(data: string) {
  showLogoff.value = false
  loginBtnDisabled[btnType.value] = false
  if (data) {
    errorStr.value = data
  }
}
function blurfn() {
  if (email.value && !emailRegex.test(email.value)) {
    errorStr.value = t('emailFormatError')
  } else {
    errorStr.value = ''
  }
}
function handleConfirm(type: string) {
  console.log('handleConfirm')
  if (type === 'login') {
    deleteAccountHandle()
  } else {
    confirmDeletet()
      .then((res) => {
        if (res.code !== 200) return
        cleanInfo()
        // showLogoff.value = false
        if (type === 'apple') {
          localStorage.removeItem('loginType')
        }
      })
      .catch(() => {
        showLogoff.value = false
      })
      .finally(() => {
        loginBtnDisabled[btnType.value] = false
      })
  }
  // callback
  //   .value(callbackParams.value)
  //   .then((res: any) => {
  //     if (res.code !== 200) return
  //     cleanInfo()
  //     if (type === 'apple') {
  //       localStorage.removeItem('loginType')
  //     }
  //   })
  //   .catch(() => {
  //     showLogoff.value = false
  //   })
  //   .finally(() => {
  //     loginBtnDisabled[btnType.value] = false
  //   })
}
function deleteAccountHandle() {
  let params = {
    code: code.value,
    email: email.value
  }
  delete_account(params)
    .then((res) => {
      if (res.code === 200) {
        cleanInfo()
      }
    })
    .catch((err) => {
      errorStr.value = err.msg
      showLogoff.value = false
    })
}

function cleanInfo() {
  showLogoff.value = false
  router.push({ name: 'LogoffResult' })
}

if (routeType?.action === 'apple_cancel') {
  const params = {
    code: routeType?.code as string,
    state: routeType?.state as string,
    id_token: routeType?.id_token as string
  }
  showAccount.value = true
  loginStore.handleLogin({ type: 'apple', value: params }, thirdPartyLogin)
}
</script>
<template>
  <div class="logoff-container">
    <van-nav-bar
      title=" "
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-24"
          @click="router.push({ name: 'Set' })"
        />
      </template>
    </van-nav-bar>
    <img
      v-if="showAccount"
      src="@/assets/images/mine/logoff-bg.png"
      alt="logoff-bg"
      class="logoff-bg"
    />
    <div class="content">
      <div
        class="title pt-16 pb-24"
        :class="showAccount ? 'padding-b-14' : ''"
      >
        {{ showAccount ? t('accountVerification') : t('accountDeactivation') }}
      </div>
      <div
        v-if="!showAccount"
        class="outbox"
      >
        <p class="head mb-16">{{ t('deactivationTerms') }}</p>
        <p class="txt">{{ t('deactivationTermsContent1') }}</p>
        <p class="txt">{{ t('deactivationTermsContent2') }}</p>
        <p class="txt">{{ t('deactivationTermsContent3') }}</p>
        <p class="txt">{{ t('deactivationTermsContent4') }}</p>
        <p class="txt">{{ t('deactivationTermsContent5') }}</p>
        <p class="pt-8 foot">{{ t('termTips') }}</p>
      </div>
      <template v-else>
        <van-field
          v-model="email"
          placeholder="Email address"
          bind:change="onChange"
          :border="false"
          @blur="blurfn"
        />
        <div class="inputCode">
          <van-field
            v-model="code"
            :border="false"
            placeholder="Verification code"
            bind:change="onChange"
            type="password"
          />
          <span
            class="getcode"
            @click="getCode"
          >
            <van-loading
              v-if="codeLoading"
              type="spinner"
              color="white"
              size="16px"
            />
            <span v-else>{{ countdown > 0 ? `${countdown}s` : t('getCaptcha') }}</span>
          </span>
        </div>
        <div
          class="error"
          v-if="errorStr"
        >
          '*'{{ errorStr }}
        </div>
        <!--        <div class="login-divider flex-center-center">-->
        <!--          <span class="mr-12 ml-12">{{ t('otherVerificationOptions') }}</span>-->
        <!--        </div>-->
        <!--        <div class="margin-24 flex-center-center svgbox">-->
        <!--          <ThirdBtn-->
        <!--            type="cancel"-->
        <!--            :disabled="loginBtnDisabled"-->
        <!--            @update:disabled="changeDisabled"-->
        <!--            @submit="loginStore.handleLogin($event, thirdPartyLogin)"-->
        <!--          />-->
        <!--        </div>-->
      </template>
    </div>
    <div
      class="next-step"
      v-if="!showAccount"
    >
      <div
        class="buling flex-center-center"
        @click="next"
      >
        <div class="check flex-start-center">
          <svg-icon
            :icon-class="check ? 'selected-round' : 'checkbox'"
            @click.stop="changeCheck"
            class="fsize-18"
          />
          <span class="ml-8">{{ t('termsAgreement') }}</span>
        </div>
        {{ t('nextStep') }}
      </div>
    </div>

    <div
      v-else
      class="buling flex-center-center"
      @click="submit"
      :style="{ background: code.length === 6 && email.trim() && emailRegex.test(email) ? '#ebdfac' : 'rgba(118, 112, 86)' }"
    >
      {{ t('verify') }}
    </div>
    <ConfirmLogoff
      v-if="showLogoff"
      :email="email"
      :code="code"
      :type="cancelType"
      @close="handleClose"
      @confirm="handleConfirm"
    />
  </div>
</template>
<style lang="scss" scoped>
:deep(.van-button--round) {
  width: 48px;
  height: 48px;
  background: #232222;
}

:deep(.apple) {
  width: 48px;
  height: 48px;
}

:deep(.facebook) {
  width: 48px;
  height: 48px;
}

:deep(.van-button__icon) {
  font-size: 48px;
}

:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
  background-color: transparent;

  .van-nav-bar__left {
    padding: 0 12px;
  }
}

:deep(.van-button--round) {
  background: #000;
}

.logoff-container {
  position: relative;
  height: 100%;
  padding: 48px 0 122px;
  background: $livCoBackColor;

  .logoff-bg {
    position: absolute;
    top: 0;
    right: 0;
    width: 245px;
    height: 239px;
    pointer-events: none;
  }
}

.content {
  padding: 0 16px;

  .title {
    font-size: 24px;
    font-weight: 600;
    line-height: 28px;

    &.padding-b-14 {
      padding-bottom: 14px;
    }
  }

  .outbox {
    padding: 20px 16px;
    background: #232222;
    border-radius: 16px;

    .head {
      font-size: 16px;
      font-weight: 600;
    }

    .txt {
      position: relative;
      padding-left: 14px;
      margin-bottom: 16px;
      font-size: 12px;
      font-weight: 400;

      &::before {
        position: absolute;
        top: 5px;
        left: 0;
        width: 6px;
        height: 6px;
        content: '';
        background: #d9d9d9;
        border-radius: 50%;
      }
    }

    .foot {
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      color: rgba(255, 255, 255, 50%);
    }
  }
}

.next-step {
  position: fixed;
  bottom: 0;
  z-index: 999;
  width: 100vw;
  height: 122px;
  background: $livCoBackColor;
}

.buling {
  position: fixed;
  bottom: 32px;
  left: 50%;
  width: 327px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: $livCoTextColor;
  background: $livCoThemeColor;
  border-radius: 40px;
  transform: translateX(-50%);

  .check {
    position: absolute;
    bottom: 58px;
    left: 50%;
    width: 100%;
    font-size: 12px;
    font-weight: 400;
    color: rgba(255, 255, 255, 50%);
    transform: translateX(-50%);
  }
}

:deep(.van-field__control) {
  width: 100%;
  height: 48px;
  padding: 0 16px;
  background: #262626;
  border: 1px solid rgba(255, 255, 255, 10%);
  border-radius: 52px;
}

.van-cell {
  padding-right: 0;
  padding-left: 0;
  background-color: transparent;
}

.inputCode {
  position: relative;

  .getcode {
    position: absolute;
    top: 50%;
    right: 16px;
    font-size: 12px;
    font-weight: 500;
    color: $livCoThemeColor;
    transform: translateY(-50%);
  }
}

.error {
  font-size: 11px;
  font-weight: 400;
  color: #fe1375;
}

.svgbox {
  gap: 24px;
  font-size: 42px;
}

.login-divider {
  margin: 20px 0 16px;
  font-size: 12px;
  font-weight: 400;
  line-height: 14px;
  color: rgba(255, 255, 255, 50%);
  text-align: center;

  &::before,
  &::after {
    box-sizing: border-box;
    display: block;
    width: 60px;
    height: 1px;
    content: '';
    background: linear-gradient(90deg, rgba(255, 255, 255, 0%) 0%, rgba(255, 255, 255, 50%) 100%);
    border-radius: 2px;
  }

  &::after {
    background: linear-gradient(-90deg, rgba(255, 255, 255, 0%) 0%, rgba(255, 255, 255, 50%) 100%);
  }
}
</style>
