<script setup lang="ts">
import { getFaq } from '@/api/mine'
import type { IFaqItem } from '@/api/mine/type'
definePage({
  name: 'FAQ',
  meta: {
    level: 2
  }
})
const { t } = useI18n()
const activeNames = ref([])
const faqList = ref<IFaqItem[]>([])
const loading = ref(false)
function getFaqHandle() {
  loading.value = true
  getFaq()
    .then((res) => {
      if (res.code === 200) {
        faqList.value = res.data.list
      }
    })
    .finally(() => {
      loading.value = false
    })
}
onMounted(() => {
  getFaqHandle()
})
</script>
<template>
  <div class="faq-container">
    <van-nav-bar
      :title="t('helpFAQ')"
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-24"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <div class="content">
      <van-collapse v-model="activeNames">
        <van-collapse-item
          v-for="item in faqList"
          :key="item.id"
          :title="item.title"
          :name="item.id"
        >
          <div v-html="item.content"></div>
        </van-collapse-item>
      </van-collapse>
    </div>
    <TailChaseLoading v-if="loading" />
  </div>
</template>
<style lang="scss" scoped>
:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
  background-color: transparent;
}

:deep(.van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after) {
  border: none;
}

:deep(.van-collapse-item--border::after) {
  border: none;
}

.faq-container {
  padding-top: 48px;
}

.content {
  padding: 8px 16px;
  // padding-top: 54px;

  .van-collapse-item {
    margin-bottom: 12px;

    :deep(.van-cell) {
      background: #232222 !important;
      border-radius: 16px;
      // transition: border-radius 0.3s ease;

      &.van-collapse-item__title--expanded {
        border-radius: 16px 16px 0 0 !important;
      }
    }

    :deep(.van-collapse-item__wrapper) {
      border-radius: 0 0 16px 16px !important;

      --van-collapse-item-content-background: #232222;

      transition: all 0.01s ease;
    }
  }
}
</style>
