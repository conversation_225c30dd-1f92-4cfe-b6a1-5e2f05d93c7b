<script setup lang="ts">
import { checkCode } from '@/api/mine'
import useUserStore from '@/stores/modules/user'
import { useModal } from '@/hooks/useModal'
definePage({
  name: 'Invitation',
  meta: {
    level: 2
  }
})

const code = ref('')
const userStore = useUserStore()
const router = useRouter()
const { t } = useI18n()
const loading = ref(false)
async function submit() {
  if (loading.value) return
  if (!code.value) {
    useModal({
      message: t('enterInviteCode')
    })
    return
  }
  try {
    loading.value = true
    let data = {
      redeem_code: code.value
    }
    await checkCode(data)
    loading.value = false
    await userStore.getUserInfo()
    router.back()
  } catch (error) {
    loading.value = false
  }
}
</script>
<template>
  <div class="invitation-container">
    <van-nav-bar
      :title="t('inviteCode')"
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-24"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <div class="content">
      <van-field
        v-model="code"
        :placeholder="t('enterInviteCode')"
      />
      <div
        class="btn flex-center-center"
        @click="submit"
      >
        {{ t('confirmSelection') }}
        <van-loading
          v-if="loading"
          type="spinner"
          color="white"
          size="16px"
        />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import 'src/assets/styles/variables';

:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
  background-color: transparent;
}

.invitation-container {
  padding-top: 48px;
}

.content {
  padding: 16px;
  // padding-top: 54px;

  :deep(.van-cell) {
    background: #232222 !important;
    border-radius: 16px;
  }

  .btn {
    width: 327px;
    height: 48px;
    margin: 42px auto;
    font-size: 16px;
    font-weight: 600;
    color: $livCoTextColor;
    background: $livCoThemeColor;
    border-radius: 40px;
  }
}
</style>
