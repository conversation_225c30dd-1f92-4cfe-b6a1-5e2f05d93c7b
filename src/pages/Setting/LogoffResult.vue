<script setup lang="ts">
import LogoffResult from '@/assets/images/mine/logoff-result.png'
import useUserStore from '@/stores/modules/user'
definePage({
  name: 'LogoffResult',
  meta: {
    level: 2
  }
})
const { t } = useI18n()
const userStore = useUserStore()
userStore.removeAllUserInfo()
</script>
<template>
  <div class="logoff-result-container">
    <div class="navbar flex-center-center">
      <SvgIcon
        icon-class="back"
        class="fsize-24 back"
        @click="$router.replace('/')"
      />
      <div class="title">{{ t('deactivationSuccessful') }}</div>
    </div>
    <div class="content mt-100">
      <van-image
        :src="LogoffResult"
        alt="success-icon"
        style="width: 100px; height: 100px"
        lazy-load
        fit="contain"
      />
      <p class="txt mt-8">{{ t('deactivationSuccessful') }}</p>
      <div
        class="btn flex-center-center"
        @click.stop="$router.replace('/')"
      >
        {{ t('confirmButton') }}
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  height: 46px;
  padding: 0 16px;

  .title {
    font-size: 16px;
    font-weight: 500;
  }

  .back {
    position: absolute;
    left: 12px;
  }
}

.logoff-result-container {
  padding: 48px 0;

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .txt {
    margin-bottom: 42px;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #fff;
  }

  .btn {
    width: 178px;
    height: 48px;
    background: #3d3c3c;
    border-radius: 12px;
  }
}
</style>
