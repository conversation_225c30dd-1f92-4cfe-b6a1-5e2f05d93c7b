<script setup lang="ts">
import { getRecommendPreference, set_preference, getPreference } from '@/api/login'
import { IRecommendPreference, ITag_ids } from '@/api/login/types'
import { useModal } from '@/hooks/useModal'
import router from '@/router'

definePage({
  name: 'Preference',
  meta: {
    level: 2
  }
})

interface genderListItem {
  name: string
  id: number
  light?: boolean
}
const { t } = useI18n()
const loading = ref(false)
const datas = reactive<{
  preferenceList: IRecommendPreference[]
  genderList: genderListItem[]
  loading: boolean
}>({
  preferenceList: [],
  genderList: [
    { id: 0, name: t('allOptions'), light: false },
    { id: 1, name: t('maleOption'), light: false },
    { id: 2, name: t('femaleOption'), light: false },
    { id: 3, name: t('nonBinaryOption'), light: false }
  ],
  loading: false
})
// function chooseTag(item: genderListItem) {
//   item.light = !item.light
//   dataChange(datas.genderList, item)
// }
function choosePreference(pitem: IRecommendPreference, item: ITag_ids) {
  item.light = !item.light
  dataChange(pitem.tag_list, item)
}
function dataChange(sourceList: ITag_ids[], item: ITag_ids) {
  const list = sourceList.filter((f) => f.light).map((item) => item.id)
  const listAll = sourceList.filter((_, pindex) => pindex !== 0).map((item) => item.light)
  if (list.length > 1) {
    sourceList[0].light = false
  }
  if (listAll.every((e) => e)) {
    sourceList.forEach((item, index) => {
      item.light = index === 0
    })
  }
  sourceList.forEach((c, cIndex) => {
    if (c.id === item.id && cIndex === 0) {
      c.light = true
      sourceList.forEach((k, v) => {
        if (v !== 0) {
          k.light = false
        }
      })
    }
  })
}
function getList() {
  loading.value = true
  getRecommendPreference()
    .then((res) => {
      datas.preferenceList = res.data
      datas.preferenceList.forEach((item: IRecommendPreference) => {
        item.tag_list = item.tag_list.map((item: ITag_ids) => {
          return { ...item, light: false }
        })
        item.tag_list.unshift({
          id: 999999,
          name: t('allOptions'),
          light: false
        })
      })
      getPreferenceInfo()
    })
    .finally(() => {
      loading.value = false
    })
}
function getPreferenceInfo() {
  getPreference().then((res) => {
    let { tag_ids } = res.data
    reviewHandle(datas.preferenceList, tag_ids)
  })
}
function reviewHandle(sourceList: IRecommendPreference[], list: number[]) {
  if (list && list.length > 0) {
    sourceList.forEach((item) => {
      item.tag_list.forEach((tag: ITag_ids) => {
        if (list.includes(tag.id)) {
          tag.light = true
        }
      })
    })
  } else {
    sourceList.forEach((item) => {
      item.tag_list.forEach((tag: ITag_ids) => {
        tag.light = false
        item.tag_list[0].light = true
      })
    })
  }
  sourceList.forEach((item) => {
    let list = item.tag_list.filter((_: ITag_ids, index: number) => index > 0)
    if (list.every((tag: ITag_ids) => tag.light)) {
      item.tag_list.forEach((tag: ITag_ids) => {
        tag.light = false
      })
      item.tag_list[0].light = true
    }
  })
}
function submit() {
  let sexs: number[] = []
  let interest: number[] = paramsFormatter(datas.preferenceList)
  let data = {
    sexs,
    interest
  }
  datas.loading = true
  set_preference(data)
    .then((res) => {
      if (res.code !== 200) return
      useModal({
        message: t('setupSuccessful'),
        loading: false,
        autoClose: true,
        duration: 1500
      })
      datas.loading = false
      close()
      setTimeout(() => {
        router.go(-1)
      }, 1500)
    })
    .catch(() => {
      datas.loading = false
    })
    .finally(() => {
      datas.loading = false
    })
}
function paramsFormatter(sourceList: IRecommendPreference[]) {
  let result: number[] = []
  sourceList.forEach((item: IRecommendPreference) => {
    if (item.tag_list[0].light) {
      result = [...result, ...item.tag_list.filter((_, pindex: number) => pindex !== 0).map((item: ITag_ids) => item.id)]
    } else {
      result = [...result, ...item.tag_list.filter((f: ITag_ids) => f.light).map((item: ITag_ids) => item.id)]
    }
  })
  result = [...new Set(result)]
  return result
}
onMounted(() => {
  getList()
})
</script>
<template>
  <div class="preference-container">
    <van-nav-bar
      :title="t('interestPreferences')"
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-24"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <div class="content">
      <!-- <div class="label pt-4 pb-12">{{ t('genderInterest') }}</div>
      <div class="tagBox">
        <div
          :class="{ tag: true, 'flex-center-center': true, lightTag: item.light }"
          v-for="(item, i) in datas.genderList"
          :key="i"
          @click="chooseTag(item)"
        >
          {{ item.name }}
        </div>
      </div> -->
      <div
        class="tag-list"
        v-for="(pitem, pindex) in datas.preferenceList"
        :key="pitem.id"
      >
        <div
          class="label pb-12"
          :class="pindex === 0 ? '' : 'pt-24'"
        >
          {{ pitem.name }}
        </div>
        <div class="tagBox">
          <div
            :class="{ tag: true, 'flex-center-center': true, lightTag: item.light }"
            v-for="(item, i) in pitem.tag_list"
            :key="i"
            @click="choosePreference(pitem, item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>

      <div class="btn-box flex-center-center">
        <div
          class="btn flex-center-center"
          @click="submit"
        >
          {{ t('confirmSelection') }}
          <van-loading
            v-if="datas.loading"
            type="spinner"
            color="white"
            size="24px"
          />
        </div>
      </div>
    </div>
    <TailChaseLoading v-if="loading" />
  </div>
</template>
<style lang="scss" scoped>
@import 'src/assets/styles/variables';

:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
  background-color: transparent;
}

.preference-container {
  padding-top: 48px;
}

.content {
  height: calc(100% - 48px);
  padding: 8px 16px 92px;
  overflow-y: scroll;

  .title {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
  }

  .skip {
    font-size: 16px;
    font-weight: 500;
    color: $themeColor;
  }

  .label {
    font-size: 14px;
    color: #8b8889;
  }

  .tagBox {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start;

    .tag {
      min-width: 80px;
      height: 38px;
      padding: 0 16px;
      background: #262626;
      border: 1px solid rgba(255, 255, 255, 10%);
      border-radius: 52px;
    }

    .lightTag {
      background: rgba(255, 53, 242, 30%);
      border: 1px solid $themeColor;
    }
  }

  .btn-box {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 12px 0 32px;
    background: #181818;
  }

  .btn {
    width: 327px;
    height: 48px;
    // margin: 60px auto 12px;
    font-size: 16px;
    font-weight: 600;
    color: white;
    background: $themeColor;
    border-radius: 40px;
  }
}
</style>
