<script setup lang="ts">
import { getTaskList, getAward, signIn } from '@/api/task'
import { ITask, ITaskCompleteInfo } from '@/api/task/types'
import { useModal } from '@/hooks/useModal'
import router from '@/router'
import useUserStore from '@/stores/modules/user'
import { copyText } from '@/utils'
import { ForwardAddressEnum } from '@/api/eventReport/index'

definePage({
  name: 'task',
  meta: {
    level: 2
  }
})
interface ITaskList {
  title: string
  list: ITask[]
}
const { t } = useI18n()
const location = ref(window.location)
const userStore = useUserStore()
userStore.getUserInfo()
const { userInformation } = toRefs(userStore)
const scrollContainerRef = ref()
const bgImgRef = ref()
const toolBannerRef = ref()
const alllist = ref<ITaskList[]>([])
const loading = ref(true)
function signInHandle(id: number) {
  signIn().then((res) => {
    if (res.code !== 200) return
    getAwardHandle(id)
  })
}

function getAwardHandle(id: number) {
  const loadingModal = useModal({
    message: t('loading'),
    autoClose: false,
    loading: true
  })
  getAward(id)
    .then((res) => {
      if (res.code !== 200) return
      loadingModal.close()
      getTaskListHandle()
      userStore.getUserInfo()
    })
    .finally(() => {
      loadingModal.close()
    })
}
function btnClickHandle(item: ITask) {
  let type = checkBtnStatus(item.complete_info, item.tag)
  // console.log(type)
  switch (type) {
    case t('notAchieved'):
    case t('rewardClaimed'):
      break
    case t('checkIn'):
      signInHandle(item.id)
      break
    case t('claimReward'):
      getAwardHandle(item.id)
      break
    case t('goComplete'):
      navToFinish(item)
      break
    default:
      break
  }
}
function navToFinish(item: ITask) {
  switch (item.tag) {
    case 'add_up_login':
    case 'login':
      break
    case 'upgrade_intimacy':
    case 'phone_call':
    case 'chat_times':
    case 'chat_count':
      router.push({ path: '/' })
      break
    case 'buy_goods':
      router.push({ path: '/PropertyStore' })
      break
    default:
      break
  }
}
function getTaskListHandle() {
  loading.value = true
  getTaskList()
    .then((res) => {
      if (res.code !== 200) return
      let everyDayTaskList = res.data.filter((item) => {
        return item.type === 'every_day'
      })
      let everyWeekTaskList = res.data.filter((item) => {
        return item.type === 'every_week'
      })
      let specialTaskList = res.data.filter((item) => {
        return item.type === 'special'
      })
      alllist.value = [
        {
          title: t('dailyTasks'),
          list: everyDayTaskList
        },
        {
          title: t('weeklyTasks'),
          list: everyWeekTaskList
        },
        {
          title: t('specialTasks'),
          list: specialTaskList
        }
      ]
    })
    .finally(() => {
      loading.value = false
    })
}

function navToCrystal() {
  console.log(1)
  sessionStorage.setItem('crystal_front_address', ForwardAddressEnum.TASK_PAGE)
  router.push({ name: 'crystal' })
}

function checkBtnStatus(completeInfo: ITaskCompleteInfo, tag?: string) {
  if (completeInfo.is_get === 1) {
    return t('rewardClaimed')
  }
  if (completeInfo.is_complete === 1) {
    return tag === 'login' ? t('checkIn') : t('claimReward')
  }
  if (completeInfo.is_complete === 0) {
    return tag === 'login' ? t('checkIn') : tag === 'add_up_login' ? t('notAchieved') : t('goComplete')
  }
}

const changeBgFlash = (scrollTop: number) => {
  bgImgRef.value.style.opacity = 1 - scrollTop / 150
  // 超过200直接隐藏
  toolBannerRef.value.style.opacity = scrollTop > 200 ? 0 : 1
}

const btndisabed = computed(
  () =>
    function (item: ITask) {
      return item.complete_info?.is_get === 1 || (item.complete_info?.is_complete === 0 && item.tag === 'add_up_login') ? 'task-btn-disabled' : ''
    }
)
onMounted(() => {
  getTaskListHandle()
})
</script>

<template>
  <div
    class="task-container"
    ref="scrollContainerRef"
  >
    <div
      class="task-container-header-bg"
      ref="bgImgRef"
    >
      <img
        class="bg-img"
        src="@/assets/images/task/task-header-bg.png"
        alt="task-header-bg"
      />
    </div>
    <van-nav-bar
      :title="t('task')"
      :border="false"
      v-scroll-gradation-bg="{ scrollElement: scrollContainerRef, completedHeight: 50, startHeight: 0, color: '24,24,24', func: changeBgFlash }"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-24"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <div
      class="tool-banner flex-center-center"
      ref="toolBannerRef"
    >
      <div
        class="tool-item flex-start-center"
        @click="navToCrystal"
      >
        <div class="data-wrap">
          <div class="data-wrap-label">
            <span>{{ t('myCrystals') }}</span>
            <SvgIcon
              icon-class="arrow-right"
              class="fsize-20"
            />
          </div>
          <div class="data-wrap-number">{{ userInformation.crystal_amount }}</div>
        </div>
      </div>
      <img
        src="@/assets/images/task/mascot.png"
        alt="mascot"
        class="mascot"
      />
    </div>
    <div class="task-card">
      <div
        class="task-list"
        v-for="pitem in alllist"
        v-show="pitem.list.length > 0"
        :key="pitem.title"
      >
        <div class="card-title">{{ pitem.title }}</div>
        <div
          class="task-list-item flex-start-center"
          v-for="item in pitem.list"
          :key="item.id"
        >
          <div class="task-type-icon flex-center-center">
            <van-image :src="item.icon" />
          </div>
          <div class="task-data-benner">
            <div class="task-data-title mb-8">
              <span class="mr-4">{{ item.name }}</span>
              <span
                v-if="item.complete_condition > 1"
                class="opcaity-half"
              >
                {{ `(${item.complete_info.complete_number})` }}
              </span>
            </div>
            <div
              class="task-data-benner"
              v-if="pitem.title === t('specialTasks')"
            >
              <div class="task-data-wrap-text mb-5">
                <span>{{ t('inviteCode') }}：{{ userInformation.my_invite_code }}</span>
                <SvgIcon
                  icon-class="copy-active"
                  class="fsize-14 ml-4"
                  @click="copyText(userInformation.my_invite_code)"
                />
              </div>
              <div class="task-data-wrap-text">
                <span>{{ t('invitationLink') }}：{{ `${location.origin}?invite_code=${userInformation.my_invite_code}` }}</span>
                <SvgIcon
                  icon-class="copy-active"
                  class="fsize-14 ml-4"
                  @click="copyText(`${location.origin}?invite_code=${userInformation.my_invite_code}`)"
                />
              </div>
              <div class="task-data-wrap-text opcaity-half">
                {{ item.complete_info?.complete_number ? t('inviteAmount').replace(/xx/g, String(item.complete_info.complete_number)) : 0 }}
              </div>
              <div class="task-data-wrap flex-start-center">
                <div
                  class="data-count flex-center-center"
                  v-if="item.reward_type === 'crystal'"
                >
                  <SvgIcon
                    icon-class="crystal-mini"
                    class="fsize-14 mr-4"
                  />
                  <div class="data-count-number">+{{ item.crystal_amount }}</div>
                </div>
              </div>
            </div>
            <div class="task-data-wrap flex-start-center">
              <div class="data-count flex-center-center">
                <SvgIcon
                  :icon-class="`${item.reward_type}-mini`"
                  class="fsize-14 mr-4"
                />
                <div class="data-count-number">{{ '+' + item.amount }}</div>
              </div>
            </div>
          </div>
          <div
            class="card-btn"
            :class="btndisabed(item)"
            v-if="pitem.title !== t('specialTasks')"
            @click="btnClickHandle(item)"
          >
            {{ checkBtnStatus(item.complete_info, item.tag) }}
          </div>
        </div>
      </div>
    </div>
    <TailChaseLoading v-if="loading" />
  </div>
</template>

<style scoped lang="scss">
:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
  background-color: transparent;
}

.task-container {
  height: 100%;
  padding: 48px 0 16px;
  overflow-y: scroll;

  .task-container-header-bg {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 300px;

    .bg-img {
      width: 100%;
      height: 100%;
    }
  }

  .tool-banner {
    position: sticky;
    top: -48px;
    //background: url('@/assets/images/task/task-header-bg.png') no-repeat top / cover;
    display: flex;
    align-items: flex-end;
    margin-top: -48px;

    .mascot {
      width: 50%;
    }

    .tool-item {
      flex: 1;
      padding: 0 0 24px 20px;
    }

    .data-wrap-number {
      margin-top: 4px;
      font-family: 'DIN Bold', serif;
      font-size: 32px;
    }

    .data-wrap-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
    }

    .data-wrap-label__icon {
      padding: 4px;
      transform: scale(2);
    }
  }

  .task-card {
    position: relative;
    z-index: 3;
    margin: 0 16px;
  }

  .task-list {
    z-index: 3;
    padding: 20px 16px 16px 12px;
    margin-bottom: 18px;
    background: #141414;
    border-radius: 16px;

    .card-title {
      margin-bottom: 8px;
      font-size: 15px;
      font-weight: 500;
    }
  }

  .task-list-item {
    padding: 13px 0;

    .task-data-benner {
      flex: 1;
    }

    &:last-child {
      padding-bottom: 2px;
    }
  }

  .task-type-icon {
    width: 42px;
    height: 42px;
    margin-right: 12px;
    background: #29252c;
    border-radius: 12px;

    :deep(.van-image) {
      width: 24px;
      height: 24px;
    }
  }

  .task-data-title {
    max-width: 180px;
    font-size: 14px;
    font-weight: 500;
  }

  .data-count {
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }

  .data-count-number {
    font-size: 12px;
    color: #d8c69d;
  }

  .card-btn {
    min-width: 68px;
    padding: 8px 16px;
    margin-left: auto;
    font-size: 12px;
    font-weight: 600;
    color: $livCoTextColor;
    text-align: center;
    word-break: keep-all;
    background: $livCoThemeColor;
    border-radius: 8px;
  }

  .task-btn-disabled {
    color: #5e5e5e;
    background: transparent;
    border: 1px solid #5e5e5e82;
  }

  .task-data-wrap-text {
    margin-bottom: 9px;
    font-size: 12px;
    word-break: break-all;
  }

  .mb-5 {
    margin-bottom: 5px;
  }

  .opcaity-half {
    color: rgba(255, 255, 255, 50%);
  }
}
</style>
