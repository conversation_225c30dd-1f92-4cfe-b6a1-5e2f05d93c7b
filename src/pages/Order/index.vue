<script setup lang="ts">
import { orderPayStatus } from '@/api/purchase'
import { usePopupMemoryStore } from '@/stores/modules/popupMemory.ts'
definePage({
  name: 'Order',
  meta: {
    title: '',
    level: 2
  }
})

const popupMemoryStore = usePopupMemoryStore()
const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const orderQuery = route.query
// const payStatus = ref<any>({})
const payStatus = ref(0)
let timer: any = null
function goback() {
  const path = popupMemoryStore.getPopupMemoryPath()
  console.log(path, 'order')
  if (path) router.replace(path)
  else router.go(-1)
}
function getPayStatus() {
  let data = {
    order_id: orderQuery.order_id as string
  }
  orderPayStatus(data).then((res) => {
    if (res.code !== 200) return
    payStatus.value = res.data
    if (payStatus.value === 3) {
      clearInterval(timer)
    }
  })
}

const firstTitle = computed(() => {
  switch (payStatus.value) {
    case 0:
      return t('paymentProcessing')
    case 1:
    case 2:
    case 3:
      return t('paySuccess')
    default:
      return t('paymentProcessing')
  }
})
const firstIcon = computed(() => {
  switch (payStatus.value) {
    case 0:
      return 'order-wait'
    case 1:
    case 2:
    case 3:
      return 'order-finish'
    default:
      return 'order-wait'
  }
})
function navToHandle() {
  window.open('https://www.facebook.com/ugenie.aichat', '_blank')
}
onMounted(() => {
  getPayStatus()
  timer = setInterval(() => {
    getPayStatus()
  }, 5000)
})
onUnmounted(() => {
  clearInterval(timer)
})
</script>

<template>
  <div class="order-containe">
    <van-nav-bar
      :title="payStatus === 3 ? t('orderCompleted') : t('awaitingOrderCompletion')"
      :border="false"
      fixed
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-22"
          @click="goback"
        />
      </template>
    </van-nav-bar>
    <div class="content">
      <div class="orderbox">
        <p class="id mb-34">{{ t('orderNumber') + '：' }}{{ route.query.order_id }}</p>
        <div class="stepbox mb-8">
          <div class="flex-start-center step">
            <SvgIcon
              :icon-class="firstIcon"
              class="fsize-22"
            />
            <div>{{ firstTitle }}</div>
          </div>
          <div class="flex-start-center step">
            <div
              class="line ml-10"
              :class="payStatus === 3 ? 'active' : ''"
            ></div>
            <div
              class="txt"
              v-if="payStatus === 0 || !payStatus"
            >
              {{ t('watiForPayment') }}
            </div>
          </div>
          <div class="flex-start-center step">
            <SvgIcon
              :icon-class="payStatus === 3 ? 'order-finish' : 'order-wait'"
              class="fsize-22"
            />
            <div>{{ payStatus === 3 ? t('completed') : t('uncompleted') }}</div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="buling flex-center-center"
      @click="goback"
    >
      {{ t('return') }}
    </div>
    <div class="buling2 flex-center-center">
      {{ t('haveAnyQuestions') }}
      <span @click="navToHandle">{{ t('contactCustomer') }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content {
  padding: 0 16px;
  padding-top: 48px;

  .orderbox {
    padding: 20px 16px;
    background: #232222;
    border-radius: 16px;

    .stepbox {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .id {
      font-size: 14px;
      font-weight: 400;
    }

    .iconbox {
      width: 24px;
      height: 24px;
      border: 1px solid;
    }

    .line {
      width: 0;
      height: 54px;
      border: 1px dashed #363636;
    }

    .line.active {
      border-color: $livCoThemeColor;
    }

    .step {
      gap: 12px;
    }

    .txt {
      height: 54px;
      font-size: 12px;
      color: #8b8889;
    }
  }
}

.buling {
  position: fixed;
  bottom: 56px;
  left: 50%;
  width: 327px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background: #2b2b2b;
  border-radius: 40px;
  transform: translateX(-50%);
}

.buling2 {
  position: fixed;
  bottom: 8px;
  left: 50%;
  width: 327px;
  font-size: 12px;
  font-weight: 400;
  color: rgba(255, 255, 255, 50%);
  transform: translateX(-50%);

  span {
    padding-left: 3px;
    font-size: 12px;
    font-weight: 500;
    color: $livCoThemeColor;
  }
}
</style>
