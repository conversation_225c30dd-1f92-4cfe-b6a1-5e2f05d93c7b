<script lang="ts" setup>
import { userFigure as userFigureList } from '@/api/mine'
import { FigureItem, FigureListReq } from '@/api/mine/type.ts'
import { SexEnum } from '@/enums'
import { setImage } from '@/api/agentChat'
import Empty from '@/components/Empty.vue'

interface FigureListFn extends FigureListReq {
  init?: boolean
}

definePage({
  name: 'selfImage',
  meta: {
    level: 2
  }
})

const router = useRouter()
const route = useRoute()

const figure = reactive<{
  list: FigureItem[]
  total: number
  page: number
  finished: boolean
  loading: boolean
  initLoading: boolean
  currentFigureId: number
  default_image_id: number | null
  openFigureId: number
}>({
  list: [],
  total: 0,
  page: 0,
  finished: false,
  loading: false,
  initLoading: true,
  currentFigureId: 0,
  default_image_id: null,
  openFigureId: 0
})

const loading = ref(false)
const delVisible = ref(false)

const selectFigure = ({ position, id }: { position: string; id: number }) => {
  if (position === 'cell') {
    if (route.query?.ai_id) {
      figure.currentFigureId = id
    }
  } else if (position === 'right') {
    delVisible.value = true
    figure.openFigureId = id
  }
}

const onLoad = () => {
  if (figure.total !== 0 && figure.list.length >= figure.total) {
    figure.finished = true
    return
  }
  // 异步更新数据
  getUserFigureList({ page: ++figure.page })
}
function onOpen(e: { name: string }) {
  figure.currentFigureId = 0
  document.getElementById(`swipe-cell${e.name}`).classList.add(`slide-right`)
}
function onClose(e: { name: string; id: number }) {
  document.getElementById(`swipe-cell${e.name}`).classList.remove(`slide-right`)
}

const getUserFigureList = ({ action = 'list', page, nickname, sex, birthday, desc, full_avatar_url, init = false }: FigureListFn) => {
  figure.loading = true
  if (init) {
    figure.initLoading = true
    figure.list = []
  }
  const data = {
    action,
    page,
    limit: 10,
    nickname,
    sex,
    birthday,
    desc,
    full_avatar_url,
    ai_id: Number(route.query?.ai_id)
  }
  userFigureList(data)
    .then((res) => {
      if (res.code === 200) {
        figure.list.push(...res.data.list)
        res.data.count !== 0 && figure.total === 0 && (figure.total = res.data.count)
        figure.default_image_id = res.data.default_image_id
        if (route.query?.ai_id) {
          figure.currentFigureId = figure.list.find((item) => item.is_choose).id
        }
      }
    })
    .finally(() => {
      figure.initLoading = false
      figure.loading = false
    })
}

const deleteFigure = () => {
  userFigureList({
    action: 'delete',
    image_id: figure.openFigureId
  })
    .then((res) => {
      if (res.code === 200) {
        delVisible.value = false
      }
    })
    .finally(() => {
      getUserFigureList({ init: true })
    })
}

const addUserFigure = () => {
  router.push(`/Mine/EditImage?action=add`)
}

const goEdit = (image_id: number) => {
  router.push(`/Mine/EditImage?action=edit&image_id=${image_id}`)
}

const submit = () => {
  if (!figure.currentFigureId) return
  loading.value = true
  figure.loading = true
  setImage({
    ai_id: Number(route.query.ai_id),
    image_id: figure.currentFigureId
  })
    .then((res) => {
      if (res.code === 200) {
        getUserFigureList({ init: true })
      }
    })
    .finally(() => {
      loading.value = false
    })
}

getUserFigureList({ init: true, page: ++figure.page })
</script>

<template>
  <div class="container">
    <van-nav-bar
      :title="$t('userCharacterOption')"
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-22"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <div class="image-card-container">
      <van-list
        v-if="figure.list.length || figure.loading || figure.initLoading"
        v-model:loading="figure.loading"
        :finished="figure.finished"
        :finished-text="$t('noMore')"
        @load="onLoad"
      >
        <van-skeleton
          :loading="figure.initLoading"
          row="2"
          style="display: block; margin-bottom: 16px"
          class="image-card"
        >
          <template #template>
            <van-skeleton-paragraph row-width="100%" />
            <van-skeleton-paragraph
              style="margin-top: 6px"
              row-width="100%"
            />
            <div class="edit-icon flex-center-center">
              <SvgIcon
                icon-class="image-edit"
                class="fsize-32"
              />
            </div>
          </template>
          <van-swipe-cell
            v-for="(item, index) in figure.list"
            :key="item.id"
            :name="index"
            @open="onOpen"
            @close="(val) => onClose({ ...val, id: item.id })"
            :id="`swipe-cell${index}`"
            @click="(position) => selectFigure({ position, id: item.id })"
          >
            <div :class="['image-card', figure.currentFigureId === item.id ? 'selected-figure' : '']">
              <div class="image-card__info flex-start-center">
                <div class="image-card__info__text">
                  <div class="flex-start-center">
                    <span class="image-card__info__text__name">{{ item.nickname }}</span>
                    <span
                      v-if="item.is_choose === 1"
                      class="image-card__info__text__tag"
                      >{{ $t('inUse') }}
                    </span>
                  </div>
                  <div class="image-card__info__text__desc">
                    <span>{{ $t(`${SexEnum[item.sex]}`) }}</span>
                    <span class="image-card__info__text__desc__line">|</span>
                    <span>{{ item.birthday }}</span>
                  </div>
                </div>
              </div>
              <div class="image-card__desc">{{ item.desc }}</div>
              <div
                class="edit-icon flex-center-center"
                @click="goEdit(item.id)"
              >
                <SvgIcon
                  icon-class="image-edit"
                  class="fsize-32"
                />
              </div>
              <div
                v-if="figure.currentFigureId === item.id"
                class="selected-icon flex-center-center"
              >
                <svg-icon icon-class="selected" />
              </div>
            </div>
            <template #right>
              <SvgIcon
                icon-class="delete"
                class="fsize-24"
              />
            </template>
          </van-swipe-cell>
        </van-skeleton>
      </van-list>
      <div
        v-else
        class="empty-box flex-center-center"
      >
        <Empty :text="$t('noMoreImage')">
          <template #empty-img>
            <img
              src="@/assets/images/mine/follow-empty.png"
              alt="empty"
            />
          </template>
        </Empty>
      </div>
    </div>
    <div class="btn-box flex-around-center">
      <div
        class="add-btn flex-center-center"
        @click="addUserFigure"
      >
        <svg-icon
          icon-class="add"
          style="font-size: 18px; color: #8b8a8a"
        />
        <span class="ml-5">{{ $t('addOption') }}</span>
      </div>
      <div
        v-if="route.query.ai_id"
        :class="['save-btn flex-center-center', figure.currentFigureId ? '' : 'disabled-mask']"
        @click="submit"
      >
        <svg-icon
          icon-class="save"
          style="font-size: 18px"
        />
        <span class="ml-5">{{ $t('saveButton') }}</span>
      </div>
    </div>
  </div>
  <van-dialog
    v-model:show="delVisible"
    :show-confirm-button="false"
    :show-cancel-button="false"
    class="delete-Dialog"
  >
    <div class="flex-center-center flex-column content">
      <div class="title">{{ $t('confirmDelFigure') }}</div>
      <div class="btnbox flex-between-center">
        <div
          class="btn flex-center-center btn1"
          @click="delVisible = false"
        >
          {{ $t('cancelButton') }}
        </div>
        <div
          class="btn flex-center-center btn2"
          @click="deleteFigure"
        >
          {{ $t('confirmButton') }}
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<style lang="scss">
.delete-Dialog {
  padding: 30px 16px 24px;

  .van-dialog__footer {
    display: none !important;
  }

  .content {
    gap: 18px;

    .title {
      padding: 10px 0 20px;
      font-size: 18px;
      font-weight: 500;
      color: #fff;
    }

    .tip {
      width: 249px;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: #c1c1c1;
      text-align: center;
    }

    .btnbox {
      width: 100%;

      .btn {
        width: 134px;
        height: 48px;
        border-radius: 12px;
      }

      .btn1 {
        color: #c1c1c1;
        background: #3d3c3c;
      }

      .btn2 {
        color: #fff;
        background: #ff35f2;
      }
    }
  }
}
</style>

<style scoped lang="scss">
@import '../../assets/styles/mixin';

:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: sticky;
  top: 0;
  width: 100vw;
}

:deep(.van-nav-bar__arrow) {
  --van-padding-base: 16px;
}

:deep(.van-button) {
  --van-button-default-height: 100% !important;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .nav-bar__text {
    margin-left: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
  }

  .nav-bar__btn {
    .add-text {
      margin-left: 4px;
      font-size: 14px;
      font-weight: 600;
      color: #ff35f2;
    }
  }

  .image-card-container {
    flex: 1;
    padding: 0 16px 20px;
    overflow-y: scroll;

    :deep(.van-swipe-cell) {
      margin-bottom: 12px;
      overflow: hidden;
      border-radius: 12px;
    }

    :deep(.van-swipe-cell__wrapper) {
      margin-right: -2px;
    }

    .slide-right {
      :deep(.van-swipe-cell__wrapper) {
        background-image: linear-gradient(to right, #181818 20%, #fe133d 50%);
      }
    }

    :deep(.van-swipe-cell__right) {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 74px;
      background: #fe133d;
      border-radius: 0 16px 16px 0;
    }

    .cell-edit {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 0;
      width: 32px;
      height: 32px;
      background: #2f2d2d;
      border-radius: 0 12px;

      .cell-edit-icon {
        width: 16px;
        height: 16px;
      }
    }

    .image-card {
      position: relative;
      min-height: 124px;
      padding: 16px;
      overflow: hidden;
      background: #232222;
      border: 1px solid transparent;
      border-radius: 12px;
      transition: all 0.28s;

      :deep(.van-skeleton-paragraph) {
        margin-top: 0;
      }

      &.active {
        border: 1px solid #ff35f2;
      }

      .edit-icon {
        position: absolute;
        top: 0;
        right: 0;
      }

      .image-card__info {
        margin-bottom: 8px;

        .avatar-image {
          width: 50px;
          height: 50px;
          margin-right: 12px;
        }

        .image-card__info__text__desc {
          margin-top: 9px;
          font-size: 12px;
          font-weight: 500;
        }

        .image-card__info__text__desc__line {
          margin: 0 6px;
        }

        .image-card__info__text {
          .image-card__info__text__name {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
          }

          .image-card__info__text__tag {
            padding: 2px 4px;
            margin-left: 8px;
            font-size: 10px;
            color: #ff35f2;
            background: rgba(255, 53, 242, 10%);
            border: 1px solid #ff35f2;
            border-radius: 4px;
          }
        }
      }

      .image-card__desc {
        @include multiLine(2);

        font-size: 13px;
        line-height: 1.5;
        color: #8b8889;
        letter-spacing: 1px;
        word-break: break-word;
      }

      .selected-icon {
        position: absolute;
        right: -1px;
        bottom: -1px;
        font-size: 22px;
      }
    }

    .selected-figure {
      border: 1px solid #ff35f2;
    }
  }

  .loading-icon {
    width: 24px;
    height: 24px;
    animation: loading 1s linear infinite;
  }

  @keyframes loading {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}

.btn-box {
  position: fixed;
  bottom: 20px;
  left: 0%;
  gap: 0 20px;
  width: 100%;
  padding: 0 20px;
}

.save-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background-color: #ff35f2;
  background-image: none;
  border-radius: 40px;
  mask: none;
  transition: mask 0.28s;
}

.add-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  color: #8b8a8a;
  background: #3d3c3c;
  border-radius: 40px;
}

.disabled-mask {
  mask: no-repeat 100% linear-gradient(rgba(0, 0, 0, 70%), rgba(0, 0, 0, 70%));
}

.empty-box {
  padding: 120px 0;

  img {
    width: 24px;
  }
}
</style>
