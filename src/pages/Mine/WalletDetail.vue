<script setup lang="ts">
import { walletDetail } from '@/api/purchase/index.ts'
import { WalletItem, WalletType } from '@/api/purchase/types.ts'
import Empty from '@/components/Empty.vue'
import { Pagination } from '@/api/mine/type.ts'

definePage({
  name: 'WalletDetail',
  meta: {
    level: 2
  }
})

const active = ref('crystal')
const loading = reactive<{
  gold: boolean
  crystal: boolean
  finished: boolean
  refreshing: boolean
  disabled: boolean
}>({
  gold: false,
  crystal: false,
  finished: false,
  refreshing: false,
  disabled: false
})
const list = reactive<{
  gold: WalletItem[]
  crystal: WalletItem[]
}>({
  gold: [],
  crystal: []
})

const pagination = reactive<Pagination>({
  page: 0,
  total: 0
})

// const onBeforeChange = (tab: WalletType) => {/*
//   if (loading[tab]) {
//     return false
//   }
//   return true
// }*/

// const onChange = (tab: WalletType) => {
//   loading[tab] = true
//   loading.finished = false
//   list[tab] = []
//   pagination.total = 0
//   pagination.page = 0
//   getWalletDetail(tab)
// }

const getWalletDetail = (type: WalletType = 'crystal') => {
  loading[type] = true
  walletDetail({
    type,
    limit: 10,
    page: ++pagination.page
  })
    .then((res) => {
      const { data, code } = res
      if (code === 200) {
        pagination.total = data.count
        if (pagination.total > list[type]?.length) {
          list[type].push(...data.list)
        } else {
          loading.finished = true
        }
      }
    })
    .finally(() => {
      loading[type] = false
      loading.refreshing = false
    })
}

const onRefresh = (tab: WalletType) => {
  loading.finished = false
  loading[tab] = true
  getWalletDetail(tab)
}

getWalletDetail()
</script>

<template>
  <div class="wallet-detail h-full">
    <div
      class="navbar"
      v-scroll-gradation-bg="{ completedHeight: 150, startHeight: 100 }"
    >
      <div class="navbar-wrapper flex-center-center w-full h-full">
        <div
          class="flex-between-center back-icon"
          @click="$router.back()"
        >
          <SvgIcon
            icon-class="back"
            class="fsize-22"
          />
        </div>
        <div class="title">{{ active === 'gold' ? $t('goldChangeDetails') : $t('crystalChangeDetails') }}</div>
      </div>
    </div>
    <div class="main wallet-tabs">
      <!--<van-tabs-->
      <!--  class="wallet-tabs"-->
      <!--  v-model:active="active"-->
      <!--  line-width="35%"-->
      <!--  :before-change="onBeforeChange"-->
      <!--  @change="onChange"-->
      <!--&gt;-->
      <!--  <van-tab-->
      <!--    :title="$t('goldDetails')"-->
      <!--    name="gold"-->
      <!--  >-->
      <!--    <div-->
      <!--      v-if="list.gold.length || loading.gold"-->
      <!--      class="gold-list w-full"-->
      <!--    >-->
      <!--      <van-pull-refresh-->
      <!--        v-model="loading.refreshing"-->
      <!--        :disabled="loading.disabled"-->
      <!--        :pulling-text="$t('pullToRefresh')"-->
      <!--        :loading-text="$t('loading')"-->
      <!--        :loosing-text="$t('looseToRefresh')"-->
      <!--        @refresh="onRefresh('gold')"-->
      <!--      >-->
      <!--        <van-list-->
      <!--          v-model:loading="loading.gold"-->
      <!--          :finished="loading.finished"-->
      <!--          :loading-text="$t('loading')"-->
      <!--          :finished-text="$t('noMore')"-->
      <!--          @load="onRefresh('gold')"-->
      <!--        >-->
      <!--          <van-skeleton-->
      <!--            :loading="loading.gold && !list.gold.length"-->
      <!--            style="display: block; padding: 0"-->
      <!--          >-->
      <!--            <template #template>-->
      <!--              <div class="gold-item">-->
      <!--                <div class="flex-between-center flex-1">-->
      <!--                  <div class="flex-center-start flex-column w-full h-full">-->
      <!--                    <van-skeleton-title-->
      <!--                      style="height: 20px"-->
      <!--                      title-width="20%"-->
      <!--                    />-->
      <!--                    <van-skeleton-paragraph-->
      <!--                      class="mt-6"-->
      <!--                      row-width="40%"-->
      <!--                    />-->
      <!--                  </div>-->
      <!--                  <van-skeleton-title-->
      <!--                    title-width="20%"-->
      <!--                    style="height: 20px"-->
      <!--                  />-->
      <!--                </div>-->
      <!--              </div>-->
      <!--            </template>-->
      <!--            <van-cell-group-->
      <!--              v-for="gold in list.gold"-->
      <!--              :key="gold.id"-->
      <!--              class="gold-item w-full"-->
      <!--              inset-->
      <!--            >-->
      <!--              <div class="flex-between-center">-->
      <!--                <div class="flex-center-start flex-column">-->
      <!--                  <h4>{{ gold.product_type_text }}</h4>-->
      <!--                  <span>{{ gold.create_time?.toString().substring(0, 16) }}</span>-->
      <!--                </div>-->
      <!--                <p>{{ gold.type === 1 ? '+' : '-' }}{{ gold.gold_amount }}</p>-->
      <!--              </div>-->
      <!--            </van-cell-group>-->
      <!--          </van-skeleton>-->
      <!--        </van-list>-->
      <!--      </van-pull-refresh>-->
      <!--    </div>-->
      <!--    <div-->
      <!--      v-else-->
      <!--      class="empty-box flex-center-center"-->
      <!--    >-->
      <!--      <Empty :text="$t('noMoreGoldDetails')">-->
      <!--        <template #empty-img>-->
      <!--          <img-->
      <!--            src="@/assets/images/mine/follow-empty.png"-->
      <!--            alt="empty"-->
      <!--          />-->
      <!--        </template>-->
      <!--      </Empty>-->
      <!--    </div>-->
      <!--  </van-tab>-->
      <!--  <van-tab-->
      <!--    :title="$t('crystalDetails')"-->
      <!--    name="crystal"-->
      <!--  >-->
      <!--    <div-->
      <!--      v-if="list.crystal.length || loading.crystal"-->
      <!--      class="crystal-list w-full"-->
      <!--    >-->
      <!--      <van-pull-refresh-->
      <!--        v-model="loading.refreshing"-->
      <!--        :disabled="loading.disabled"-->
      <!--        :pulling-text="$t('pullToRefresh')"-->
      <!--        :loading-text="$t('loading')"-->
      <!--        :loosing-text="$t('looseToRefresh')"-->
      <!--        @refresh="onRefresh('crystal')"-->
      <!--      >-->
      <!--        <van-list-->
      <!--          v-model:loading="loading.crystal"-->
      <!--          :finished="loading.finished"-->
      <!--          :loading-text="$t('loading')"-->
      <!--          :finished-text="$t('noMore')"-->
      <!--          @load="onRefresh('crystal')"-->
      <!--        >-->
      <!--          <van-skeleton-->
      <!--            :loading="loading.crystal && !list.crystal.length"-->
      <!--            style="display: block; padding: 0"-->
      <!--          >-->
      <!--            <template #template>-->
      <!--              <div class="crystal-item">-->
      <!--                <div class="flex-between-center flex-1">-->
      <!--                  <div class="flex-center-start flex-column w-full h-full">-->
      <!--                    <van-skeleton-title-->
      <!--                      style="height: 20px"-->
      <!--                      title-width="20%"-->
      <!--                    />-->
      <!--                    <van-skeleton-paragraph-->
      <!--                      class="mt-6"-->
      <!--                      row-width="40%"-->
      <!--                    />-->
      <!--                  </div>-->
      <!--                  <van-skeleton-title-->
      <!--                    title-width="20%"-->
      <!--                    style="height: 20px"-->
      <!--                  />-->
      <!--                </div>-->
      <!--              </div>-->
      <!--            </template>-->
      <!--            <van-cell-group-->
      <!--              v-for="crystal in list.crystal"-->
      <!--              :key="crystal.id"-->
      <!--              class="crystal-item w-full"-->
      <!--              inset-->
      <!--            >-->
      <!--              <div class="flex-between-center">-->
      <!--                <div class="flex-center-start flex-column">-->
      <!--                  <h4>{{ crystal.product_type_text }}</h4>-->
      <!--                  <span>{{ crystal.create_time?.toString().substring(0, 16) }}</span>-->
      <!--                </div>-->
      <!--                <p>{{ crystal.type === 1 ? '+' : '-' }}{{ crystal.crystal_amount }}</p>-->
      <!--              </div>-->
      <!--            </van-cell-group>-->
      <!--          </van-skeleton>-->
      <!--        </van-list>-->
      <!--      </van-pull-refresh>-->
      <!--    </div>-->
      <!--    <div-->
      <!--      v-else-->
      <!--      class="empty-box flex-center-center"-->
      <!--    >-->
      <!--      <Empty :text="$t('noMoreCrystalDetails')">-->
      <!--        <template #empty-img>-->
      <!--          <img-->
      <!--            src="@/assets/images/mine/follow-empty.png"-->
      <!--            alt="empty"-->
      <!--          />-->
      <!--        </template>-->
      <!--      </Empty>-->
      <!--    </div>-->
      <!--  </van-tab>-->
      <!--</van-tabs>-->
      <div
        v-if="list.crystal.length || loading.crystal"
        class="crystal-list w-full"
      >
        <van-pull-refresh
          v-model="loading.refreshing"
          :disabled="loading.disabled"
          :pulling-text="$t('pullToRefresh')"
          :loading-text="$t('loading')"
          :loosing-text="$t('looseToRefresh')"
          @refresh="onRefresh('crystal')"
        >
          <van-list
            v-model:loading="loading.crystal"
            :finished="loading.finished"
            :loading-text="$t('loading')"
            :finished-text="$t('noMore')"
            @load="onRefresh('crystal')"
          >
            <van-skeleton
              :loading="loading.crystal && !list.crystal.length"
              style="display: block; padding: 0"
            >
              <template #template>
                <div class="crystal-item">
                  <div class="flex-between-center flex-1">
                    <div class="flex-center-start flex-column w-full h-full">
                      <van-skeleton-title
                        style="height: 20px"
                        title-width="20%"
                      />
                      <van-skeleton-paragraph
                        class="mt-6"
                        row-width="40%"
                      />
                    </div>
                    <van-skeleton-title
                      title-width="20%"
                      style="height: 20px"
                    />
                  </div>
                </div>
              </template>
              <van-cell-group
                v-for="crystal in list.crystal"
                :key="crystal.id"
                class="crystal-item w-full"
                inset
              >
                <div class="flex-between-center">
                  <div class="flex-center-start flex-column">
                    <h4>{{ crystal.product_type_text }}</h4>
                    <span>{{ crystal.create_time?.toString().substring(0, 16) }}</span>
                  </div>
                  <p>{{ crystal.type === 1 ? '+' : '-' }}{{ crystal.crystal_amount }}</p>
                </div>
              </van-cell-group>
            </van-skeleton>
          </van-list>
        </van-pull-refresh>
      </div>
      <div
        v-else
        class="empty-box flex-center-center"
      >
        <Empty :text="$t('noMoreCrystalDetails')">
          <template #empty-img>
            <img
              src="@/assets/images/mine/follow-empty.png"
              alt="empty"
            />
          </template>
        </Empty>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.wallet-detail {
  overflow: hidden;

  :deep(.van-tabs__nav) {
    background: linear-gradient(180deg, rgba(24, 24, 24, 50%) 0%, rgba(24, 24, 24, 0%) 100%);
  }

  :deep(.van-tabs__content) {
    height: calc(100% - 46px);
    overflow: hidden auto;
  }

  :deep(.van-tabs__line) {
    height: 2px !important;
    background: linear-gradient(90deg, #ff35f2 0%, #8459ff 100%);
  }

  :deep(.van-pull-refresh) {
    height: 100%;
    overflow: inherit;
  }

  :deep(.van-tab__panel) {
    height: 100%;
  }

  .gold-list,
  .crystal-list {
    height: 100%;
  }
}

.navbar {
  position: fixed;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 46px;
  padding: 0 16px;
  background-color: rgba(var(--van-nav-bar-background), 0.8);
  backdrop-filter: blur(10px);

  .navbar-wrapper {
    position: relative;
  }

  .back-icon {
    position: absolute;
    left: 0;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.main {
  height: calc(100% - 46px);
  margin-top: 46px;
}

.wallet-tabs {
  //height: 100%;

  :deep(.van-tabs) {
    height: 100%;
  }

  .gold-list,
  .crystal-list {
    height: 100%;
    padding: 16px 16px 10px;
    overflow: hidden auto;
  }

  .gold-item,
  .crystal-item {
    box-sizing: border-box;
    padding: 10px 16px;
    margin: 0;
    margin-bottom: 16px;
    line-height: 1.5;
    background: #222;
    border-radius: 16px;

    :deep(.van-skeleton-paragraph) {
      margin-top: 0;
    }

    h4 {
      margin: 0;
      font-size: 16px;
      color: #fff;
    }

    .mt-6 {
      margin-top: 6px;
    }

    span {
      font-size: 14px;
      color: #abaeb6;
    }

    p {
      font-size: 20px;
      color: #f6295a;
    }
  }
}

.empty-box {
  padding: 60px 5%;

  img {
    width: 240px;
  }
}
</style>
