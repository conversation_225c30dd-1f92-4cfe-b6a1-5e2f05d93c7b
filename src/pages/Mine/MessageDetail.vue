<script setup lang="ts">
import { msgDetail } from '@/api/msg/index.ts'
import { UnReadMsgItem } from '@/api/msg/type.ts'
// import Empty from '@/components/Empty.vue'

const route = useRoute()
const router = useRouter()
const msgInfo = ref<UnReadMsgItem>()

const getMsgDetail = (msg_id: number) => {
  msgDetail({ msg_id }).then((res) => {
    if (res.code === 200) {
      msgInfo.value = res.data
    }
  })
}

function onBack() {
  if (window.history.state.back) history.back()
  else router.replace('/')
}

watch(
  () => route.query,
  (query) => {
    if (query.msg_id) {
      typeof query.msg_id === 'string' && getMsgDetail(Number(query.msg_id))
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <div class="message-detail">
    <van-nav-bar
      :title="$t('msgDetail')"
      :border="false"
      style="min-height: 50px"
      safe-area-inset-top
      fixed
    >
      <template #left>
        <van-icon
          name="arrow-left"
          size="18px"
          @click="onBack"
        />
      </template>
    </van-nav-bar>
    <div class="message-detail-container mt-navbar">
      <h4>{{ msgInfo?.title }}</h4>
      <span class="message-time">{{ msgInfo?.create_time }}</span>
      <div class="message-content">
        <p v-if="msgInfo?.content">{{ msgInfo?.content }}</p>
        <!--<div-->
        <!--  v-else-->
        <!--  class="empty-box flex-center-center"-->
        <!--&gt;-->
        <!--  <Empty :text="$t('noMoreContent')">-->
        <!--    <template #empty-img>-->
        <!--      <img-->
        <!--        src="@/assets/images/mine/follow-empty.png"-->
        <!--        alt="empty"-->
        <!--      />-->
        <!--    </template>-->
        <!--  </Empty>-->
        <!--</div>-->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.message-detail {
}

.message-detail-container {
  padding: 20px;
}

h4,
span,
p {
  line-height: 1.5;
  color: #fff;
}

h4 {
  margin: 0;
  font-size: 20px;
  font-weight: normal;
}

.message-time {
  display: inline-block;
  padding: 10px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 50%);
}

.message-content {
  font-size: 16px;
}

.empty-box {
  padding: 100px 0;

  img {
    width: 240px;
  }
}
</style>
