<script setup lang="ts">
import router from '@/router'
import { getUserInfo } from '@/api/mine'
import type { IUserInfo } from '@/api/mine/type'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { setFollow } from '@/api/search'
definePage({
  name: 'others',
  meta: {
    level: 2
  }
})
const { t } = useI18n()
const route = useRoute()
const clientId = ref<string>(route.query.id as string)
const mineInfo = ref<IUserInfo>({})
const memberInfo = ref<IUserInfo>({})
const memberFields = ['vip_name', 'vip_goods_id', 'vip_expiration_time', 'vip_type']
function setFollowHandle(client_id: number) {
  setFollow(client_id).then((res) => {
    if (res.code === 200) {
      getUserInfoHandle()
    }
  })
}
function getUserInfoHandle() {
  getUserInfo(Number(clientId.value)).then((res) => {
    if (res.code === 200) {
      memberInfo.value = JSON.parse(JSON.stringify(res.data, memberFields))
      mineInfo.value = res.data
    }
  })
}
function tabLabelClick(e: string) {
  router.push({ name: 'search', query: { searchLabel: e } })
}
getUserInfoHandle()
eventReport({
  event_type: EventTypeEnum.VISIT_OTHER_PROFILE,
  other_client_id: Number(clientId.value)
}).catch((err) => {
  console.warn(err)
})
</script>

<template>
  <div class="ohters-page-container">
    <van-nav-bar
      title=""
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-22"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <div class="mine-info-header">
      <MineInfo
        type="others"
        :mineInfo="mineInfo"
        @set-follow="setFollowHandle"
      />
    </div>
    <div class="role-banner">
      <div :class="['tab-item active']">{{ t('characterOption') }}</div>
      <RoleCardList
        :otherClientId="clientId"
        type="others"
        @tab-click="tabLabelClick"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
}

:deep(.mine-container-bg) {
  background: #181818 !important;
}

.ohters-page-container {
  padding: 56px 0 16px;

  .mine-info-header {
    padding: 0 16px;
  }

  .options-banner-top {
    position: fixed;
    top: 0;
    left: 50%;
    z-index: 999;
    width: 100vw;
    padding: 12px 16px;
    background-color: #181818;
    transition: all 0.3s ease-in-out;
    transform: translateX(-50%);

    .options-item {
      width: 24px;
      height: 24px;

      img {
        width: 100%;
        height: 100%;
      }

      &:last-child {
        margin-left: 16px;
      }
    }
  }

  .mine-container {
    margin-bottom: 36px;
  }

  .options-banner {
    padding: 12px 0;

    .options-item {
      width: 24px;
      height: 24px;

      img {
        width: 100%;
        height: 100%;
      }

      &:last-child {
        margin-left: 16px;
      }
    }
  }

  .role-banner {
    padding: 0 16px;

    .tab-item {
      position: relative;
      // width: 32px;
      margin-right: 16px;
      margin-bottom: 16px;
      color: rgba(255, 255, 255, 30%);

      &.active {
        color: #fff;

        &::after {
          position: absolute;
          bottom: -5px;
          left: 0;
          z-index: -1;
          width: 26px;
          height: 10px;
          content: '';
          background: linear-gradient(90deg, #d311c6 0%, rgba(211, 17, 198, 0%) 100%);
          border-radius: 22px;
        }
      }
    }
  }
}
</style>
