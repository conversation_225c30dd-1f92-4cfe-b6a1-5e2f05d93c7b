<script setup lang="ts">
import { getPriceList } from '@/api/mine'
import { ICrystalPromotion } from '@/api/mine/type'
import { useRouter } from 'vue-router'
import useUserStore from '@/stores/modules/user'
import SvgIcon from '@/components/SvgIcon.vue'
import { useModal } from '@/hooks/useModal'
import { query_price } from '@/api/mine/index'
import { usePopupMemoryStore } from '@/stores/modules/popupMemory.ts'
import { eventReport, EventTypeEnum } from '@/api/eventReport'

const userStore = useUserStore()
userStore.getUserInfo()
const { userInformation } = toRefs(userStore)
const router = useRouter()
const popupMemoryStore = usePopupMemoryStore()
definePage({
  name: 'crystal',
  meta: {
    level: 2
  }
})
const { t } = useI18n()
const scrollContainerRef = ref()
const currentIndex = ref<number | null>(null)
const priceList = ref<ICrystalPromotion[]>([])
const price = ref<string>('')
const isRequestAll = ref(false)
function selectCrystalSet(item: ICrystalPromotion) {
  currentIndex.value = item.id
  price.value = item.price
}
function getPriceListHandle() {
  const { close } = useModal({
    message: t('loading'),
    loading: true,
    autoClose: false
  })
  getPriceList()
    .then((res) => {
      if (res.code !== 200) return
      priceList.value = res.data
      isRequestAll.value = true
    })
    .finally(() => {
      close()
    })
}
function goToBuy() {
  // if (loading.value) return
  if (!currentIndex.value)
    return useModal({
      message: t('selectPurchaseItem'),
      duration: 1500
    })
  const { close } = useModal({
    message: t('loading'),
    loading: true,
    autoClose: false
  })
  let params = {
    product_type: 'crystal',
    product_id: currentIndex.value,
    order_type: 'newpay',
    front_address: sessionStorage.getItem('crystal_front_address') || ''
  }
  query_price(params)
    .then((res) => {
      router.push({
        name: 'Payment',
        query: { amount_id: res.data.amount_id, price: price.value }
      })
      sessionStorage.setItem('buyType', 'crystal')
    })
    .finally(() => {
      close()
    })
}

function goBack() {
  // sessionStorage.getItem('buyType') === 'crystal' ? router.push({ name: 'mine' }) : router.go(-1)
  const path = popupMemoryStore.getPopupMemoryPath()
  if (path) router.replace(path)
  else router.go(-1)
}

onMounted(() => {
  eventReport({
    event_type: EventTypeEnum.SHOW_BUY_CRYSTAL
  })
  getPriceListHandle()
})
</script>

<template>
  <div
    class="crystrl-container"
    ref="scrollContainerRef"
  >
    <div class="crystrl-container-header-bg">
      <img
        class="bg-img"
        src="@/assets/images/mine/crysta-header-gb.png"
        alt="crystrl-header-bg"
      />
    </div>
    <van-nav-bar
      :title="t('myCrystals')"
      :border="false"
      v-scroll-gradation-bg="{ scrollElement: scrollContainerRef, completedHeight: 200, startHeight: 50 }"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-24"
          @click="goBack"
        />
      </template>
      <template #right>
        <div class="nav-bar__btn flex-center-center">
          <SvgIcon
            icon-class="crystal-details"
            class="fsize-24"
            @click="router.push({ name: 'orderRecord', query: { active: 'crystal' } })"
          />
        </div>
      </template>
    </van-nav-bar>
    <div class="crystrl-banner">
      <div class="mine-crystal flex-start-center mb-42">
        <img
          class="mine-crystal-icon mr-12"
          src="@/assets/images/mine/mine-crystal.png"
          alt="mine-crysta"
        />
        <div
          class="mine-crystal-text"
          id="cystalHeaderTop"
        >
          <div class="mine-crystal-text-label mb-8">{{ t('remainingDiamonds') }}</div>
          <div class="mine-crystal-text-number">{{ userInformation.crystal_amount }}</div>
        </div>
      </div>
      <div class="crystrl-card-container">
        <div class="banner flex-start-center">
          <div
            class="card-item mr-13 mb-28 flex-center-center"
            :class="currentIndex === item.id ? 'active' : ''"
            v-for="item in priceList"
            :key="item.id"
            @click="selectCrystalSet(item)"
          >
            <div
              class="discount"
              v-if="item.discount > 0"
            >
              -{{ item.discount }}%
            </div>
            <div class="count mb-8 flex-center-center">
              <SvgIcon
                icon-class="crystal-mini"
                class="fsize-20"
              />
              <span>{{ item.amount }}</span>
            </div>
            <div class="price">${{ item.price }}</div>

            <div
              class="discount-price"
              :class="item.discount > 0 ? '' : 'hide'"
            >
              ${{ item.original_price }}
            </div>
          </div>
        </div>
      </div>
      <div
        class="buy-btn"
        @click="goToBuy"
      >
        {{ t('purchaseOption') }}
      </div>
    </div>
    <div class="crystrl-no-data">
      <EmptyIcon
        v-if="priceList.length === 0 && isRequestAll"
        :text="t('noData')"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
  background: transparent;
}

:deep(.van-nav-bar__arrow) {
  --van-padding-base: 16px;
}

.crystrl-container {
  height: 100%;
  padding: 64px 0;
  overflow-y: scroll;

  .crystrl-container-header-bg {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 100vh;
    background: #000;

    .bg-img {
      width: 100%;
      height: 231px;
    }
  }
}

.mine-crystal {
  padding: 10px 0 10px 20px;
  // background: url('~/images/mine/mine-crystal-bg.png') center / cover;
  background: rgba($color: #2d2d2d, $alpha: 50%);
  border-radius: 16px;

  .mine-crystal-icon {
    width: 76px;
    height: 76px;
  }

  .mine-crystal-text-label {
    font-size: 14px;
  }

  .mine-crystal-text-number {
    font-size: 32px;
    font-weight: 800;
    color: $livCoThemeColor;
  }
}

.crystrl-banner {
  padding: 0 16px;
  overflow-x: hidden;
}

.crystrl-card-container {
  width: 120%;

  .banner {
    flex-wrap: wrap;
    width: 105%;
  }

  .card-item {
    position: relative;
    flex-direction: column;
    width: 106px;
    height: 98px;
    padding: 22px 0 16px;
    background: #1a1a1a;
    border: 1px solid transparent;
    border-radius: 16px;

    .discount {
      position: absolute;
      top: -11px;
      left: 50%;
      width: 49px;
      padding: 3px 0;
      font-size: 14px;
      line-height: 14px;
      color: $livCoTextColor;
      text-align: center;
      background: $livCoThemeColor;
      border: 1px solid rgba(255, 255, 255, 50%);
      border-radius: 8px;
      transform: translateX(-50%);
    }

    .count {
      font-size: 18px;
      font-weight: 800;
      line-height: 21px;
      color: $livCoThemeColor;
    }

    .price {
      margin-bottom: 4px;
      font-size: 16px;
      font-weight: bold;
      color: #fff;
    }

    .discount-price {
      font-size: 12px;
      color: #615e5e;
      text-decoration: line-through;

      &.hide {
        opacity: 0;
      }
    }
  }

  .card-item.active {
    background: rgba(235, 223, 172, 20%);
    border: 1px solid $livCoThemeColor;
  }
}

.buy-btn {
  position: fixed;
  bottom: 16px;
  left: 50%;
  width: calc(100% - 32px);
  height: 52px;
  padding: 18px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 16px;
  color: $livCoTextColor;
  text-align: center;
  background: #ebdfac;
  background-size: 100% 100%;
  border-radius: 40px;
  transform: translateX(-50%);
}

.crystrl-no-data {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
