<script setup lang="ts">
import { setFollow } from '@/api/search'
import { IAiListItem } from '@/api/home/<USER>'
import { getFollowList, getFansList, getLoveList, setLike } from '@/api/mine'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { IFollowListItem } from '@/api/mine/type'
import router from '@/router'
import { useModal } from '@/hooks/useModal'
import useUserStore from '@/stores/modules/user'
definePage({
  name: 'follow',
  meta: {
    level: 2
  }
})
interface IParams {
  page: number
  limit: number
}

const { t } = useI18n()
const route = useRoute()
const title = ref<string>(route.query.name as string)
const active = ref<number>(0)
const cid = ref<string>(route.query.cid as string)
const userStore = useUserStore()
active.value = route.query.active ? Number(route.query.active) : 0
const fansList = ref<IFollowListItem[]>([])
const loveList = ref<IAiListItem[]>([])
const followList = ref<IFollowListItem[]>([])
const loading = ref(false)
const finished = ref(false)
const lovloading = ref(false)
const lovfinished = ref(false)
const folloading = ref(false)
const folfinished = ref(false)
const fansListParams = ref<IParams>({
  page: 1,
  limit: 10
})
const createrListParams = ref<IParams>({
  page: 1,
  limit: 10
})
const followListParams = ref<IParams>({
  page: 1,
  limit: 10
})
const codeList = [EventTypeEnum.OPEN_FANS_LIST, EventTypeEnum.OPEN_LIKE_LIST, EventTypeEnum.OPEN_FOLLOW_LIST]
tabClickHandle(active.value)
function tabClickHandle(val: number) {
  eventReport({
    event_type: codeList[val]
  }).catch((err) => {
    console.warn(err)
  })
}

function getFansListHandle() {
  const params = {
    ...fansListParams.value,
    client_id: Number(cid.value) ? Number(cid.value) : undefined
  }
  getFansList(params).then((res) => {
    if (res.code !== 200) return
    if (!res.data.list) return (finished.value = true)
    fansList.value.push(...res.data.list)
    // 加载状态结束
    fansListParams.value.page++
    loading.value = false
    if (fansList.value.length >= res.data.count) {
      finished.value = true
    }
  })
}

function getLoveListHandle() {
  const params = {
    ...createrListParams.value,
    client_id: Number(cid.value) ? Number(cid.value) : undefined
  }
  getLoveList(params).then((res) => {
    if (res.code !== 200) return
    if (!res.data.list) return (lovfinished.value = true)
    let list = res.data.list.map((item) => {
      return { ...item }
    })
    loveList.value.push(...list)
    // 加载状态结束
    createrListParams.value.page++
    lovloading.value = false
    if (loveList.value.length >= res.data.count) {
      lovfinished.value = true
    }
  })
}

function getFollowListHandle() {
  let params = {
    ...followListParams.value,
    client_id: Number(cid.value) ? Number(cid.value) : undefined
  }
  getFollowList(params).then((res) => {
    if (res.code !== 200) return
    if (!res.data.list) return (folfinished.value = true)
    let list = res.data.list.map((item) => {
      return { ...item }
    })
    followList.value.push(...list)
    // 加载状态结束
    followListParams.value.page++
    folloading.value = false
    if (followList.value.length >= res.data.count) {
      folfinished.value = true
    }
  })
}

const onLoad = () => {
  // 异步更新数据
  switch (active.value) {
    case 0:
      getFansListHandle()
      break
    case 1:
      getLoveListHandle()
      break
    case 2:
      getFollowListHandle()
      break
    default:
      break
  }
}

const isShowEmptyIcon = computed(() => {
  return (
    (active.value === 0 && fansList.value.length === 0) ||
    (active.value === 1 && loveList.value.length === 0) ||
    (active.value === 2 && followList.value.length === 0)
  )
})

function followHandle(item: IFollowListItem) {
  console.log(item.client_id)
  setFollow(item.client_id).then((res) => {
    if (res.code !== 200) return
    if (item.is_follow === 0) {
      item.is_follow = 1
      useModal({
        message: t('focusonSuccess'),
        duration: 1500
      })
    } else {
      useModal({
        message: t('focusonSuccess'),
        duration: 1500
      })
      item.is_follow = 0
    }
  })
}
function setLikeHandle(item: IAiListItem) {
  setLike({ ai_id: item.id }).then((res) => {
    if (res.code !== 200) return
    item.like_status = item.like_status === 0 ? 1 : 0
  })
}
function navToHomePage(id: number, type?: string) {
  // type === 'ai' ? router.push(`/AgentChat/${id}`) : router.push({ path: '/Mine/Others', query: { id } })
  if (type === 'ai') {
    router.push(`/chat/${id}`)
  } else {
    if (id === userStore.userInformation.client_id) {
      router.push({ path: '/Mine' })
      return
    }
    router.push({ path: '/Mine/Others', query: { id } })
  }
}
</script>
<template>
  <div class="follow-container">
    <van-nav-bar
      :title="title"
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-22"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <van-tabs
      v-model:active="active"
      line-width="33.33%"
      title-active-color="#FFF"
      title-inactive-color="rgba(255,255,255,0.5)"
      background="#181818"
      @change="tabClickHandle"
    >
      <van-tab
        :title="t('fansOption')"
        title-class="tab1"
      >
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text=""
          @load="onLoad"
        >
          <template #loading>
            <img
              class="loading-icon"
              src="../../assets/images/index/loading-icon.png"
              alt="loading"
            />
          </template>
          <div class="search-result-list">
            <div
              class="search-result-item flex-start-center"
              v-for="item in fansList"
              :key="item.client_id"
            >
              <div
                class="search-result-img"
                @click="navToHomePage(item.client_id)"
              >
                <van-image
                  :src="item.avatar_url"
                  alt="avatar"
                />
              </div>
              <div class="search-result-name">{{ item.name }}</div>
            </div>
          </div>
        </van-list>
      </van-tab>

      <van-tab
        :title="t('likesOption')"
        title-class="tab2"
      >
        <van-list
          v-model:loading="lovloading"
          :finished="lovfinished"
          finished-text=""
          @load="onLoad"
          class="search-list"
          offset="400"
        >
          <template #loading>
            <img
              class="loading-icon"
              src="../../assets/images/index/loading-icon.png"
              alt="loading"
            />
          </template>
          <div class="search-result-list">
            <div
              v-for="item in loveList"
              :key="item.client_id"
              class="search-result-item flex-start-center"
            >
              <div
                class="search-result-img"
                @click="navToHomePage(item.id, 'ai')"
              >
                <van-image
                  :src="item.avatar_url"
                  alt="avatar"
                />
              </div>
              <div class="search-result-name">{{ item.name }}</div>

              <div
                class="love-icon flex-center-center"
                @click="setLikeHandle(item)"
              >
                <SvgIcon
                  :icon-class="item.like_status === 1 ? 'love-active' : 'love'"
                  class="fsize-16"
                />
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>

      <van-tab
        :title="t('followOption')"
        title-class="tab3"
      >
        <van-list
          v-model:loading="folloading"
          :finished="folfinished"
          finished-text=""
          @load="onLoad"
        >
          <template #loading>
            <img
              class="loading-icon"
              src="../../assets/images/index/loading-icon.png"
              alt="loading"
            />
          </template>
          <div class="search-result-list">
            <div
              class="search-result-item flex-start-center"
              v-for="item in followList"
              :key="item.client_id"
            >
              <div
                class="search-result-img"
                @click="navToHomePage(item.client_id)"
              >
                <van-image
                  :src="item.avatar_url"
                  alt="avatar"
                />
              </div>
              <div class="search-result-name">{{ item.name }}</div>
              <div
                class="search-result-btn"
                v-if="item.is_me === 0"
              >
                <van-button
                  @click="followHandle(item)"
                  round
                  size="small"
                  :class="item.is_follow === 0 ? '' : 'follow-btn'"
                  >{{ item.is_follow === 0 ? t('followOption') : t('unfollowOption') }}</van-button
                >
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>
    <div
      class="search-result-nodata"
      v-if="isShowEmptyIcon"
    >
      <EmptyIcon
        url="follow"
        :text="t('emptySpaceNotice')"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.van-list) {
  height: 100%;
}

// :deep(.van-tabs) {
// padding: 0 16px;
// }

:deep(.van-tabs__wrap) {
  position: fixed;
  left: 50%;
  z-index: 99;
  width: calc(100% - 30px);
  transform: translateX(-50%);
}

:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
}

:deep(.van-image__error) {
  border-radius: 74px;
}

:deep(.van-image__loading) {
  border-radius: 74px;
}

.follow-container {
  // 加载数据问题
  height: 99.9%;
  padding-top: 40px;
  overflow-y: auto;

  .search-result-item {
    padding: 12px 16px;

    &:first-child {
      padding-top: 18px;
    }
  }

  .search-result-list {
    padding-top: 48px;

    .search-result-name {
      font-size: 16px;
      font-weight: 600;
      line-height: 16px;
    }
  }

  .love-icon {
    width: 32px;
    height: 32px;
    margin-left: auto;
    background: #232222;
    border-radius: 12px;
  }

  .search-result-btn {
    margin-left: auto;
  }

  .search-result-img {
    width: 48px;
    height: 48px;
    margin-right: 12px;

    :deep(.van-image) {
      width: 100%;
      height: 100%;

      img {
        border-radius: 50%;
      }
    }
  }

  .search-result-nodata {
    position: absolute;
    top: 50%;
    left: 50%;
    text-align: center;
    transform: translate(-50%, -50%);

    img {
      width: 142px;
      height: 114px;
    }

    .nodata-text {
      font-size: 13px;
      line-height: 20px;
      color: rgba(255, 255, 255, 50%);
    }
  }

  .mb-8 {
    margin-bottom: 8px !important;
  }

  .loading-icon {
    width: 24px;
    height: 24px;
    animation: loading 1s linear infinite;
  }

  @keyframes loading {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .no-result {
    position: fixed;
    bottom: 0;
    left: 50%;
    padding-bottom: 36px;
    text-align: center;
    transform: translateX(-50%);

    span {
      font-size: 14px;
      color: rgba(255, 255, 255, 50%);
    }

    .link {
      margin-left: 4px;
      color: #ff35f2;
    }
  }
}

$mainColor: #ff35f2;

:deep(.van-tabs) {
  margin-bottom: 8px;

  .van-tabs__line {
    width: calc((100% / 3) - 16px) !important;
    background: linear-gradient(90deg, $mainColor 0%, #8459ff 100%);

    --van-tabs-bottom-bar-height: 2px;
  }

  .van-tab--line.tab1 {
    padding-right: 8px !important;
    padding-left: 0 !important;
  }

  .van-tab--line.tab2 {
    padding-right: 8px !important;
    padding-left: 8px !important;
  }

  .van-tab--line.tab3 {
    padding-left: 8px !important;
  }
}

:deep(.van-button) {
  min-width: 74px;
  font-size: 12px;
  color: #fff;
  background: $mainColor;
  border: 1px solid $mainColor;

  &.follow-btn {
    color: $mainColor;
    background-color: rgba(255, 53, 242, 10%);
  }
}
</style>
