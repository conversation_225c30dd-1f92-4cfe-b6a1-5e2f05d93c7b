<script setup lang="ts">
import { userFigure } from '@/api/mine/index'
import { FigureListReq } from '@/api/mine/type.ts'

definePage({
  name: 'EditImage',
  meta: {
    level: 2
  }
})

const route = useRoute()
const router = useRouter()

const user_figure = ref<FigureListReq>({
  birthday: '2000-01-01'
})
const sexList = ref([
  { label: '男', value: 1, icon: 'mans' },
  { label: '女', value: 2, icon: 'girls' },
  { label: '无性别', value: 3, icon: 'nosex' }
])
const currentDate = ref(['2000', '01', '01'])
const showDate = ref(false)
const loading = ref(false)
const file_url = ref('')

function changeDate(e: { selectedValues: string[] }) {
  user_figure.value.birthday = e.selectedValues[0] + '-' + e.selectedValues[1] + '-' + e.selectedValues[2]
}

const submit = () => {
  if (loading.value) {
    return
  }
  loading.value = true
  const data = {
    action: route.query.action && !route.query.image_id ? 'add' : 'edit',
    nickname: user_figure.value.nickname,
    avatar_url: user_figure.value.avatar_url,
    birthday: user_figure.value.birthday,
    desc: user_figure.value.desc,
    sex: user_figure.value.sex,
    image_id: route.query.action === 'edit' ? Number(route.query.image_id) : undefined
  }
  userFigure(data)
    .then((res) => {
      if (res.code === 200) {
        if (window.history.state.back) history.back()
        else router.replace('/')
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const getUserFigureInfo = (image_id: number) => {
  const data = {
    action: 'list',
    image_id
  }
  userFigure(data).then((res) => {
    if (res.code === 200) {
      user_figure.value = res.data.list[0]
      file_url.value = user_figure.value.full_avatar_url
    }
  })
}

const changeBirthday = () => {
  changeDate({ selectedValues: currentDate.value })
  showDate.value = false
}

watch(
  () => route.query,
  (query) => {
    if (query.action === 'edit' && !!query.image_id) {
      getUserFigureInfo(Number(query.image_id))
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <div class="edit-image">
    <div
      class="navbar"
      v-scroll-gradation-bg="{ completedHeight: 150, startHeight: 100 }"
    >
      <div class="navbar-wrapper flex-center-center w-full h-full">
        <div
          class="flex-between-center back-icon"
          @click="$router.back()"
        >
          <SvgIcon
            icon-class="back"
            class="fsize-22"
          />
        </div>
        <div class="title">{{ $t('editFigure') }}</div>
      </div>
    </div>
    <div
      class="content"
      style="position: relative; padding: 0 16px 120px"
    >
      <div class="title">{{ $t('nicknameOption') }}</div>
      <van-field
        v-model="user_figure.nickname"
        maxlength="20"
      >
        <template #button> {{ user_figure.nickname?.length ?? 0 }}/20 </template>
      </van-field>
      <div class="title">{{ $t('genderOption') }}</div>
      <div class="sex flex-between-center fsize-14">
        <div
          :class="{ sexItem: true, 'flex-center-center': true, 'flex-column': true, lightSex: user_figure.sex === item.value }"
          v-for="item in sexList"
          :key="item.value"
          @click="
            () => {
              user_figure.sex = item.value
            }
          "
        >
          <SvgIcon
            :icon-class="item.icon"
            class="fsize-22"
          />
          {{ item.label }}
        </div>
      </div>
      <div class="title">{{ $t('birthdaySelection') }}</div>
      <div
        class="birth flex-between-center"
        @click="showDate = true"
      >
        <span v-if="!user_figure.birthday"></span>
        <span v-else>{{ user_figure.birthday }}</span>

        <SvgIcon
          icon-class="down-arrow"
          class="fsize-22"
        />
      </div>
      <div class="title flex-between-center">{{ $t('profile') }}</div>
      <div class="textarea">
        <van-field
          v-model="user_figure.desc"
          rows="4"
          autosize
          type="textarea"
          maxlength="150"
          show-word-limit
          placeholder=""
        />
      </div>
      <div
        class="buling flex-center-center"
        @click="submit"
      >
        <van-loading
          v-if="loading"
          type="spinner"
          color="white"
          size="24px"
        />
        <span>{{ $t('saveButton') }}</span>
      </div>
    </div>
  </div>
  <van-popup
    v-model:show="showDate"
    position="bottom"
    :style="{
      'border-radius': '24px 24px 0px 0px',
      background: '#232222'
    }"
    :overlay-style="{
      background: 'rgba(0, 0, 0, 0.6)'
    }"
    :close-on-click-overlay="false"
    @click-overlay="showDate = false"
  >
    <div
      class="pt-24 pb-10 pr-16 pl-16 flex-between-center"
      style="background: #232222"
    >
      <div class="birth-title">
        <span>{{ $t('selectBirthday') }}</span>
      </div>
      <van-icon
        name="cross"
        size="20"
        color="white"
        @click="showDate = false"
      />
    </div>
    <van-date-picker
      v-model="currentDate"
      :show-toolbar="false"
      @change="changeDate"
      :min-date="new Date(1900, 0, 1)"
      :max-date="new Date()"
    />
    <div
      class="change-birthday flex-center-center"
      @click="changeBirthday"
    >
      <span>{{ $t('confirmSelection') }}</span>
    </div>
  </van-popup>
</template>

<style lang="scss">
.van-theme-dark {
  --van-picker-mask-color: #232222;
}
</style>

<style lang="scss" scoped>
.navbar {
  position: fixed;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 46px;
  padding: 0 16px;
  background-color: rgba(var(--van-nav-bar-background), 0.8);
  backdrop-filter: blur(10px);

  .navbar-wrapper {
    position: relative;
  }

  .back-icon {
    position: absolute;
    left: 0;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.content {
  :deep(.van-image) {
    .van-image__img {
      border-radius: 50%;
    }
  }

  .camera {
    position: absolute;
    right: 0;
    bottom: 0;
  }

  .cut {
    position: absolute;
    top: -5px;
    left: -5px;
    z-index: 10;
    width: 28px;
    height: 28px;
  }

  .title {
    padding: 16px 0;
    font-size: 14px;
    font-weight: 500;
    color: #8b8889;
  }

  .van-cell {
    background: #232222;
    border-radius: 41px;
  }

  :deep(.van-field__control) {
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    background: #232222;
  }

  .sexItem {
    gap: 8px;
    width: 109px;
    height: 80px;
    background: #232222;
    border-radius: 41px;
  }

  .lightSex {
    background: rgba(255, 53, 242, 30%);
    border: 1px solid #ff35f2;
  }

  .birth {
    height: 48px;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 600;
    color: #fff;
    background: #232222;
    border-radius: 41px;
  }

  .textarea {
    .van-cell {
      border-radius: 16px;
    }

    :deep(.van-field__control) {
      font-size: 14px;
      line-height: 1.5;
      letter-spacing: 1px;
    }

    :deep(.van-field__control::placeholder) {
      font-size: 12px;
      line-height: 1.5;
      color: #424041;
    }

    :deep(.van-field__word-limit) {
      font-size: 14px;
      color: #8b8889;
      letter-spacing: 1px;
    }
  }
}

.buling {
  position: fixed;
  bottom: 32px;
  left: 50%;
  width: 327px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background-color: #ff35f2;
  border-radius: 40px;
  transform: translateX(-50%);
}

.change-birthday {
  width: 327px;
  height: 48px;
  margin: 0 auto 30px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background-color: #ff35f2;
  border-radius: 40px;
}

.pb-120 {
  padding-bottom: 120px;
}

:deep(.van-picker) {
  mask: linear-gradient(to bottom, transparent 0, transparent 10%, #232222, transparent 90%, transparent 100%);

  .van-picker__columns {
    position: relative;
    background: #232222;

    &::before {
      position: absolute;
      top: 41%;
      right: 0;
      left: 0;
      width: 86%;
      height: 46px;
      margin: auto;
      content: '';
      background: #2e2d2d;
      border-radius: 16px;
    }
  }
}

.birth-title {
  font-size: 18px;
  font-weight: 600;
}
</style>
