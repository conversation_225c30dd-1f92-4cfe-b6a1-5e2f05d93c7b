<script setup lang="ts">
import { copyText } from '@/utils'
import router from '@/router'
import useUserStore from '@/stores/modules/user'
import { ForwardAddressEnum } from '@/api/eventReport'
import Membership from '@/components/Mine/Membership.vue'
import type { IMemberInfo } from '@/api/login/types'

definePage({
  name: 'mine',
  meta: {
    level: 1
  }
})
const userStore = useUserStore()
const { userInformation } = toRefs(userStore)
const { t } = useI18n()
const showUnRead = computed(() => userStore.userInformation?.no_read_msg === 1)
const userToken = computed(() => userStore.userToken)
const userMemberInfo = ref<IMemberInfo>({
  vip_info: userInformation.value.vip_info,
  vip_count: userInformation.value.vip_count
})
const membershipRef = ref<InstanceType<typeof Membership> | null>()
if (userToken.value) {
  userStore.getUserInfo().then((res) => {
    userMemberInfo.value.vip_info = res.data.vip_info
    userMemberInfo.value.vip_count = res.data.vip_count
    membershipRef.value?.findHighestVipTypeHandle()
  })
}

function goMessage() {
  router.push('/Mine/Message')
}

function goSet() {
  router.push({ name: 'Set' })
}

const goTask = () => {
  router.push('/Task')
}

function goIllustratedGuide() {
  router.push('/illustratedGuide')
}

function goCrystal() {
  sessionStorage.setItem('crystal_front_address', ForwardAddressEnum.MY_PAGE)
  router.push('/Mine/Crystal')
}

const goFacebook = () => {
  window.open('https://www.facebook.com/ugenie.aichat')
}

const goDiscord = () => {
  window.open('https://discord.gg/Bb26UGAVa3')
}
// function tabLabelClick(e: string) {
//   router.push({ name: 'search', query: { searchLabel: e } })
// }
</script>

<template>
  <div class="mine-page-container">
    <div class="options-banner-top flex-end-center">
      <div
        class="options-item"
        @click="goMessage"
      >
        <IconSvgMessageIcon class="fsize-24" />
        <div
          v-if="showUnRead"
          class="un-read-point"
        ></div>
      </div>

      <div
        class="options-item"
        @click="goSet"
      >
        <IconSvgSettingIcon class="fsize-24" />
      </div>
    </div>
    <div class="info-card">
      <div class="flex-start-center mb-24">
        <van-image :src="userInformation.avatar_url" />
        <div class="msg-box pl-16">
          <div class="name mb-8">{{ userInformation.name }}</div>
          <div class="clientId flex-start-center">
            <span>ID: {{ userInformation.client_id }}</span>
            <IconSvgCopy
              class="fsize-12 ml-4"
              @click="copyText(userInformation.client_id)"
            />
          </div>
        </div>
      </div>

      <div class="registration-days flex-between-center">
        <div>{{ t('encounteredAlready').split('X')[0] }}</div>
        <div class="flex-start-center">
          <IconSvgDateIcon class="fsize-20 mr-4" />
          <span>{{ userInformation.eros_days }}{{ t('encounteredAlready').split('X')[1] }}</span>
        </div>
      </div>

      <!-- <Member class="mt-16 mb-12" /> -->
      <Membership
        ref="membershipRef"
        :userInformation="userMemberInfo"
        class="mb-12"
      />
      <div class="option-card">
        <div
          class="skin-card"
          @click="goIllustratedGuide"
        >
          <div class="card-item"></div>
          <div class="card-item2"></div>
          <div class="card-item3"></div>
          <div class="card-item4"></div>
          <div class="img-content">
            <img
              src="@/assets/images/mine/skin-folder.png"
              alt="folder"
            />
            <div class="text-content">
              <div class="title">我的图鉴</div>
              <div class="see-more">See more <IconSvgArrow class="fsize-12" /></div>
            </div>
          </div>
        </div>
        <div class="right-card">
          <div
            class="card-item crystal"
            @click="goCrystal"
          >
            <img
              src="@/assets/images/mine/diamond-newbg.png"
              alt="diamond"
              class="diamond-bg"
            />
            <div class="flex-start-center">
              <IconSvgCrystalMini class="fsize-16" />
              <div class="crystal-count">{{ userInformation.crystal_amount }}</div>
            </div>
            <div class="see-more">
              {{ t('myCrystals') }}
              <IconSvgArrow class="fsize-12" />
            </div>
          </div>
          <div
            class="card-item task-item flex-center-start"
            @click="goTask"
          >
            <img
              src="@/assets/images/mine/mascot.png"
              alt="mascot"
              class="mascot"
            />
            <div class="fsize-14">{{ t('task') }}</div>
            <div class="see-more flex-start-center">
              <span class="fsize-11">{{ t('seeMore') }}</span>
              <IconSvgArrow class="fsize-12" />
            </div>
          </div>
        </div>
      </div>
      <div class="community mt-24">
        <div class="title">{{ t('community') }}</div>
        <div class="community-list">
          <div
            class="community-item"
            @click="goFacebook"
          >
            <img
              src="@/assets/images/facebook.png"
              alt="facebook"
              width="28"
              height="28"
            />
            <div class="ml-12 fsize-14">Facebook</div>
            <IconSvgRightArrowGrey
              class="fsize-20"
              style="margin-left: auto"
            />
          </div>
          <div
            class="community-item"
            @click="goDiscord"
          >
            <img
              src="@/assets/images/discord.png"
              alt="discord"
              width="28"
              height="28"
            />
            <div class="ml-12 fsize-14">Discord</div>
            <div
              style="margin-left: auto"
              class="flex-center-center cg-4"
            >
              <div class="gift">{{ t('communityCrystal') }}</div>
              <IconSvgRightArrowGrey
                icon-class="right-arrow-grey"
                class="fsize-20"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.van-image__loading) {
  background: #242424;
  border-radius: 50% !important;
}

:deep(.van-image__error) {
  background-color: #242424;
  border-radius: 50% !important;
}

.mine-page-container {
  position: relative;
  height: 100%;
  padding-bottom: 80px;
  overflow-y: scroll;
  background: $livCoBackColor;

  .mine-container-header-bg {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 0;
    width: 100%;
    height: 295px;
    pointer-events: none;
  }

  .options-banner-top {
    position: fixed;
    top: 0;
    left: 50%;
    z-index: 999;
    width: 100vw;
    padding: 12px 16px;
    transition: all 0.3s ease-in-out;
    transform: translateX(-50%);

    .options-item {
      position: relative;
      width: 24px;
      height: 24px;

      img {
        width: 100%;
        height: 100%;
      }

      &:last-child {
        margin-left: 16px;
      }

      .un-read-point {
        position: absolute;
        top: -1px;
        right: 0;
        width: 6px;
        height: 6px;
        background: #ff482b;
        border-radius: 50%;
      }
    }
  }

  .info-card {
    padding: 20px 12px;
    margin-top: 48px;
    background-color: $livCoBackColor;
    border-radius: 16px 16px 0 0;

    .community {
      .title {
        font-size: 14px;
        font-weight: 600;
        line-height: 100%;
        color: #fff;
      }

      .community-list {
        display: flex;
        flex-direction: column;
        row-gap: 24px;
        padding: 16px 12px;
        margin-top: 12px;
        background: #151516;
        border-radius: 12px;

        .community-item {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;

          .name {
            font-size: 14px;
            font-weight: 400;
            line-height: 100%;
            color: #fff;
          }

          .gift {
            padding: 2px 8px;
            font-size: 10px;
            font-weight: 500;
            color: #fff;
            background: linear-gradient(0deg, #e74432, #e74432), linear-gradient(270deg, #f94647 0%, #ff6d3e 100%);
            border-radius: 12px;
          }
        }
      }
    }
  }

  .msg-box {
    flex: 1;
  }

  :deep(.van-image) {
    width: 88px;
    height: 88px;

    img {
      border: 2px solid #363636;
      border-radius: 50%;
    }
  }

  .name {
    font-size: 22px;
    font-weight: 500;
    line-height: 26px;
    color: #fff;
    word-break: break-all;
  }

  .clientId {
    width: fit-content;
    padding: 2px 8px;
    font-size: 12px;
    line-height: 14px;
    color: rgba(255, 255, 255, 50%);
    background: rgba(255, 255, 255, 17%);
    border-radius: 13px;
  }

  .registration-days {
    padding: 16px 16px 33px;
    margin-bottom: -18px;
    font-size: 14px;
    line-height: 16px;
    background: #151516;
    border: 1px solid #ffffff2e;
    border-radius: 12px;
  }

  .option-card {
    display: flex;
    column-gap: 12px;
  }

  .skin-card {
    position: relative;
    display: flex;
    flex-basis: 50%;
    flex-direction: column;
    justify-content: flex-end;
    background: rgba(21, 21, 22, 100%);
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 12px;

    .card-item {
      position: absolute;
      top: 9px;
      left: 6px;
      width: 59px;
      height: 95px;
      background: url('@/assets/images/mine/49b66793ce076fe5a88ddf758359650563fc73b7.png') no-repeat center center / cover;
      border: 1px solid #fff;
      border-radius: 8px;
      rotate: -7.74deg;
      animation: trans1 8s ease-in-out infinite;
    }

    @keyframes trans1 {
      0% {
        transform: translate(0%, 0%);
      }

      5% {
        transform: translate(0%, 0%);
      }

      10% {
        transform: translate(-5%, -5%);
      }

      20% {
        transform: translate(-6%, -6%);
      }

      30% {
        transform: translate(-5%, -5%);
      }

      40% {
        transform: translate(-5%, -5%);
      }

      45% {
        transform: translate(0%, 0%);
      }

      100% {
        transform: translate(0%, 0%);
      }
    }

    .card-item2 {
      position: absolute;
      top: 12px;
      left: 35px;
      width: 59px;
      height: 95px;
      background: url('@/assets/images/mine/49b66793ce076fe5a88ddf758359650563fc73b7.png') no-repeat center center / cover;
      border: 1px solid #fff;
      border-radius: 8px;
      rotate: -6.42deg;
      animation: trans2 8s ease-in-out infinite;
    }

    @keyframes trans2 {
      0% {
        transform: translate(0%, 0%);
      }

      5% {
        transform: translate(0%, 0%);
      }

      10% {
        transform: translate(-3%, -8%);
      }

      20% {
        transform: translate(-3%, -9%);
      }

      30% {
        transform: translate(-3%, -8%);
      }

      40% {
        transform: translate(-3%, -8%);
      }

      45% {
        transform: translate(0%, 0%);
      }

      100% {
        transform: translate(0%, 0%);
      }
    }

    .card-item3 {
      position: absolute;
      top: 4px;
      left: 64px;
      width: 59px;
      height: 95px;
      background: url('@/assets/images/mine/49b66793ce076fe5a88ddf758359650563fc73b7.png') no-repeat center center / cover;
      border: 1px solid #fff;
      border-radius: 8px;
      animation: trans3 8s ease-in-out infinite;
    }

    @keyframes trans3 {
      0% {
        transform: translateY(0);
      }

      5% {
        transform: translateY(0%);
      }

      10% {
        transform: translateY(-8%);
      }

      40% {
        transform: translateY(-8%);
      }

      45% {
        transform: translateY(0%);
      }

      /* 从 37.5% 到 100% 保持最终状态（相当于等待 5s） */
      100% {
        transform: translateY(0);
      }
    }

    .card-item4 {
      position: absolute;
      top: 10px;
      left: 100px;
      width: 59px;
      height: 95px;
      background: url('@/assets/images/mine/49b66793ce076fe5a88ddf758359650563fc73b7.png') no-repeat center center / cover;
      border: 1px solid #fff;
      border-radius: 8px;
      rotate: 11.22deg;
      animation: trans4 8s ease-in-out infinite;
    }

    @keyframes trans4 {
      0% {
        transform: translate(0%, 0%);
      }

      5% {
        transform: translate(0%, 0%);
      }

      10% {
        transform: translate(10%, -10%);
      }

      20% {
        transform: translate(11%, -11%);
      }

      30% {
        transform: translate(10%, -10%);
      }

      40% {
        transform: translate(10%, -10%);
      }

      45% {
        transform: translate(0%, 0%);
      }

      100% {
        transform: translate(0%, 0%);
      }
    }

    .img-content {
      position: relative;

      img {
        display: block;
        width: 100%;
      }

      .text-content {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        display: flex;
        flex-direction: column;
        row-gap: 8px;
        width: 100%;
        height: 100%;
        padding: 19px 12px 16px;
      }
    }

    //&::before {
    //  position: absolute;
    //  bottom: 0;
    //  left: 0;
    //  z-index: 2;
    //  width: 100%;
    //  height: 100%;
    //  content: '';
    //  background: url('@/assets/images/mine/task-bg.png') no-repeat center center / cover;
    //}
  }

  .right-card {
    display: flex;
    flex-basis: 50%;
    flex-direction: column;
    row-gap: 11px;
  }

  .card-item {
    position: relative;
    height: 54px;
    background: #151516;
    border: 1px solid #ffffff1a;
    border-radius: 12px;

    &.crystal {
      display: flex;
      flex-direction: column;
      row-gap: 6px;
      padding: 10px 12px 9px;

      .diamond-bg {
        position: absolute;
        right: 0;
        bottom: 0;
        height: 100%;
        border-radius: 0 0 12px;
      }

      .crystal-count {
        max-width: 150px;
        font-size: 14px;
        line-height: 100%;
        word-wrap: break-word;
      }
    }

    &.task-item {
      flex-direction: column;
      row-gap: 6px;
      padding: 10px 12px 9px;

      .mascot {
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 1;
        display: block;
        height: 90%;
      }
    }
  }

  .see-more {
    display: flex;
    align-items: center;
    font-size: 11px;
    line-height: 100%;
    color: #686e80;
  }
}
</style>
