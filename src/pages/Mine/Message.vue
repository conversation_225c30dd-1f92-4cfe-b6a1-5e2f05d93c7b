<script setup lang="ts">
import { systemList, interactionList, msgRead, allRead } from '@/api/msg/index'
// import Loading from '@/components/Loading.vue'
import { UnReadMsgItem } from '@/api/msg/type.ts'
import { Pagination } from '@/api/mine/type.ts'
import Empty from '@/components/Empty.vue'
import { debounce } from 'lodash-es'
// import { replaceStars } from '@/utils'

const router = useRouter()

const loading = reactive<{
  system: boolean
  interaction: boolean
  finished: boolean
  refreshing: boolean
  disabled: boolean
}>({
  system: false,
  interaction: false,
  finished: false,
  refreshing: false,
  disabled: false
})
const curTab = ref<'system' | 'interaction'>('system')
const systemData = ref<UnReadMsgItem[]>([])
const interactionData = ref<UnReadMsgItem[]>([])
const pagination = reactive<Pagination>({
  limit: 10,
  page: 0,
  total: 0
})

const onChangeTab = () => {
  pagination.total = 0
  pagination.page = 0
  loading.finished = false
  if (curTab.value === 'system') {
    interactionData.value = []
  } else {
    systemData.value = []
  }
  refreshFn()
}

const refreshFn = () => {
  return curTab.value === 'system' ? getSystemList() : getInteractionList()
}

function onBack() {
  if (window.history.state.back) history.back()
  else router.replace('/')
}

const getSystemList = () => {
  loading.system = true
  systemList({
    limit: pagination.limit,
    page: ++pagination.page
  })
    .then((res) => {
      if (res.code === 200) {
        pagination.total = res.data?.count
        if (pagination.total === systemData.value.length) {
          loading.finished = true
        } else {
          systemData.value.push(...res.data.list)
        }
      }
    })
    .finally(() => {
      loading.system = false
      loading.refreshing = false
    })
}

const getInteractionList = () => {
  loading.interaction = true
  interactionList({
    limit: pagination.limit,
    page: ++pagination.page
  })
    .then((res) => {
      if (res.code === 200) {
        pagination.total = res.data?.count
        if (pagination.total === interactionData.value.length) {
          loading.finished = true
        } else {
          interactionData.value.push(...res.data.list)
        }
      }
    })
    .finally(() => {
      loading.interaction = false
      loading.refreshing = false
    })
}

const onReadAll = debounce(() => {
  loading.system = true
  loading.interaction = true
  loading.refreshing = true
  allRead({
    type: curTab.value
  })
    .then(() => {})
    .finally(() => {
      systemData.value = []
      interactionData.value = []
      onChangeTab()
    })
}, 400)

const goMessageDetail = (msg_id: number) => {
  msgRead({ msg_id })
  router.push(`/Mine/MessageDetail?msg_id=${msg_id}`)
}

const onRefresh = () => {
  loading.finished = false
  loading.system = true
  refreshFn()
}

refreshFn()
</script>

<template>
  <van-nav-bar
    :title="$t('messageNotification')"
    :border="false"
    style="min-height: 50px"
    safe-area-inset-top
    fixed
  >
    <template #left>
      <van-icon
        name="arrow-left"
        size="18px"
        @click="onBack"
      />
    </template>
    <template #right>
      <div
        class="right-bar flex-center-center"
        @click="onReadAll"
      >
        <svg-icon
          icon-class="readAll"
          style="font-size: 20px"
        />
      </div>
    </template>
  </van-nav-bar>
  <div class="message-container">
    <!--<van-tabs-->
    <!--  v-model:active="curTab"-->
    <!--  line-width="35%"-->
    <!--  :shrink="false"-->
    <!--  @change="onChangeTab"-->
    <!--&gt;-->
    <!--  <van-tab-->
    <!--    name="system"-->
    <!--    :title="$t('systemMessages')"-->
    <!--  >-->
    <!--    <div class="system-message">-->
    <!--      <van-pull-refresh-->
    <!--        v-model="loading.refreshing"-->
    <!--        :disabled="loading.disabled"-->
    <!--        pulling-text=""-->
    <!--        loading-text=""-->
    <!--        loosing-text=""-->
    <!--        @refresh="onRefresh"-->
    <!--      >-->
    <!--        <van-list-->
    <!--          v-if="systemData.length"-->
    <!--          v-model:loading="loading.system"-->
    <!--          :finished="loading.finished"-->
    <!--          :finished-text="$t('noMore')"-->
    <!--          @load="refreshFn"-->
    <!--        >-->
    <!--          <div-->
    <!--            class="message-item flex-column flex-between-start"-->
    <!--            v-for="message in systemData"-->
    <!--            :key="message.id"-->
    <!--            @click="goMessageDetail(message.id)"-->
    <!--          >-->
    <!--            <h4>{{ message.title }}</h4>-->
    <!--            <p>{{ message.content }}</p>-->
    <!--            <div class="flex-between-center w-full">-->
    <!--              <span>{{ message.create_time }}</span>-->
    <!--              <div :class="[message.is_read === 0 ? 'un-read-point point' : 'read-point point']"></div>-->
    <!--            </div>-->
    <!--          </div>-->
    <!--        </van-list>-->
    <!--        <div-->
    <!--          v-else-->
    <!--          class="empty-box flex-center-center"-->
    <!--        >-->
    <!--          <Empty :text="$t('noMoreMsg')">-->
    <!--            <template #empty-img>-->
    <!--              <img-->
    <!--                src="@/assets/images/mine/follow-empty.png"-->
    <!--                alt="empty"-->
    <!--              />-->
    <!--            </template>-->
    <!--          </Empty>-->
    <!--        </div>-->
    <!--      </van-pull-refresh>-->
    <!--    </div>-->
    <!--  </van-tab>-->
    <!--<van-tab-->
    <!--  name="interaction"-->
    <!--  :title="$t('interactiveMessages')"-->
    <!--&gt;-->
    <!--  <div class="interact-message">-->
    <!--    <van-pull-refresh-->
    <!--      v-model="loading.refreshing"-->
    <!--      @refresh="onRefresh"-->
    <!--    >-->
    <!--      <van-list-->
    <!--        v-if="interactionData.length"-->
    <!--        v-model:loading="loading.interaction"-->
    <!--        :finished="loading.finished"-->
    <!--        :finished-text="$t('noMore')"-->
    <!--        @load="refreshFn"-->
    <!--      >-->
    <!--        <div-->
    <!--          class="message-item w-full flex-start-center"-->
    <!--          v-for="message in interactionData"-->
    <!--          :key="message.id"-->
    <!--        >-->
    <!--          <div class="avatar flex-center-center">-->
    <!--            <van-image-->
    <!--              round-->
    <!--              :src="message.promoter_client_info?.avatar_url"-->
    <!--              width="44px"-->
    <!--              height="44px"-->
    <!--              fit="cover"-->
    <!--              error-icon="photo-fail"-->
    <!--              icon-size="24px"-->
    <!--            >-->
    <!--              <template-->
    <!--                #loading-->
    <!--                v-if="message.promoter_client_info?.avatar_url"-->
    <!--              >-->
    <!--                <loading />-->
    <!--              </template>-->
    <!--              <template-->
    <!--                #error-->
    <!--                v-if="!message.promoter_client_info?.avatar_url"-->
    <!--              >-->
    <!--                <van-icon-->
    <!--                  name="photo-fail"-->
    <!--                  size="20"-->
    <!--                />-->
    <!--              </template>-->
    <!--            </van-image>-->
    <!--          </div>-->
    <!--          <div class="promoter-info flex-column flex-between-start w-full">-->
    <!--            <div class="info-top flex-between-center w-full">-->
    <!--              <h4>{{ message.promoter_client_info?.name }}</h4>-->
    <!--              <span class="message-item-time">{{ message.create_time?.toString().substring(5, 16) }}</span>-->
    <!--            </div>-->
    <!--            <div class="info-bottom flex-between-center w-full">-->
    <!--              <p class="message-item-content">-->
    <!--                {{ message.ai_id === 0 ? replaceStars($t('followedYouNotice')) : replaceStars($t('followedYour'), ['', message.ai_info?.name]) }}-->
    <!--              </p>-->
    <!--              <div :class="[message.is_read === 0 ? 'un-read-point point' : 'read-point point']"></div>-->
    <!--            </div>-->
    <!--          </div>-->
    <!--        </div>-->
    <!--      </van-list>-->
    <!--      <div-->
    <!--        v-else-->
    <!--        class="empty-box flex-center-center"-->
    <!--      >-->
    <!--        <Empty :text="$t('noMoreMsg')">-->
    <!--          <template #empty-img>-->
    <!--            <img-->
    <!--              src="@/assets/images/mine/follow-empty.png"-->
    <!--              alt="empty"-->
    <!--            />-->
    <!--          </template>-->
    <!--        </Empty>-->
    <!--      </div>-->
    <!--    </van-pull-refresh>-->
    <!--  </div>-->
    <!--</van-tab>-->
    <!--</van-tabs>-->
    <div class="system-message">
      <van-pull-refresh
        v-model="loading.refreshing"
        :disabled="loading.disabled"
        pulling-text=""
        loading-text=""
        loosing-text=""
        @refresh="onRefresh"
      >
        <van-list
          v-if="systemData.length"
          v-model:loading="loading.system"
          :finished="loading.finished"
          :finished-text="$t('noMore')"
          @load="refreshFn"
        >
          <div
            class="message-item"
            v-for="message in systemData"
            :key="message.id"
            @click="goMessageDetail(message.id)"
          >
            <h4 v-if="message.title">{{ message.title }}</h4>
            <p v-if="message.content">{{ message.content }}</p>
            <div class="flex-between-center w-full">
              <span>{{ message.create_time }}</span>
              <div :class="[message.is_read === 0 ? 'un-read-point point' : 'read-point point']"></div>
            </div>
          </div>
        </van-list>
        <div
          v-else
          class="empty-box flex-center-center"
        >
          <Empty :text="$t('noMoreMsg')">
            <template #empty-img>
              <img
                src="@/assets/images/mine/follow-empty.png"
                alt="empty"
              />
            </template>
          </Empty>
        </div>
      </van-pull-refresh>
    </div>
  </div>
</template>

<style lang="scss">
.van-tabs__nav {
  background: #181818;
}

.van-pull-refresh,
.van-tab__panel,
.van-list {
  height: 100%;
}

.van-pull-refresh {
  overflow: auto;
}
</style>

<style lang="scss" scoped>
@import '../../assets/styles/mixin';

.loading-box,
.empty-box {
  padding: 120px 0;

  img {
    width: 240px;
  }
}

.message-container {
  padding-top: 46px;

  :deep(.van-tabs__line) {
    z-index: 0;
    height: 2px !important;
    background: linear-gradient(90deg, #ff35f2 0%, #8459ff 100%);
  }
}

h1 {
  font-size: 16px;
  color: #fff;
}

h4,
p,
span {
  padding: 0;
  margin: 0;
  line-height: 1.5;
}

h4 {
  width: 100%;
  margin-bottom: 4px;
  font-size: 16px;
  font-weight: normal;
  line-height: 19px;
  color: #fff;

  @include singleLine();
}

p {
  margin: 0 0 8px;
  font-size: 14px;
  line-height: 16px;
  color: #9fa3aa;

  @include multiLine(3);
}

span {
  font-size: 12px;
  line-height: 14px;
  color: #4d4d4d;
}

.interact-message,
.system-message {
  //height: calc(100vh - 90px);
  height: calc(100vh - 50px);
}

.system-message {
  padding: 0 12px;

  .message-item {
    background: #232222;
  }
}

.message-item {
  box-sizing: border-box;
  width: 100%;
  padding: 12px 16px;
  margin-top: 12px;
  border-radius: 12px;

  .avatar {
    width: 44px;
    height: 44px;
    margin-right: 10px;
  }

  .promoter-info,
  .info-top,
  .info-bottom {
    flex-grow: 0;
    flex-shrink: 1;
  }

  .promoter-info {
    width: calc(100% - 44px - 10px);
  }

  h4,
  .message-item-time,
  .un-read-point,
  .message-item-content {
    display: inline-block;
    flex-grow: 0;
    flex-shrink: 1;
  }

  .point {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  .un-read-point {
    background-color: #fe133d;
  }

  .read-point {
    background-color: transparent;
  }

  .message-item-time {
    font-size: 12px;
    color: #4d4d4d;
    letter-spacing: 1px;
    white-space: nowrap;
  }

  .message-item-content {
    margin: 0;
    font-size: 14px;
    color: #9fa3aa;
  }
}
</style>
