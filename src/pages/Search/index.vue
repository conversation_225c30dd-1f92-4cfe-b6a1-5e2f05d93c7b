<script setup lang="ts">
// import { showToast } from 'vant'
import { ISearchRecordItem } from '@/api/search/types'
import HistorySearch from '@/components/Search/HistorySearch.vue'
import SearchResult from '@/components/Search/SearchResult.vue'
definePage({
  name: 'search'
})

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const searchValue = ref<string>((route.query.searchLabel as string) || '')
const isSearching = ref<boolean>(false)
if (route.query.searchLabel) {
  isSearching.value = true
}
const SearchResultRef = ref<InstanceType<typeof SearchResult>>()
const onSearch = (val: string) => {
  if (val === '') return
  isSearching.value = true
  // showToast(val)
  SearchResultRef.value?.resetParams()
  // SearchResultRef.value?.active === 0 ? SearchResultRef.value?.getAiListHandle() : SearchResultRef.value?.getCreaterListHandle()
}
const HistorySearchRef = ref<InstanceType<typeof HistorySearch>>()
const OnClear = () => {
  clearSearchValueHandle()
}
function clearSearchValueHandle() {
  isSearching.value = false
  searchValue.value = ''
  HistorySearchRef.value?.getSearchRecordHandle()
  if (sessionStorage.getItem('searchValue')) {
    sessionStorage.removeItem('searchValue')
  }
}
const OnCancel = () => {
  if (isSearching.value) {
    clearSearchValueHandle()
    return
  }
  if (sessionStorage.getItem('searchValue')) {
    sessionStorage.removeItem('searchValue')
  }
  router.go(-1)
}

const historyTabClick = (val: ISearchRecordItem) => {
  searchValue.value = val.content
  isSearching.value = true
}

if (sessionStorage.getItem('searchValue')) {
  searchValue.value = JSON.parse(sessionStorage.getItem('searchValue') as string).searchValue
  isSearching.value = true
}
</script>

<template>
  <div class="search-page-container">
    <div class="search-ipt">
      <Search
        v-model:modelValue="searchValue"
        :placeholder="t('searchAgentName')"
        @search="onSearch"
        @clear="OnClear"
        @cancel="OnCancel"
      />
    </div>
    <div
      class="history-search"
      v-show="!isSearching"
    >
      <HistorySearch
        ref="HistorySearchRef"
        @tab-click="historyTabClick"
      />
    </div>
    <div
      class="hot-role-list"
      v-if="!isSearching"
    >
      <HotRoleList />
    </div>
    <div
      class="search-result"
      v-if="isSearching"
    >
      <SearchResult
        ref="SearchResultRef"
        :searchValue="searchValue"
      />
    </div>
    <Feedback
      v-if="!isSearching"
      :positionWay="'absolute'"
    />
  </div>
</template>

<style scoped lang="scss">
.search-page-container {
  position: relative;
  min-height: 100vh;
  padding: 48px 16px 0;
  background: $livCoBackColor;

  .hot-role-list {
    padding-bottom: 116px;
  }

  .search-ipt {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    width: 100vw;
    padding: 4px 12px;
    background-color: $livCoBackColor;
  }

  .history-search {
    margin: 24px 0 20px;
  }

  .search-result {
    margin-top: 8px;
  }

  .search-loading {
    font-size: 14px;
    color: rgba(255, 255, 255, 50%);
    text-align: center;
  }
}
</style>
