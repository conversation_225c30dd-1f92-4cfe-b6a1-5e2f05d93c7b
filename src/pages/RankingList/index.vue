<script lang="ts" setup>
import RankList from './component/RankList.vue'
// import { EventTypeEnum } from '@/api/eventReport'
import { getAlbumList } from '@/api/ranking'
import useHomeStore from '@/stores/modules/home.ts'
defineOptions({
  name: 'RankingList'
})
definePage({
  name: 'RankingList',
  meta: {
    level: 1,
    keepAlive: true
  }
})
interface Itag {
  id: number
  name: string
}
// const { t } = useI18n()
const homeStore = useHomeStore()
const tagList = ref<Itag[]>([])
const rankListRef = ref<InstanceType<typeof RankList> | null>(null)

const currentTag = ref()
function getAlbumsList() {
  getAlbumList().then((res) => {
    if (res.code === 200) {
      tagList.value = res.data
      currentTag.value = res.data[0].id
      console.log(currentTag.value)
      rankListRef.value?.refresh()
      console.log(currentTag.value)
    }
  })
}
function tabChangeHandle(status: number) {
  if (rankListRef.value.loading) return
  currentTag.value = status
}

onActivated(() => {
  if (homeStore.isUpdateAlbum) {
    getAlbumsList()
    homeStore.setUpdateAlbum(false)
  }
})
getAlbumsList()
onDeactivated(() => {
  console.log(currentTag.value)
})
</script>

<template>
  <div class="rankingList-container">
    <!-- <div class="title">{{ t('leaderboard') }}</div> -->
    <div class="tab-wrap">
      <!-- <div
        class="line"
      ></div> -->
      <div
        v-for="tag in tagList"
        :key="tag.id"
        @click="tabChangeHandle(tag.id)"
        class="tab-item"
        :class="[currentTag === tag.id ? 'active-item' : '']"
      >
        {{ tag.name }}
        <div class="line"></div>
      </div>
    </div>
    <RankList
      ref="rankListRef"
      :type="currentTag"
    />
  </div>
</template>

<style lang="scss" scoped>
.rankingList-container {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: 74px;
  overflow: hidden;

  .bg-img {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 209px;
  }

  .title {
    padding: 13px 0 12px 16px;
    font-size: 20px;
    font-weight: 500;
    line-height: 23px;
    color: #fff;
  }

  .ranking-icon {
    position: absolute;
    top: -10px;
    right: -5px;
    z-index: 0;
    width: 174px;
    height: 182px;
    background: url('~@/assets/images/search/ranking-icon.png');
    background-size: contain;
  }

  .tab-wrap {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: start;
    min-height: 42px;
    margin-top: 12px;
    margin-bottom: 16px;
    overflow-x: scroll;
    white-space: nowrap;
  }

  .tab-item {
    position: relative;
    height: 18px;
    margin: 0 12px;
    font-size: 16px;
    font-weight: 500;
    line-height: 18px;
    color: #9da0a6;
    text-align: center;
  }

  .left-item {
    left: 25% !important;
  }

  .right-item {
    left: 75% !important;
  }

  .active-item {
    font-size: 18px;
    color: #fff !important;

    .line {
      position: absolute;
      bottom: -10px;
      left: 50%;
      z-index: -1;
      width: 100%;
      height: 2px;
      background: #ebdfac;
      transition: 0.3s;
      transform: translateX(-50%);
    }
  }
}
</style>
