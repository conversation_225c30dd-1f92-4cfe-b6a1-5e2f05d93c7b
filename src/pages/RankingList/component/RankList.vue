<script lang="ts" setup>
import { getRankingList } from '@/api/ranking'
import { IRankingItem, IRankingParams } from '@/api/ranking/types'
import useAppStore from '@/stores/modules/app'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

const props = defineProps({
  type: {
    type: Number,
    default: 0
  }
})
const appStore = useAppStore()
const router = useRouter()
const rankingList = ref<IRankingItem[]>([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const rankParams = ref<IRankingParams>({
  page: 1,
  limit: 10,
  sort_type: 1,
  album_id: props.type
})

const reportTypeList = [ForwardAddressEnum.POPULARITY_CHART, ForwardAddressEnum.FLOWER_LIST]

// 控制标签显示逻辑
const visibleTagsMap = ref<Record<number, number>>({})

// 计算每个角色项可显示的标签数量
const calculateVisibleTags = () => {
  nextTick(() => {
    const items = document.querySelectorAll('.hot-role-item')

    items.forEach((item, index) => {
      const infoContainer = item.querySelector('.hot-role-content__info') as HTMLElement
      if (!infoContainer) return

      const tagContainer = infoContainer.querySelector('.tag-container') as HTMLElement
      if (!tagContainer) return

      const infoWidth = infoContainer.offsetWidth
      const popularityWidth = (infoContainer.querySelector('.info-popularity') as HTMLElement)?.offsetWidth || 0
      const availableWidth = infoWidth - (popularityWidth + 17) // 减去间距和一些余量

      // 如果可用宽度太小，直接设置为0
      if (availableWidth <= 0) {
        if (rankingList.value[index]) {
          visibleTagsMap.value[rankingList.value[index].id] = 0
        }
        return
      }
      //
      // console.log(infoWidth, 'infoWidth')
      // console.log(popularityWidth, 'popularityWidth')
      // console.log(availableWidth, 'availableWidth')

      const tags = Array.from(tagContainer.querySelectorAll('.tag'))
      let totalWidth = 0
      let visibleCount = 0

      // 创建一个临时的tag容器来测量宽度
      const tempTagContainer = document.createElement('div')
      tempTagContainer.style.cssText = `
          position: absolute;
          top: -9999px;
          left: -9999px;
          visibility: hidden;
          display: flex;
          flex-wrap: nowrap;
        `
      document.body.appendChild(tempTagContainer)

      try {
        for (let i = 0; i < tags.length; i++) {
          const tag = tags[i] as HTMLElement

          // 克隆实际的标签元素来获取准确宽度
          const clonedTag = tag.cloneNode(true) as HTMLElement
          clonedTag.style.display = 'block'
          clonedTag.style.visibility = 'visible'
          clonedTag.style.position = 'static'
          tempTagContainer.appendChild(clonedTag)

          const tagWidth = clonedTag.offsetWidth
          const marginLeft = 8 // 根据CSS，所有标签都有margin-left: 8px

          // 检查加上这个标签后是否会超出可用宽度
          if (totalWidth + tagWidth + marginLeft > availableWidth) {
            break
          }

          totalWidth += tagWidth + marginLeft
          visibleCount++
          // console.log(`Tag ${visibleCount}: "${tag.textContent}" width=${tagWidth}, totalWidth=${totalWidth}`)
        }
      } finally {
        // 清理临时容器
        if (tempTagContainer.parentNode) {
          document.body.removeChild(tempTagContainer)
        }
      }

      // console.log(visibleCount, 'visibleCount')

      if (rankingList.value[index]) {
        visibleTagsMap.value[rankingList.value[index].id] = visibleCount
      }
    })
  })
}

// 在列表数据更新后计算可见标签
watch(
  () => rankingList.value,
  () => {
    calculateVisibleTags()
  },
  { deep: true }
)

// 窗口大小变化时重新计算
onMounted(() => {
  window.addEventListener('resize', calculateVisibleTags)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', calculateVisibleTags)
})

// 定义onLoad函数
const onLoad = () => {
  if (refreshing.value) {
    rankingList.value = []
    loading.value = true
    refreshing.value = false
  }
  if (props.type) {
    getHotSearchHandle()
  }
  // 列表加载完成后计算可见标签
  nextTick(() => {
    calculateVisibleTags()
  })
}

function navToChat(ai_id: number) {
  eventReport({
    event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
    front_address: reportTypeList[rankParams.value.sort_type - 1],
    ai_id: ai_id
  }).catch((err) => {
    console.warn(err)
  })
  const element = document.querySelector('.rank-container')
  appStore.scrollTop = element.scrollTop
  router.push(`/chat/${ai_id}`)
}
onActivated(() => {
  const element = document.querySelector('.rank-container')
  element.scrollTo(0, appStore.scrollTop)
})
function getHotSearchHandle() {
  if (!rankParams.value.album_id) return
  loading.value = true
  getRankingList(rankParams.value)
    .then((res) => {
      if (res.code !== 200) return
      if (!res.data.list) return (finished.value = true)
      rankingList.value.push(...res.data.list)
      rankParams.value.page++
      if (rankingList.value.length >= res.data.count) {
        finished.value = true
      }
    })
    .catch(() => {
      loading.value = false
      refreshing.value = false
      finished.value = true
    })
    .finally(() => {
      loading.value = false
      refreshing.value = false
    })
}
const onRefresh = () => {
  // 清空列表数据
  finished.value = false
  loading.value = true
  onLoad()
}

const refresh = () => {
  rankParams.value.sort_type = 1
  rankParams.value.album_id = props.type
  console.log(rankParams.value, props.type)
  rankParams.value.page = 1
  rankingList.value = []
  finished.value = false
  getHotSearchHandle()
}
watch(
  () => props.type,
  () => {
    refresh()
  }
)

defineExpose({
  loading,
  refresh
})
</script>

<template>
  <div class="rank-container">
    <van-pull-refresh
      :pulling-text="$t('pullToRefresh')"
      :loading-text="$t('loading')"
      :loosing-text="$t('looseToRefresh')"
      :disabled="true"
      v-model="refreshing"
      @refresh="onRefresh"
    >
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :loading-text="$t('loading')"
        :finished-text="''"
        @load="onLoad"
      >
        <template #loading>
          <img
            class="loading-icon"
            src="@/assets/images/index/loading-icon.png"
            alt="loading"
          />
        </template>
        <div
          class="out-wrap"
          v-for="item in rankingList"
          :key="item.id"
        >
          <div
            class="hot-role-item flex-between-center"
            @click="navToChat(item.id)"
          >
            <van-image
              class="hot-role-img"
              :src="item.avatar_url"
              alt="hot-role-img"
              fit="cover"
              position="top"
            />
            <div class="hot-role-content">
              <div class="hot-role-content__title">{{ item.name }}</div>
              <div class="hot-role-content__text">{{ item.synopsis }}</div>
              <div class="hot-role-content__info flex-start-center">
                <div class="info-popularity flex-between-center">
                  <div class="view flex-between-center">
                    <SvgIcon
                      icon-class="chat-times"
                      class="fsize-14"
                    />
                    <div class="ml-2">{{ item.chat_times }}</div>
                  </div>
                  <div
                    class="love flex-between-center"
                    v-if="props.type === 2"
                  >
                    <SvgIcon
                      :icon-class="'gift-icon'"
                      class="fsize-14 ml-6"
                    />
                    <div class="ml-2">{{ item.received_gift_count }}</div>
                  </div>
                </div>
                <div class="tag-container">
                  <div
                    v-for="(tagItem, tagIndex) in item.tag_list"
                    :key="tagItem.id"
                    class="tag"
                    v-show="tagIndex < (visibleTagsMap[item.id] || 0)"
                  >
                    {{ tagItem.name }}
                  </div>
                </div>
              </div>
            </div>
            <!-- <div :class="['hot-role-subscript flex-center-center', index < 3 ? `active${index + 1} br0` : '']">
              {{ index < 3 ? '' : index + 1 }}
            </div> -->
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<style lang="scss" scoped>
@import '@/assets/styles/mixin';

:deep(.van-list) {
  height: 100%;
}

:deep(.van-image__error) {
  border-radius: 8px;
}

.rank-container {
  flex: 1;
  padding: 0 16px;
  overflow-y: scroll;

  .info-author {
    display: flex;
    align-items: center;
    line-height: 14px;
    vertical-align: middle;
  }

  .info-popularity {
    padding: 3px 4px;
    font-size: 10px;
    background-color: rgba(235, 223, 172, 16%);
    border-radius: 6px;
  }

  .out-wrap {
    margin-bottom: 12px;
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    border: 1px solid transparent;
    border-radius: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .out-wrap-active1 {
    background-image: linear-gradient(to right, #1a1a1a, #1a1a1a), linear-gradient(90deg, rgba(255, 189, 90, 0%), rgba(255, 189, 90, 50%));
  }

  .out-wrap-active2 {
    background-image: linear-gradient(to right, #1a1a1a, #1a1a1a), linear-gradient(90deg, rgba(146, 187, 250, 0%), rgba(146, 187, 250, 30%));
  }

  .out-wrap-active3 {
    background-image: linear-gradient(to right, #1a1a1a, #1a1a1a), linear-gradient(90deg, rgba(255, 152, 118, 0%), rgba(255, 152, 118, 30%));
  }

  .hot-role-item {
    position: relative;
    max-height: 120px;
    padding-bottom: 12px;
    border-bottom: 1px solid #191a1b;

    :deep(.van-image) {
      img {
        border-radius: 8px;
      }
    }
  }

  .hot-role-img {
    flex-shrink: 0;
    width: 98px;
    height: 98px;
    margin-right: 12px;
    vertical-align: middle;
    border-radius: 12px;
  }

  .hot-role-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-around;
    height: 98px;
    overflow: hidden;
  }

  .hot-role-content__title {
    font-weight: 500;
    line-height: 16px;
  }

  .hot-role-content__text {
    width: 100%;
    padding: 0;
    margin-top: -8px;
    font-size: 13px;
    line-height: normal;
    color: rgba(255, 255, 255, 60%);

    @include multiLine(2);
  }

  .hot-role-content__info {
    font-size: 12px;
    color: rgba(255, 255, 255, 50%);

    .tag-container {
      display: flex;
      flex-wrap: nowrap;
      overflow: hidden;
    }

    .tag {
      height: 100%;
      padding: 4px 6px;
      margin-left: 8px;
      font-size: 10px;
      color: #ffffffb2;
      white-space: nowrap;
      background: #ffffff1a;
      border-radius: 6px;
    }

    .more-tag {
      background: rgba(255, 255, 255, 15%);
    }
  }

  .hot-role-subscript {
    position: absolute;
    top: 0;
    left: 0;
    min-width: 20px;
    min-height: 24px;
    padding: 4px 8px;
    font-size: 14px;
    font-weight: 900;
    color: #cbcbcb;
    background: #363535;
    border-radius: 12px 0;

    &.active1 {
      background: url('~/images/search/hot-icon-one.png') center / contain no-repeat;
    }

    &.active2 {
      background: url('~/images/search/hot-icon-two.png') center / contain no-repeat;
    }

    &.active3 {
      background: url('~/images/search/hot-icon-three.png') center / contain no-repeat;
    }

    &.br0 {
      border-radius: 0;
    }
  }
}

.loading-icon {
  width: 24px;
  height: 24px;
  animation: loading 1s linear infinite;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
