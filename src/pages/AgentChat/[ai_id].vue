<script setup lang="ts"></script>
<template>1</template>
<!--<script setup lang="ts">-->
<!--import Recorder from 'recorder-core/recorder.mp3.min.js'-->
<!--import '../../../scripts/frequency.histogram.view.cjs'-->
<!--import 'recorder-core/src/extensions/lib.fft.js'-->
<!--import 'recorder-core/src/extensions/buffer_stream.player.js'-->
<!--import anime from 'animejs'-->
<!--import { vOnLongPress } from '@vueuse/components'-->
<!--// import lottie, { AnimationItem } from 'lottie-web'-->
<!--import { PopoverPlacement } from 'vant'-->
<!--import { debounce } from 'lodash-es'-->
<!--// import { Howl } from 'howler'-->
<!--import { useIntersectionObserver } from '@vueuse/core'-->

<!--import { FAKE_COMMENT_END_FLAG, FAKE_COMMENT_START_FLAG, handleFakeCommentRecord, useHandleStream } from '@/hooks/useHandleStream.ts'-->
<!--// import animation from '@/assets/lottie/heart-diffuse.json'-->
<!--import { StringKeyValueTType } from '@/types'-->
<!--import { useModal } from '@/hooks/useModal.ts'-->
<!--import {-->
<!--  agentAudioChat,-->
<!--  agentTextChat,-->
<!--  bgmSetting,-->
<!--  getChatRecord,-->
<!--  getDressByID,-->
<!--  getSelfie,-->
<!--  getThreeDSource,-->
<!--  likeOrUnlikeAgentText,-->
<!--  setDress,-->
<!--  startNewChat-->
<!--  // ttsMessage,-->
<!--  // ttsMessageText-->
<!--} from '@/api/agentChat'-->
<!--import { ResultEnum } from '@/enums/httpEnum.ts'-->
<!--import useUserStore from '@/stores/modules/user.ts'-->
<!--import { BOOL_NUMBER, CHAT_BOX_STYLE_CLASS } from '@/constant'-->
<!--import { copyText, markdownToHtml, removeBracketsAndContent, replaceBracketsWithAsterisks, scrollToBottom } from '@/utils'-->
<!--import { AgentMessageType } from '@/api/agentHomePage/types.ts'-->
<!--import { getAIInfo } from '@/api/agentHomePage'-->
<!--import { IGiftListType, SelfieType, ThreeDSourceType } from '@/api/agentChat/types.ts'-->
<!--import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'-->
<!--import { useBackHistory } from '@/hooks/useCommon.ts'-->
<!--import { useConfirm } from '@/hooks/useConfirm.ts'-->
<!--import { eventReport, EventTypeEnum, ForwardAddressEnum, InteractiveTypeEnum } from '@/api/eventReport'-->
<!--import { ChatRecordTypeEnum, ICurrencyTypeEnum } from '@/enums'-->
<!--import { getAgentContentGameList, getAgentContentGameOfMyList, getSingleAgentContentGameInChatPage } from '@/api/contentGame'-->
<!--import { ContentGameStatusEnum, IContentListRes, IContentRes } from '@/api/contentGame/types.ts'-->
<!--import { POPUP_TYPE_NAME, usePopupMemoryStore } from '@/stores/modules/popupMemory.ts'-->

<!--let isIntimacyUpgrade: number = undefined-->
<!--const popupMemoryStore = usePopupMemoryStore()-->
<!--const { t } = useI18n()-->
<!--const bgmHowlerInstance = ref<Howl | null>(null)-->
<!--const live2dClear = ref<HTMLElement | null>(null)-->
<!--const chatPageRef = ref<HTMLElement | null>(null)-->
<!--const l2dRef = ref(null)-->
<!--const isClearMode = ref(false)-->
<!--const isFirstInNoAction = ref(true)-->
<!--const isNoRecord = ref(false)-->
<!--const hadStoryBook = ref(false)-->

<!--interface ChatRecordType {-->
<!--  timestamp?: number-->
<!--  type: ChatRecordTypeEnum-->
<!--  content: string-->
<!--  message_id?: string-->
<!--  replayTime?: number-->
<!--  // 一个数组可能有0,1,2个字符串元素-->
<!--  // replayContent?: [string?, string?]-->
<!--  is_like?: number-->
<!--  is_bad?: number-->
<!--  is_fake_comment?: boolean-->
<!--  is_phone_call?: number-->
<!--  is_later?: number-->
<!--  audio_file_url?: string-->
<!--  audio_length?: number-->
<!--  call_time?: number-->
<!--  ttsLoading?: boolean-->
<!--  ttsPlaying?: boolean-->
<!--  textLoading?: boolean-->
<!--  userTTS?: boolean-->
<!--  photoDetail?: SelfieType-->
<!--  photo_id?: number-->
<!--  content_id?: number-->
<!--  currency_type?: ICurrencyTypeEnum-->
<!--  price?: number-->
<!--  gift_info?: IGiftListType-->
<!--}-->

<!--enum QueryTypeEnum {-->
<!--  TIPS = 3,-->
<!--  UPGRADE = 101,-->
<!--  ENDING = 102-->
<!--}-->
<!--// const runningContextAudioForPhone = ref()-->
<!--const userStore = useUserStore()-->
<!--const route = useRoute()-->
<!--const CANCEL_DISTANCE = 60-->
<!--const router = useRouter()-->
<!--// 是否取消发送语音-->
<!--const isCancelDistance = computed(() => {-->
<!--  // 两个值有一个为0应该返回false-->
<!--  return Math.abs(slidePoint.start - slidePoint.end) > CANCEL_DISTANCE && slidePoint.start !== 0 && slidePoint.end !== 0-->
<!--})-->

<!--// @ts-ignore-->
<!--const aiID = ref(route.params.ai_id as number)-->

<!--const intimacyPercent = computed(() => {-->
<!--  const prevIndex = agentMessage.value.intimacy_level_info.findIndex((item) => item.level === agentMessage.value.ai_chat_setting.intimacy_level)-->
<!--  if (-->
<!--    prevIndex === agentMessage.value.intimacy_level_info.length - 1 &&-->
<!--    agentMessage.value.ai_chat_setting.intimacy_upgrade - agentMessage.value.intimacy_level_info[prevIndex]?.experience === 0-->
<!--  ) {-->
<!--    return '100%'-->
<!--  }-->
<!--  if (prevIndex !== -1) {-->
<!--    return (-->
<!--      ((agentMessage.value.ai_chat_setting.intimacy_now - agentMessage.value.intimacy_level_info[prevIndex].experience) /-->
<!--        (agentMessage.value.ai_chat_setting.intimacy_upgrade - agentMessage.value.intimacy_level_info[prevIndex].experience)) *-->
<!--        100 +-->
<!--      '%'-->
<!--    )-->
<!--  } else return (agentMessage.value.ai_chat_setting.intimacy_now / agentMessage.value.ai_chat_setting.intimacy_upgrade) * 100 + '%'-->
<!--})-->

<!--const agentMessage = ref<AgentMessageType>({-->
<!--  tag_list: [],-->
<!--  like_status: 0,-->
<!--  ai_chat_setting: {-->
<!--    image_id: undefined,-->
<!--    show_disclaimer: 0,-->
<!--    show_character: 0,-->
<!--    show_prologue: 0,-->
<!--    show_muse: 0,-->
<!--    intimacy_now: 0,-->
<!--    intimacy_upgrade: 0,-->
<!--    intimacy_level: 0,-->
<!--    intimacy_interest_tags: []-->
<!--  },-->
<!--  opening_option: [],-->
<!--  id: undefined,-->
<!--  name: undefined,-->
<!--  follow: '0',-->
<!--  has_3d: undefined,-->
<!--  is_repeat: undefined,-->
<!--  chat_times: '0',-->
<!--  image_url: undefined,-->
<!--  camera_pic_status: undefined,-->
<!--  init_plot: undefined,-->
<!--  bgm: '',-->
<!--  bg_info: {-->
<!--    bg_url: undefined,-->
<!--    set_id: undefined,-->
<!--    dress_id: undefined,-->
<!--    type: undefined-->
<!--  },-->
<!--  update_time: undefined,-->
<!--  sound_ray_url: undefined,-->
<!--  opening_statement_voice: undefined,-->
<!--  intimacy_level_info: [],-->
<!--  description: undefined,-->
<!--  api_key: undefined,-->
<!--  dataset_id: undefined,-->
<!--  app_id: undefined,-->
<!--  sex: undefined,-->
<!--  role_setting: undefined,-->
<!--  opening_statement: undefined,-->
<!--  back_story: undefined,-->
<!--  talk_example: undefined,-->
<!--  synopsis: undefined,-->
<!--  voice_id: undefined,-->
<!--  is_show: 0,-->
<!--  is_quick: 0,-->
<!--  last_set_id: undefined,-->
<!--  status: 0,-->
<!--  avatar_url: undefined,-->
<!--  client_image_id: undefined,-->
<!--  terminal: undefined,-->
<!--  client_info: {-->
<!--    client_id: undefined,-->
<!--    name: undefined,-->
<!--    email: undefined,-->
<!--    avatar_url: undefined-->
<!--  },-->
<!--  admin_info: {-->
<!--    admin_id: undefined,-->
<!--    email: undefined,-->
<!--    name: undefined,-->
<!--    avatar_url: undefined-->
<!--  },-->
<!--  resident_function: [],-->
<!--  resident_type: 3-->
<!--})-->

<!--const clickTime = reactive({-->
<!--  start: 0,-->
<!--  end: 0-->
<!--})-->
<!--const loadingStatus = reactive({-->
<!--  chatRecord: false, // 加载聊天记录-->
<!--  tts: false, // 语音转文字-->
<!--  streaming: false, // 流式请求中-->
<!--  textOutputting: false, // 文字输出中-->
<!--  retrying: false,-->
<!--  record: false, // 录音-->
<!--  feedback: false // 点赞点踩loading-->
<!--})-->
<!--const flagStatus = reactive({-->
<!--  // 半屏状态-->
<!--  halfScreen: false,-->
<!--  // 是否出现向下滚动-->
<!--  showScrollDown: false,-->
<!--  // 打电话-->
<!--  phoneCall: false,-->
<!--  // live2d显示-->
<!--  live2d: false,-->
<!--  // 预览-->
<!--  previewL2D: false,-->
<!--  // 麦克风授权状态-->
<!--  micAllow: false,-->
<!--  // 键盘输入or语音输入-->
<!--  inputting: true-->
<!--})-->
<!--const live2dStatus = computed(() => {-->
<!--  return flagStatus.live2d-->
<!--})-->
<!--const showPopover = ref<StringKeyValueTType<boolean>>({})-->
<!--const isCollapse = ref(false)-->
<!--const question = ref('')-->
<!--const tempPhotoData = ref()-->
<!--const contentGameInPageList = ref<IContentListRes>([])-->
<!--const contentGameList = ref<IContentListRes>([])-->

<!--const slidePoint = reactive({-->
<!--  start: 0,-->
<!--  end: 0-->
<!--})-->
<!--// const currentReplayIndex = ref(0)-->

<!--const guideForClearModeVisible = ref(false)-->
<!--const guideForTouchVisible = ref(false)-->
<!--const topUpVisible = ref(false)-->
<!--const unlockVisible = ref(false)-->
<!--const previewVisible = ref(false)-->
<!--const giftVisible = ref(false)-->
<!--const permissionVisible = ref(false)-->
<!--const intimacyVisible = ref(false)-->
<!--const intimacyLevelVisible = ref(false)-->
<!--const skinVisible = ref(false)-->

<!--const myRecorder = ref(null)-->
<!--const myStream = ref(null)-->
<!--const myHistogram = ref(null)-->
<!--const heartRef = ref(null)-->
<!--const chatRef = ref(null)-->
<!--const talkBtnRef = ref(null)-->
<!--const collapseContentRef = ref(null)-->
<!--const replayItemRef = ref(null)-->
<!--const countDownRef = ref(null) // 倒计时ref-->
<!--// const animationInstance = ref<AnimationItem | null>(null)-->
<!--// const howlInstance = ref<Howl | null>(null)-->

<!--const chatRecord = ref<ChatRecordType[]>([])-->
<!--const skinList = ref<DressType[]>([])-->
<!--const l2dSkinData = computed(() => {-->
<!--  return skinList.value-->
<!--    .filter((item) => item.type === SkinTypeEnum.ThreeD)-->
<!--    ?.sort((a, b) => {-->
<!--      return new Date(b.set_bg_time).getTime() - new Date(a.set_bg_time).getTime()-->
<!--    })?.[0]-->
<!--})-->
<!--const live2dResource = ref<ThreeDSourceType>({-->
<!--  model_json: undefined,-->
<!--  action_list: [],-->
<!--  rotate: 0-->
<!--})-->

<!--const getUniqueChatItem = (timestamp: number, chatType: ChatRecordTypeEnum): ChatRecordType => {-->
<!--  if (chatRecord.value[chatRecord.value.length - 1].timestamp !== timestamp || chatRecord.value[chatRecord.value.length - 1].type !== chatType) {-->
<!--    return chatRecord.value.findLast((item) => item.timestamp === timestamp && item.type === chatType)-->
<!--  } else {-->
<!--    return chatRecord.value[chatRecord.value.length - 1]-->
<!--  }-->
<!--}-->

<!--const getUniqueChatItemIndex = (timestamp: number, chatType: ChatRecordTypeEnum) => {-->
<!--  if (chatRecord.value[chatRecord.value.length - 1].timestamp !== timestamp || chatRecord.value[chatRecord.value.length - 1].type !== chatType) {-->
<!--    return chatRecord.value.findLastIndex((item) => item.timestamp === timestamp && item.type === chatType)-->
<!--  } else {-->
<!--    return chatRecord.value.length - 1-->
<!--  }-->
<!--}-->

<!--const finishOnceComment = () => {-->
<!--  if (isIntimacyUpgrade) {-->
<!--    getSingleAgentContentGameInChatPage({-->
<!--      ai_id: aiID.value-->
<!--    }).then(({ code, data }) => {-->
<!--      if (code === ResultEnum.SUCCESS) {-->
<!--        console.log(data)-->
<!--        contentGameInPageList.value = data-->
<!--      }-->
<!--    })-->
<!--    const newContent = contentGameList.value.find((item) => item.level === isIntimacyUpgrade + '')-->
<!--    if (newContent) {-->
<!--      const baseRecord = {-->
<!--        is_like: 0,-->
<!--        is_bad: 0,-->
<!--        message_id: '',-->
<!--        is_phone_call: 0,-->
<!--        call_time: 0,-->
<!--        replayTime: 0,-->
<!--        // replayContent: [],-->
<!--        ttsLoading: false,-->
<!--        audio_file_url: '',-->
<!--        audio_length: 0-->
<!--      }-->
<!--      chatRecord.value.push({-->
<!--        ...baseRecord,-->
<!--        content_id: newContent.id,-->
<!--        type: ChatRecordTypeEnum.STORY_IMAGE,-->
<!--        content: newContent?.prefix_url + newContent?.cover_url-->
<!--      })-->
<!--      chatRecord.value.push({-->
<!--        ...baseRecord,-->
<!--        type: ChatRecordTypeEnum.TIPS,-->
<!--        content: t('clickToStory')-->
<!--      })-->
<!--      if (newContent?.opening_statement_outside) {-->
<!--        chatRecord.value.push({-->
<!--          ...baseRecord,-->
<!--          type: ChatRecordTypeEnum.AI,-->
<!--          content: newContent?.opening_statement_outside || ''-->
<!--        })-->
<!--      }-->
<!--      scrollToBottom(chatRef.value)-->
<!--    }-->
<!--    isIntimacyUpgrade = undefined-->
<!--  }-->
<!--}-->

<!--// 录音初始化-->
<!--const recOpenInit = (success: typeof recStart) => {-->
<!--  if (!myRecorder.value) {-->
<!--    myRecorder.value = new Recorder({-->
<!--      type: 'mp3',-->
<!--      sampleRate: 16000,-->
<!--      bitRate: 16,-->
<!--      onProcess: function (buffers: any, powerLevel: number, _: number, bufferSampleRate: number) {-->
<!--        myHistogram.value.input(buffers[buffers.length - 1], powerLevel, bufferSampleRate)-->
<!--      }-->
<!--    })-->
<!--  }-->
<!--  myRecorder.value.open(-->
<!--    function () {-->
<!--      flagStatus.micAllow = true-->
<!--      //打开麦克风授权获得相关资源-->
<!--      success && success()-->
<!--    },-->
<!--    function (msg: string, isUserNotAllow: boolean) {-->
<!--      //用户拒绝未授权或不支持-->
<!--      if (isUserNotAllow) {-->
<!--        useModal({-->
<!--          message: t('recordingPermissionError'),-->
<!--          duration: 1000-->
<!--        })-->
<!--      } else {-->
<!--        useModal({-->
<!--          message: t('recordingFailed') + msg,-->
<!--          duration: 1000-->
<!--        })-->
<!--      }-->
<!--    }-->
<!--  )-->
<!--}-->

<!--// 录音开始-->
<!--const recStart = () => {-->
<!--  loadingStatus.record = true-->
<!--  nextTick(() => {-->
<!--    if (!myHistogram.value) {-->
<!--      myHistogram.value = Recorder.FrequencyHistogramView({-->
<!--        elem: '.wave',-->
<!--        lineCount: 15,-->
<!--        linear: [0, '#2f2101', 1, '#2f2101'],-->
<!--        position: 0,-->
<!--        widthRatio: 0.4,-->
<!--        mirrorEnable: true,-->
<!--        stripeEnable: false,-->
<!--        minHeight: 3-->
<!--      })-->
<!--    }-->
<!--    myRecorder.value.start()-->
<!--    countDownRef.value.start()-->
<!--  })-->
<!--}-->

<!--// 录音结束-->
<!--const recStop = (isCancel: boolean) => {-->
<!--  myRecorder.value.stop(-->
<!--    function (blob: Blob, duration: number) {-->
<!--      loadingStatus.record = false-->
<!--      console.log(blob, '时长:' + duration + 'ms')-->
<!--      myRecorder.value.close() //释放录音资源，当然可以不释放，后面可以连续调用start；但不释放时系统或浏览器会一直提示在录音，最佳操作是录完就close掉-->
<!--      myRecorder.value = null-->
<!--      if (duration < 500) {-->
<!--        // useModal({-->
<!--        //   message: t('speechTooShort')-->
<!--        // })-->
<!--        return-->
<!--      }-->
<!--      if (!isCancel) {-->
<!--        let timestamp = Date.now()-->
<!--        //已经拿到blob文件对象想干嘛就干嘛：立即播放、上传、下载保存-->
<!--        const file = new File([blob], 'record.mp3')-->
<!--        const formData = new FormData()-->
<!--        startChatReport(InteractiveTypeEnum.CHAT)-->
<!--        formData.append('audio_file', file)-->
<!--        formData.append('type', '2')-->
<!--        formData.append('ai_id', String(aiID.value))-->
<!--        formData.append('interactive_model', String(live2dStatus.value ? 2 : 1))-->
<!--        loadingStatus.streaming = true-->
<!--        chatRecord.value.push({-->
<!--          type: ChatRecordTypeEnum.USER,-->
<!--          timestamp,-->
<!--          content: '',-->
<!--          is_like: BOOL_NUMBER.NO,-->
<!--          is_bad: BOOL_NUMBER.NO,-->
<!--          is_phone_call: BOOL_NUMBER.NO,-->
<!--          message_id: '',-->
<!--          userTTS: false-->
<!--        })-->
<!--        loadingStatus.textOutputting = true-->
<!--        scrollToBottom(chatRef.value)-->
<!--        getUniqueChatItem(timestamp, ChatRecordTypeEnum.USER).userTTS = true-->
<!--        agentAudioChat(formData)-->
<!--          .then(async (response) => {-->
<!--            useHandleStream(response, {-->
<!--              onAudioToText: (input_query) => {-->
<!--                getUniqueChatItem(timestamp, ChatRecordTypeEnum.USER).content = input_query-->
<!--                chatRecord.value.push({-->
<!--                  type: ChatRecordTypeEnum.AI,-->
<!--                  timestamp,-->
<!--                  content: '',-->
<!--                  is_like: BOOL_NUMBER.NO,-->
<!--                  is_bad: BOOL_NUMBER.NO,-->
<!--                  is_phone_call: BOOL_NUMBER.NO,-->
<!--                  message_id: '',-->
<!--                  ttsLoading: false-->
<!--                })-->
<!--                getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).textLoading = true-->
<!--                getUniqueChatItem(timestamp, ChatRecordTypeEnum.USER).userTTS = false-->
<!--                scrollToBottom(chatRef.value)-->
<!--              },-->
<!--              onData: (message, moreInfo, isFirstMessage) => {-->
<!--                if (isFirstMessage) {-->
<!--                  chatRecord.value.push({-->
<!--                    type: ChatRecordTypeEnum.AI,-->
<!--                    timestamp,-->
<!--                    content: '',-->
<!--                    is_like: BOOL_NUMBER.NO,-->
<!--                    is_bad: BOOL_NUMBER.NO,-->
<!--                    is_phone_call: BOOL_NUMBER.NO,-->
<!--                    message_id: '',-->
<!--                    replayTime: 0,-->
<!--                    // replayContent: [],-->
<!--                    ttsLoading: false,-->
<!--                    ttsPlaying: false,-->
<!--                    audio_length: null-->
<!--                  })-->
<!--                  scrollToBottom(chatRef.value)-->
<!--                }-->
<!--                if (message) {-->
<!--                  getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).content += message-->
<!--                  getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).message_id = moreInfo.message_id-->
<!--                  getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).textLoading = false-->
<!--                  scrollToBottom(chatRef.value)-->
<!--                }-->
<!--              },-->
<!--              onFakeComment: (message, _, isFirstMessage) => {-->
<!--                if (isFirstMessage) {-->
<!--                  chatRecord.value.push({-->
<!--                    type: ChatRecordTypeEnum.AI,-->
<!--                    timestamp,-->
<!--                    content: message,-->
<!--                    is_like: BOOL_NUMBER.NO,-->
<!--                    is_fake_comment: true,-->
<!--                    is_bad: BOOL_NUMBER.NO,-->
<!--                    is_phone_call: BOOL_NUMBER.NO,-->
<!--                    message_id: '',-->
<!--                    replayTime: 0,-->
<!--                    // replayContent: [],-->
<!--                    ttsLoading: false,-->
<!--                    ttsPlaying: false,-->
<!--                    audio_length: Number((removeBracketsAndContent(message).length * 0.15).toFixed(0))-->
<!--                  })-->
<!--                  scrollToBottom(chatRef.value)-->
<!--                } else {-->
<!--                  fakeCommentBlockList.value.push({-->
<!--                    type: ChatRecordTypeEnum.AI,-->
<!--                    timestamp,-->
<!--                    content: message,-->
<!--                    is_like: BOOL_NUMBER.NO,-->
<!--                    is_fake_comment: true,-->
<!--                    is_bad: BOOL_NUMBER.NO,-->
<!--                    is_phone_call: BOOL_NUMBER.NO,-->
<!--                    message_id: '',-->
<!--                    replayTime: 0,-->
<!--                    // replayContent: [],-->
<!--                    ttsLoading: false,-->
<!--                    ttsPlaying: false,-->
<!--                    audio_length: Number((removeBracketsAndContent(message).length * 0.15).toFixed(0))-->
<!--                  })-->
<!--                }-->
<!--              },-->
<!--              onFinish: () => {-->
<!--                loadingStatus.streaming = false-->
<!--                if (fakeCommentBlockList.value.length) {-->
<!--                  pushTask()-->
<!--                } else {-->
<!--                  getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).audio_length = Number(-->
<!--                    (removeBracketsAndContent(getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI)?.content)?.length * 0.15).toFixed(0)-->
<!--                  )-->
<!--                  finishOnceComment()-->
<!--                  loadingStatus.textOutputting = false-->
<!--                }-->
<!--              },-->
<!--              onCompleted: () => {},-->
<!--              onMessageEnd: () => {-->
<!--                console.log('messageEnd')-->
<!--                if (!fakeCommentBlockList.value.length) {-->
<!--                  loadingStatus.textOutputting = false-->
<!--                }-->
<!--              },-->
<!--              onTryAgain: (query, conversationID) => {-->
<!--                loadingStatus.retrying = false-->
<!--                loadingStatus.streaming = true-->
<!--                scrollToBottom(chatRef.value)-->
<!--                agentChatRecursion(query, Date.now(), conversationID)-->
<!--              },-->
<!--              onIntimacyInfo: (intimacy_level, intimacy_now, intimacy_upgrade, intimacy_interest_tags) => {-->
<!--                isIntimacyUpgrade = handleIntimacy(intimacy_level, intimacy_now, intimacy_upgrade, intimacy_interest_tags)-->
<!--              },-->
<!--              onError: (e) => {-->
<!--                console.warn(e)-->
<!--              },-->
<!--              onNormalResponse: (code, _, msg) => {-->
<!--                if (code === ResultEnum.AGENT_REMOVED) {-->
<!--                  useModal({-->
<!--                    message: msg,-->
<!--                    duration: 1500,-->
<!--                    onClose: () => {-->
<!--                      router.push('/')-->
<!--                    }-->
<!--                  })-->
<!--                  return-->
<!--                }-->
<!--                if (code === ResultEnum.TTS_ERROR) {-->
<!--                  chatRecord.value.splice(getUniqueChatItemIndex(timestamp, ChatRecordTypeEnum.USER), 1)-->
<!--                  getUniqueChatItem(timestamp, ChatRecordTypeEnum.USER).userTTS = false-->
<!--                }-->
<!--                if (code === ResultEnum.REPLYING) {-->
<!--                  loadingStatus.retrying = true-->
<!--                  return-->
<!--                }-->
<!--                if (code === ResultEnum.RISK_CHAT) {-->
<!--                  chatRecord.value.splice(getUniqueChatItemIndex(timestamp, ChatRecordTypeEnum.USER), 1)-->
<!--                }-->
<!--                useModal({-->
<!--                  message: msg,-->
<!--                  duration: 1500-->
<!--                })-->
<!--              }-->
<!--            })-->
<!--          })-->
<!--          .catch((err) => {-->
<!--            console.log(err)-->
<!--          })-->
<!--          .finally(() => {})-->
<!--      }-->
<!--    },-->
<!--    function (msg: string) {-->
<!--      console.log('录音失败:' + msg)-->
<!--      myRecorder.value.close() //可以通过stop方法的第3个参数来自动调用close-->
<!--      myRecorder.value = null-->
<!--    }-->
<!--  )-->
<!--}-->

<!--// const handleTTSCase = (data: ChatRecordType) => {-->
<!--//   if (chatRecord.value.find((item) => item.ttsLoading)) return-->
<!--//   if (data.ttsPlaying) {-->
<!--//     data.ttsPlaying = false-->
<!--//     myStream.value?.stop()-->
<!--//     myStream.value = null-->
<!--//     howlInstance.value?.stop()-->
<!--//     howlInstance.value = null-->
<!--//     return-->
<!--//   }-->
<!--//   if (data.content === agentMessage.value.opening_statement && !agentMessage.value.opening_statement_voice) {-->
<!--//     data.ttsLoading = true-->
<!--//     myStream.value = Recorder.BufferStreamPlayer({-->
<!--//       play: true,-->
<!--//       onPlayEnd: () => {-->
<!--//         data.ttsPlaying = false-->
<!--//         data.ttsLoading = false-->
<!--//         myStream.value = null-->
<!--//       },-->
<!--//       realtime: false,-->
<!--//       decode: true-->
<!--//     })-->
<!--//     myStream.value.start(-->
<!--//       () => {-->
<!--//         console.log('audio play start')-->
<!--//         getOpeningVoice({-->
<!--//           ai_id: aiID.value-->
<!--//         }).then(async (response) => {-->
<!--//           useHandleStream(response, {-->
<!--//             onAudioData: (audioData) => {-->
<!--//               const hexString = audioData.data.audio-->
<!--//               if (audioData.data.status === 1) {-->
<!--//                 if (hexString) {-->
<!--//                   data.ttsLoading = false-->
<!--//                   const arrayBuffer = new ArrayBuffer(hexString.length / 2)-->
<!--//                   // 创建一个新的Uint8Array视图，以便我们可以将数据写入ArrayBuffer-->
<!--//                   const uint8Array = new Uint8Array(arrayBuffer)-->
<!--//-->
<!--//                   // 遍历十六进制字符串，每两个字符转换为一个字节-->
<!--//                   for (let i = 0; i < hexString.length; i += 2) {-->
<!--//                     uint8Array[i / 2] = parseInt(hexString.slice(i, i + 2), 16)-->
<!--//                   }-->
<!--//                   myStream.value.input(arrayBuffer)-->
<!--//                   data.ttsPlaying = true-->
<!--//                 }-->
<!--//               } else if (audioData.data.status === 2) {-->
<!--//                 data.audio_length = Number((audioData.extra_info.audio_length / 1000).toFixed(0))-->
<!--//               }-->
<!--//             },-->
<!--//             onError: (e) => {-->
<!--//               console.warn(e)-->
<!--//             },-->
<!--//             onNormalResponse: (code, normalObject) => {-->
<!--//               if (code === ResultEnum.SUCCESS) {-->
<!--//                 howlInstance.value = new Howl({-->
<!--//                   src: normalObject,-->
<!--//                   onplay: () => {-->
<!--//                     data.ttsLoading = false-->
<!--//                     data.ttsPlaying = true-->
<!--//                   },-->
<!--//                   onstop: () => {-->
<!--//                     data.ttsLoading = false-->
<!--//                     data.ttsPlaying = false-->
<!--//                   },-->
<!--//                   onload: () => {-->
<!--//                     howlInstance.value.play()-->
<!--//                   },-->
<!--//                   onend: () => {-->
<!--//                     data.ttsLoading = false-->
<!--//                     data.ttsPlaying = false-->
<!--//                     howlInstance.value = null-->
<!--//                   }-->
<!--//                 })-->
<!--//               } else {-->
<!--//                 data.ttsLoading = false-->
<!--//                 data.ttsPlaying = false-->
<!--//               }-->
<!--//             }-->
<!--//           })-->
<!--//         })-->
<!--//       },-->
<!--//       (errMsg: string) => {-->
<!--//         // ttsLoading.value[index] = false-->
<!--//         useModal({-->
<!--//           message: errMsg,-->
<!--//           duration: 1000-->
<!--//         })-->
<!--//       }-->
<!--//     )-->
<!--//     return-->
<!--//   }-->
<!--//   if (data.audio_file_url) {-->
<!--//     if (howlInstance.value) {-->
<!--//       howlInstance.value.stop()-->
<!--//       howlInstance.value = null-->
<!--//     } else if (myStream.value) {-->
<!--//       myStream.value.stop()-->
<!--//       myStream.value = null-->
<!--//     }-->
<!--//     data.ttsLoading = true-->
<!--//     howlInstance.value = new Howl({-->
<!--//       src: data.audio_file_url,-->
<!--//       onplay: () => {-->
<!--//         data.ttsLoading = false-->
<!--//         data.ttsPlaying = true-->
<!--//         console.log('Audio is playing.')-->
<!--//       },-->
<!--//       onstop: () => {-->
<!--//         console.log('Audio has stopped.')-->
<!--//         data.ttsLoading = false-->
<!--//         data.ttsPlaying = false-->
<!--//       },-->
<!--//       onload: () => {-->
<!--//         howlInstance.value.play()-->
<!--//         console.log('Audio has loaded.')-->
<!--//       },-->
<!--//       onend: () => {-->
<!--//         console.log('Audio has ended.')-->
<!--//         data.ttsLoading = false-->
<!--//         data.ttsPlaying = false-->
<!--//         howlInstance.value = null-->
<!--//       }-->
<!--//     })-->
<!--//   } else {-->
<!--//     ttsTrans(data)-->
<!--//   }-->
<!--// }-->

<!--// @ts-ignore-->
<!--// const debounceHandleTTSCase = debounce(handleTTSCase, 500, {-->
<!--//   leading: true,-->
<!--//   trailing: false-->
<!--// })-->

<!--// 播放音频初始化-->
<!--// const ttsTrans = (data: ChatRecordType) => {-->
<!--//   data.ttsLoading = true-->
<!--//   if (myStream.value) {-->
<!--//     myStream.value.stop()-->
<!--//   }-->
<!--//   if (howlInstance.value) {-->
<!--//     howlInstance.value.stop()-->
<!--//     howlInstance.value = null-->
<!--//   }-->
<!--//   const idTrans = () => {-->
<!--//     ttsMessage({-->
<!--//       message_id: data.message_id-->
<!--//     }).then(async (response) => {-->
<!--//       useHandleStream(response, {-->
<!--//         onAudioData: (audioData) => {-->
<!--//           const hexString = audioData.data.audio-->
<!--//           if (audioData.data.status === 1) {-->
<!--//             if (hexString) {-->
<!--//               data.ttsLoading = false-->
<!--//               const arrayBuffer = new ArrayBuffer(hexString.length / 2)-->
<!--//               // 创建一个新的Uint8Array视图，以便我们可以将数据写入ArrayBuffer-->
<!--//               const uint8Array = new Uint8Array(arrayBuffer)-->
<!--//-->
<!--//               // 遍历十六进制字符串，每两个字符转换为一个字节-->
<!--//               for (let i = 0; i < hexString.length; i += 2) {-->
<!--//                 uint8Array[i / 2] = parseInt(hexString.slice(i, i + 2), 16)-->
<!--//               }-->
<!--//               myStream.value.input(arrayBuffer)-->
<!--//               data.ttsPlaying = true-->
<!--//             }-->
<!--//           } else if (audioData.data.status === 2) {-->
<!--//             data.audio_length = Number((audioData.extra_info.audio_length / 1000).toFixed(0))-->
<!--//           }-->
<!--//         },-->
<!--//         onError: (e) => {-->
<!--//           console.warn(e)-->
<!--//         },-->
<!--//         onNormalResponse: (code, normalObject, msg) => {-->
<!--//           if (code === ResultEnum.AGENT_REMOVED) {-->
<!--//             useModal({-->
<!--//               message: msg,-->
<!--//               duration: 1500,-->
<!--//               onClose: () => {-->
<!--//                 router.push('/')-->
<!--//               }-->
<!--//             })-->
<!--//             return-->
<!--//           }-->
<!--//           if (code === ResultEnum.SUCCESS) {-->
<!--//             howlInstance.value = new Howl({-->
<!--//               src: normalObject,-->
<!--//               onplay: () => {-->
<!--//                 data.ttsLoading = false-->
<!--//                 data.ttsPlaying = true-->
<!--//               },-->
<!--//               onstop: () => {-->
<!--//                 data.ttsLoading = false-->
<!--//                 data.ttsPlaying = false-->
<!--//               },-->
<!--//               onload: () => {-->
<!--//                 howlInstance.value.play()-->
<!--//               },-->
<!--//               onend: () => {-->
<!--//                 data.ttsLoading = false-->
<!--//                 data.ttsPlaying = false-->
<!--//                 howlInstance.value = null-->
<!--//               }-->
<!--//             })-->
<!--//           } else {-->
<!--//             data.ttsLoading = false-->
<!--//             data.ttsPlaying = false-->
<!--//           }-->
<!--//         }-->
<!--//       })-->
<!--//     })-->
<!--//   }-->
<!--//   const textTrans = () => {-->
<!--//     ttsMessageText({-->
<!--//       text: data.content,-->
<!--//       ai_id: aiID.value-->
<!--//     }).then(async (response) => {-->
<!--//       useHandleStream(response, {-->
<!--//         onAudioData: (audioData) => {-->
<!--//           const hexString = audioData.data.audio-->
<!--//           if (audioData.data.status === 1) {-->
<!--//             if (hexString) {-->
<!--//               data.ttsLoading = false-->
<!--//               const arrayBuffer = new ArrayBuffer(hexString.length / 2)-->
<!--//               // 创建一个新的Uint8Array视图，以便我们可以将数据写入ArrayBuffer-->
<!--//               const uint8Array = new Uint8Array(arrayBuffer)-->
<!--//-->
<!--//               // 遍历十六进制字符串，每两个字符转换为一个字节-->
<!--//               for (let i = 0; i < hexString.length; i += 2) {-->
<!--//                 uint8Array[i / 2] = parseInt(hexString.slice(i, i + 2), 16)-->
<!--//               }-->
<!--//               myStream.value.input(arrayBuffer)-->
<!--//               data.ttsPlaying = true-->
<!--//             }-->
<!--//           } else if (audioData.data.status === 2) {-->
<!--//             data.audio_length = Number((audioData.extra_info.audio_length / 1000).toFixed(0))-->
<!--//           }-->
<!--//         },-->
<!--//         onError: (e) => {-->
<!--//           console.warn(e)-->
<!--//         },-->
<!--//         onNormalResponse: (code, normalObject, msg) => {-->
<!--//           if (code === ResultEnum.AGENT_REMOVED) {-->
<!--//             useModal({-->
<!--//               message: msg,-->
<!--//               duration: 1500,-->
<!--//               onClose: () => {-->
<!--//                 router.push('/')-->
<!--//               }-->
<!--//             })-->
<!--//             return-->
<!--//           }-->
<!--//           if (code === ResultEnum.SUCCESS) {-->
<!--//             howlInstance.value = new Howl({-->
<!--//               src: normalObject,-->
<!--//               onplay: () => {-->
<!--//                 data.ttsLoading = false-->
<!--//                 data.ttsPlaying = true-->
<!--//               },-->
<!--//               onstop: () => {-->
<!--//                 data.ttsLoading = false-->
<!--//                 data.ttsPlaying = false-->
<!--//               },-->
<!--//               onload: () => {-->
<!--//                 howlInstance.value.play()-->
<!--//               },-->
<!--//               onend: () => {-->
<!--//                 data.ttsLoading = false-->
<!--//                 data.ttsPlaying = false-->
<!--//                 howlInstance.value = null-->
<!--//               }-->
<!--//             })-->
<!--//           } else {-->
<!--//             data.ttsLoading = false-->
<!--//             data.ttsPlaying = false-->
<!--//           }-->
<!--//         }-->
<!--//       })-->
<!--//     })-->
<!--//   }-->
<!--//   myStream.value = Recorder.BufferStreamPlayer({-->
<!--//     play: true,-->
<!--//     onPlayEnd: () => {-->
<!--//       data.ttsPlaying = false-->
<!--//       data.ttsLoading = false-->
<!--//       myStream.value = null-->
<!--//     },-->
<!--//     realtime: false,-->
<!--//     decode: true-->
<!--//   })-->
<!--//   myStream.value.start(-->
<!--//     () => {-->
<!--//       console.log('audio play start')-->
<!--//       if (data.is_fake_comment) {-->
<!--//         textTrans()-->
<!--//       } else {-->
<!--//         idTrans()-->
<!--//       }-->
<!--//     },-->
<!--//     (errMsg: string) => {-->
<!--//       // ttsLoading.value[index] = false-->
<!--//       useModal({-->
<!--//         message: errMsg,-->
<!--//         duration: 1000-->
<!--//       })-->
<!--//     }-->
<!--//   )-->
<!--// }-->

<!--const newSendMessage = () => {-->
<!--  if (!question.value) return-->
<!--  const timestamp = Date.now()-->
<!--  loadingStatus.streaming = true-->
<!--  chatRecord.value.push({-->
<!--    type: ChatRecordTypeEnum.USER,-->
<!--    timestamp,-->
<!--    content: question.value,-->
<!--    is_like: BOOL_NUMBER.NO,-->
<!--    is_bad: BOOL_NUMBER.NO,-->
<!--    is_phone_call: BOOL_NUMBER.NO,-->
<!--    message_id: '',-->
<!--    replayTime: 0-->
<!--    // replayContent: []-->
<!--  })-->
<!--  startChatReport(InteractiveTypeEnum.CHAT)-->
<!--  agentChatRecursion(question.value, timestamp)-->
<!--  question.value = ''-->
<!--  scrollToBottom(chatRef.value)-->
<!--}-->

<!--const fakeCommentBlockList = ref([])-->
<!--const pushTask = () => {-->
<!--  const id = setInterval(() => {-->
<!--    if (fakeCommentBlockList.value.length === 0) return-->
<!--    const item = fakeCommentBlockList.value.shift() as ChatRecordType-->
<!--    console.log(item, 'sdsd')-->
<!--    if (item) {-->
<!--      chatRecord.value.push(item)-->
<!--    }-->
<!--    scrollToBottom(chatRef.value)-->
<!--    if (!fakeCommentBlockList.value.length && !loadingStatus.streaming) {-->
<!--      setTimeout(() => {-->
<!--        clearInterval(id)-->
<!--        finishOnceComment()-->
<!--        loadingStatus.textOutputting = false-->
<!--      }, 1000)-->
<!--    }-->
<!--  }, 1800)-->
<!--}-->

<!--const agentChatRecursion = (query: string, timestamp: number, conversationID?: string) => {-->
<!--  agentTextChat({-->
<!--    query: query,-->
<!--    ai_id: aiID.value,-->
<!--    interactive_model: live2dStatus.value ? 2 : 1,-->
<!--    conversation_id: conversationID-->
<!--  })-->
<!--    .then(async (response) => {-->
<!--      loadingStatus.textOutputting = true-->
<!--      scrollToBottom(chatRef.value)-->
<!--      useHandleStream(response, {-->
<!--        onData: (message, moreInfo, isFirstMessage) => {-->
<!--          if (isFirstMessage) {-->
<!--            chatRecord.value.push({-->
<!--              type: ChatRecordTypeEnum.AI,-->
<!--              timestamp,-->
<!--              content: '',-->
<!--              is_like: BOOL_NUMBER.NO,-->
<!--              is_bad: BOOL_NUMBER.NO,-->
<!--              is_phone_call: BOOL_NUMBER.NO,-->
<!--              message_id: '',-->
<!--              replayTime: 0,-->
<!--              // replayContent: [],-->
<!--              ttsLoading: false,-->
<!--              ttsPlaying: false,-->
<!--              audio_length: null-->
<!--            })-->
<!--            scrollToBottom(chatRef.value)-->
<!--          }-->
<!--          if (message) {-->
<!--            getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).content += message-->
<!--            getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).message_id = moreInfo.message_id-->
<!--            getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).textLoading = false-->
<!--            scrollToBottom(chatRef.value)-->
<!--          }-->
<!--        },-->
<!--        onFakeComment: (message, _, isFirstMessage) => {-->
<!--          if (isFirstMessage) {-->
<!--            chatRecord.value.push({-->
<!--              type: ChatRecordTypeEnum.AI,-->
<!--              timestamp,-->
<!--              content: message,-->
<!--              is_like: BOOL_NUMBER.NO,-->
<!--              is_fake_comment: true,-->
<!--              is_bad: BOOL_NUMBER.NO,-->
<!--              is_phone_call: BOOL_NUMBER.NO,-->
<!--              message_id: '',-->
<!--              replayTime: 0,-->
<!--              // replayContent: [],-->
<!--              ttsLoading: false,-->
<!--              ttsPlaying: false,-->
<!--              audio_length: Number((removeBracketsAndContent(message).length * 0.15).toFixed(0))-->
<!--            })-->
<!--            scrollToBottom(chatRef.value)-->
<!--          } else {-->
<!--            fakeCommentBlockList.value.push({-->
<!--              type: ChatRecordTypeEnum.AI,-->
<!--              timestamp,-->
<!--              content: message,-->
<!--              is_like: BOOL_NUMBER.NO,-->
<!--              is_fake_comment: true,-->
<!--              is_bad: BOOL_NUMBER.NO,-->
<!--              is_phone_call: BOOL_NUMBER.NO,-->
<!--              message_id: '',-->
<!--              replayTime: 0,-->
<!--              // replayContent: [],-->
<!--              ttsLoading: false,-->
<!--              ttsPlaying: false,-->
<!--              audio_length: Number((removeBracketsAndContent(message).length * 0.15).toFixed(0))-->
<!--            })-->
<!--          }-->
<!--        },-->
<!--        onFinish: () => {-->
<!--          loadingStatus.streaming = false-->
<!--          if (fakeCommentBlockList.value.length) {-->
<!--            pushTask()-->
<!--          } else {-->
<!--            getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).audio_length = Number(-->
<!--              (removeBracketsAndContent(getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI)?.content)?.length * 0.15).toFixed(0)-->
<!--            )-->
<!--            finishOnceComment()-->
<!--            loadingStatus.textOutputting = false-->
<!--          }-->
<!--        },-->
<!--        onCompleted: () => {},-->
<!--        onMessageEnd: () => {-->
<!--          console.log('messageEnd')-->
<!--          if (!fakeCommentBlockList.value.length) {-->
<!--            loadingStatus.textOutputting = false-->
<!--          }-->
<!--        },-->
<!--        onTryAgain: (query, conversationID) => {-->
<!--          loadingStatus.retrying = false-->
<!--          loadingStatus.streaming = true-->
<!--          scrollToBottom(chatRef.value)-->
<!--          agentChatRecursion(query, Date.now(), conversationID)-->
<!--        },-->
<!--        onIntimacyInfo: (intimacy_level, intimacy_now, intimacy_upgrade, intimacy_interest_tags) => {-->
<!--          isIntimacyUpgrade = handleIntimacy(intimacy_level, intimacy_now, intimacy_upgrade, intimacy_interest_tags)-->
<!--        },-->
<!--        onError: (e) => {-->
<!--          console.warn(e)-->
<!--        },-->
<!--        onStreamError: (msg) => {-->
<!--          useModal({-->
<!--            message: msg,-->
<!--            duration: 1000-->
<!--          })-->
<!--          chatRecord.value.splice(getUniqueChatItemIndex(timestamp, ChatRecordTypeEnum.AI), 1)-->
<!--          chatRecord.value.splice(getUniqueChatItemIndex(timestamp, ChatRecordTypeEnum.USER), 1)-->
<!--        },-->
<!--        onNormalResponse: (code, _, msg) => {-->
<!--          if (code === ResultEnum.AGENT_REMOVED) {-->
<!--            useModal({-->
<!--              message: msg,-->
<!--              duration: 1500,-->
<!--              onClose: () => {-->
<!--                router.push('/')-->
<!--              }-->
<!--            })-->
<!--            return-->
<!--          }-->
<!--          if (code !== ResultEnum.SUCCESS) {-->
<!--            if (code === ResultEnum.REPLYING) {-->
<!--              loadingStatus.retrying = true-->
<!--              return-->
<!--            }-->
<!--            if (code === ResultEnum.RISK_CHAT) {-->
<!--              chatRecord.value.splice(getUniqueChatItemIndex(timestamp, ChatRecordTypeEnum.AI), 1)-->
<!--              chatRecord.value.splice(getUniqueChatItemIndex(timestamp, ChatRecordTypeEnum.USER), 1)-->
<!--            }-->
<!--            useModal({-->
<!--              message: msg,-->
<!--              duration: 1000-->
<!--            })-->
<!--          }-->
<!--        }-->
<!--      })-->
<!--    })-->
<!--    .catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--}-->

<!--// const sendMessage = () => {-->
<!--//   if (!question.value) return-->
<!--//   const timestamp = Date.now()-->
<!--//   loadingStatus.streaming = true-->
<!--//   if (loadingStatus.retrying === true) {-->
<!--//     const index = chatRecord.value.findLastIndex((item) => item.textLoading)-->
<!--//     chatRecord.value.splice(index, 0, {-->
<!--//       type: ChatRecordTypeEnum.USER,-->
<!--//       timestamp,-->
<!--//       content: question.value,-->
<!--//       is_like: BOOL_NUMBER.NO,-->
<!--//       is_bad: BOOL_NUMBER.NO,-->
<!--//       is_phone_call: BOOL_NUMBER.NO,-->
<!--//       message_id: '',-->
<!--//       replayTime: 0-->
<!--//       // replayContent: []-->
<!--//     })-->
<!--//   } else {-->
<!--//     chatRecord.value.push({-->
<!--//       type: ChatRecordTypeEnum.USER,-->
<!--//       timestamp,-->
<!--//       content: question.value,-->
<!--//       is_like: BOOL_NUMBER.NO,-->
<!--//       is_bad: BOOL_NUMBER.NO,-->
<!--//       is_phone_call: BOOL_NUMBER.NO,-->
<!--//       message_id: '',-->
<!--//       replayTime: 0-->
<!--//       // replayContent: []-->
<!--//     })-->
<!--//   }-->
<!--//   if (loadingStatus.retrying !== true) {-->
<!--//     chatRecord.value.push({-->
<!--//       type: ChatRecordTypeEnum.AI,-->
<!--//       timestamp,-->
<!--//       content: '',-->
<!--//       is_like: BOOL_NUMBER.NO,-->
<!--//       is_bad: BOOL_NUMBER.NO,-->
<!--//       is_phone_call: BOOL_NUMBER.NO,-->
<!--//       message_id: '',-->
<!--//       replayTime: 0,-->
<!--//       // replayContent: [],-->
<!--//       textLoading: true,-->
<!--//       ttsLoading: false,-->
<!--//       ttsPlaying: false,-->
<!--//       audio_length: null-->
<!--//     })-->
<!--//   }-->
<!--//   startChatReport(InteractiveTypeEnum.CHAT)-->
<!--//   agentChatRecursion(question.value, timestamp)-->
<!--//   question.value = ''-->
<!--//   scrollToBottom(chatRef.value)-->
<!--// }-->

<!--const debounceSendMessage = debounce(newSendMessage, 500, {-->
<!--  leading: true,-->
<!--  trailing: false-->
<!--})-->

<!--// 送礼触发-->
<!--const triggerGiftMessage = async ({ response, gift }: { response: ReadableStream; gift: IGiftListType }) => {-->
<!--  loadingStatus.streaming = true-->
<!--  chatRecord.value.push({-->
<!--    type: ChatRecordTypeEnum.GIFT,-->
<!--    content: t('send') + `${gift.name},`,-->
<!--    is_like: BOOL_NUMBER.NO,-->
<!--    is_bad: BOOL_NUMBER.NO,-->
<!--    is_phone_call: BOOL_NUMBER.NO,-->
<!--    message_id: '',-->
<!--    replayTime: 0,-->
<!--    // replayContent: [],-->
<!--    ttsLoading: false,-->
<!--    ttsPlaying: false,-->
<!--    audio_length: null,-->
<!--    gift_info: gift-->
<!--  })-->
<!--  await nextTick(() => {})-->
<!--  let timestamp = Date.now()-->
<!--  chatRecord.value.push({-->
<!--    type: ChatRecordTypeEnum.AI,-->
<!--    timestamp,-->
<!--    content: '',-->
<!--    is_like: BOOL_NUMBER.NO,-->
<!--    is_bad: BOOL_NUMBER.NO,-->
<!--    is_phone_call: BOOL_NUMBER.NO,-->
<!--    message_id: '',-->
<!--    replayTime: 0,-->
<!--    // replayContent: [],-->
<!--    ttsLoading: false,-->
<!--    ttsPlaying: false,-->
<!--    audio_length: null-->
<!--  })-->
<!--  getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).textLoading = true-->
<!--  loadingStatus.textOutputting = true-->
<!--  scrollToBottom(chatRef.value)-->
<!--  startChatReport(InteractiveTypeEnum.SEND_GIFT)-->
<!--  useHandleStream(response, {-->
<!--    onData: (message, moreInfo, isFirstMessage) => {-->
<!--      if (!getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI) && isFirstMessage) {-->
<!--        chatRecord.value.push({-->
<!--          type: ChatRecordTypeEnum.AI,-->
<!--          timestamp,-->
<!--          content: '',-->
<!--          is_like: BOOL_NUMBER.NO,-->
<!--          is_bad: BOOL_NUMBER.NO,-->
<!--          is_phone_call: BOOL_NUMBER.NO,-->
<!--          message_id: '',-->
<!--          replayTime: 0,-->
<!--          // replayContent: [],-->
<!--          ttsLoading: false,-->
<!--          ttsPlaying: false,-->
<!--          audio_length: null-->
<!--        })-->
<!--        scrollToBottom(chatRef.value)-->
<!--      }-->
<!--      if (message && message !== ' \n') {-->
<!--        getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).content += message-->
<!--        getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).message_id = moreInfo.message_id-->
<!--        getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).textLoading = false-->
<!--        scrollToBottom(chatRef.value)-->
<!--      }-->
<!--    },-->
<!--    onFakeComment: (message) => {-->
<!--      console.log(message, 'sdddff')-->
<!--      fakeCommentBlockList.value.push({-->
<!--        type: ChatRecordTypeEnum.AI,-->
<!--        timestamp,-->
<!--        content: message,-->
<!--        is_like: BOOL_NUMBER.NO,-->
<!--        is_fake_comment: true,-->
<!--        is_bad: BOOL_NUMBER.NO,-->
<!--        is_phone_call: BOOL_NUMBER.NO,-->
<!--        message_id: '',-->
<!--        replayTime: 0,-->
<!--        // replayContent: [],-->
<!--        ttsLoading: false,-->
<!--        ttsPlaying: false,-->
<!--        audio_length: Number((removeBracketsAndContent(message).length * 0.15).toFixed(0))-->
<!--      })-->
<!--    },-->
<!--    onFinish: () => {-->
<!--      finishOnceComment()-->
<!--    },-->
<!--    onCompleted: () => {-->
<!--      loadingStatus.streaming = false-->
<!--      if (fakeCommentBlockList.value.length) {-->
<!--        pushTask()-->
<!--      } else {-->
<!--        getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI).audio_length = Number(-->
<!--          (removeBracketsAndContent(getUniqueChatItem(timestamp, ChatRecordTypeEnum.AI)?.content)?.length * 0.15).toFixed(0)-->
<!--        )-->
<!--        loadingStatus.textOutputting = false-->
<!--      }-->
<!--      console.log('completed')-->
<!--    },-->
<!--    onMessageEnd: () => {-->
<!--      console.log('messageEnd')-->
<!--      if (!fakeCommentBlockList.value.length) {-->
<!--        loadingStatus.textOutputting = false-->
<!--      }-->
<!--      if (!chatRecord.value[chatRecord.value.length - 1].content.trim() && !fakeCommentBlockList.value.length) {-->
<!--        chatRecord.value.pop()-->
<!--      }-->
<!--    },-->
<!--    onIntimacyInfo: (intimacy_level, intimacy_now, intimacy_upgrade, intimacy_interest_tags) => {-->
<!--      console.log('intimacyInfo', intimacy_level, intimacy_now, intimacy_upgrade)-->
<!--      isIntimacyUpgrade = handleIntimacy(intimacy_level, intimacy_now, intimacy_upgrade, intimacy_interest_tags)-->
<!--    },-->
<!--    onError: (e) => {-->
<!--      console.warn(e)-->
<!--    },-->
<!--    onNormalResponse: (code, data, msg) => {-->
<!--      if (code === ResultEnum.AGENT_REMOVED) {-->
<!--        useModal({-->
<!--          message: msg,-->
<!--          duration: 1500,-->
<!--          onClose: () => {-->
<!--            router.push('/')-->
<!--          }-->
<!--        })-->
<!--        return-->
<!--      }-->
<!--      if (code === ResultEnum.REPLYING) {-->
<!--        chatRecord.value.splice(getUniqueChatItemIndex(timestamp, ChatRecordTypeEnum.AI), 1)-->
<!--        return-->
<!--      }-->
<!--      console.log('normalResponse', msg, code, data)-->
<!--    }-->
<!--  })-->
<!--}-->

<!--// 进入打电话-->
<!--const enterPhoneCall = () => {-->
<!--  if (!agentMessage.value.ai_chat_setting.intimacy_interest_tags.includes('phone_call')) {-->
<!--    useModal({-->
<!--      message: t('intimacyUnlockFunc')-->
<!--    })-->
<!--    return-->
<!--  }-->
<!--  if (userStore.userInformation.phone_time <= 0) {-->
<!--    topUpVisible.value = true-->
<!--    return-->
<!--  }-->
<!--  if (flagStatus.micAllow) {-->
<!--    // runningContextAudioForPhone.value = Recorder.GetContext(true)-->
<!--    Howler.stop()-->
<!--    flagStatus.phoneCall = true-->
<!--    startChatReport(InteractiveTypeEnum.CALL)-->
<!--  } else {-->
<!--    permissionVisible.value = true-->
<!--  }-->
<!--}-->

<!--const phoneCallPermission = () => {-->
<!--  recOpenInit(() => {-->
<!--    myRecorder.value.close()-->
<!--    myRecorder.value = null-->
<!--    Howler.stop()-->
<!--    flagStatus.phoneCall = true-->
<!--  })-->
<!--}-->

<!--const startChatReport = (type: InteractiveTypeEnum) => {-->
<!--  if (isFirstInNoAction.value) {-->
<!--    isFirstInNoAction.value = false-->
<!--    eventReport({-->
<!--      event_type: isNoRecord.value ? EventTypeEnum.START_CHAT : EventTypeEnum.CONTINUE_CHAT,-->
<!--      ai_id: aiID.value,-->
<!--      interactive_type: type-->
<!--    }).catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--  }-->
<!--}-->

<!--// 倒计时结束-->
<!--const countDownFinish = () => {-->
<!--  recStop(false)-->
<!--}-->

<!--const handleIntimacy = (level: number, value: number, upgrade: number, intimacy_interest_tags: string[]) => {-->
<!--  const currentLevel = agentMessage.value.ai_chat_setting.intimacy_level-->
<!--  const currentValue = agentMessage.value.ai_chat_setting.intimacy_now-->
<!--  agentMessage.value.ai_chat_setting.intimacy_now = value-->
<!--  agentMessage.value.ai_chat_setting.intimacy_level = level-->
<!--  agentMessage.value.ai_chat_setting.intimacy_upgrade = upgrade-->
<!--  agentMessage.value.ai_chat_setting.intimacy_interest_tags = intimacy_interest_tags-->
<!--  if (value > currentValue) {-->
<!--    // animationInstance.value.play()-->
<!--  }-->
<!--  if (level > currentLevel) {-->
<!--    intimacyVisible.value = true-->
<!--    return level-->
<!--  }-->
<!--  return undefined-->
<!--}-->

<!--const exitClearMode = () => {-->
<!--  isClearMode.value = false-->
<!--  eventReport({-->
<!--    event_type: EventTypeEnum.EXIT_IMMERSE_MODE,-->
<!--    ai_id: aiID.value-->
<!--  }).catch((err) => {-->
<!--    console.warn(err)-->
<!--  })-->
<!--  chatPageRef.value.scrollIntoView({ behavior: 'smooth' })-->
<!--}-->

<!--const l2dSwitch = async (buy?: boolean) => {-->
<!--  if (buy) {-->
<!--    const { data, code } = await getDressByID({-->
<!--      ai_id: aiID.value-->
<!--    })-->
<!--    if (code === ResultEnum.SUCCESS) {-->
<!--      skinList.value = data-->
<!--    }-->
<!--  }-->
<!--  if (!flagStatus.live2d) {-->
<!--    console.log(1, l2dSkinData.value)-->
<!--    if (l2dSkinData.value.is_buy) {-->
<!--      getAgentMessageAndRecord()-->
<!--      setDress({-->
<!--        set_id: l2dSkinData.value.set_id,-->
<!--        ai_id: aiID.value-->
<!--      })-->
<!--        .then((res) => {-->
<!--          if (res.code === ResultEnum.SUCCESS) {-->
<!--            handleSkinSwitch(l2dSkinData.value)-->
<!--            useModal({-->
<!--              message: t('setupSuccessful')-->
<!--            })-->
<!--          }-->
<!--        })-->
<!--        .catch((err) => {-->
<!--          console.warn(err)-->
<!--        })-->
<!--        .finally(() => {})-->
<!--    } else {-->
<!--      flagStatus.previewL2D = true-->
<!--    }-->
<!--  } else {-->
<!--    const lastSkin = skinList.value.find((item) => item.set_id === agentMessage.value.last_set_id)-->
<!--    console.log(lastSkin, agentMessage.value.last_set_id)-->
<!--    setDress({-->
<!--      set_id: lastSkin.set_id,-->
<!--      ai_id: aiID.value-->
<!--    })-->
<!--      .then((res) => {-->
<!--        if (res.code === ResultEnum.SUCCESS) {-->
<!--          handleSkinSwitch(lastSkin)-->

<!--          useModal({-->
<!--            message: t('setupSuccessful')-->
<!--          })-->
<!--        }-->
<!--      })-->
<!--      .catch((err) => {-->
<!--        console.warn(err)-->
<!--      })-->
<!--      .finally(() => {})-->
<!--  }-->
<!--}-->

<!--const handleGuideClearMode = () => {-->
<!--  console.log(1, 'hand')-->
<!--  if (!userStore.newerOption.hadSwitchToClearMode && live2dStatus.value) {-->
<!--    guideForClearModeVisible.value = true-->
<!--    userStore.newerOption.hadSwitchToClearMode = true-->
<!--    eventReport({-->
<!--      event_type: EventTypeEnum.SHOW_CLEAR_SCREEN_TUTORIAL,-->
<!--      ai_id: aiID.value-->
<!--    }).catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--  }-->
<!--}-->

<!--const handleGuideTouch = () => {-->
<!--  if (!userStore.newerOption.hadTouch) {-->
<!--    guideForTouchVisible.value = true-->
<!--    userStore.newerOption.hadTouch = true-->
<!--    eventReport({-->
<!--      event_type: EventTypeEnum.SHOW_TOUCH_ACTION_TUTORIAL,-->
<!--      ai_id: aiID.value-->
<!--    }).catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--  }-->
<!--}-->

<!--const handleSkinSwitch = (skin: DressType) => {-->
<!--  agentMessage.value.bg_info.bg_url = skin.pic_url-->
<!--  console.log('sdsds')-->
<!--  if (skin.type === SkinTypeEnum.ThreeD) {-->
<!--    console.log('sdsds')-->
<!--    getThreeDResource(skin.dress_id)-->
<!--  } else {-->
<!--    live2dResource.value = {-->
<!--      model_json: undefined,-->
<!--      action_list: [],-->
<!--      rotate: 0-->
<!--    }-->
<!--    if (agentMessage.value.bg_info.type !== skin.type) {-->
<!--      flagStatus.live2d = false-->
<!--    }-->
<!--  }-->
<!--  agentMessage.value.bg_info.type = skin.type-->
<!--  getDress()-->
<!--}-->

<!--const sendPhoto = () => {-->
<!--  const timestamp = Date.now()-->
<!--  getSelfie({-->
<!--    ai_id: aiID.value-->
<!--  }).then((res) => {-->
<!--    const { code, data } = res-->
<!--    if (code === ResultEnum.SUCCESS) {-->
<!--      chatRecord.value.push({-->
<!--        type: ChatRecordTypeEnum.IMAGE,-->
<!--        timestamp,-->
<!--        photoDetail: data,-->
<!--        currency_type: data.currency_type,-->
<!--        content: data.vague_pic_url,-->
<!--        is_like: BOOL_NUMBER.NO,-->
<!--        is_bad: BOOL_NUMBER.NO,-->
<!--        is_phone_call: BOOL_NUMBER.NO,-->
<!--        message_id: '',-->
<!--        replayTime: 0-->
<!--        // replayContent: []-->
<!--      })-->
<!--      scrollToBottom(chatRef.value)-->
<!--    }-->
<!--  })-->
<!--}-->

<!--const handlePhoto = (item: ChatRecordType) => {-->
<!--  tempPhotoData.value = item.photoDetail-->
<!--  if (item.photoDetail.pic_url) {-->
<!--    previewVisible.value = true-->
<!--  } else {-->
<!--    unlockVisible.value = true-->
<!--  }-->
<!--}-->

<!--const refreshPhoto = (url: string) => {-->
<!--  tempPhotoData.value.pic_url = url-->
<!--  tempPhotoData.value.content = url-->
<!--  getAgentRecord()-->
<!--}-->

<!--const setBgmStatus = () => {-->
<!--  bgmSetting({-->
<!--    bgm: Boolean(userStore.userInformation.bgm) ? 0 : 1-->
<!--  }).then((res) => {-->
<!--    userStore.userInformation.bgm = Boolean(userStore.userInformation.bgm) ? 0 : 1-->
<!--    if (res.code === ResultEnum.SUCCESS) {-->
<!--      if (userStore.userInformation.bgm) {-->
<!--        bgmHowlerInstance.value.play()-->
<!--      } else {-->
<!--        bgmHowlerInstance.value.pause()-->
<!--      }-->
<!--    }-->
<!--  })-->
<!--}-->

<!--const debounceSetBgmStatus = debounce(setBgmStatus, 1000, {-->
<!--  leading: true,-->
<!--  trailing: false-->
<!--})-->

<!--// const goSelfImage = () => {-->
<!--//   if (!userStore.newerOption.hadChangeSelfImage) {-->
<!--//     userStore.newerOption.hadChangeSelfImage = true-->
<!--//     useConfirm({-->
<!--//       content: t('chatBasedOnPersona'),-->
<!--//       confirmText: t('confirmButton'),-->
<!--//       cancelText: t('cancelButton'),-->
<!--//       onConfirm: () => {-->
<!--//         eventReport({-->
<!--//           event_type: EventTypeEnum.SHOW_PERSON_GUIDE_POPUP-->
<!--//         }).catch((err) => {-->
<!--//           console.warn(err)-->
<!--//         })-->
<!--//         eventReport({-->
<!--//           event_type: EventTypeEnum.OPEN_MY_AVATAR_PAGE,-->
<!--//           front_address: ForwardAddressEnum.CHAT_PAGE-->
<!--//         }).catch((err) => {-->
<!--//           console.warn(err)-->
<!--//         })-->
<!--//         router.push({-->
<!--//           path: '/mine/selfImage',-->
<!--//           query: {-->
<!--//             ai_id: aiID.value-->
<!--//           }-->
<!--//         })-->
<!--//       }-->
<!--//     })-->
<!--//     return-->
<!--//   }-->
<!--//   eventReport({-->
<!--//     event_type: EventTypeEnum.OPEN_MY_AVATAR_PAGE,-->
<!--//     front_address: ForwardAddressEnum.CHAT_PAGE-->
<!--//   }).catch((err) => {-->
<!--//     console.warn(err)-->
<!--//   })-->
<!--//   router.push({-->
<!--//     path: '/mine/selfImage',-->
<!--//     query: {-->
<!--//       ai_id: aiID.value-->
<!--//     }-->
<!--//   })-->
<!--// }-->

<!--const goStory = () => {-->
<!--  if (hadStoryBook.value) {-->
<!--    router.push(`/contentGame/storyBook?id=${aiID.value}`)-->
<!--  } else {-->
<!--    router.push(`/agentHomePage/${aiID.value}?tab=d`)-->
<!--  }-->
<!--}-->

<!--const goHomePage = () => {-->
<!--  eventReport({-->
<!--    event_type: EventTypeEnum.OPEN_AI_PROFILE,-->
<!--    ai_id: aiID.value-->
<!--  }).catch((err) => {-->
<!--    console.warn(err)-->
<!--  })-->
<!--  router.push(`/agentHomePage/${aiID.value}`)-->
<!--}-->
<!--const goBack = () => {-->
<!--  eventReport({-->
<!--    event_type: EventTypeEnum.EXIT_CHAT,-->
<!--    ai_id: aiID.value-->
<!--  }).catch((err) => {-->
<!--    console.warn(err)-->
<!--  })-->
<!--  if (route.query.path === 'create') {-->
<!--    router.push('/')-->
<!--  } else {-->
<!--    useBackHistory()-->
<!--  }-->
<!--}-->

<!--const onLongPressCallbackDirective = (item: ChatRecordType, index: number) => {-->
<!--  if (item.type === ChatRecordTypeEnum.AI) {-->
<!--    showPopover.value[index] = true-->
<!--  }-->
<!--}-->

<!--const dividePlacement = (type: ChatRecordTypeEnum): PopoverPlacement => {-->
<!--  switch (type) {-->
<!--    case ChatRecordTypeEnum.USER:-->
<!--      return 'top-end'-->
<!--    case ChatRecordTypeEnum.AI:-->
<!--      return 'top-start'-->
<!--    default:-->
<!--      return 'top-end'-->
<!--  }-->
<!--}-->

<!--const handleCollapse = (flag: boolean = false) => {-->
<!--  if (flag) {-->
<!--    debounceSendMessage()-->
<!--    return-->
<!--  } else {-->
<!--    if (collapseContentRef.value.style.maxHeight) {-->
<!--      closeCollapse()-->
<!--    } else {-->
<!--      flagStatus.inputting = true-->
<!--      openCollapse()-->
<!--    }-->
<!--  }-->
<!--}-->

<!--const closeCollapse = () => {-->
<!--  if (!isCollapse.value) return-->
<!--  collapseContentRef.value.style.maxHeight = null-->
<!--  collapseContentRef.value.style.marginTop = '0'-->
<!--  scrollToBottom(chatRef.value)-->
<!--  anime({-->
<!--    targets: '.switch-mode',-->
<!--    rotate: [0, 90],-->
<!--    duration: 100,-->
<!--    easing: 'linear',-->
<!--    complete: () => {-->
<!--      isCollapse.value = false-->
<!--    }-->
<!--  })-->
<!--}-->

<!--const openCollapse = () => {-->
<!--  collapseContentRef.value.style.maxHeight = collapseContentRef.value.scrollHeight + 'px'-->
<!--  collapseContentRef.value.style.marginTop = '16px'-->
<!--  anime({-->
<!--    targets: '.switch-mode',-->
<!--    rotate: [90, 0],-->
<!--    duration: 100,-->
<!--    easing: 'linear',-->
<!--    complete: () => {-->
<!--      isCollapse.value = true-->
<!--    }-->
<!--  })-->
<!--}-->

<!--const changeInputting = () => {-->
<!--  if (flagStatus.inputting) {-->
<!--    closeCollapse()-->
<!--  }-->
<!--  flagStatus.inputting = !flagStatus.inputting-->
<!--}-->
<!--// const replayScrollIntoView = (index: number) => {-->
<!--//   console.log(index, replayItemRef.value[replayItemRef.value.length - 1].children)-->
<!--//   replayItemRef.value[replayItemRef.value.length - 1].children[index].scrollIntoView({-->
<!--//     behavior: 'smooth'-->
<!--//   })-->
<!--//   if (index === currentReplayIndex.value - 1) {-->
<!--//     currentReplayIndex.value -= 1-->
<!--//   } else {-->
<!--//     currentReplayIndex.value += 1-->
<!--//   }-->
<!--// }-->

<!--const getDress = () => {-->
<!--  getDressByID({-->
<!--    ai_id: aiID.value-->
<!--  })-->
<!--    .then((res) => {-->
<!--      const { code, data } = res-->
<!--      if (code === 200) {-->
<!--        skinList.value = data-->
<!--      }-->
<!--    })-->
<!--    .catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--    .finally(() => {})-->
<!--}-->

<!--const getAgentMessageAndRecord = (record?: boolean) => {-->
<!--  getAIInfo({ ai_id: aiID.value }).then((res) => {-->
<!--    const { code, data, msg } = res-->
<!--    if (code === 200) {-->
<!--      agentMessage.value = data-->
<!--      if (record && data.bg_info.type === SkinTypeEnum.ThreeD) {-->
<!--        getThreeDResource(data.bg_info.dress_id)-->
<!--      }-->
<!--      if (record) {-->
<!--        getAgentRecord()-->
<!--      }-->
<!--      if (agentMessage.value.bgm && !bgmHowlerInstance.value) {-->
<!--        bgmHowlerInstance.value = new Howl({-->
<!--          src: agentMessage.value.bgm,-->
<!--          loop: true-->
<!--        })-->
<!--        if (userStore.userInformation.bgm) {-->
<!--          bgmHowlerInstance.value.play()-->
<!--        }-->
<!--      }-->
<!--    } else if (code === ResultEnum.ABSENT_AGENT) {-->
<!--      useModal({-->
<!--        message: msg,-->
<!--        duration: 1500,-->
<!--        onClose: () => {-->
<!--          router.push('/')-->
<!--        }-->
<!--      })-->
<!--    } else if (code === ResultEnum.UN_EXISTENCE) {-->
<!--      router.replace('/404')-->
<!--    }-->
<!--  })-->
<!--}-->

<!--const getAgentRecord = () => {-->
<!--  getChatRecord({-->
<!--    ai_id: aiID.value,-->
<!--    limit: 9999,-->
<!--    page: 1-->
<!--  }).then((res) => {-->
<!--    const { code, data } = res-->
<!--    if (code === 200) {-->
<!--      let record: ChatRecordType[] = []-->
<!--      // 没记录，显示ai生成提示-->
<!--      if (data.list.filter((item) => !item.content_info).length === 0) {-->
<!--        isNoRecord.value = true-->
<!--        record = [-->
<!--          {-->
<!--            type: ChatRecordTypeEnum.WARN,-->
<!--            content: '',-->
<!--            is_like: BOOL_NUMBER.NO,-->
<!--            is_bad: BOOL_NUMBER.NO,-->
<!--            message_id: '',-->
<!--            is_phone_call: BOOL_NUMBER.NO,-->
<!--            replayTime: 0-->
<!--            // replayContent: []-->
<!--          }-->
<!--        ]-->
<!--      }-->
<!--      // 剧情和开场白-->
<!--      if (agentMessage.value.init_plot) {-->
<!--        record.push({-->
<!--          type: ChatRecordTypeEnum.SYNOPSIS,-->
<!--          content: agentMessage.value.init_plot-->
<!--        })-->
<!--      }-->
<!--      if (agentMessage.value.opening_statement) {-->
<!--        record.push({-->
<!--          type: ChatRecordTypeEnum.AI,-->
<!--          content: agentMessage.value.opening_statement,-->
<!--          is_like: BOOL_NUMBER.NO,-->
<!--          is_bad: BOOL_NUMBER.NO,-->
<!--          is_phone_call: BOOL_NUMBER.NO,-->
<!--          message_id: '',-->
<!--          replayTime: 0,-->
<!--          // replayContent: [],-->
<!--          ttsLoading: false,-->
<!--          ttsPlaying: false,-->
<!--          audio_file_url: agentMessage.value.opening_statement_voice,-->
<!--          audio_length: Number((agentMessage.value.opening_statement.length * 0.15).toFixed(0))-->
<!--        })-->
<!--      }-->
<!--      // 记录列表-->
<!--      data.list.toReversed().map((item) => {-->
<!--        const baseRecord = {-->
<!--          is_like: item.is_like,-->
<!--          is_bad: item.is_bad,-->
<!--          message_id: item.message_id,-->
<!--          is_phone_call: item.is_phone_call,-->
<!--          call_time: item.call_time,-->
<!--          replayTime: 0,-->
<!--          // replayContent: [],-->
<!--          ttsLoading: false,-->
<!--          audio_file_url: item?.audio_file_url,-->
<!--          audio_length: item?.audio_length-->
<!--        }-->
<!--        // 语音记录同步提示-->
<!--        if (item.is_phone_call) {-->
<!--          record.push({-->
<!--            ...baseRecord,-->
<!--            type: ChatRecordTypeEnum.PHONE,-->
<!--            content: t('syncPhoneCallRecord')-->
<!--          })-->
<!--          return-->
<!--        }-->

<!--        // 继续未完成的内容玩法提示-->
<!--        if (item.input_query === 'continue_content') {-->
<!--          // record.push({-->
<!--          //   ...baseRecord,-->
<!--          //   type: ChatRecordTypeEnum.KEEP_TALK,-->
<!--          //   content: t('Talking last time：') + item.content_info.name,-->
<!--          //   content_id: item.content_id-->
<!--          // })-->
<!--          return-->
<!--        }-->

<!--        // 重启对话提示-->
<!--        if (item.output_type === QueryTypeEnum.TIPS) {-->
<!--          record.push({-->
<!--            ...baseRecord,-->
<!--            type: ChatRecordTypeEnum.TIPS,-->
<!--            content: t('newChatStartedNotice')-->
<!--          })-->

<!--          // 有开场白在重启后面加一句开场白-->
<!--          if (agentMessage.value.opening_statement) {-->
<!--            record.push({-->
<!--              ...baseRecord,-->
<!--              type: ChatRecordTypeEnum.AI,-->
<!--              content: agentMessage.value.opening_statement,-->
<!--              is_like: BOOL_NUMBER.NO,-->
<!--              is_bad: BOOL_NUMBER.NO,-->
<!--              is_phone_call: BOOL_NUMBER.NO,-->
<!--              message_id: '',-->
<!--              replayTime: 0,-->
<!--              ttsPlaying: false,-->
<!--              audio_file_url: agentMessage.value.opening_statement_voice,-->
<!--              audio_length: Number((agentMessage.value.opening_statement?.length * 0.15).toFixed(0))-->
<!--            })-->
<!--          }-->
<!--          return-->
<!--        }-->

<!--        // 好感度玩法已解锁提示-->
<!--        if (item.input_query === 'unlocked_content') {-->
<!--          // if (item.is_later === BOOL_NUMBER.NO) {-->
<!--          //   flagStatus.showContentGameBtn = true-->
<!--          //   showContentGameMessage.value.sessionID = item.session_id-->
<!--          //   showContentGameMessage.value.contentID = item.content_id-->
<!--          //   showContentGameMessage.value.goBtnText = item.content_info?.into_button-->
<!--          //   showContentGameMessage.value.laterBtnText = item.content_info?.close_button-->
<!--          // }-->
<!--          // record.push({-->
<!--          //   ...baseRecord,-->
<!--          //   type: ChatRecordTypeEnum.STORY_UNLOCK,-->
<!--          //   content: `LV${item?.content_info?.level} exclusive gameplay has been unlocked`,-->
<!--          //   is_later: item.is_later,-->
<!--          //   content_id: item.content_id-->
<!--          // })-->
<!--          return-->
<!--        }-->

<!--        // 好感度升级提示-->
<!--        if (item.input_type === QueryTypeEnum.UPGRADE) {-->
<!--          // record.push({-->
<!--          //   ...baseRecord,-->
<!--          //   type: ChatRecordTypeEnum.INTIMACY_UPGRADE,-->
<!--          //   content: `Intimacy level upgraded to LV${item.input_query}`-->
<!--          // })-->
<!--          return-->
<!--        }-->

<!--        // 故事结束同步记录提示-->
<!--        if (item.input_type === QueryTypeEnum.ENDING) {-->
<!--          record.push({-->
<!--            ...baseRecord,-->
<!--            type: ChatRecordTypeEnum.TIPS,-->
<!--            content: t('syncGameMemory', { level: item.content_info.level })-->
<!--            // content: `已同步LV${item.content_info.level}专属玩法记忆`-->
<!--          })-->
<!--          record.push({-->
<!--            ...baseRecord,-->
<!--            type: ChatRecordTypeEnum.AI,-->
<!--            content: item.output_text-->
<!--          })-->
<!--          return-->
<!--        }-->

<!--        // 自拍图-->
<!--        if (item.pic_id && item.pic_info?.pic_url) {-->
<!--          record.push({-->
<!--            ...baseRecord,-->
<!--            type: ChatRecordTypeEnum.IMAGE,-->
<!--            content: item.pic_info?.pic_url || item.pic_info?.vague_pic_url,-->
<!--            photoDetail: item.pic_info-->
<!--          })-->
<!--          return-->
<!--        }-->

<!--        // 内容玩法提示图与开场白-->
<!--        if (item.input_query === 'start_content') {-->
<!--          record.push({-->
<!--            ...baseRecord,-->
<!--            content_id: item.content_id,-->
<!--            type: ChatRecordTypeEnum.STORY_IMAGE,-->
<!--            content: item.content_info?.prefix_url + item.content_info?.cover_url-->
<!--          })-->
<!--          record.push({-->
<!--            ...baseRecord,-->
<!--            type: ChatRecordTypeEnum.TIPS,-->
<!--            content: t('clickToStory')-->
<!--          })-->
<!--          if (item.content_info?.opening_statement_outside) {-->
<!--            record.push({-->
<!--              ...baseRecord,-->
<!--              type: ChatRecordTypeEnum.AI,-->
<!--              content: item.content_info?.opening_statement_outside || ''-->
<!--            })-->
<!--          }-->
<!--          return-->
<!--        }-->

<!--        // 对话-->
<!--        if (item.input_query) {-->
<!--          // 送礼提示语不显示-->
<!--          if (item.send_gift !== BOOL_NUMBER.YES && item.input_query !== '☆') {-->
<!--            record.push({-->
<!--              ...baseRecord,-->
<!--              type: ChatRecordTypeEnum.USER,-->
<!--              content: item.input_query-->
<!--            })-->
<!--          }-->
<!--        }-->

<!--        if (item.output_text) {-->
<!--          if (item.output_text.includes(FAKE_COMMENT_START_FLAG) || item.output_text.includes(FAKE_COMMENT_END_FLAG)) {-->
<!--            // console.log(handleFakeCommentRecord(item.output_text))-->
<!--            handleFakeCommentRecord(item.output_text).map((item) => {-->
<!--              record.push({-->
<!--                ...baseRecord,-->
<!--                audio_file_url: undefined,-->
<!--                type: ChatRecordTypeEnum.AI,-->
<!--                content: item,-->
<!--                is_fake_comment: true,-->
<!--                audio_length: Number((item?.length * 0.15).toFixed(0))-->
<!--              })-->
<!--            })-->
<!--          } else {-->
<!--            if (item.gift_info) {-->
<!--              record.push({-->
<!--                ...baseRecord,-->
<!--                type: ChatRecordTypeEnum.GIFT,-->
<!--                gift_info: item.gift_info,-->
<!--                content: t('send') + `${item.gift_info.name}，`-->
<!--              })-->
<!--            }-->
<!--            record.push({-->
<!--              ...baseRecord,-->
<!--              type: ChatRecordTypeEnum.AI,-->
<!--              content: item.output_text-->
<!--            })-->
<!--          }-->
<!--        }-->
<!--      })-->

<!--      chatRecord.value = record-->
<!--      scrollToBottom(chatRef.value, 'instant')-->
<!--    }-->
<!--  })-->
<!--}-->

<!--const getThreeDResource = (id: number) => {-->
<!--  getThreeDSource({-->
<!--    id-->
<!--  }).then((res) => {-->
<!--    const { data, code } = res-->
<!--    if (code === ResultEnum.SUCCESS) {-->
<!--      live2dResource.value = data-->
<!--      flagStatus.live2d = true-->
<!--      handleGuideClearMode()-->
<!--      nextTick(() => {-->
<!--        l2dRef.value.createNewModel()-->
<!--      })-->
<!--    }-->
<!--  })-->
<!--}-->

<!--const restartChat = () => {-->
<!--  useConfirm({-->
<!--    content: t('resetCharacterNotice'),-->
<!--    confirmText: t('confirmButton'),-->
<!--    cancelText: t('cancelButton'),-->
<!--    onConfirm: () => {-->
<!--      const { close } = useModal({-->
<!--        loading: true,-->
<!--        message: t('restarting'),-->
<!--        autoClose: false-->
<!--      })-->
<!--      startNewChat({ ai_id: aiID.value })-->
<!--        .then((res) => {-->
<!--          const { code } = res-->
<!--          if (code === 200) {-->
<!--            useModal({-->
<!--              message: t('restartSuccessful')-->
<!--            })-->
<!--            getAgentRecord()-->
<!--          }-->
<!--        })-->
<!--        .catch((err) => {-->
<!--          console.warn(err)-->
<!--        })-->
<!--        .finally(() => {-->
<!--          close()-->
<!--        })-->
<!--    }-->
<!--  })-->
<!--}-->

<!--const permissionInit = () => {-->
<!--  // @ts-ignore-->
<!--  navigator.permissions?.query({ name: 'microphone' }).then((res) => {-->
<!--    if (res.state === 'granted') {-->
<!--      flagStatus.micAllow = true-->
<!--    }-->
<!--  })-->
<!--  const popup = popupMemoryStore.getPopupMemory()-->
<!--  if (popup) {-->
<!--    switch (popup.type) {-->
<!--      case POPUP_TYPE_NAME.BUY_SKIN:-->
<!--        nextTick(() => {-->
<!--          skinVisible.value = true-->
<!--        })-->
<!--        break-->
<!--      case POPUP_TYPE_NAME.SEND_GIFT:-->
<!--        nextTick(() => {-->
<!--          giftVisible.value = true-->
<!--          popupMemoryStore.resetAllPopupMemory()-->
<!--        })-->
<!--        break-->
<!--      // case POPUP_TYPE_NAME.BUY_SELFIE:-->
<!--      //   unlockVisible.value = true-->
<!--      //   break-->
<!--      case POPUP_TYPE_NAME.BUY_CALL_TIME:-->
<!--        topUpVisible.value = true-->
<!--        popupMemoryStore.resetAllPopupMemory()-->
<!--        break-->
<!--    }-->
<!--  }-->
<!--  loadingStatus.record = true-->
<!--  nextTick(() => {-->
<!--    loadingStatus.record = false-->
<!--  })-->
<!--  console.log(route, 'sdsds')-->
<!--  if (/\/contentGame\/\d+/.test(route.redirectedFrom?.path)) {-->
<!--    eventReport({-->
<!--      event_type: EventTypeEnum.ENTER_STORY_PAGE,-->
<!--      front_address: ForwardAddressEnum.STORY_CHAT_PAGE_BACK_CHAT-->
<!--    }).catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--  }-->
<!--  getAgentContentGameOfMyList({-->
<!--    ai_id: aiID.value-->
<!--  })-->
<!--    .then(({ data, code }) => {-->
<!--      if (code === ResultEnum.SUCCESS) {-->
<!--        console.log(data.length)-->
<!--        hadStoryBook.value = Boolean(data.length)-->
<!--      }-->
<!--    })-->
<!--    .catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--  getAgentContentGameList({-->
<!--    ai_id: aiID.value-->
<!--  }).then(({ code, data }) => {-->
<!--    if (code === ResultEnum.SUCCESS) {-->
<!--      contentGameList.value = data-->
<!--    }-->
<!--  })-->
<!--  getSingleAgentContentGameInChatPage({-->
<!--    ai_id: aiID.value-->
<!--  }).then(({ code, data }) => {-->
<!--    if (code === ResultEnum.SUCCESS) {-->
<!--      console.log(data)-->
<!--      contentGameInPageList.value = data-->
<!--    }-->
<!--  })-->
<!--  getAgentMessageAndRecord(true)-->
<!--  userStore.getUserInfo()-->
<!--}-->

<!--const goContentGame = (item: IContentRes | number) => {-->
<!--  console.log(typeof item)-->
<!--  if (typeof item === 'number') {-->
<!--    eventReport({-->
<!--      event_type: EventTypeEnum.ENTER_STORY,-->
<!--      content_id: item,-->
<!--      front_address: ForwardAddressEnum.CHAT_PAGE,-->
<!--      ai_id: aiID.value-->
<!--    }).catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--    router.push(`/contentGame/${item}`)-->
<!--  } else {-->
<!--    if (item.lock_status === ContentGameStatusEnum.LOCKED) {-->
<!--      useModal({-->
<!--        message: t('upgradeToUnlockStory')-->
<!--      })-->
<!--      return-->
<!--    }-->
<!--    router.push(`/contentGame/${item.id}`)-->
<!--    eventReport({-->
<!--      event_type: EventTypeEnum.ENTER_STORY,-->
<!--      content_id: item.id,-->
<!--      front_address: ForwardAddressEnum.CHAT_BOX_BTN,-->
<!--      ai_id: aiID.value-->
<!--    }).catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--  }-->
<!--}-->

<!--const likeAgentText = (data: ChatRecordType) => {-->
<!--  if (loadingStatus.feedback) return-->
<!--  loadingStatus.feedback = true-->
<!--  likeOrUnlikeAgentText({-->
<!--    message_id: data.message_id,-->
<!--    is_like: data.is_like ? BOOL_NUMBER.NO : BOOL_NUMBER.YES,-->
<!--    is_bad: data.is_like ? undefined : BOOL_NUMBER.NO-->
<!--  })-->
<!--    .then((res) => {-->
<!--      if (res.code === 200) {-->
<!--        useModal({-->
<!--          message: data.is_like ? t('likeCancelled') : t('likeSuccessful')-->
<!--        })-->
<!--        if (!data.is_like) {-->
<!--          data.is_bad = BOOL_NUMBER.NO-->
<!--        }-->
<!--        data.is_like = data.is_like ? BOOL_NUMBER.NO : BOOL_NUMBER.YES-->
<!--      }-->
<!--    })-->
<!--    .catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--    .finally(() => {-->
<!--      loadingStatus.feedback = false-->
<!--    })-->
<!--}-->

<!--const unlikeAgentText = (data: ChatRecordType) => {-->
<!--  if (loadingStatus.feedback) return-->
<!--  loadingStatus.feedback = true-->
<!--  likeOrUnlikeAgentText({-->
<!--    message_id: data.message_id,-->
<!--    is_bad: data.is_bad ? BOOL_NUMBER.NO : BOOL_NUMBER.YES,-->
<!--    is_like: data.is_bad ? undefined : BOOL_NUMBER.NO-->
<!--  })-->
<!--    .then((res) => {-->
<!--      if (res.code === 200) {-->
<!--        useModal({-->
<!--          message: data.is_bad ? t('dislikeCancelled') : t('dislikeSuccessful')-->
<!--        })-->
<!--        if (!data.is_bad) {-->
<!--          data.is_like = BOOL_NUMBER.NO-->
<!--        }-->
<!--        data.is_bad = data.is_bad ? BOOL_NUMBER.NO : BOOL_NUMBER.YES-->
<!--      }-->
<!--    })-->
<!--    .catch((err) => {-->
<!--      console.warn(err)-->
<!--    })-->
<!--    .finally(() => {-->
<!--      loadingStatus.feedback = false-->
<!--    })-->
<!--}-->

<!--getDress()-->
<!--permissionInit()-->

<!--provide('aiID', aiID)-->
<!--provide('agentMessage', agentMessage)-->
<!--provide('live2dStatus', live2dStatus)-->

<!--watch(-->
<!--  // @ts-ignore-->
<!--  () => route.params.ai_id,-->
<!--  (id) => {-->
<!--    if (id) {-->
<!--      aiID.value = id as number-->
<!--    }-->
<!--  },-->
<!--  {-->
<!--    immediate: true,-->
<!--    deep: true-->
<!--  }-->
<!--)-->

<!--watch(-->
<!--  () => live2dStatus.value,-->
<!--  (val) => {-->
<!--    if (val) {-->
<!--      eventReport({-->
<!--        event_type: EventTypeEnum.ENTER_3D_BACKGROUND,-->
<!--        ai_id: aiID.value-->
<!--      }).catch((err) => {-->
<!--        console.warn(err)-->
<!--      })-->
<!--      nextTick(() => {-->
<!--        useIntersectionObserver(-->
<!--          live2dClear.value,-->
<!--          ([{ isIntersecting }]) => {-->
<!--            isClearMode.value = isIntersecting-->
<!--            if (isIntersecting) {-->
<!--              eventReport({-->
<!--                event_type: EventTypeEnum.ENTER_IMMERSE_MODE,-->
<!--                ai_id: aiID.value-->
<!--              }).catch((err) => {-->
<!--                console.warn(err)-->
<!--              })-->
<!--              userStore.newerOption.hadSwitchToClearMode = true-->
<!--              handleGuideTouch()-->
<!--            }-->
<!--          },-->
<!--          {-->
<!--            threshold: 0.8-->
<!--          }-->
<!--        )-->
<!--      })-->
<!--    } else {-->
<!--      l2dRef.value.erosModel?.destroySound()-->
<!--      eventReport({-->
<!--        event_type: EventTypeEnum.EXIT_3D_BACKGROUND,-->
<!--        ai_id: aiID.value-->
<!--      }).catch((err) => {-->
<!--        console.warn(err)-->
<!--      })-->
<!--    }-->
<!--  }-->
<!--)-->

<!--document.addEventListener('visibilitychange', () => {-->
<!--  if (document.visibilityState === 'visible') {-->
<!--    bgmHowlerInstance.value?.play()-->
<!--  }-->
<!--})-->

<!--onMounted(() => {-->
<!--  const observer = new ResizeObserver(() => {-->
<!--    scrollToBottom(chatRef.value)-->
<!--  })-->
<!--  observer.observe(collapseContentRef.value)-->

<!--  window.addEventListener('resize', () => {-->
<!--    scrollToBottom(chatRef.value)-->
<!--  })-->

<!--  // animationInstance.value = lottie.loadAnimation({-->
<!--  //   container: heartRef.value, // 容器-->
<!--  //   renderer: 'svg', // 通过svg或canvas渲染-->
<!--  //   loop: false, // 是否循环-->
<!--  //   autoplay: false, // 是否自动播放-->
<!--  //   animationData: animation, // 动画文件-->
<!--  //   assetsPath: (import.meta.env.VITE_APP_PUBLIC_PATH === '/' ? '' : import.meta.env.VITE_APP_PUBLIC_PATH) + '/heart-diffuse/' // 动画文件路径-->
<!--  // })-->
<!--  // //@ts-ignore-->
<!--  // animationInstance.value.onComplete = () => {-->
<!--  //   animationInstance.value.stop()-->
<!--  // }-->

<!--  // 无法滚动时 电脑适配滚轮-->
<!--  chatRef.value.addEventListener('wheel', (event: WheelEvent) => {-->
<!--    if (event.deltaY > 0) {-->
<!--      if (chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop <= 4) {-->
<!--        flagStatus.halfScreen = true-->
<!--      }-->
<!--    } else {-->
<!--      flagStatus.halfScreen = false-->
<!--    }-->
<!--  })-->
<!--  // 无法滚动时适配手机触摸-->
<!--  chatRef.value.addEventListener('touchstart', (e: TouchEvent) => {-->
<!--    closeCollapse()-->
<!--    slidePoint.start = e.touches[0].pageY-->
<!--  })-->
<!--  chatRef.value?.addEventListener('scroll', () => {-->
<!--    flagStatus.showScrollDown = chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop > 120-->
<!--  })-->
<!--  chatRef.value.addEventListener('touchmove', (e: TouchEvent) => {-->
<!--    slidePoint.end = e.touches[0].pageY-->
<!--    if (slidePoint.end > slidePoint.start) {-->
<!--      flagStatus.halfScreen = false-->
<!--    } else if (slidePoint.end < slidePoint.start) {-->
<!--      if (chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop <= 4) {-->
<!--        flagStatus.halfScreen = true-->
<!--      }-->
<!--    }-->
<!--  })-->

<!--  talkBtnRef.value.addEventListener('touchstart', (e: TouchEvent) => {-->
<!--    bgmHowlerInstance.value.pause()-->
<!--    // 禁止穿透-->
<!--    e.preventDefault()-->
<!--    if (loadingStatus.tts || loadingStatus.streaming || loadingStatus.textOutputting) {-->
<!--      useModal({-->
<!--        message: t('conversationProcessing')-->
<!--      })-->
<!--      return-->
<!--    }-->
<!--    if (flagStatus.micAllow) {-->
<!--      navigator?.vibrate?.(100)-->
<!--      recOpenInit(recStart)-->
<!--      clickTime.start = Date.now()-->
<!--      slidePoint.start = e.touches[0].pageY-->
<!--    } else {-->
<!--      recOpenInit(null)-->
<!--    }-->
<!--  })-->

<!--  talkBtnRef.value.addEventListener('touchmove', (e: TouchEvent) => {-->
<!--    // if (loadingStatus.tts || loadingStatus.text) return-->
<!--    if (flagStatus.micAllow) {-->
<!--      slidePoint.end = e.touches[0].pageY-->
<!--    }-->
<!--  })-->
<!--  talkBtnRef.value.addEventListener('touchend', (e: TouchEvent) => {-->
<!--    bgmHowlerInstance.value.play()-->
<!--    // if (loadingStatus.tts || loadingStatus.text) return-->
<!--    if (flagStatus.micAllow) {-->
<!--      clickTime.end = Date.now()-->
<!--      slidePoint.end = e.changedTouches[0].pageY-->
<!--      try {-->
<!--        // 向下滑动不取消-->
<!--        if (slidePoint.start - slidePoint.end > 60 && slidePoint.end < slidePoint.start) {-->
<!--          console.log('取消录音')-->
<!--          recStop(true)-->
<!--        } else {-->
<!--          console.log('正常录音')-->
<!--          recStop(false)-->
<!--        }-->
<!--      } catch (error) {-->
<!--        console.warn(error)-->
<!--      }-->
<!--      countDownRef.value.reset()-->
<!--      loadingStatus.record = false-->
<!--      if (clickTime.end - clickTime.start < 500) {-->
<!--        useModal({-->
<!--          message: t('voiceTooShort'),-->
<!--          duration: 1000-->
<!--        })-->
<!--      }-->
<!--    }-->
<!--    slidePoint.start = 0-->
<!--    slidePoint.end = 0-->
<!--  })-->
<!--})-->

<!--onBeforeUnmount(() => {-->
<!--  l2dRef.value?.erosModel?.destroySound?.()-->
<!--  Howler.stop()-->
<!--  if (myStream.value) {-->
<!--    myStream.value.stop()-->
<!--    myStream.value = null-->
<!--  }-->
<!--})-->
<!--</script>-->

<!--<template>-->
<!--  <div-->
<!--    id="modelContainer"-->
<!--    class="whole-chat-container"-->
<!--    :style="-->
<!--      agentMessage.bg_info?.bg_url &&-->
<!--      agentMessage.bg_info.type !== SkinTypeEnum.ThreeD && {-->
<!--        background: `url('${agentMessage.bg_info?.bg_url}') center / cover no-repeat`-->
<!--      }-->
<!--    "-->
<!--  >-->
<!--    <GuideForClearMode-->
<!--      v-if="guideForClearModeVisible"-->
<!--      v-model="guideForClearModeVisible"-->
<!--    />-->
<!--    <GuideForTouch-->
<!--      v-if="guideForTouchVisible"-->
<!--      v-model="guideForTouchVisible"-->
<!--    />-->
<!--    <div class="top-bar">-->
<!--      <div-->
<!--        v-show="!isClearMode"-->
<!--        class="left-content flex-center-center cg-8"-->
<!--      >-->
<!--        <SvgIcon-->
<!--          icon-class="left-arrow"-->
<!--          class="fsize-24"-->
<!--          @click="goBack"-->
<!--        />-->
<!--        <div class="head-message">-->
<!--          <div-->
<!--            class="avatar-container"-->
<!--            :style="{ '&#45;&#45;percentage': intimacyPercent }"-->
<!--            @click="intimacyLevelVisible = true"-->
<!--          >-->
<!--            <div-->
<!--              ref="heartRef"-->
<!--              class="heart-down"-->
<!--            >-->
<!--              <SvgIcon-->
<!--                icon-class="heart-intimacy"-->
<!--                class="fsize-16"-->
<!--              />-->
<!--              <div class="num">{{ agentMessage.ai_chat_setting.intimacy_level }}</div>-->
<!--            </div>-->
<!--            <div-->
<!--              class="avatar"-->
<!--              :style="{ backgroundImage: `url('${agentMessage.avatar_url}')` }"-->
<!--            ></div>-->
<!--          </div>-->
<!--          <div-->
<!--            class="name"-->
<!--            @click="goHomePage"-->
<!--          >-->
<!--            {{ agentMessage.name }} <SvgIcon icon-class="arrow-right-jump" />-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--      <div-->
<!--        class="right-btn"-->
<!--        v-if="!isClearMode"-->
<!--      >-->
<!--        <div-->
<!--          v-if="agentMessage.bgm"-->
<!--          class="switch-btn flex-center-center"-->
<!--          @click="debounceSetBgmStatus"-->
<!--        >-->
<!--          <SvgIcon-->
<!--            v-show="userStore.userInformation.bgm"-->
<!--            icon-class="music-on"-->
<!--            class="fsize-24"-->
<!--          />-->
<!--          <SvgIcon-->
<!--            v-show="!userStore.userInformation.bgm"-->
<!--            icon-class="music-off"-->
<!--            class="fsize-24"-->
<!--          />-->
<!--        </div>-->
<!--        <div-->
<!--          class="switch-btn flex-center-center"-->
<!--          v-show="l2dSkinData?.id"-->
<!--        >-->
<!--          <SvgIcon-->
<!--            v-show="!isClearMode"-->
<!--            icon-class="l2d-change"-->
<!--            class="fsize-24"-->
<!--            @click="l2dSwitch()"-->
<!--          />-->
<!--        </div>-->
<!--      </div>-->
<!--      <div-->
<!--        v-show="isClearMode"-->
<!--        @click="exitClearMode"-->
<!--        class="exit-l2d"-->
<!--      >-->
<!--        <van-icon-->
<!--          name="arrow-left"-->
<!--          class="mr-4"-->
<!--        />{{ t('exitClearMode') }}-->
<!--      </div>-->
<!--    </div>-->
<!--    <div-->
<!--      class="chat-page"-->
<!--      ref="chatPageRef"-->
<!--    >-->
<!--      <div-->
<!--        ref="chatRef"-->
<!--        class="chat"-->
<!--        :style="{-->
<!--          '&#45;&#45;chat-mask-start': flagStatus.halfScreen ? '50%' : '50px',-->
<!--          '&#45;&#45;chat-mask-end': flagStatus.halfScreen ? '55%' : '75px'-->
<!--        }"-->
<!--      >-->
<!--        <div-->
<!--          class="whole-chat-item"-->
<!--          v-for="(item, index) in chatRecord"-->
<!--          :key="item.message_id + item.type + String(index)"-->
<!--        >-->
<!--          <div-->
<!--            ref="replayItemRef"-->
<!--            class="chat-item-container"-->
<!--          >-->
<!--            &lt;!&ndash;            <div&ndash;&gt;-->
<!--            &lt;!&ndash;              class="sound"&ndash;&gt;-->
<!--            &lt;!&ndash;              v-if="item.type === ChatRecordTypeEnum.AI && item.audio_length"&ndash;&gt;-->
<!--            &lt;!&ndash;              @click="debounceHandleTTSCase(item)"&ndash;&gt;-->
<!--            &lt;!&ndash;            >&ndash;&gt;-->
<!--            &lt;!&ndash;              <SoundLoading&ndash;&gt;-->
<!--            &lt;!&ndash;                v-if="item.ttsPlaying"&ndash;&gt;-->
<!--            &lt;!&ndash;                class="h-full"&ndash;&gt;-->
<!--            &lt;!&ndash;              />&ndash;&gt;-->
<!--            &lt;!&ndash;              <img&ndash;&gt;-->
<!--            &lt;!&ndash;                v-else-if="item.ttsLoading"&ndash;&gt;-->
<!--            &lt;!&ndash;                class="sound-loading"&ndash;&gt;-->
<!--            &lt;!&ndash;                src="@/assets/images/sound-loading.png"&ndash;&gt;-->
<!--            &lt;!&ndash;                alt="loading"&ndash;&gt;-->
<!--            &lt;!&ndash;              />&ndash;&gt;-->
<!--            &lt;!&ndash;              <SvgIcon&ndash;&gt;-->
<!--            &lt;!&ndash;                v-else&ndash;&gt;-->
<!--            &lt;!&ndash;                icon-class="play"&ndash;&gt;-->
<!--            &lt;!&ndash;                class="fsize-14"&ndash;&gt;-->
<!--            &lt;!&ndash;                style="color: #fff"&ndash;&gt;-->
<!--            &lt;!&ndash;              />&ndash;&gt;-->
<!--            &lt;!&ndash;              <div class="text">{{ item.audio_length.toFixed(0) }}”</div>&ndash;&gt;-->
<!--            &lt;!&ndash;            </div>&ndash;&gt;-->
<!--            <div class="chat-item">-->
<!--              <div-->
<!--                v-if="item.type === ChatRecordTypeEnum.IMAGE"-->
<!--                :class="CHAT_BOX_STYLE_CLASS[item.type]"-->
<!--                @click="handlePhoto(item)"-->
<!--              >-->
<!--                <van-image-->
<!--                  class="van-img"-->
<!--                  :src="item.photoDetail.pic_url || item.photoDetail.vague_pic_url"-->
<!--                  fit="cover"-->
<!--                >-->
<!--                  <template #loading>-->
<!--                    <img-->
<!--                      src="/ugenie.png"-->
<!--                      alt=""-->
<!--                    />-->
<!--                  </template>-->
<!--                  <template #error>-->
<!--                    <img-->
<!--                      src="/ugenie.png"-->
<!--                      alt=""-->
<!--                    />-->
<!--                  </template>-->
<!--                </van-image>-->
<!--                <div-->
<!--                  class="selfie-overlay"-->
<!--                  v-if="!item.photoDetail.pic_url"-->
<!--                >-->
<!--                  <SvgIcon-->
<!--                    icon-class="lock"-->
<!--                    class="fsize-28"-->
<!--                  />-->
<!--                  <div class="flex-center-center">-->
<!--                    <SvgIcon-->
<!--                      :icon-class="item.currency_type === ICurrencyTypeEnum.Coin ? 'coin' : 'crystal'"-->
<!--                      class="fsize-16"-->
<!--                    />-->
<!--                    <span class="fsize-12 ml-2">-->
<!--                      {{ item.photoDetail.value }}{{ item.currency_type === ICurrencyTypeEnum.Coin ? t('gold') : t('crystal') }}-->
<!--                    </span>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
<!--              <div-->
<!--                v-else-if="item.type === ChatRecordTypeEnum.WARN"-->
<!--                :class="CHAT_BOX_STYLE_CLASS[item.type]"-->
<!--              >-->
<!--                <div class="tips">{{ t('aiContentDisclaimer') }}</div>-->
<!--              </div>-->
<!--              <div-->
<!--                v-else-if="item.type === ChatRecordTypeEnum.SYNOPSIS"-->
<!--                :class="CHAT_BOX_STYLE_CLASS[item.type]"-->
<!--              >-->
<!--                <div class="synopsis">-->
<!--                  <SvgIcon-->
<!--                    icon-class="opening"-->
<!--                    class="fsize-18"-->
<!--                  />-->
<!--                  <div>{{ item.content }}</div>-->
<!--                </div>-->
<!--              </div>-->
<!--              &lt;!&ndash;              <div&ndash;&gt;-->
<!--              &lt;!&ndash;                class="chat-item-tips"&ndash;&gt;-->
<!--              &lt;!&ndash;                v-else-if="item.type === ChatRecordTypeEnum.TIPS"&ndash;&gt;-->
<!--              &lt;!&ndash;              >&ndash;&gt;-->
<!--              &lt;!&ndash;                <div class="tips">{{ item.content }}</div>&ndash;&gt;-->
<!--              &lt;!&ndash;              </div>&ndash;&gt;-->
<!--              <div-->
<!--                v-else-if="item.type === ChatRecordTypeEnum.PHONE"-->
<!--                :class="CHAT_BOX_STYLE_CLASS[item.type]"-->
<!--              >-->
<!--                <div-->
<!--                  class="line"-->
<!--                  style="height: 1px"-->
<!--                ></div>-->
<!--                <div class="content">{{ item.content }}</div>-->
<!--                <div-->
<!--                  class="line"-->
<!--                  style="height: 1px"-->
<!--                ></div>-->
<!--              </div>-->
<!--              <div-->
<!--                v-else-if="item.type === ChatRecordTypeEnum.TIPS"-->
<!--                :class="CHAT_BOX_STYLE_CLASS[item.type]"-->
<!--              >-->
<!--                <div-->
<!--                  class="line"-->
<!--                  style="height: 1px"-->
<!--                ></div>-->
<!--                <div class="content">{{ item.content }}</div>-->
<!--                <div-->
<!--                  class="line"-->
<!--                  style="height: 1px"-->
<!--                ></div>-->
<!--              </div>-->
<!--              <div-->
<!--                v-else-if="item.type === ChatRecordTypeEnum.GIFT"-->
<!--                :class="CHAT_BOX_STYLE_CLASS[item.type]"-->
<!--              >-->
<!--                <div-->
<!--                  class="line"-->
<!--                  style="height: 1px"-->
<!--                ></div>-->
<!--                <div class="content flex-center-center">-->
<!--                  {{ item.content }}-->
<!--                  <SvgIcon-->
<!--                    icon-class="intimacy-value-heart"-->
<!--                    class="fsize-12"-->
<!--                  />+{{ item.gift_info?.intimacy }}-->
<!--                </div>-->
<!--                <div-->
<!--                  class="line"-->
<!--                  style="height: 1px"-->
<!--                ></div>-->
<!--              </div>-->
<!--              <div-->
<!--                v-else-if="item.type === ChatRecordTypeEnum.STORY_IMAGE"-->
<!--                :class="CHAT_BOX_STYLE_CLASS[item.type]"-->
<!--                :style="{ backgroundImage: `url('${item.content}')` }"-->
<!--                @click="goContentGame(item.content_id)"-->
<!--              >-->
<!--                <div class="story-flag">-->
<!--                  <SvgIcon-->
<!--                    icon-class="opening"-->
<!--                    class="fsize-14"-->
<!--                  />{{ t('story') }}-->
<!--                </div>-->
<!--                <div class="enter-story flex-center-center cg-4">-->
<!--                  {{ t('startNow') }}-->
<!--                  <SvgIcon-->
<!--                    icon-class="arrow-right-content"-->
<!--                    class="fsize-16"-->
<!--                  />-->
<!--                </div>-->
<!--                &lt;!&ndash;                <img&ndash;&gt;-->
<!--                &lt;!&ndash;                  :src="item.content"&ndash;&gt;-->
<!--                &lt;!&ndash;                  alt=""&ndash;&gt;-->
<!--                &lt;!&ndash;                />&ndash;&gt;-->
<!--              </div>-->
<!--              <div-->
<!--                :class="CHAT_BOX_STYLE_CLASS[item.type]"-->
<!--                v-else-->
<!--              >-->
<!--                <div-->
<!--                  v-on-long-press.prevent="() => onLongPressCallbackDirective(item, index)"-->
<!--                  class="chat-record"-->
<!--                >-->
<!--                  <ChatLoading v-if="item.userTTS" />-->
<!--                  <van-popover-->
<!--                    v-model:show="showPopover[index]"-->
<!--                    teleport="#app"-->
<!--                    trigger="manual"-->
<!--                    :placement="dividePlacement(item.type)"-->
<!--                  >-->
<!--                    <div class="flex-center-center pb-14 pt-14 pl-14 pr-14 cg-44">-->
<!--                      <div-->
<!--                        class="flex-center-center flex-column fsize-12 rg-8"-->
<!--                        @click="likeAgentText(item)"-->
<!--                      >-->
<!--                        <SvgIcon-->
<!--                          :icon-class="item.is_like ? 'like-hand-active' : 'like-hand'"-->
<!--                          class="fsize-20"-->
<!--                        />{{ t('like') }}-->
<!--                      </div>-->
<!--                      <div-->
<!--                        class="flex-center-center flex-column fsize-12 rg-8"-->
<!--                        @click="unlikeAgentText(item)"-->
<!--                      >-->
<!--                        <SvgIcon-->
<!--                          :icon-class="item.is_bad ? 'unlike-hand-active' : 'unlike-hand'"-->
<!--                          class="fsize-20"-->
<!--                        />{{ t('dislike') }}-->
<!--                      </div>-->
<!--                      <div-->
<!--                        class="flex-center-center flex-column fsize-12 rg-8"-->
<!--                        @click="copyText(item.content)"-->
<!--                      >-->
<!--                        <SvgIcon-->
<!--                          icon-class="copy-option"-->
<!--                          class="fsize-20"-->
<!--                        />{{ t('copy') }}-->
<!--                      </div>-->
<!--                    </div>-->

<!--                    <template #reference>-->
<!--                      <div-->
<!--                        class="no-selected"-->
<!--                        v-html="markdownToHtml(replaceBracketsWithAsterisks(item.content))"-->
<!--                      ></div>-->
<!--                    </template>-->
<!--                  </van-popover>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
<!--            &lt;!&ndash;            重说暂不实现&ndash;&gt;-->
<!--            &lt;!&ndash;            <template v-if="item.replayTime > 0">&ndash;&gt;-->
<!--            &lt;!&ndash;              <div&ndash;&gt;-->
<!--            &lt;!&ndash;                class="chat-item"&ndash;&gt;-->
<!--            &lt;!&ndash;                v-for="(replayItem, replayIndex) in item.replayContent"&ndash;&gt;-->
<!--            &lt;!&ndash;                :key="replayIndex"&ndash;&gt;-->
<!--            &lt;!&ndash;              >&ndash;&gt;-->
<!--            &lt;!&ndash;                <div :class="CHAT_BOX_STYLE_CLASS[item.type]">&ndash;&gt;-->
<!--            &lt;!&ndash;                  <div&ndash;&gt;-->
<!--            &lt;!&ndash;                    v-on-long-press.prevent="() => onLongPressCallbackDirective(item, index)"&ndash;&gt;-->
<!--            &lt;!&ndash;                    class="chat-record"&ndash;&gt;-->
<!--            &lt;!&ndash;                  >&ndash;&gt;-->
<!--            &lt;!&ndash;                    &lt;!&ndash;              <ChatLoading />&ndash;&gt;&ndash;&gt;-->
<!--            &lt;!&ndash;                    <van-popover&ndash;&gt;-->
<!--            &lt;!&ndash;                      v-model:show="showPopover[index]"&ndash;&gt;-->
<!--            &lt;!&ndash;                      teleport="#app"&ndash;&gt;-->
<!--            &lt;!&ndash;                      trigger="manual"&ndash;&gt;-->
<!--            &lt;!&ndash;                      :placement="dividePlacement(item.type)"&ndash;&gt;-->
<!--            &lt;!&ndash;                    >&ndash;&gt;-->
<!--            &lt;!&ndash;                      <div class="flex-center-center pb-16 pt-16 pl-28 pr-28 cg-44">&ndash;&gt;-->
<!--            &lt;!&ndash;                        <div&ndash;&gt;-->
<!--            &lt;!&ndash;                          class="flex-center-center flex-column fsize-12 rg-8"&ndash;&gt;-->
<!--            &lt;!&ndash;                          @click="likeAgentText(item)"&ndash;&gt;-->
<!--            &lt;!&ndash;                        >&ndash;&gt;-->
<!--            &lt;!&ndash;                          <SvgIcon&ndash;&gt;-->
<!--            &lt;!&ndash;                            :icon-class="item.is_like ? 'like-hand-active' : 'like-hand'"&ndash;&gt;-->
<!--            &lt;!&ndash;                            class="fsize-20"&ndash;&gt;-->
<!--            &lt;!&ndash;                          />{{ t('like') }}&ndash;&gt;-->
<!--            &lt;!&ndash;                        </div>&ndash;&gt;-->
<!--            &lt;!&ndash;                        <div&ndash;&gt;-->
<!--            &lt;!&ndash;                          class="flex-center-center flex-column fsize-12 rg-8"&ndash;&gt;-->
<!--            &lt;!&ndash;                          @click="unlikeAgentText(item)"&ndash;&gt;-->
<!--            &lt;!&ndash;                        >&ndash;&gt;-->
<!--            &lt;!&ndash;                          <SvgIcon&ndash;&gt;-->
<!--            &lt;!&ndash;                            :icon-class="item.is_bad ? 'unlike-hand-active' : 'unlike-hand'"&ndash;&gt;-->
<!--            &lt;!&ndash;                            class="fsize-20"&ndash;&gt;-->
<!--            &lt;!&ndash;                          />{{ t('dislike') }}&ndash;&gt;-->
<!--            &lt;!&ndash;                        </div>&ndash;&gt;-->
<!--            &lt;!&ndash;                        <div&ndash;&gt;-->
<!--            &lt;!&ndash;                          class="flex-center-center flex-column fsize-12 rg-8"&ndash;&gt;-->
<!--            &lt;!&ndash;                          @click="copyText(item.content)"&ndash;&gt;-->
<!--            &lt;!&ndash;                        >&ndash;&gt;-->
<!--            &lt;!&ndash;                          <SvgIcon&ndash;&gt;-->
<!--            &lt;!&ndash;                            icon-class="copy-option"&ndash;&gt;-->
<!--            &lt;!&ndash;                            class="fsize-20"&ndash;&gt;-->
<!--            &lt;!&ndash;                          />{{ t('copy') }}&ndash;&gt;-->
<!--            &lt;!&ndash;                        </div>&ndash;&gt;-->
<!--            &lt;!&ndash;                      </div>&ndash;&gt;-->

<!--            &lt;!&ndash;                      <template #reference>&ndash;&gt;-->
<!--            &lt;!&ndash;                        <div class="no-selected">{{ replayItem }}</div>&ndash;&gt;-->
<!--            &lt;!&ndash;                      </template>&ndash;&gt;-->
<!--            &lt;!&ndash;                    </van-popover>&ndash;&gt;-->
<!--            &lt;!&ndash;                  </div>&ndash;&gt;-->
<!--            &lt;!&ndash;                </div>&ndash;&gt;-->
<!--            &lt;!&ndash;              </div>&ndash;&gt;-->
<!--            &lt;!&ndash;            </template>&ndash;&gt;-->
<!--          </div>-->
<!--          &lt;!&ndash;          暂不实现重说&ndash;&gt;-->
<!--          &lt;!&ndash;          <div&ndash;&gt;-->
<!--          &lt;!&ndash;            v-if="index === chatRecord.length - 1 && item.type === ChatRecordTypeEnum.AI"&ndash;&gt;-->
<!--          &lt;!&ndash;            class="down-option"&ndash;&gt;-->
<!--          &lt;!&ndash;          >&ndash;&gt;-->
<!--          &lt;!&ndash;            <div class="pardon">&ndash;&gt;-->
<!--          &lt;!&ndash;              <SvgIcon&ndash;&gt;-->
<!--          &lt;!&ndash;                icon-class="reload"&ndash;&gt;-->
<!--          &lt;!&ndash;                class="mr-4"&ndash;&gt;-->
<!--          &lt;!&ndash;              />Pardon&ndash;&gt;-->
<!--          &lt;!&ndash;            </div>&ndash;&gt;-->
<!--          &lt;!&ndash;            <div class="replay-change">&ndash;&gt;-->
<!--          &lt;!&ndash;              <div&ndash;&gt;-->
<!--          &lt;!&ndash;                v-if="currentReplayIndex !== 0"&ndash;&gt;-->
<!--          &lt;!&ndash;                class="arrow"&ndash;&gt;-->
<!--          &lt;!&ndash;                @click="replayScrollIntoView(currentReplayIndex - 1)"&ndash;&gt;-->
<!--          &lt;!&ndash;              >&ndash;&gt;-->
<!--          &lt;!&ndash;                <SvgIcon icon-class="left-arrow" />&ndash;&gt;-->
<!--          &lt;!&ndash;              </div>&ndash;&gt;-->

<!--          &lt;!&ndash;              <div>{{ currentReplayIndex + 1 }}/{{ item.replayContent.length + 1 }}</div>&ndash;&gt;-->
<!--          &lt;!&ndash;              <div&ndash;&gt;-->
<!--          &lt;!&ndash;                v-if="currentReplayIndex !== item.replayContent.length"&ndash;&gt;-->
<!--          &lt;!&ndash;                class="arrow"&ndash;&gt;-->
<!--          &lt;!&ndash;                @click="replayScrollIntoView(currentReplayIndex + 1)"&ndash;&gt;-->
<!--          &lt;!&ndash;              >&ndash;&gt;-->
<!--          &lt;!&ndash;                <SvgIcon icon-class="right-arrow" />&ndash;&gt;-->
<!--          &lt;!&ndash;              </div>&ndash;&gt;-->
<!--          &lt;!&ndash;            </div>&ndash;&gt;-->
<!--          &lt;!&ndash;          </div>&ndash;&gt;-->
<!--        </div>-->
<!--        <div-->
<!--          class="whole-chat-item"-->
<!--          v-if="loadingStatus.streaming"-->
<!--        >-->
<!--          <div :class="CHAT_BOX_STYLE_CLASS[ChatRecordTypeEnum.AI]">-->
<!--            <div class="chat-record">-->
<!--              <ChatLoading />-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
<!--        &lt;!&ndash;        <div&ndash;&gt;-->
<!--        &lt;!&ndash;          v-if="flagStatus.halfScreen"&ndash;&gt;-->
<!--        &lt;!&ndash;          class="half-screen"&ndash;&gt;-->
<!--        &lt;!&ndash;          :style="&ndash;&gt;-->
<!--        &lt;!&ndash;            isCollapse && {&ndash;&gt;-->
<!--        &lt;!&ndash;              height: 'calc((100% - 190px) / 2)'&ndash;&gt;-->
<!--        &lt;!&ndash;            }&ndash;&gt;-->
<!--        &lt;!&ndash;          "&ndash;&gt;-->
<!--        &lt;!&ndash;          @click.stop&ndash;&gt;-->
<!--        &lt;!&ndash;        ></div>&ndash;&gt;-->

<!--        &lt;!&ndash;                @click="halfScreenClick"&ndash;&gt;-->
<!--      </div>-->
<!--      <div class="accordion">-->
<!--        <div-->
<!--          class="scroll-top flex-center-center"-->
<!--          v-show="flagStatus.showScrollDown"-->
<!--          @click="scrollToBottom(chatRef, 'smooth', false)"-->
<!--        >-->
<!--          <SvgIcon-->
<!--            icon-class="scroll-arrow"-->
<!--            class="fsize-24"-->
<!--          />-->
<!--        </div>-->
<!--        <div class="undress-gift">-->
<!--          <div-->
<!--            v-if="agentMessage.camera_pic_status"-->
<!--            class="undress"-->
<!--            :style="!agentMessage.ai_chat_setting?.intimacy_interest_tags?.includes('get_a_selfie') && { opacity: 0.5 }"-->
<!--            @click="sendPhoto"-->
<!--          >-->
<!--            <SvgIcon-->
<!--              icon-class="naked"-->
<!--              class="fsize-16"-->
<!--            />-->
<!--            <div class="fsize-12">{{ t('getSelfie') }}</div>-->
<!--          </div>-->
<!--          <div-->
<!--            class="gift"-->
<!--            @click="giftVisible = true"-->
<!--          >-->
<!--            <img-->
<!--              src="@/assets/images/agentChat/gift-chat.webp"-->
<!--              alt=""-->
<!--            />-->
<!--          </div>-->
<!--          <div class="content-game-list">-->
<!--            <div-->
<!--              v-for="item in contentGameInPageList"-->
<!--              class="content-game"-->
<!--              :key="item.id"-->
<!--              @click="goContentGame(item)"-->
<!--            >-->
<!--              <SvgIcon-->
<!--                v-show="item.lock_status !== ContentGameStatusEnum.LOCKED"-->
<!--                icon-class="content-game-open"-->
<!--                class="fsize-14"-->
<!--              />-->
<!--              <SvgIcon-->
<!--                v-show="item.lock_status === ContentGameStatusEnum.LOCKED"-->
<!--                icon-class="content-game-lock"-->
<!--                class="fsize-14"-->
<!--              />-->
<!--              {{ item.short_name || item.name }}-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
<!--        <div-->
<!--          class="talk-mask"-->
<!--          v-if="loadingStatus.record"-->
<!--        >-->
<!--          <div-->
<!--            class="mask-text"-->
<!--            :style="isCancelDistance ? 'color: #F62A5A' : 'color: #fff'"-->
<!--          >-->
<!--            {{ isCancelDistance ? t('releaseToCancelSend') : t('releaseToSendSwipeUpCancel') }}-->
<!--          </div>-->
<!--        </div>-->
<!--        <div class="input-container">-->
<!--          <SvgIcon-->
<!--            @click="changeInputting"-->
<!--            :icon-class="flagStatus.inputting ? 'microphone' : 'keyboard'"-->
<!--            class="fsize-28"-->
<!--          />-->
<!--          <van-field-->
<!--            type="textarea"-->
<!--            v-show="flagStatus.inputting"-->
<!--            class="flex-1 input"-->
<!--            v-model="question"-->
<!--            @focus="closeCollapse"-->
<!--            :autosize="{-->
<!--              maxHeight: 100-->
<!--            }"-->
<!--            rows="1"-->
<!--            @keydown.enter.prevent="debounceSendMessage"-->
<!--          />-->
<!--          <div-->
<!--            v-show="!flagStatus.inputting"-->
<!--            ref="talkBtnRef"-->
<!--            :style="loadingStatus.record && (isCancelDistance ? 'background: #FF5B4D' : 'background: #EBDFAC')"-->
<!--            class="flex-1 talk-btn flex-center-center"-->
<!--          >-->
<!--            <div v-show="!loadingStatus.record">{{ t('holdToSpeak') }}</div>-->
<!--            <div-->
<!--              class="wave"-->
<!--              v-show="loadingStatus.record"-->
<!--            ></div>-->
<!--            <van-count-down-->
<!--              v-show="loadingStatus.record"-->
<!--              ref="countDownRef"-->
<!--              millisecond-->
<!--              :time="60000"-->
<!--              :auto-start="false"-->
<!--              @finish="countDownFinish"-->
<!--            >-->
<!--              <template #default="timeData">-->
<!--                <span-->
<!--                  class="block"-->
<!--                  :style="isCancelDistance ? 'color: #fff' : 'color: #2f2101'"-->
<!--                  >{{ timeData.seconds }}s-->
<!--                </span>-->
<!--              </template>-->
<!--            </van-count-down>-->
<!--          </div>-->
<!--          <SvgIcon-->
<!--            v-if="question"-->
<!--            icon-class="send"-->
<!--            class="fsize-36"-->
<!--            @click="handleCollapse(Boolean(question))"-->
<!--          />-->
<!--          <SvgIcon-->
<!--            v-else-->
<!--            :icon-class="isCollapse ? 'cross-button' : 'add-plus-button'"-->
<!--            class="fsize-28 switch-mode"-->
<!--            @click="handleCollapse(Boolean(question))"-->
<!--          />-->
<!--        </div>-->
<!--        <div-->
<!--          ref="collapseContentRef"-->
<!--          class="collapse-content"-->
<!--        >-->
<!--          <div-->
<!--            class="collapse-item"-->
<!--            :style="!agentMessage.ai_chat_setting?.intimacy_interest_tags?.includes('phone_call') && { opacity: 0.5 }"-->
<!--            @click="enterPhoneCall"-->
<!--          >-->
<!--            <SvgIcon-->
<!--              icon-class="phone"-->
<!--              class="fsize-32"-->
<!--            />-->
<!--            <div class="fsize-12">{{ t('makeCall') }}</div>-->
<!--          </div>-->
<!--          <div-->
<!--            class="collapse-item"-->
<!--            @click="giftVisible = true"-->
<!--          >-->
<!--            <SvgIcon-->
<!--              icon-class="gift"-->
<!--              class="fsize-32"-->
<!--            />-->
<!--            <div class="fsize-12">{{ t('giftItems') }}</div>-->
<!--          </div>-->
<!--          <div-->
<!--            class="collapse-item"-->
<!--            @click="restartChat"-->
<!--          >-->
<!--            <SvgIcon-->
<!--              icon-class="agent-restart"-->
<!--              class="fsize-32"-->
<!--            />-->
<!--            <div class="fsize-12">{{ t('startNewChat') }}</div>-->
<!--          </div>-->
<!--          &lt;!&ndash;          <div&ndash;&gt;-->
<!--          &lt;!&ndash;            class="collapse-item"&ndash;&gt;-->
<!--          &lt;!&ndash;            @click="goSelfImage"&ndash;&gt;-->
<!--          &lt;!&ndash;          >&ndash;&gt;-->
<!--          &lt;!&ndash;            <SvgIcon&ndash;&gt;-->
<!--          &lt;!&ndash;              icon-class="act"&ndash;&gt;-->
<!--          &lt;!&ndash;              class="fsize-32"&ndash;&gt;-->
<!--          &lt;!&ndash;            />&ndash;&gt;-->
<!--          &lt;!&ndash;            <div class="fsize-12">{{ t('userCharacterOption') }}</div>&ndash;&gt;-->
<!--          &lt;!&ndash;            <div&ndash;&gt;-->
<!--          &lt;!&ndash;              class="used-act"&ndash;&gt;-->
<!--          &lt;!&ndash;              v-if="agentMessage.ai_chat_setting.image_id"&ndash;&gt;-->
<!--          &lt;!&ndash;            >&ndash;&gt;-->
<!--          &lt;!&ndash;              {{ t('inUse') }}&ndash;&gt;-->
<!--          &lt;!&ndash;            </div>&ndash;&gt;-->
<!--          &lt;!&ndash;          </div>&ndash;&gt;-->
<!--          <div-->
<!--            class="collapse-item"-->
<!--            @click="skinVisible = true"-->
<!--          >-->
<!--            <SvgIcon-->
<!--              icon-class="skin"-->
<!--              class="fsize-32"-->
<!--            />-->
<!--            <div class="fsize-12">{{ t('outfits') }}</div>-->
<!--          </div>-->
<!--          <div-->
<!--            class="collapse-item"-->
<!--            @click="goStory"-->
<!--          >-->
<!--            <SvgIcon-->
<!--              icon-class="story"-->
<!--              class="fsize-32"-->
<!--            />-->
<!--            <div class="fsize-12">{{ t('story') }}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
<!--    <div-->
<!--      v-if="live2dStatus"-->
<!--      ref="live2dClear"-->
<!--      class="live2d-clear"-->
<!--    ></div>-->
<!--    <RecordPermissionDialog-->
<!--      v-model="permissionVisible"-->
<!--      @get-permission="phoneCallPermission"-->
<!--    />-->
<!--    <SendGiftPopup-->
<!--      v-model="giftVisible"-->
<!--      @trigger-gift-message="triggerGiftMessage"-->
<!--    />-->
<!--    <IntimacyAnimation-->
<!--      v-if="intimacyVisible"-->
<!--      v-model="intimacyVisible"-->
<!--    />-->
<!--    <SkinPopup-->
<!--      v-model="skinVisible"-->
<!--      @get-dress="getDress"-->
<!--      @switch="handleSkinSwitch"-->
<!--    />-->
<!--    <KeepAlive>-->
<!--      <Live2D-->
<!--        ref="l2dRef"-->
<!--        v-if="flagStatus.live2d"-->
<!--        :style="!live2dStatus && { opacity: 0 }"-->
<!--        :is-clear-mode="isClearMode"-->
<!--        :asset-url="live2dResource.model_json"-->
<!--        :change-url="live2dResource.change_model_json"-->
<!--        :action-list="live2dResource.action_list"-->
<!--        :back-pic="live2dResource.back_pic"-->
<!--        :params="{-->
<!--          scale: live2dResource.scale,-->
<!--          rotate: live2dResource.rotate / 360,-->
<!--          anchor: {-->
<!--            x: live2dResource.vertical,-->
<!--            y: live2dResource.horizontal-->
<!--          }-->
<!--        }"-->
<!--      />-->
<!--    </KeepAlive>-->
<!--    &lt;!&ndash;    <PhoneCall&ndash;&gt;-->
<!--    &lt;!&ndash;      :key="Date.now()"&ndash;&gt;-->
<!--    &lt;!&ndash;      v-model="flagStatus.phoneCall"&ndash;&gt;-->
<!--    &lt;!&ndash;      v-if="flagStatus.phoneCall"&ndash;&gt;-->
<!--    &lt;!&ndash;      :audio-context="runningContextAudioForPhone"&ndash;&gt;-->
<!--    &lt;!&ndash;      @get-record="permissionInit"&ndash;&gt;-->
<!--    &lt;!&ndash;    />&ndash;&gt;-->
<!--    <PhoneCall-->
<!--      v-if="flagStatus.phoneCall"-->
<!--      v-model="flagStatus.phoneCall"-->
<!--    />-->
<!--    <IntimacyLevelPopup-->
<!--      v-model="intimacyLevelVisible"-->
<!--      :current-level="agentMessage.ai_chat_setting.intimacy_level"-->
<!--      :current-value="agentMessage.ai_chat_setting.intimacy_now"-->
<!--      @send-gift="giftVisible = true"-->
<!--    />-->
<!--    <UnlockPhotoPopup-->
<!--      v-model="unlockVisible"-->
<!--      :photo-item="tempPhotoData"-->
<!--      @refresh="refreshPhoto"-->
<!--    />-->
<!--    <PhotoPreviewDialog-->
<!--      v-model="previewVisible"-->
<!--      :photo-item="tempPhotoData"-->
<!--    />-->
<!--    <CallTimeTopUpPopup v-model="topUpVisible" />-->
<!--    <PreviewLive2D-->
<!--      v-if="flagStatus.previewL2D"-->
<!--      v-model="flagStatus.previewL2D"-->
<!--      :skin-i-d="l2dSkinData.id"-->
<!--      @switch="l2dSwitch"-->
<!--    />-->
<!--  </div>-->
<!--</template>-->
<!--<style>-->
<!--p {-->
<!--  margin: 0;-->
<!--}-->

<!--em {-->
<!--  margin-right: 4px;-->
<!--  opacity: 0.5;-->
<!--}-->
<!--</style>-->
<!--<style scoped lang="scss">-->
<!--@import 'src/assets/styles/chatbox';-->

<!--@property &#45;&#45;chat-mask-start {-->
<!--  syntax: '<length-percentage>';-->
<!--  inherits: false;-->
<!--  initial-value: 50px;-->
<!--}-->

<!--@property &#45;&#45;chat-mask-end {-->
<!--  syntax: '<length-percentage>';-->
<!--  inherits: false;-->
<!--  initial-value: 75px;-->
<!--}-->

<!--.no-selected {-->
<!--  pointer-events: none;-->
<!--}-->

<!--.whole-chat-container {-->
<!--  display: flex;-->
<!--  width: 100%;-->
<!--  height: 100%;-->
<!--  overflow-x: auto;-->
<!--  overscroll-behavior: none;-->
<!--  background: url('/ugenie.png') center / 50% no-repeat;-->
<!--  scroll-snap-type: x mandatory;-->

<!--  &::-webkit-scrollbar {-->
<!--    display: none;-->
<!--  }-->

<!--  .top-bar {-->
<!--    position: fixed;-->
<!--    top: 0;-->
<!--    z-index: 3;-->
<!--    display: flex;-->
<!--    align-items: center;-->
<!--    justify-content: space-between;-->
<!--    width: 100%;-->
<!--    height: 60px;-->
<!--    padding: 6px 12px 14px 6px;-->
<!--    overscroll-behavior: none;-->
<!--    background: linear-gradient(180deg, rgba(24, 24, 24, 50%) 0%, rgba(24, 24, 24, 0%) 100%);-->

<!--    .right-btn {-->
<!--      display: flex;-->
<!--      column-gap: 10px;-->
<!--      align-items: center;-->
<!--      justify-content: center;-->
<!--    }-->

<!--    .switch-btn {-->
<!--      width: 38px;-->
<!--      height: 38px;-->
<!--      background: rgba(24, 24, 24, 60%);-->
<!--      backdrop-filter: blur(16px);-->
<!--      border-radius: 12px;-->
<!--    }-->

<!--    .head-message {-->
<!--      position: relative;-->
<!--      display: flex;-->
<!--      column-gap: 8px;-->
<!--      align-items: center;-->
<!--      justify-content: center;-->
<!--      background: rgba(24, 24, 24, 60%);-->
<!--      backdrop-filter: blur(16px);-->
<!--      border-radius: 28px;-->

<!--      .heart-down {-->
<!--        position: absolute;-->
<!--        bottom: 0;-->
<!--        left: 50%;-->
<!--        z-index: 4;-->
<!--        display: flex;-->
<!--        align-items: center;-->
<!--        justify-content: center;-->
<!--        //width: 36px;-->
<!--        //height: 56px;-->
<!--        width: 16px;-->
<!--        height: 16px;-->
<!--        font-size: 12px;-->
<!--        font-weight: bold;-->
<!--        transform: translate(-50%, 40%);-->

<!--        .num {-->
<!--          position: absolute;-->
<!--          bottom: 0;-->
<!--          z-index: 4;-->
<!--          font-size: 10px;-->
<!--          color: $livCoTextColor;-->
<!--          transform: translateY(-20%);-->
<!--        }-->
<!--      }-->

<!--      .avatar-container {-->
<!--        position: relative;-->
<!--        display: flex;-->
<!--        align-items: center;-->
<!--        justify-content: center;-->
<!--        width: 32px;-->
<!--        height: 32px;-->
<!--        margin: 3px;-->
<!--        background: conic-gradient(from -180deg, $livCoThemeColor var(&#45;&#45;percentage), rgba(235, 223, 172, 30%) var(&#45;&#45;percentage) 100%);-->
<!--        border-radius: 50%;-->

<!--        .avatar {-->
<!--          width: 28px;-->
<!--          height: 28px;-->
<!--          background: url('@/assets/images/normal-holder.png') no-repeat center / cover;-->
<!--          border-radius: 50%;-->
<!--        }-->
<!--      }-->

<!--      .name {-->
<!--        display: flex;-->
<!--        align-items: center;-->
<!--        margin-right: 8px;-->
<!--        font-family: IBMSerif, Roboto, serif;-->
<!--        font-size: 14px;-->
<!--      }-->
<!--    }-->

<!--    .exit-l2d {-->
<!--      padding: 10px 16px 10px 10px;-->
<!--      margin-left: 10px;-->
<!--      font-size: 14px;-->
<!--      background: rgba(24, 24, 24, 40%);-->
<!--      backdrop-filter: blur(4px);-->
<!--      border: 1px solid rgba(255, 255, 255, 20%);-->
<!--      border-radius: 24px;-->
<!--    }-->
<!--  }-->

<!--  .chat-page {-->
<!--    z-index: 2;-->
<!--    display: flex;-->
<!--    flex-direction: column;-->
<!--    flex-shrink: 0;-->
<!--    align-items: flex-end;-->
<!--    width: 100%;-->
<!--    height: 100%;-->
<!--    overscroll-behavior: none;-->
<!--  }-->

<!--  .chat {-->
<!--    position: relative;-->
<!--    z-index: 1;-->
<!--    display: flex;-->
<!--    flex: 1;-->
<!--    flex-direction: column;-->
<!--    row-gap: 11px;-->
<!--    width: 100%;-->
<!--    height: 100%;-->
<!--    padding: 64px 12px 20px;-->
<!--    overflow: auto;-->
<!--    transition:-->
<!--      &#45;&#45;chat-mask-start 0.2s linear,-->
<!--      &#45;&#45;chat-mask-end 0.2s linear;-->
<!--    scroll-snap-align: start;-->
<!--    mask-image: linear-gradient(-->
<!--      to bottom,-->
<!--      rgba(0, 0, 0, 0%) var(&#45;&#45;chat-mask-start),-->
<!--      rgba(0, 0, 0, 100%) var(&#45;&#45;chat-mask-end),-->
<!--      rgba(0, 0, 0, 100%) calc(100% - 20px),-->
<!--      rgba(0, 0, 0, 0%) 100%-->
<!--    );-->

<!--    .half-screen {-->
<!--      position: fixed;-->
<!--      top: 0;-->
<!--      left: 0;-->
<!--      width: 100%;-->
<!--      height: 50%;-->
<!--      background: transparent;-->
<!--    }-->

<!--    :deep(.van-popover__wrapper) {-->
<!--      display: block;-->
<!--    }-->

<!--    .whole-chat-item {-->
<!--      .down-option {-->
<!--        display: flex;-->
<!--        align-items: center;-->
<!--        justify-content: space-between;-->
<!--        margin-top: 8px;-->

<!--        .pardon {-->
<!--          padding: 6px 12px;-->
<!--          background: rgba(24, 24, 24, 60%);-->
<!--          border-radius: 12px;-->
<!--        }-->

<!--        .replay-change {-->
<!--          display: flex;-->
<!--          column-gap: 12px;-->
<!--          align-items: center;-->

<!--          .arrow {-->
<!--            padding: 6px 8px;-->
<!--            background: rgba(24, 24, 24, 60%);-->
<!--            border-radius: 12px;-->
<!--          }-->
<!--        }-->
<!--      }-->
<!--    }-->

<!--    .chat-item-container {-->
<!--      .sound {-->
<!--        position: absolute;-->
<!--        top: 0;-->
<!--        left: 4px;-->
<!--        z-index: 1;-->
<!--        display: flex;-->
<!--        column-gap: 2px;-->
<!--        align-items: center;-->
<!--        justify-content: center;-->
<!--        height: 22px;-->
<!--        padding: 4px 8px;-->
<!--        background: #b8aa6e;-->
<!--        border-radius: 16px 16px 16px 8px;-->

<!--        :deep(.loading-bar) {-->
<!--          background: #fff;-->
<!--        }-->

<!--        .text {-->
<!--          font-size: 12px;-->
<!--          color: #fff;-->
<!--        }-->

<!--        .sound-loading {-->
<!--          width: 14px;-->
<!--          height: 14px;-->
<!--          color: #fff;-->
<!--          animation: loading 1s linear infinite;-->
<!--        }-->

<!--        @keyframes loading {-->
<!--          0% {-->
<!--            transform: rotate(0deg);-->
<!--          }-->

<!--          100% {-->
<!--            transform: rotate(360deg);-->
<!--          }-->
<!--        }-->
<!--      }-->
<!--    }-->
<!--  }-->

<!--  .undress-gift {-->
<!--    z-index: 3;-->
<!--    display: flex;-->
<!--    column-gap: 8px;-->
<!--    align-items: center;-->
<!--    width: 100%;-->
<!--    padding: 0 0 8px;-->

<!--    .undress {-->
<!--      display: flex;-->
<!--      column-gap: 8px;-->
<!--      align-items: center;-->
<!--      justify-content: center;-->
<!--      padding: 7px 12px;-->
<!--      font-weight: 400;-->
<!--      background: transparent;-->
<!--      border: 1px solid rgba(255, 255, 255, 20%);-->
<!--      border-radius: 12px;-->
<!--    }-->

<!--    .gift {-->
<!--      display: flex;-->
<!--      column-gap: 8px;-->
<!--      align-items: center;-->
<!--      justify-content: center;-->
<!--      padding: 3px;-->
<!--      background: transparent;-->
<!--      border: 1px solid rgba(255, 255, 255, 20%);-->
<!--      border-radius: 12px;-->

<!--      img {-->
<!--        width: 24px;-->
<!--        height: 24px;-->
<!--      }-->
<!--    }-->

<!--    .content-game-list {-->
<!--      display: flex;-->
<!--      flex: 1;-->
<!--      column-gap: 8px;-->
<!--      height: fit-content;-->
<!--      padding: 0;-->
<!--      overflow: auto;-->

<!--      &::-webkit-scrollbar {-->
<!--        display: none;-->
<!--      }-->

<!--      .content-game {-->
<!--        display: flex;-->
<!--        flex-shrink: 0;-->
<!--        column-gap: 2px;-->
<!--        align-items: center;-->
<!--        justify-content: center;-->
<!--        padding: 8px;-->
<!--        font-size: 12px;-->
<!--        font-weight: 400;-->
<!--        line-height: 12px;-->
<!--        color: #fff;-->
<!--        text-align: left;-->
<!--        background: #ffffff14;-->
<!--        border-radius: 11px;-->
<!--      }-->
<!--    }-->
<!--  }-->

<!--  .accordion {-->
<!--    position: relative;-->
<!--    z-index: 4;-->
<!--    width: 100%;-->
<!--    padding: 8px 0 24px 12px;-->
<!--    background: rgba(24, 24, 24, 40%);-->
<!--    backdrop-filter: blur(32px);-->
<!--    border-radius: 8px 8px 0 0;-->

<!--    .scroll-top {-->
<!--      position: absolute;-->
<!--      top: -58px;-->
<!--      right: 12px;-->
<!--      width: 42px;-->
<!--      height: 42px;-->
<!--      background: rgba(62, 62, 62, 70%);-->
<!--      backdrop-filter: blur(64px);-->
<!--      border: 1px solid rgba(255, 255, 255, 10%);-->
<!--      border-radius: 52px;-->
<!--    }-->

<!--    .talk-mask {-->
<!--      position: absolute;-->
<!--      bottom: 0;-->
<!--      left: 0;-->
<!--      width: 100%;-->
<!--      height: 208px;-->
<!--      pointer-events: none;-->
<!--      background: linear-gradient(360deg, #000 0%, #000 55%, rgba(0, 0, 0, 0%) 100%);-->

<!--      .mask-text {-->
<!--        position: absolute;-->
<!--        bottom: 84px;-->
<!--        display: flex;-->
<!--        align-items: center;-->
<!--        justify-content: center;-->
<!--        width: 100%;-->
<!--        font-size: 12px;-->
<!--      }-->
<!--    }-->

<!--    .input-container {-->
<!--      display: flex;-->
<!--      column-gap: 12px;-->
<!--      align-items: center;-->
<!--      width: 100%;-->
<!--      padding-right: 12px;-->

<!--      :deep(.van-field) {-->
<!--        min-height: 42px;-->
<!--        padding-top: 4px;-->
<!--        padding-bottom: 4px;-->

<!--        &::after {-->
<!--          display: none;-->
<!--        }-->
<!--      }-->
<!--    }-->

<!--    .input {-->
<!--      background: rgba(255, 255, 255, 15%);-->
<!--      border: 1px solid rgba(223, 219, 255, 20%);-->
<!--      border-radius: 16px;-->
<!--    }-->

<!--    .talk-btn {-->
<!--      z-index: 3;-->
<!--      display: flex;-->
<!--      align-items: center;-->
<!--      justify-content: center;-->
<!--      height: 42px;-->
<!--      padding: 0 12px;-->
<!--      user-select: none;-->
<!--      background: rgba(133, 120, 137, 50%);-->
<!--      border: 1px solid rgba(223, 219, 255, 20%);-->
<!--      border-radius: 16px;-->

<!--      .wave {-->
<!--        flex: 1;-->
<!--        height: 100%;-->
<!--      }-->
<!--    }-->

<!--    .collapse-content {-->
<!--      display: grid;-->
<!--      grid-template-columns: repeat(4, 1fr);-->
<!--      row-gap: 18px;-->
<!--      width: 100%;-->
<!--      max-height: 0;-->
<!--      padding-right: 12px;-->
<!--      overflow: hidden;-->
<!--      transition: max-height 0.2s ease-out;-->

<!--      .collapse-item {-->
<!--        position: relative;-->
<!--        display: flex;-->
<!--        flex-direction: column;-->
<!--        row-gap: 12px;-->
<!--        align-items: center;-->
<!--        justify-content: flex-start;-->
<!--        min-height: 78px;-->
<!--        padding: 8px;-->
<!--        overflow: hidden;-->

<!--        svg {-->
<!--          flex-shrink: 0;-->
<!--        }-->

<!--        div {-->
<!--          flex-shrink: 0;-->
<!--          text-align: center;-->
<!--        }-->

<!--        .used-act {-->
<!--          position: absolute;-->
<!--          top: 0;-->
<!--          left: 0;-->
<!--          padding: 4px 6px;-->
<!--          font-size: 8px;-->
<!--          background: linear-gradient(140deg, #ff35f2 0%, #98f 100%);-->
<!--          border-radius: 12px 12px 12px 4px;-->
<!--        }-->
<!--      }-->
<!--    }-->
<!--  }-->

<!--  .content-game-btn {-->
<!--    z-index: 3;-->
<!--    width: 100%;-->
<!--    padding: 12px 16px;-->
<!--    background: rgba(24, 24, 24, 40%);-->
<!--    backdrop-filter: blur(88px);-->
<!--    border-radius: 12px 12px 0 0;-->

<!--    .go {-->
<!--      display: flex;-->
<!--      column-gap: 4px;-->
<!--      align-items: center;-->
<!--      justify-content: center;-->
<!--      padding: 16px 0;-->
<!--      margin-bottom: 12px;-->
<!--      font-size: 14px;-->
<!--      font-weight: 600;-->
<!--      line-height: 16px;-->
<!--      color: #fff;-->
<!--      text-align: center;-->
<!--      background: rgba(255, 255, 255, 15%);-->
<!--      border-radius: 16px;-->
<!--    }-->

<!--    .later {-->
<!--      padding: 16px 0;-->
<!--      font-size: 14px;-->
<!--      font-weight: 600;-->
<!--      line-height: 16px;-->
<!--      color: #fff;-->
<!--      text-align: center;-->
<!--      background: rgba(255, 255, 255, 15%);-->
<!--      border-radius: 16px;-->
<!--    }-->
<!--  }-->

<!--  .live2d-clear {-->
<!--    scroll-snap-align: start;-->
<!--    z-index: 2;-->
<!--    display: flex;-->
<!--    flex-shrink: 0;-->
<!--    align-items: flex-end;-->
<!--    width: 100%;-->
<!--    height: 100%;-->
<!--    pointer-events: none;-->
<!--  }-->
<!--}-->
<!--</style>-->
