<script setup lang="ts">
import { getIntimacyStrategy } from '@/api/agentChat'
import { ResultEnum } from '@/enums/httpEnum.ts'

const mdContent = ref('')

const getStrategy = () => {
  getIntimacyStrategy().then((res) => {
    const { data, code } = res
    if (code === ResultEnum.SUCCESS) {
      console.log(data)
      mdContent.value = data
    }
  })
}

getStrategy()
</script>

<template>
  <div class="h-full w-full bg">
    <NavBar
      lang-title="Intimacy upgrade strategy"
      class="bar"
    />
    <div
      v-html="mdContent"
      class="content intimacy-md"
    ></div>
  </div>
</template>
<style lang="scss">
.intimacy-md {
  img {
    width: 100%;
  }

  p {
    color: rgba(255, 255, 255, 70%);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 24px 0;
  }
}
</style>
<style scoped lang="scss">
.bg {
  background: url('@/assets/images/agentChat/intimacy-strategy-bg.png') no-repeat top / contain;
}

.bar {
  background: transparent;
}

.content {
  width: 100%;
  height: calc(100% - var(--van-nav-bar-height));
  padding: calc(var(--van-nav-bar-height) + 16px) 24px 16px;
  overflow: auto;
}
</style>
