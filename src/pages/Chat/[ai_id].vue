<script setup lang="ts">
import { toRef } from 'vue'
import Recorder from 'recorder-core/recorder.mp3.min.js'
import 'recorder-core/src/extensions/buffer_stream.player.js'
import anime from 'animejs'
import { cloneDeep, debounce, isEmpty } from 'lodash-es'
import { useModal } from '@/hooks/useModal.ts'
import { blockAgentAudio, getDressByID, getThreeDSource, startNewChat } from '@/api/agentChat'
import { ResultEnum } from '@/enums/httpEnum.ts'
import useUserStore from '@/stores/modules/user.ts'
import useNotifyStore from '@/stores/modules/notification.ts'
import { BOOL_NUMBER } from '@/constant'
import { playChatAudio, scrollToBottom } from '@/utils'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { getAIInfo } from '@/api/agentHomePage'
import { IGiftListType, ThreeDSourceType } from '@/api/agentChat/types.ts'
import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'
import { useBackHistory } from '@/hooks/useCommon.ts'
import { eventReport, EventTypeEnum, ForwardAddressEnum, InteractiveTypeEnum } from '@/api/eventReport'
import { RecordEnum } from '@/enums'
import { POPUP_TYPE_NAME, usePopupMemoryStore } from '@/stores/modules/popupMemory.ts'
import { useConfirm } from '@/hooks/useConfirm.ts'
import { ugPhoneLinkSdk } from '@/utils/ugPhoneLinkSdk.ts'
import useWs from '@/hooks/useWs.ts'
import { chat, getAdvInfo, getChatRecord, getCrystal, getCrystalCode, getVideoMapByName, playGetCrystal, searchTool } from '@/api/chat'
import {
  BtnContentType,
  ButtonType,
  ChapterObj,
  GlobalScoreObj,
  IChatRecordType,
  IChatResponseType,
  IDialogueRecord,
  QueryTypeEnum,
  RecordType,
  ToolContentType
} from '@/api/chat/types.ts'
import Tips from '@/components/Chat/Tips.vue'
import UserComment from '@/components/Chat/UserComment.vue'
import AIComment from '@/components/Chat/AIComment.vue'
import PictureMarkdown from '@/components/Chat/PictureMarkdown.vue'
import PictureRequest from '@/components/Chat/PictureRequest.vue'
import PictureAds from '@/components/Chat/PictureAds.vue'
import VideoAds from '@/components/Chat/VideoAds.vue'
import Narration from '@/components/Chat/Narration.vue'
import VoiceOver from '@/components/Chat/VoiceOver.vue'
import GameInvitation from '@/components/Chat/GameInvitation.vue'
import GameResult from '@/components/Chat/GameResult.vue'
import Phone from '@/components/Chat/Phone.vue'
import StoryImage from '@/components/Chat/StoryImage.vue'
import GiftTips from '@/components/Chat/GiftTips.vue'
import Synopsis from '@/components/Chat/Synopsis.vue'
import WarnTips from '@/components/Chat/WarnTips.vue'
import BgmSwitchBtn from '@/components/Chat/BgmSwitchBtn.vue'
import { useAudioSequence } from '@/hooks/useAudioSequence.ts'
import { ContentGameStatusEnum, IContentListRes, IContentRes } from '@/api/contentGame/types.ts'
import { getAgentContentGameList, getAgentContentGameOfMyList, getSingleAgentContentGameInChatPage } from '@/api/contentGame'
import GameRewardTips from '@/components/Chat/GameRewardTips.vue'
import { useAdStore } from '@/stores/modules/ad.ts'
import useAppStore from '@/stores/modules/app.ts'
import { cancelAllRequests } from '@/utils/request.ts'
import { useVideoPreload, VideoType } from '@/hooks/useVideoPreload.ts'
import * as gsap from 'gsap'
import ContentGamePopup from '@/components/Chat/ContentGamePopup.vue'

const { playUrlSequence, stop } = useAudioSequence()

const popupMemoryStore = usePopupMemoryStore()
const adStore = useAdStore()
const appStore = useAppStore()
const { t } = useI18n()
const bgmSwitchRef = ref<InstanceType<typeof BgmSwitchBtn>>(null)
const live2dClear = ref<HTMLElement | null>(null)
const chatPageRef = ref<HTMLElement | null>(null)
const l2dRef = ref(null)
const isClearMode = ref(false)
const isFirstInNoAction = ref(true)
const isNoRecord = ref(false)
const resultReward = ref(0)
const conversationID = ref('')

const runningContextAudioForPhone = ref()
const userStore = useUserStore()
const notifyStore = useNotifyStore()
const route = useRoute()
const CANCEL_DISTANCE = 60
const router = useRouter()
// 是否取消发送语音
const isCancelDistance = computed(() => {
  // 两个值有一个为0应该返回false
  return Math.abs(slidePoint.start - slidePoint.end) > CANCEL_DISTANCE && slidePoint.start !== 0 && slidePoint.end !== 0
})

const ComponentReflect: Partial<Record<RecordEnum, Component>> = {
  [RecordEnum.USER_COMMENT]: UserComment,
  [RecordEnum.AI_COMMENT]: AIComment,
  [RecordEnum.PICTURE_MARKDOWN]: PictureMarkdown,
  [RecordEnum.PICTURE_REQUEST]: PictureRequest,
  [RecordEnum.PICTURE_ADS]: PictureAds,
  [RecordEnum.VIDEO_ADS]: VideoAds,
  [RecordEnum.NARRATION]: Narration,
  [RecordEnum.VOICEOVER]: VoiceOver,
  [RecordEnum.GAME_INVITATION]: GameInvitation,
  [RecordEnum.TIPS]: Tips,
  [RecordEnum.WARN]: WarnTips,
  [RecordEnum.GAME_RESULT]: GameResult,
  [RecordEnum.SYNOPSIS]: Synopsis,
  [RecordEnum.PHONE]: Phone,
  [RecordEnum.STORY_IMAGE]: StoryImage,
  [RecordEnum.GIFT]: GiftTips,
  [RecordEnum.GAME_REWARD_TIPS]: GameRewardTips
}

// @ts-ignore
const aiID = ref(route.params.ai_id as number)

const agentMessage = ref<AgentMessageType>({
  tag_list: [],
  like_status: 0,
  resident_function: [],
  ai_chat_setting: {
    image_id: undefined,
    show_disclaimer: 0,
    show_character: 0,
    show_prologue: 0,
    show_muse: 0,
    intimacy_now: 0,
    intimacy_upgrade: 0,
    intimacy_level: 0,
    intimacy_interest_tags: []
  },
  opening_option: [],
  id: undefined,
  name: undefined,
  follow: '0',
  has_3d: undefined,
  is_repeat: undefined,
  chat_times: '0',
  image_url: undefined,
  camera_pic_status: undefined,
  init_plot: undefined,
  bgm: '',
  bg_info: {
    bg_url: undefined,
    set_id: undefined,
    dress_id: undefined,
    type: undefined
  },
  default_live2d: {
    bg_url: undefined,
    set_id: undefined,
    dress_id: undefined,
    type: undefined
  },
  default_background: 1,
  default_video_map: {
    name: '',
    url_prefix: '',
    standby_url: '',
    transition_url: '',
    action_urls: []
  },
  update_time: undefined,
  sound_ray_url: undefined,
  opening_statement_voice: undefined,
  intimacy_level_info: [],
  description: undefined,
  api_key: undefined,
  dataset_id: undefined,
  app_id: undefined,
  sex: undefined,
  role_setting: undefined,
  opening_statement: undefined,
  back_story: undefined,
  talk_example: undefined,
  synopsis: undefined,
  voice_id: undefined,
  is_show: 0,
  is_quick: 0,
  last_set_id: undefined,
  status: 0,
  avatar_url: undefined,
  client_image_id: undefined,
  terminal: undefined,
  client_info: {
    client_id: undefined,
    name: undefined,
    email: undefined,
    avatar_url: undefined
  },
  admin_info: {
    admin_id: undefined,
    email: undefined,
    name: undefined,
    avatar_url: undefined
  },
  resident_type: 3
})

const clickTime = reactive({
  start: 0,
  end: 0
})
const loadingStatus = reactive({
  attachLongBtn: false, // 下拉框按钮loading
  tts: false, // 语音转文字
  chat: false, // 聊天请求中
  textOutputting: false, // 文字分段输出中
  record: false // 录音
})

const feedbackVisible = ref(false)

const btnShowing = reactive({
  attachShort: false,
  globalShort: false,
  globalProgress: false,
  chapterAnimate: false
})

const expandOptionsStatus = reactive({
  agentOptions: false,
  attachLong: false
})

const chapterContent = ref<ChapterObj>({
  content: '',
  tips: ''
})
const progressOptions = ref<GlobalScoreObj[]>([])

const normalInput = computed(() => {
  return !btnShowing.globalShort
})

const attachBtnList = ref<BtnContentType[]>([])
const blockBtnList = ref<BtnContentType[]>([])
const noBlockBtnList = ref<BtnContentType[]>([])

const flagStatus = reactive({
  // 半屏状态
  halfScreen: false,
  // 是否出现向下滚动
  showScrollDown: false,
  // 打电话
  phoneCall: false,
  // live2d显示
  live2d: false,
  // 麦克风授权状态
  micAllow: false,
  // 键盘输入or语音输入
  inputting: true,
  // 输入锁定
  lock: false,
  // 自动弹出选项
  autoExpandLongOption: true,
  // 加载完图片或l2d再显示聊天界面
  showInterface: false
})
const hadStoryBook = ref(false)
const showPopover = ref<Record<string, boolean>>({})
const question = ref('')
const contentGameInPageList = ref<IContentListRes>([])
const contentGameList = ref<IContentListRes>([])
const toolsContent = ref<ToolContentType[]>([])

const slidePoint = reactive({
  start: 0,
  end: 0
})

// 横向滑动相关状态
const horizontalSwipe = reactive({
  startX: 0,
  startY: 0,
  currentX: 0,
  currentY: 0,
  isDragging: false,
  translateX: 0,
  threshold: 150, // 滑动阈值，滑动超过100px才移出界面
  isTransitioning: false,
  targetOpacity: 1, // 目标透明度，用于过渡期间
  live2dOpacity: 1 // live2d透明度
})

// 计算聊天页面的位置
const chatPageTransform = computed(() => {
  if (horizontalSwipe.isDragging || horizontalSwipe.isTransitioning) {
    // 拖拽或过渡期间，使用实时的translateX值
    return `translateX(${horizontalSwipe.translateX}%)`
  } else if (isClearMode.value) {
    // 在清屏模式下且不在拖拽时，聊天页面完全移出屏幕（向左移动）
    return `translateX(-100%)`
  } else {
    // 正常模式下，默认位置
    return `translateX(0%)`
  }
})

// 计算透明度
const chatPageOpacity = computed(() => {
  if (!flagStatus.showInterface) return 0
  if (horizontalSwipe.isDragging) {
    return 1
  } else if (horizontalSwipe.isTransitioning) {
    // 过渡期间，使用预设的目标透明度
    return horizontalSwipe.targetOpacity
  } else if (isClearMode.value) {
    return 0 // 清屏模式下完全透明
  } else {
    return 1 // 正常模式下完全不透明
  }
})

// 计算整体样式（包含transform和opacity）
const chatElementStyle: Ref<Record<string, string | number>> = computed(() => ({
  transform: chatPageTransform.value,
  opacity: chatPageOpacity.value,
  transition: horizontalSwipe.isDragging ? 'none' : 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.3s ease',
  // 添加硬件加速和优化属性
  willChange: 'auto',
  backfaceVisibility: 'hidden',
  perspective: '1000px'
}))

const guideForClearModeVisible = ref(false)
// const guideForTouchVisible = ref(false)
const topUpVisible = ref(false)
const giftVisible = ref(false)
const intimacyVisible = ref(false)
const intimacyLevelVisible = ref(false)
const permissionVisible = ref(false)
const skinVisible = ref(false)
const contentGameVisible = ref(false)

const myRecorder = ref(null)
const myStream = ref(null)
const heartRef = ref(null)
const chatRef = ref(null)
const talkBtnRef = ref(null)
const collapseContentRef = ref(null)
const replayItemRef = ref(null)
const countDownRef = ref(null) // 倒计时ref

const l2dEmo = ref('')
const chatRecord = ref<RecordType[]>([])
const skinList = ref<DressType[]>([])

const live2dResource = ref<ThreeDSourceType>({
  model_json: undefined,
  action_list: [],
  rotate: 0,
  parameters: {}
})

const hadIntimacySystem = computed(() => {
  return agentMessage.value.resident_function.includes('intimacy')
})

const intimacyPercent = computed(() => {
  if (!hadIntimacySystem.value) return '100%'
  const prevIndex = agentMessage.value.intimacy_level_info.findIndex((item) => item.level === agentMessage.value.ai_chat_setting.intimacy_level)
  if (
    prevIndex === agentMessage.value.intimacy_level_info.length - 1 &&
    agentMessage.value.ai_chat_setting.intimacy_upgrade - agentMessage.value.intimacy_level_info[prevIndex]?.experience === 0
  ) {
    return '100%'
  }
  if (prevIndex !== -1) {
    return (
      ((agentMessage.value.ai_chat_setting.intimacy_now - agentMessage.value.intimacy_level_info[prevIndex].experience) /
        (agentMessage.value.ai_chat_setting.intimacy_upgrade - agentMessage.value.intimacy_level_info[prevIndex].experience)) *
        100 +
      '%'
    )
  } else return (agentMessage.value.ai_chat_setting.intimacy_now / agentMessage.value.ai_chat_setting.intimacy_upgrade) * 100 + '%'
})

const handleIntimacy = (level: number, value: number, upgrade: number, intimacy_interest_tags: string[]) => {
  const currentLevel = agentMessage.value.ai_chat_setting.intimacy_level
  const currentValue = agentMessage.value.ai_chat_setting.intimacy_now
  agentMessage.value.ai_chat_setting.intimacy_now = value
  agentMessage.value.ai_chat_setting.intimacy_level = level
  agentMessage.value.ai_chat_setting.intimacy_upgrade = upgrade
  agentMessage.value.ai_chat_setting.intimacy_interest_tags = intimacy_interest_tags
  if (value > currentValue) {
    // animationInstance.value.play()
  }
  if (level > currentLevel) {
    intimacyVisible.value = true
    return level
  }
  return undefined
}

const { videoElements, preloadVideos, emotionCdController } = useVideoPreload()
const videoContainerRef = ref<HTMLElement | null>(null)

const nowEnv = ref('')
const nowVideoType = ref('normal')

const environmentChange = async (env: string) => {
  const { code, data } = await getVideoMapByName({
    name: env,
    ai_id: aiID.value
  })
  console.log(code, data)
  if (code !== 200 || !data) return
  preloadVideos(
    [
      {
        type: VideoType.STANDBY,
        url: data.url_prefix + data.standby_url
      },
      {
        type: VideoType.TRANSITION,
        url: data.url_prefix + data.transition_url
      },
      ...data.action_urls.map((item) => ({
        type: item.emotion,
        url: data.url_prefix + item.url
      }))
    ],
    data.name
  )
  gsap.gsap.to(videoElements.value[nowEnv.value][nowVideoType.value], {
    opacity: 0,
    duration: 0.5,
    ease: 'linear'
  })
  videoElements.value[nowEnv.value][nowVideoType.value].pause()
  nowVideoType.value = VideoType.TRANSITION
  videoElements.value[nowEnv.value][VideoType.TRANSITION].currentTime = 0
  videoElements.value[nowEnv.value][VideoType.TRANSITION].play()
  gsap.gsap.to(videoElements.value[nowEnv.value][VideoType.TRANSITION], {
    opacity: 1,
    duration: 0.5,
    ease: 'linear'
  })
  videoElements.value[nowEnv.value][VideoType.TRANSITION].addEventListener('ended', () => {
    gsap.gsap.to(videoElements.value[nowEnv.value][VideoType.TRANSITION], {
      opacity: 0,
      duration: 0.5,
      ease: 'linear',
      onComplete: () => {
        const lastEnv = nowEnv.value
        nowEnv.value = env
        data.action_urls.forEach((item) => {
          videoContainerRef.value.append(videoElements.value[data.name][item.emotion])
        })
        videoContainerRef.value.append(videoElements.value[data.name].transition)
        videoContainerRef.value.append(videoElements.value[data.name].standby)
        videoElements.value[data.name].standby.style.opacity = '1'
        videoElements.value[data.name].standby.play()
        nowVideoType.value = VideoType.STANDBY
        gsap.gsap.to(videoElements.value[nowEnv.value][VideoType.STANDBY], {
          opacity: 1,
          duration: 0.5,
          ease: 'linear',
          onComplete: () => {
            Object.keys(videoElements.value[lastEnv]).forEach((key) => {
              videoElements.value[lastEnv][key].remove()
            })
          }
        })
      }
    })
  })
}

const emotionChange = (emotion: string) => {
  console.log(emotion, nowVideoType.value)
  if (nowVideoType.value === VideoType.TRANSITION) return
  gsap.gsap.to(videoElements.value[nowEnv.value][nowVideoType.value], {
    opacity: 0,
    duration: 0.5,
    ease: 'linear',
    onComplete: () => {
      videoElements.value[nowEnv.value][nowVideoType.value].pause()
      nowVideoType.value = emotion
    }
  })
  gsap.gsap.to(videoElements.value[nowEnv.value][emotion], {
    opacity: 1,
    duration: 0.5,
    ease: 'linear'
  })
  videoElements.value[nowEnv.value][emotion].currentTime = 0
  videoElements.value[nowEnv.value][emotion].play()
  videoElements.value[nowEnv.value][emotion].addEventListener('ended', () => {
    gsap.gsap.to(videoElements.value[nowEnv.value][emotion], {
      opacity: 0,
      duration: 0.5,
      ease: 'linear',
      onComplete: () => {
        // videoElements.value[nowEnv.value][emotion].removeEventListener('ended')
      }
    })
    videoElements.value[nowEnv.value].standby.currentTime = 0
    videoElements.value[nowEnv.value].standby.play()
    gsap.gsap.to(videoElements.value[nowEnv.value][VideoType.STANDBY], {
      opacity: 1,
      duration: 0.5,
      ease: 'linear'
    })
    nowVideoType.value = VideoType.STANDBY
  })
}

const handleBackSvg = () => {
  return !userStore.isLogin && (localStorage.getItem('testCode') === 'B' || localStorage.getItem('testCode') === 'C')
}

// 录音初始化
const recOpenInit = (success: typeof recStart) => {
  if (!myRecorder.value) {
    myRecorder.value = new Recorder({
      type: 'mp3',
      sampleRate: 16000,
      bitRate: 16
    })
  }
  myRecorder.value.open(
    function () {
      flagStatus.micAllow = true
      //打开麦克风授权获得相关资源
      success && success()
    },
    function (msg: string, isUserNotAllow: boolean) {
      //用户拒绝未授权或不支持
      if (isUserNotAllow) {
        useModal({
          message: t('recordingPermissionError'),
          duration: 1000
        })
      } else {
        console.error(msg)
      }
    }
  )
}

// 录音开始
const recStart = () => {
  bgmSwitchRef.value.control(false, true) // 停止BGM
  loadingStatus.record = true
  nextTick(() => {
    myRecorder.value.start()
    countDownRef.value.start()
  })
}

// 录音结束
const recStop = (isCancel: boolean) => {
  bgmSwitchRef.value.control() // 恢复BGM
  myRecorder.value.stop(
    function (blob: Blob, duration: number) {
      loadingStatus.record = false
      console.log(blob, '时长:' + duration + 'ms')
      myRecorder.value.close() //释放录音资源，当然可以不释放，后面可以连续调用start；但不释放时系统或浏览器会一直提示在录音，最佳操作是录完就close掉
      myRecorder.value = null
      if (duration < 500) {
        // useModal({
        //   message: t('speechTooShort')
        // })
        return
      }
      if (!isCancel) {
        let timestamp = Date.now()
        //已经拿到blob文件对象想干嘛就干嘛：立即播放、上传、下载保存
        const file = new File([blob], 'record.mp3')
        const formData = new FormData()
        startChatReport(InteractiveTypeEnum.CHAT)
        formData.append('audio_file', file)
        formData.append('ai_id', String(aiID.value))
        formData.append('interactive_model', String(flagStatus.live2d ? 2 : 1))
        // loadingStatus.streaming = true
        const userObj = reactive({
          type: RecordEnum.USER_COMMENT,
          timestamp,
          content: '',
          is_like: BOOL_NUMBER.NO,
          is_bad: BOOL_NUMBER.NO,
          is_phone_call: BOOL_NUMBER.NO,
          message_id: '',
          userTTS: false
        })
        chatRecord.value.push(userObj)
        const userIndex = chatRecord.value.findIndex((item) => item === userObj)
        loadingStatus.tts = true
        scrollToBottom(chatRef.value)
        userObj.userTTS = true
        blockAgentAudio(formData)
          .then(({ code, data }) => {
            if (code === ResultEnum.SUCCESS) {
              userObj.content = data
              userObj.userTTS = false
              scrollToBottom(chatRef.value)
              blockSendQuestion({ text: data, responseWay: '语音输入' })
            }
          })
          .catch((err) => {
            chatRecord.value.splice(userIndex, 1)
            console.log(err)
          })
          .finally(() => {
            loadingStatus.tts = false
          })
      }
    },
    function (msg: string) {
      console.log('录音失败:' + msg)
      myRecorder.value.close() //可以通过stop方法的第3个参数来自动调用close
      myRecorder.value = null
    }
  )
}

// 进入打电话
const enterPhoneCall = () => {
  startChatReport(InteractiveTypeEnum.CALL)
  if (!agentMessage.value.ai_chat_setting.intimacy_interest_tags.includes('phone_call')) {
    useModal({
      message: t('intimacyUnlockFunc')
    })
    return
  }
  if (userStore.userInformation.phone_time <= 0) {
    topUpVisible.value = true
    return
  }
  if (useWs.status() !== 'OPEN') {
    useModal({
      message: t('networkErrorRetry')
    })
    useWs.connect()
    return
  }
  if (flagStatus.micAllow) {
    Howler.stop()
    flagStatus.phoneCall = true
    startChatReport(InteractiveTypeEnum.CALL)
  } else {
    permissionVisible.value = true
  }
}

const phoneCallPermission = () => {
  recOpenInit(() => {
    myRecorder.value.close()
    myRecorder.value = null
    Howler.stop()
    flagStatus.phoneCall = true
  })
}

const startChatReport = (type: InteractiveTypeEnum) => {
  if (isFirstInNoAction.value) {
    isFirstInNoAction.value = false
    eventReport({
      event_type: isNoRecord.value ? EventTypeEnum.START_CHAT : EventTypeEnum.CONTINUE_CHAT,
      ai_id: aiID.value,
      interactive_type: type
    }).catch((err) => {
      console.warn(err)
    })
  }
}

// 倒计时结束
const countDownFinish = () => {
  recStop(false)
}

const exitClearMode = () => {
  // 先设置过渡状态
  horizontalSwipe.isTransitioning = true
  horizontalSwipe.translateX = 0
  horizontalSwipe.targetOpacity = 1 // 设置目标透明度为1

  setTimeout(() => {
    isClearMode.value = false
    horizontalSwipe.isTransitioning = false
  }, 300)

  eventReport({
    event_type: EventTypeEnum.EXIT_IMMERSE_MODE,
    ai_id: aiID.value
  }).catch((err) => {
    console.warn(err)
  })
}

const handleGuideClearMode = () => {
  if (!userStore.newerOption.hadSwitchToClearMode && flagStatus.live2d) {
    guideForClearModeVisible.value = true
    userStore.newerOption.hadSwitchToClearMode = true
    eventReport({
      event_type: EventTypeEnum.SHOW_CLEAR_SCREEN_TUTORIAL,
      ai_id: aiID.value
    }).catch((err) => {
      console.warn(err)
    })
  }
}

// const handleGuideTouch = () => {
//   if (!userStore.newerOption.hadTouch) {
//     guideForTouchVisible.value = true
//     userStore.newerOption.hadTouch = true
//     eventReport({
//       event_type: EventTypeEnum.SHOW_TOUCH_ACTION_TUTORIAL,
//       ai_id: aiID.value
//     }).catch((err) => {
//       console.warn(err)
//     })
//   }
// }

const restartChat = () => {
  useConfirm({
    content: t('resetCharacterNotice'),
    confirmText: t('confirmButton'),
    cancelText: t('cancelButton'),
    onConfirm: () => {
      const { close } = useModal({
        loading: true,
        message: t('restarting'),
        autoClose: false
      })
      startNewChat({ ai_id: aiID.value })
        .then((res) => {
          conversationID.value = ''
          const { code } = res
          if (code === 200) {
            useModal({
              message: t('restartSuccessful')
            })
            resetButtonStatus()
            getAgentRecord()
            closeCollapse()
            getVideoMapByName({
              name: agentMessage.value.default_video_map.name,
              ai_id: aiID.value
            })
            if (nowEnv.value !== agentMessage.value.default_video_map.name) {
              environmentChange(agentMessage.value.default_video_map.name)
            }
          }
        })
        .catch((err) => {
          console.warn(err)
        })
        .finally(() => {
          close()
        })
    }
  })
}

const triggerContentGameInsert = (isIntimacyUpgrade?: number) => {
  console.log('triggerContentGameInsert', isIntimacyUpgrade)
  getSingleAgentContentGameInChatPage({
    ai_id: aiID.value
  }).then(({ code, data }) => {
    if (code === ResultEnum.SUCCESS) {
      contentGameInPageList.value = data
    }
  })
  const newContent = contentGameList.value.find((item) => item.level === isIntimacyUpgrade)
  console.log(newContent, 'newContent')
  if (newContent) {
    const baseRecord = {
      is_like: 0,
      is_bad: 0,
      message_id: '',
      is_phone_call: 0,
      call_time: 0
    }
    chatRecord.value.push({
      ...baseRecord,
      content_id: newContent.api_key,
      type: RecordEnum.STORY_IMAGE,
      content: newContent?.cover_url
    })
    chatRecord.value.push({
      ...baseRecord,
      type: RecordEnum.TIPS,
      content: t('clickToStory')
    })
    if (newContent?.opening_statement_outside) {
      chatRecord.value.push({
        ...baseRecord,
        type: RecordEnum.AI_COMMENT,
        content: newContent?.opening_statement_outside || ''
      })
    }
    scrollToBottom(chatRef.value)
  }
}

const goStory = () => {
  contentGameVisible.value = true
}
const goContentGame = (item: IContentRes | number) => {
  if (!userStore.checkLoginStatus()) return
  if (typeof item === 'number') {
    eventReport({
      event_type: EventTypeEnum.ENTER_STORY,
      content_id: item,
      front_address: ForwardAddressEnum.CHAT_PAGE,
      ai_id: aiID.value
    }).catch((err) => {
      console.warn(err)
    })
    router.push(`/chat/${item}`)
  } else {
    if (item.lock_status === ContentGameStatusEnum.LOCKED) {
      useModal({
        message: t('upgradeToUnlockStory')
      })
      return
    }
    eventReport({
      event_type: EventTypeEnum.ENTER_STORY,
      content_id: item.api_key,
      front_address: ForwardAddressEnum.CHAT_BOX_BTN,
      ai_id: aiID.value
    }).catch((err) => {
      console.warn(err)
    })
    router.push(`/chat/${item.api_key}`)
  }
}

const goHomePage = () => {
  if (!userStore.checkLoginStatus()) return
  eventReport({
    event_type: EventTypeEnum.OPEN_AI_PROFILE,
    ai_id: aiID.value
  }).catch((err) => {
    console.warn(err)
  })
  router.push(`/agentHomePage/${aiID.value}`)
}

const goBack = () => {
  adStore
    .showCheckAdv('quit_chat', {
      aiID: aiID.value,
      trigger_position: '离开页面'
    })
    .then(() => {
      eventReport({
        event_type: EventTypeEnum.EXIT_CHAT,
        ai_id: aiID.value
      }).catch((err) => {
        console.warn(err)
      })
      if (route.query.path === 'create') {
        router.push('/')
      } else {
        useBackHistory()
      }
    })
    .catch((err) => {
      console.warn(err)
      if (err === 'loading') {
        return
      }
      eventReport({
        event_type: EventTypeEnum.EXIT_CHAT,
        ai_id: aiID.value
      }).catch((err) => {
        console.warn(err)
      })
      if (route.query.path === 'create') {
        router.push('/')
      } else {
        useBackHistory()
      }
    })
}

const toggleExpandOption = (key: keyof typeof expandOptionsStatus, value: boolean) => {
  // 如果要设置为true，先将所有选项设为false
  if (value) {
    Object.keys(expandOptionsStatus).forEach((k) => {
      k !== key && (expandOptionsStatus[k as keyof typeof expandOptionsStatus] = false)
    })
  }
  // 然后设置目标选项的值
  expandOptionsStatus[key as keyof typeof expandOptionsStatus] = value
}

// 添加动画控制函数
const handleAnimation = (isOpening: boolean) => {
  console.log(isOpening, 'isOpening')
  anime({
    targets: '.switch-mode',
    rotate: isOpening ? [0, 90] : [90, 0],
    duration: 100,
    easing: 'linear'
  })
}

const handleCollapse = (type: keyof typeof expandOptionsStatus, status?: boolean) => {
  if (!userStore.checkLoginStatus()) return
  // 处理直接设置状态的情况
  if (typeof status !== 'undefined') {
    console.log(1)
    toggleExpandOption(type, status)
    nextTick(() => {
      if (status) {
        console.log('11')
        flagStatus.inputting = true
        if (collapseContentRef.value.style.height === '0px') {
          console.log(111)
          nextTick(() => {
            openCollapse(type === 'agentOptions')
          })
        }
      } else {
        if (collapseContentRef.value.style.height !== '0px') {
          closeCollapse(type === 'agentOptions')
        }
      }
    })
    return
  }

  // 处理状态切换的情况
  console.log(2)
  const isCurrentlyOpen = expandOptionsStatus[type]
  const isAgentOptions = type === 'agentOptions'

  if (isCurrentlyOpen) {
    // 关闭当前选项
    toggleExpandOption(type, false)
    nextTick(() => closeCollapse(isAgentOptions))
  } else {
    // 检查是否有其他选项打开
    const hasOtherOpen = Object.values(expandOptionsStatus).some((v) => v)

    if (!hasOtherOpen) {
      // 如果没有其他选项打开，直接打开当前选项
      toggleExpandOption(type, true)
      nextTick(() => openCollapse(isAgentOptions))
    } else {
      // 如果有其他选项打开，处理切换动画
      if (isAgentOptions || expandOptionsStatus.agentOptions) {
        handleAnimation(isAgentOptions)
      }
      // 处理选项高度

      toggleExpandOption(type, true)
      nextTick(() => {
        collapseContentRef.value.style.height = '0px'
        collapseContentRef.value.style.height = 'auto'
      })
    }
  }
}

const closeCollapse = (animation?: boolean) => {
  const { height } = collapseContentRef.value.getBoundingClientRect()
  collapseContentRef.value.style.height = `${height}px`
  collapseContentRef.value.getBoundingClientRect()
  collapseContentRef.value.style.height = '0px'

  if (animation) {
    console.log('sdsds')
    handleAnimation(false)
  }
}

const openCollapse = (animation?: boolean) => {
  collapseContentRef.value.style.height = 'auto'
  const { height } = collapseContentRef.value.getBoundingClientRect()
  console.log(height, ' sd')
  collapseContentRef.value.style.height = '0px'
  collapseContentRef.value.getBoundingClientRect()
  collapseContentRef.value.style.height = `${height}px`

  flagStatus.inputting = true
  if (animation) {
    handleAnimation(true)
  }
}

const changeInputting = () => {
  if (!userStore.checkLoginStatus()) return
  if (flagStatus.lock) return
  if (flagStatus.inputting) {
    handleCloseExpand()
  }
  flagStatus.inputting = !flagStatus.inputting
}

const purchaseCrystal = () => {
  sessionStorage.setItem('crystal_front_address', ForwardAddressEnum.CHAT_PAGE)
  router.push({ name: 'crystal' })
}

const triggerGiftMessage = async ({ data, gift }: { data: IChatResponseType; gift: IGiftListType }) => {
  loadingStatus.chat = true
  console.log(gift)
  l2dEmo.value = data.pjh_resp.llm_emotion
  if (data.pjh_resp.tts_answer?.length) {
    playUrlSequence(data.pjh_resp.tts_answer.map((item) => item.voice_path))
  }
  await answerTaskAsync(data)
}

const getDress = () => {
  getDressByID({
    ai_id: aiID.value
  })
    .then((res) => {
      const { code, data } = res
      if (code === 200) {
        skinList.value = data
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {})
}

const handleSkinSwitch = (skin: DressType) => {
  agentMessage.value.bg_info.bg_url = skin.pic_url
  if (skin.type === SkinTypeEnum.ThreeD) {
    getThreeDResource(skin.dress_id)
  } else {
    live2dResource.value = {
      model_json: undefined,
      action_list: [],
      rotate: 0
    }
    if (agentMessage.value.bg_info.type !== skin.type) {
      flagStatus.live2d = false
    }
  }
  agentMessage.value.bg_info.type = skin.type
  getDress()
}

const getThreeDResource = (id: number) => {
  getThreeDSource({
    id
  }).then((res) => {
    const { data, code } = res
    if (code === ResultEnum.SUCCESS) {
      live2dResource.value = data
      const configJson = live2dResource.value.model_json.replace('model.json', 'config.json')
      fetch(configJson)
        .then((response) => {
          if (response.ok) {
            return response.json()
          } else {
            throw new Error()
          }
        })
        .then((data) => {
          if (!data || !data.setparameter) {
            throw new Error()
          }
          live2dResource.value.parameters = data.setparameter
        })
        .catch(() => {
          console.log('No config')
        })
      console.log(configJson)
      flagStatus.live2d = true
      handleGuideClearMode()
      nextTick(() => {
        l2dRef.value.createNewModel()
      })
    }
  })
}

const getAdv = () => {
  getAdvInfo().then((res) => {
    const { code } = res
    if (code === ResultEnum.SUCCESS) {
      // 广告信息获取成功
    }
  })
}

getAdv()

const getAgentMessageAndRecord = () => {
  getAIInfo({ ai_id: aiID.value }).then((res) => {
    const { code, data, msg } = res
    if (code === 200) {
      agentMessage.value = data
      if (isEmpty(data.bg_info) && data.default_background) {
        switch (data.default_background) {
          case SkinTypeEnum.ThreeD:
            agentMessage.value.bg_info.type = SkinTypeEnum.ThreeD
            agentMessage.value.bg_info.dress_id = data.default_live2d.dress_id
            agentMessage.value.bg_info.bg_url = data.default_live2d.bg_url
            getThreeDResource(data.bg_info.dress_id)
            break
          case SkinTypeEnum.Normal:
            agentMessage.value.bg_info.type = SkinTypeEnum.Normal
            agentMessage.value.bg_info.bg_url = data.image_url
            break
          case SkinTypeEnum.Video:
            agentMessage.value.bg_info.type = SkinTypeEnum.Video
            agentMessage.value.bg_info.bg_url = data.image_url
            nowEnv.value = data.video_map.name
            nowVideoType.value = VideoType.STANDBY
            preloadVideos(
              [
                {
                  type: VideoType.STANDBY,
                  url: data.video_map.url_prefix + data.video_map.standby_url
                },
                {
                  type: VideoType.TRANSITION,
                  url: data.video_map.url_prefix + data.video_map.transition_url
                },
                ...data.video_map.action_urls.map((item) => ({
                  type: item.emotion,
                  url: data.video_map.url_prefix + item.url
                }))
              ],
              data.video_map.name
            )
            nextTick(() => {
              data.video_map.action_urls.forEach((item) => {
                videoContainerRef.value.append(videoElements.value[data.video_map.name][item.emotion])
              })
              videoContainerRef.value.append(videoElements.value[data.video_map.name].transition)
              videoContainerRef.value.append(videoElements.value[data.video_map.name].standby)
              videoElements.value[data.video_map.name].standby.poster = data.image_url
              videoElements.value[data.video_map.name].standby.style.opacity = '1'
              videoElements.value[data.video_map.name].standby.play()
            })
            break
        }
      }
      if (data.bg_info.type === SkinTypeEnum.ThreeD) {
        getThreeDResource(data.bg_info.dress_id)
      }
      const img = new Image()
      img.src = data.bg_info.bg_url
      img.onload = () => {
        setTimeout(() => {
          flagStatus.showInterface = true
          isNoRecord.value && playChatAudio(agentMessage.value.opening_statement_voice)
        }, 400)
      }
      img.onerror = () => {
        setTimeout(() => {
          flagStatus.showInterface = true
          isNoRecord.value && playChatAudio(agentMessage.value.opening_statement_voice)
        }, 400)
      }

      getAgentRecord()
      // 初始化BGM
      nextTick(() => {
        bgmSwitchRef.value.init()
      })
    } else if (code === ResultEnum.ABSENT_AGENT) {
      useModal({
        message: msg,
        duration: 1500,
        onClose: () => {
          router.push('/')
        }
      })
    } else if (code === ResultEnum.UN_EXISTENCE) {
      router.replace('/404')
    }
  })
}

const getAgentRecord = async () => {
  try {
    const res = await getChatRecord({
      ai_id: aiID.value,
      limit: 9999,
      page: 1
    })

    const { code, data } = res
    if (code === 200) {
      resultReward.value = data.double_get_crystal || 0
      let record: RecordType[] = []
      // 没记录，显示ai生成提示
      if (data.list.length === 0) {
        isNoRecord.value = true
        blockBtnList.value = agentMessage.value.opening_option
          ?.filter((item) => item)
          ?.map((item, index) => ({
            ad: false,
            callback: index + 1 + '',
            content: item
          }))
        btnShowing.globalShort = Boolean(blockBtnList.value?.length)

        record = [
          {
            type: RecordEnum.WARN,
            content: '',
            is_like: BOOL_NUMBER.NO,
            is_bad: BOOL_NUMBER.NO,
            message_id: '',
            is_phone_call: BOOL_NUMBER.NO,
            replayTime: 0
            // replayContent: []
          }
        ]
      }
      // 剧情和开场白
      if (agentMessage.value.init_plot) {
        record.push({
          type: RecordEnum.SYNOPSIS,
          content: agentMessage.value.init_plot
        })
      }
      if (agentMessage.value.opening_statement) {
        record.push({
          type: RecordEnum.AI_COMMENT,
          content: agentMessage.value.opening_statement,
          is_like: BOOL_NUMBER.NO,
          is_bad: BOOL_NUMBER.NO,
          is_phone_call: BOOL_NUMBER.NO,
          message_id: '',
          ttsLoading: false,
          ttsPlaying: false
        })
      }
      conversationID.value = data.list[0]?.conversation_id
      const tempList = cloneDeep(data.list)
      // 记录列表
      tempList.reverse().map((item, index) => {
        record.push(...recordClassify(item.output_text, true, item.id, index === tempList.length - 1, item))
      })

      chatRecord.value = record
      scrollToBottom(chatRef.value, 'instant')
    }
  } catch (error) {
    console.error('获取聊天记录失败:', error)
  }
}

const resetButtonStatus = () => {
  btnShowing.globalShort = false
  expandOptionsStatus.attachLong = false
  btnShowing.attachShort = false
  btnShowing.globalProgress = false
  expandOptionsStatus.agentOptions = false
  progressOptions.value = []
  blockBtnList.value = []
  noBlockBtnList.value = []
  attachBtnList.value = []
}

const getCrystalAd = async () => {
  if (appStore.isAndroid) {
    let crystalCode = ''
    const { code, data } = await getCrystalCode({
      tag: 'get_crystal'
    })
    if (code === ResultEnum.SUCCESS) {
      crystalCode = data
    }
    adStore
      .showCheckAdv('get_crystal', {
        aiID: aiID.value,
        trigger_position: '领取水晶'
      })
      .then(async () => {
        const { code } = await getCrystal({ code: crystalCode })
        if (code === ResultEnum.SUCCESS) {
          useModal({
            message: t('getSuccessfully'),
            duration: 3000
          })
          userStore.getUserInfo()
        }
      })
  } else {
    if (appStore.isUg) {
      window.JsAndroid.jumpOutBrowser('https://ugenie.net/landing')
      return
    }
    purchaseCrystal()
  }
}

const recordClassify = (
  record: string | IDialogueRecord[],
  isInit?: boolean,
  recordID?: number,
  isLastRecord?: boolean,
  item?: IChatRecordType
): RecordType[] => {
  const transRecord: RecordType[] = []
  console.log(record)
  if (item?.query_type === QueryTypeEnum.UPGRADE) {
    transRecord.push({
      type: RecordEnum.TIPS,
      content: t('intimacyUpgrade', { level: record })
    })
  } else if (item?.query_type === QueryTypeEnum.CONTENT) {
    if (record === 'start_content') {
      transRecord.push({
        type: RecordEnum.STORY_IMAGE,
        content: item.content_info.prefix_url + item.content_info.cover_url,
        content_id: item.content_info.api_key
      })
    } else if (record === 'unlocked_content') {
      transRecord.push({
        type: RecordEnum.TIPS,
        content: t('contentGameUnlock', { level: item?.content_info?.level })
      })
    }
  } else if (typeof record === 'string') {
    record.split('|').map((item) => {
      transRecord.push({
        type: RecordEnum.USER_COMMENT,
        content: item
      })
    })
  } else {
    console.log(record)
    record.map((perRecord, index) => {
      const needInit = isLastRecord && isInit
      const keyName = Object.getOwnPropertyNames(perRecord)[0]
      const commonObj: {
        revert_content: IDialogueRecord
        buttonType?: ButtonType
        record_id: number
      } = {
        revert_content: perRecord,
        record_id: recordID
      }
      // 处理 button_no_blocking_long 类型
      // @ts-ignore
      const longButtonList = perRecord[keyName]['button_no_blocking_long']
      if (longButtonList) {
        commonObj.buttonType = 'button_no_blocking_long'
        if (isLastRecord) {
          attachBtnList.value = longButtonList
        }
        if (needInit && flagStatus.autoExpandLongOption && !loadingStatus.chat) {
          console.log(!!longButtonList, isLastRecord && !!longButtonList, 'test')
          nextTick(() => handleCollapse('attachLong', isLastRecord && !!longButtonList))
        }
      } else {
        attachBtnList.value = []
      }

      // 处理 button_blocking 类型
      // @ts-ignore
      const blockingButtonList = perRecord[keyName]['button_blocking']
      if (blockingButtonList) {
        commonObj.buttonType = 'button_blocking'
      }
      blockBtnList.value = blockingButtonList || []
      if (needInit) {
        btnShowing.globalShort = !!blockingButtonList
      }

      // 处理 button_no_blocking_short 类型
      // @ts-ignore
      const shortButtonList = perRecord[keyName]['button_no_blocking_short']
      if (shortButtonList) {
        commonObj.buttonType = 'button_no_blocking_short'
      }
      noBlockBtnList.value = shortButtonList || []
      if (needInit) {
        btnShowing.attachShort = !!shortButtonList
      }

      switch (keyName) {
        case 'dialogue':
          perRecord[keyName].content.map((item, index) => {
            transRecord.push({
              type: RecordEnum.AI_COMMENT,
              content: item,
              ...commonObj,
              buttonType: index === perRecord[keyName].content.length - 1 ? commonObj.buttonType : undefined
            })
          })
          break
        case 'narration_normal':
          transRecord.push({
            type: RecordEnum.NARRATION,
            content: perRecord[keyName].content,
            ...commonObj
          })
          break
        case 'picture_url':
          transRecord.push({
            type: RecordEnum.PICTURE_MARKDOWN,
            content: perRecord[keyName].content,
            ...commonObj
          })
          break
        case 'game_status':
          transRecord.push({
            type: RecordEnum.GAME_REWARD_TIPS,
            content: '2',
            ...commonObj,
            buttonType: undefined
          })
          transRecord.push({
            type: RecordEnum.GAME_RESULT,
            content: perRecord[keyName].explanation,
            ...commonObj
          })
          break
        case 'global_score':
          btnShowing.globalProgress = true
          progressOptions.value = [...perRecord[keyName]]
          break
        case 'short_tips':
          transRecord.push({
            type: RecordEnum.TIPS,
            content: perRecord[keyName].content,
            ...commonObj
          })
          break
        case 'narration_chapter_title':
          // 播放动画
          transRecord.push({
            type: RecordEnum.CHAPTER,
            content: perRecord[keyName].content,
            ...commonObj
          })
          break
        case 'chat_lock':
          flagStatus.lock = index === record.length - 1 && perRecord[keyName].isLock
          flagStatus.autoExpandLongOption = true
          transRecord.push({
            type: RecordEnum.LOCK,
            content: '',
            ...commonObj
          })
          break
        case 'game_invitation':
          transRecord.push({
            type: RecordEnum.GAME_INVITATION,
            content: perRecord[keyName].invitation_content,
            ...commonObj
          })
          break
        case 'picture_backend_id':
          transRecord.push({
            type: RecordEnum.PICTURE_ADS,
            content: perRecord[keyName].content,
            ...commonObj
          })
          break
        case 'video_backend_id':
          transRecord.push({
            type: RecordEnum.VIDEO_ADS,
            content: perRecord[keyName].content,
            ...commonObj
          })
          break
        case 'story_image':
          transRecord.push({
            type: RecordEnum.STORY_IMAGE,
            content: perRecord[keyName].content,
            content_id: perRecord[keyName].content_id,
            ...commonObj
          })
          transRecord.push({
            type: RecordEnum.TIPS,
            content: t('clickToStory'),
            ...commonObj
          })
          break
      }
    })
  }
  return transRecord
}

const permissionInit = () => {
  // @ts-ignore
  navigator.permissions?.query({ name: 'microphone' }).then((res) => {
    if (res.state === 'granted') {
      flagStatus.micAllow = true
    }
  })
  const popup = popupMemoryStore.getPopupMemory()
  if (popup) {
    switch (popup.type) {
      case POPUP_TYPE_NAME.BUY_SKIN:
        nextTick(() => {
          skinVisible.value = true
        })
        break
      case POPUP_TYPE_NAME.SEND_GIFT:
        nextTick(() => {
          giftVisible.value = true
          popupMemoryStore.resetAllPopupMemory()
        })
        break
      // case POPUP_TYPE_NAME.BUY_SELFIE:
      //   unlockVisible.value = true
      //   break
      case POPUP_TYPE_NAME.BUY_CALL_TIME:
        topUpVisible.value = true
        popupMemoryStore.resetAllPopupMemory()
        break
    }
  }
  loadingStatus.record = true
  nextTick(() => {
    loadingStatus.record = false
  })
  if (/\/contentGame\/\d+/.test(route.redirectedFrom?.path)) {
    eventReport({
      event_type: EventTypeEnum.ENTER_STORY_PAGE,
      front_address: ForwardAddressEnum.STORY_CHAT_PAGE_BACK_CHAT
    }).catch((err) => {
      console.warn(err)
    })
  }
  getAgentMessageAndRecord()

  getSingleAgentContentGameInChatPage({
    ai_id: aiID.value
  }).then(({ code, data }) => {
    if (code === ResultEnum.SUCCESS) {
      contentGameInPageList.value = data
    }
  })
  getAgentContentGameList({
    ai_id: aiID.value
  }).then(({ code, data }) => {
    if (code === ResultEnum.SUCCESS) {
      contentGameList.value = data
    }
  })

  getAgentContentGameOfMyList({
    ai_id: aiID.value
  })
    .then(({ data, code }) => {
      if (code === ResultEnum.SUCCESS) {
        hadStoryBook.value = Boolean(data.length)
      }
    })
    .catch((err) => {
      console.warn(err)
    })
  userStore.getUserInfo()
}

// 监听MQTT消息触发的聊天页面事件
const handleMqttChatEvents = () => {
  // 监听新消息事件
  watch(
    () => notifyStore.chatPageEvents.newMessage.timestamp,
    (newTimestamp, oldTimestamp) => {
      console.log(notifyStore.chatPageEvents.newMessage, 'newMessage')
      if (newTimestamp > oldTimestamp && newTimestamp > 0) {
        const { data, aiId } = notifyStore.chatPageEvents.newMessage
        // 检查是否是当前聊天页面的AI
        if (aiId === Number(aiID.value)) {
          console.log('收到MQTT新消息，执行聊天页面方法', data)
          // 执行聊天页面的相应方法
          if (data?.pjh_resp) {
            // 如果是聊天回复，直接处理
            processChatResponse(data)
          } else {
            // 如果是其他类型消息，刷新聊天记录
            getAgentMessageAndRecord()
          }
        }
      }
    },
    { immediate: false }
  )

  // 监听刷新聊天事件
  watch(
    () => notifyStore.chatPageEvents.loginSendChat.timestamp,
    async (newTimestamp, oldTimestamp) => {
      if (newTimestamp > oldTimestamp && newTimestamp > 0) {
        const { aiId } = notifyStore.chatPageEvents.loginSendChat
        // 检查是否是当前聊天页面的AI
        if (aiId === aiID.value) {
          await getAgentRecord()
          await blockSendQuestion(
            noLoginTempBtn.value
              ? {
                  text: noLoginTempBtn.value,
                  responseWay: '遮挡聊天框的按钮'
                }
              : {}
          )
          btnShowing.globalShort = false
          noLoginTempBtn.value = undefined
        }
      }
    },
    { immediate: false }
  )
}

const chatQueryCacheList = ref<string[]>([])

const processChatResponse = (data: IChatResponseType) => {
  if (agentMessage.value.bg_info.type === SkinTypeEnum.Video) {
    if (data.pjh_resp.environment_change && data.pjh_resp.environment_change !== nowEnv.value) {
      environmentChange(data.pjh_resp.environment_change)
    } else {
      emotionCdController(data.pjh_resp.llm_emotion) && emotionChange(data.pjh_resp.llm_emotion)
    }
  } else if (agentMessage.value.bg_info.type === SkinTypeEnum.ThreeD) {
    l2dEmo.value = data.pjh_resp.llm_emotion
  }
  if (data.pjh_resp.tts_answer?.length) {
    playUrlSequence(data.pjh_resp.tts_answer.map((item) => item.voice_path))
  }
  answerTaskAsync(data)
  toolsContent.value = data.pjh_resp?.tools
}

const answerTaskAsync = async (data: IChatResponseType) => {
  const answer = data.pjh_resp.frontend_answer
  const recordID = data.record_id
  const matches = [...recordClassify(answer, false, recordID, true)]
  loadingStatus.textOutputting = true
  if (!matches[matches.length - 1].revert_content.chat_lock) {
    flagStatus.lock = false
  }
  // 逐条添加消息并延迟
  for (let i = 0; i < matches.length; i++) {
    // // 跳过空消息
    // if (!matches[i].trim()) continue

    if (matches[i].type === RecordEnum.CHAPTER) {
      chapterContent.value.content = matches[i].revert_content.narration_chapter_title.content
      chapterContent.value.tips = matches[i].revert_content.narration_chapter_title.tips

      // 显示章节动画
      btnShowing.chapterAnimate = true

      // 等待动画完成
      await new Promise<void>((resolve) => {
        // 创建定时器用于自动关闭
        const timer = setTimeout(() => {
          btnShowing.chapterAnimate = false
          resolve()
          chapterContent.value.content = ''
          chapterContent.value.tips = ''
        }, 3000)

        // 提供手动关闭方法
        const handleClose = () => {
          clearTimeout(timer)
          btnShowing.chapterAnimate = false
          resolve()
          // 移除事件监听
          window.removeEventListener('chapter-animation-close', handleClose)
        }

        // 监听手动关闭事件
        window.addEventListener('chapter-animation-close', handleClose)
      })
    } else {
      // 其他类型消息的处理
      // 如果不是第一条消息，等待1.2秒
      if (i > 0) {
        await new Promise((resolve) => {
          setTimeout(resolve, 1200)
        })
      }
      if (matches[i].revert_content?.game_status?.result === 'win' && matches[i].type === RecordEnum.GAME_RESULT) {
        playGetCrystal()
      }
      // 将消息添加到chatRecord
      chatRecord.value.push(matches[i])
      scrollToBottom(chatRef.value)
      if (matches[i].buttonType) {
        switch (matches[i].buttonType) {
          case 'button_no_blocking_long':
            console.log(matches[i])
            loadingStatus.attachLongBtn = false
            if (flagStatus.autoExpandLongOption) {
              nextTick(() => handleCollapse('attachLong', true)).then()
            }
            break
          case 'button_blocking':
            btnShowing.globalShort = true
            break
          case 'button_no_blocking_short':
            btnShowing.attachShort = true
            break
        }
      }
    }
  }
  loadingStatus.chat = false
  loadingStatus.textOutputting = false
  const intimacyUpgrade = handleIntimacy(data.intimacy_level, data.intimacy_now, data.intimacy_upgrade, data.intimacy_interest_tags)
  intimacyUpgrade && triggerContentGameInsert(intimacyUpgrade)
  console.log('answer', matches)
  const noMoreCache = Boolean(
    matches.filter((item) => {
      return item.buttonType === 'button_blocking'
    }).length || flagStatus.lock
  )
  console.log(noMoreCache, 'noMoreCache')
  if (chatQueryCacheList.value.length) {
    if (noMoreCache) {
      useModal({
        message: t('unableToSend')
      })
      chatQueryCacheList.value = []
      return
    }
    // 处理所有缓存的消息
    const cachedMessages = chatQueryCacheList.value.join('|')
    chatQueryCacheList.value = []
    await blockSendQuestion({ text: cachedMessages, responseWay: '打字输入' })
  }
}

const saveSound = ref(false)
const noLoginTempBtn = ref<BtnContentType>()
const blockSendQuestion = async ({ text, responseWay }: { text?: string | BtnContentType; responseWay?: string }) => {
  if (!userStore.checkLoginStatus()) {
    if (typeof text === 'object') {
      noLoginTempBtn.value = text
    }
    eventReport({
      event_type: EventTypeEnum.REPLY_WITHOUT_LOGIN
    })
    return
  }
  // 如果没有传入text且输入框为空，则直接返回
  if (!text && !question.value.trim()) return
  handleCloseExpand()
  const timestamp = Date.now()
  const messageContent = text || question.value

  let query = ''
  let pjh_query = ''
  if (typeof messageContent === 'object') {
    if (loadingStatus.chat) return
    handleCollapse('attachLong', false)
    flagStatus.autoExpandLongOption = true
    query = messageContent.content
    pjh_query = messageContent.callback
    chatRecord.value.push({
      type: RecordEnum.USER_COMMENT,
      timestamp,
      content: query,
      is_like: BOOL_NUMBER.NO,
      is_bad: BOOL_NUMBER.NO,
      is_phone_call: BOOL_NUMBER.NO,
      message_id: '',
      replayTime: 0
    })
    scrollToBottom(chatRef.value)
  } else if (typeof messageContent === 'string') {
    flagStatus.autoExpandLongOption = false
    query = messageContent
    pjh_query = messageContent
    // 只有当不是处理缓存消息时，才添加到聊天记录
    // 如果是处理缓存消息(text有值)，则不需要添加，因为消息已经在之前添加过了

    // 如果正在处理其他消息，则缓存当前消息
    if (!text) {
      chatRecord.value.push({
        type: RecordEnum.USER_COMMENT,
        timestamp,
        content: query,
        is_like: BOOL_NUMBER.NO,
        is_bad: BOOL_NUMBER.NO,
        is_phone_call: BOOL_NUMBER.NO,
        message_id: '',
        replayTime: 0
      })
      scrollToBottom(chatRef.value)
    }

    if (loadingStatus.chat) {
      saveSound.value = !loadingStatus.textOutputting
      if (!text) {
        // 只有新消息才添加到缓存
        chatQueryCacheList.value.push(messageContent)
        question.value = ''
      }
      return
    }
  }

  // 清空输入框
  if (!text) {
    question.value = ''
  }
  loadingStatus.chat = true
  loadingStatus.attachLongBtn = true
  // flagStatus.lock = false
  const volume = await appStore.getSystemVolume()
  chat({
    pjh_query: pjh_query,
    ai_id: aiID.value,
    conversation_id: conversationID.value,
    query,
    interactive_model: flagStatus.live2d ? 2 : 1,
    response_way: responseWay,
    volume: volume && userStore.userInformation.sound ? 1 : 0
  })
    .then(({ code, data }) => {
      if (code === ResultEnum.SUCCESS) {
        if (isEmpty(data)) {
          console.log('stop return')
          loadingStatus.chat = false
          return
        }
        resultReward.value = data.double_get_crystal
        conversationID.value = data.pjh_resp.conversation_id
        // 使用优化后的代码
        if (data.adv_info && appStore.isAndroid) {
          adStore
            .showCheckAdv('chat_times', {
              aiID: aiID.value,
              trigger_position: '聊天轮次触发'
            })
            .then(() => {
              processChatResponse(data)
            })
        } else {
          processChatResponse(data)
        }
      }
    })
    .catch((err) => {
      console.warn(err)
      loadingStatus.attachLongBtn = false
      loadingStatus.chat = false
    })
}

const webSearch = ({ tools }: { tools: ToolContentType[] }) => {
  loadingStatus.chat = true
  scrollToBottom(chatRef.value)
  searchTool({
    ai_id: aiID.value,
    ...tools[0]
  })
    .then(({ code, data }) => {
      if (code === ResultEnum.SUCCESS) {
        l2dEmo.value = data.pjh_resp.llm_emotion
        if (data.pjh_resp.tts_answer?.length) {
          playUrlSequence(data.pjh_resp.tts_answer.map((item) => item.voice_path))
        }
        answerTaskAsync(data)
        toolsContent.value = data.pjh_resp?.tools
      }
    })
    .finally(() => {
      loadingStatus.chat = false
      if (chatQueryCacheList.value.length) {
        // 处理所有缓存的消息
        const cachedMessages = chatQueryCacheList.value.join('|')
        chatQueryCacheList.value = []
        blockSendQuestion({ text: cachedMessages, responseWay: '打字输入' })
      }
    })
}

const debounceSendMessage = debounce(blockSendQuestion, 500, {
  leading: true,
  trailing: false
})

getDress()
permissionInit()
ugPhoneLinkSdk.registerBack(() => {
  goBack()
})
// 监听清屏模式变化，确保滑动状态同步
watch(
  () => isClearMode.value,
  (newVal) => {
    if (!horizontalSwipe.isDragging && !horizontalSwipe.isTransitioning) {
      // 只在非拖拽和非过渡状态下同步位置和透明度
      horizontalSwipe.translateX = newVal ? -100 : 0
      horizontalSwipe.targetOpacity = newVal ? 0 : 1
    }
  }
)

watch(
  () => flagStatus.phoneCall,
  (value) => {
    if (!value) {
      bgmSwitchRef.value?.control()
    }
  },
  {
    deep: true
  }
)

watch(
  // @ts-ignore
  () => route.params.ai_id,
  (id) => {
    if (id) {
      aiID.value = id as number
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const loadAd = () => {
  adStore.loadIncentiveAd()
  adStore.loadInsetAd()
}

const handleCloseExpand = () => {
  expandOptionsStatus.attachLong && !flagStatus.lock && handleCollapse('attachLong', false)
  expandOptionsStatus.agentOptions && !flagStatus.lock && handleCollapse('agentOptions', false)
}

// 横向滑动处理函数
const handleHorizontalTouchStart = (e: TouchEvent) => {
  if (horizontalSwipe.isTransitioning) return

  horizontalSwipe.startX = e.touches[0].clientX
  horizontalSwipe.startY = e.touches[0].clientY
  horizontalSwipe.currentX = horizontalSwipe.startX
  horizontalSwipe.currentY = horizontalSwipe.startY
  horizontalSwipe.isDragging = false

  // 确保translateX和targetOpacity反映当前的实际状态
  if (isClearMode.value) {
    horizontalSwipe.translateX = -100
    horizontalSwipe.targetOpacity = 0
  } else {
    horizontalSwipe.translateX = 0
    horizontalSwipe.targetOpacity = 1
  }
}

const handleHorizontalTouchMove = (e: TouchEvent) => {
  if (horizontalSwipe.isTransitioning) return

  horizontalSwipe.currentX = e.touches[0].clientX
  horizontalSwipe.currentY = e.touches[0].clientY

  const deltaX = horizontalSwipe.currentX - horizontalSwipe.startX
  const deltaY = horizontalSwipe.currentY - horizontalSwipe.startY

  // 判断是否为横向滑动（横向移动距离大于纵向移动距离）
  if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
    // 只处理从右向左的滑动（deltaX < 0）或者在清屏模式下从左向右返回（deltaX > 0）
    if ((deltaX < 0 && !isClearMode.value && flagStatus.live2d) || (deltaX > 0 && isClearMode.value)) {
      if (!horizontalSwipe.isDragging) {
        horizontalSwipe.isDragging = true
      }

      // 阻止默认滚动行为和事件冒泡
      e.preventDefault()
      e.stopPropagation()

      if (!isClearMode.value) {
        // 正常模式下，从右向左滑动
        // 将像素转换为百分比，使用requestAnimationFrame优化性能
        let progress = Math.abs(deltaX) / window.innerWidth
        progress = Math.min(progress, 0.8) // 限制最大为80%
        const newTranslateX = -progress * 100 // 负值表示向左移动

        // 只在值发生变化时更新
        if (Math.abs(horizontalSwipe.translateX - newTranslateX) > 0.1) {
          horizontalSwipe.translateX = newTranslateX
        }
      } else {
        // 清屏模式下，从左向右滑动返回
        // 从当前的-100%位置开始，根据滑动距离向右移动
        let progress = deltaX / window.innerWidth
        progress = Math.max(0, Math.min(progress, 1)) // 限制在0-1之间
        // 从-100%开始，最多移动到0%
        const newTranslateX = -100 + progress * 100
        horizontalSwipe.live2dOpacity = 1 - progress

        // 只在值发生变化时更新
        if (Math.abs(horizontalSwipe.translateX - newTranslateX) > 0.1) {
          horizontalSwipe.translateX = newTranslateX
        }
      }
    }
  }
}

const handleHorizontalTouchEnd = () => {
  if (horizontalSwipe.isTransitioning) return

  if (horizontalSwipe.isDragging) {
    const deltaX = horizontalSwipe.currentX - horizontalSwipe.startX

    // 判断是否超过阈值
    if (Math.abs(deltaX) > horizontalSwipe.threshold) {
      horizontalSwipe.isTransitioning = true

      if (deltaX < 0 && !isClearMode.value && flagStatus.live2d) {
        // 从右向左滑动，切换到live2d页面
        horizontalSwipe.translateX = -100 // 移动到-100%
        horizontalSwipe.targetOpacity = 0 // 设置目标透明度为0
        setTimeout(() => {
          isClearMode.value = true
          horizontalSwipe.translateX = -100 // 保持在-100%位置
          horizontalSwipe.isTransitioning = false
        }, 300)
      } else if (deltaX > 0 && isClearMode.value) {
        // 从左向右滑动，从live2d页面返回
        horizontalSwipe.translateX = 0
        horizontalSwipe.targetOpacity = 1 // 设置目标透明度为1
        isClearMode.value = false
        setTimeout(() => {
          horizontalSwipe.translateX = 0 // 回到0%位置
          horizontalSwipe.isTransitioning = false
        }, 300)
      } else {
        // 回弹
        horizontalSwipe.translateX = isClearMode.value ? -100 : 0
        horizontalSwipe.targetOpacity = isClearMode.value ? 0 : 1
        horizontalSwipe.isTransitioning = false
      }
    } else {
      // 未超过阈值，回弹
      horizontalSwipe.isTransitioning = true
      horizontalSwipe.translateX = isClearMode.value ? -100 : 0
      horizontalSwipe.targetOpacity = isClearMode.value ? 0 : 1
      setTimeout(() => {
        horizontalSwipe.isTransitioning = false
      }, 300)
    }
  }

  // 重置状态
  horizontalSwipe.isDragging = false
  horizontalSwipe.startX = 0
  horizontalSwipe.startY = 0
  horizontalSwipe.currentX = 0
  horizontalSwipe.currentY = 0
}

onUnmounted(() => {
  ugPhoneLinkSdk.unregisterBack()

  // 清理横向滑动事件监听器
  const container = document.getElementById('modelContainer')
  if (container) {
    container.removeEventListener('touchstart', handleHorizontalTouchStart)
    container.removeEventListener('touchmove', handleHorizontalTouchMove)
    container.removeEventListener('touchend', handleHorizontalTouchEnd)
  }
})

onMounted(() => {
  loadAd()
  const observer = new ResizeObserver(() => {
    scrollToBottom(chatRef.value, 'instant')
  })
  observer.observe(collapseContentRef.value)

  window.addEventListener('resize', () => {
    scrollToBottom(chatRef.value, 'instant')
  })

  // 添加横向滑动事件监听到整个容器
  const container = document.getElementById('modelContainer')
  if (container) {
    container.addEventListener('touchstart', handleHorizontalTouchStart, { passive: false })
    container.addEventListener('touchmove', handleHorizontalTouchMove, { passive: false })
    container.addEventListener('touchend', handleHorizontalTouchEnd, { passive: false })
  }

  // 无法滚动时 电脑适配滚轮
  chatRef.value.addEventListener('wheel', (event: WheelEvent) => {
    if (event.deltaY > 0) {
      if (chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop <= 4) {
        // if (chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop <= 4 && flagStatus.live2d) {
        flagStatus.halfScreen = true
      }
    } else {
      // flagStatus.live2d && (flagStatus.halfScreen = false)
      flagStatus.halfScreen = false
    }
  })
  // 无法滚动时适配手机触摸
  chatRef.value.addEventListener('touchstart', (e: TouchEvent) => {
    handleCloseExpand()
    slidePoint.start = e.touches[0].pageY
  })
  chatRef.value?.addEventListener('scroll', () => {
    flagStatus.showScrollDown = chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop > 120
  })
  chatRef.value.addEventListener('touchmove', (e: TouchEvent) => {
    slidePoint.end = e.touches[0].pageY
    if (slidePoint.end > slidePoint.start) {
      // if (slidePoint.end > slidePoint.start && flagStatus.live2d) {
      flagStatus.halfScreen = false
    } else if (slidePoint.end < slidePoint.start) {
      if (chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop <= 4) {
        // if (chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop <= 4 && flagStatus.live2d) {
        flagStatus.halfScreen = true
      }
    }
  })

  talkBtnRef.value.addEventListener('touchstart', (e: TouchEvent) => {
    console.log(flagStatus.micAllow)
    ugPhoneLinkSdk.micAllow()
    // 禁止穿透
    e.preventDefault()
    if (loadingStatus.tts) {
      useModal({
        message: t('conversationProcessing')
      })
      return
    }
    if (flagStatus.micAllow) {
      navigator?.vibrate?.(100)
      recOpenInit(recStart)
      clickTime.start = Date.now()
      slidePoint.start = e.touches[0].pageY
    } else {
      recOpenInit(null)
    }
  })

  talkBtnRef.value.addEventListener('touchmove', (e: TouchEvent) => {
    // if (loadingStatus.tts || loadingStatus.text) return
    if (flagStatus.micAllow) {
      slidePoint.end = e.touches[0].pageY
    }
  })
  talkBtnRef.value.addEventListener('touchend', (e: TouchEvent) => {
    // if (loadingStatus.tts || loadingStatus.text) return
    if (flagStatus.micAllow) {
      clickTime.end = Date.now()
      slidePoint.end = e.changedTouches[0].pageY
      try {
        // 向下滑动不取消
        if (slidePoint.start - slidePoint.end > 60 && slidePoint.end < slidePoint.start) {
          console.log('取消录音')
          recStop(true)
        } else {
          console.log('正常录音')
          recStop(false)
        }
      } catch (error) {
        console.warn(error)
      }
      countDownRef.value.reset()
      loadingStatus.record = false
      if (clickTime.end - clickTime.start < 500) {
        useModal({
          message: t('voiceTooShort'),
          duration: 1000
        })
      }
    }
    slidePoint.start = 0
    slidePoint.end = 0
  })

  // 初始化MQTT事件监听
  handleMqttChatEvents()
})

onBeforeUnmount(() => {
  l2dRef.value?.erosModel?.destroySound?.()
  l2dRef.value?.erosModel?.stopMotionSync?.()
  l2dRef.value?.deleteModel?.()
  bgmSwitchRef.value?.control(false, true)
  if (myStream.value) {
    myStream.value.stop()
    myStream.value = null
  }
  stop()
})

onUnmounted(() => {
  cancelAllRequests()
})

provide('aiID', aiID)
provide('agentMessage', agentMessage)
provide('resultReward', resultReward)
provide('live2dStatus', toRef(flagStatus, 'live2d'))
provide('showPopover', showPopover)
provide('showInterface', toRef(flagStatus, 'showInterface'))
provide('isNoRecord', isNoRecord)
provide('live2dOpacity', toRef(horizontalSwipe, 'live2dOpacity'))
provide('isDragging', toRef(horizontalSwipe, 'isDragging'))
provide('topUpVisible', topUpVisible)
provide('phoneCall', toRef(flagStatus, 'phoneCall'))
</script>

<template>
  <div
    id="modelContainer"
    class="whole-chat-container"
    :style="
      agentMessage.bg_info?.bg_url &&
      agentMessage.bg_info.type !== SkinTypeEnum.Video && {
        background: `url('${agentMessage.bg_info?.bg_url}') center / cover no-repeat`
      }
    "
  >
    <div
      ref="videoContainerRef"
      class="video-bg"
    ></div>
    <div
      v-show="isClearMode"
      @click="exitClearMode"
      class="exit-l2d flex-center-center"
      :style="{ opacity: isClearMode && !horizontalSwipe.isDragging ? 1 : horizontalSwipe.live2dOpacity - 0.4 }"
    >
      <IconSvgLeftLineArrow class="fsize-22" />
    </div>
    <FeedbackDrawer v-model="feedbackVisible" />
    <div
      class="top-bar"
      :style="chatElementStyle"
    >
      <div class="left-content flex-center-center cg-8">
        <div
          v-if="handleBackSvg()"
          class="padding-4 home flex-center-center"
          @click="goBack"
        >
          <IconSvgHome class="fsize-20" />
        </div>
        <IconSvgLeftArrow
          v-else
          class="fsize-24"
          @click="goBack"
        />
        <div class="head-message">
          <div
            class="avatar-container"
            :style="{ '--percentage': intimacyPercent }"
            @click="
              () => {
                if (!userStore.checkLoginStatus()) return
                intimacyLevelVisible = hadIntimacySystem
              }
            "
          >
            <div
              ref="heartRef"
              class="heart-down"
              v-if="hadIntimacySystem"
            >
              <IconSvgHeartIntimacy class="fsize-16" />
              <div class="num">{{ agentMessage.ai_chat_setting.intimacy_level }}</div>
            </div>
            <div
              class="avatar"
              :style="{ backgroundImage: `url('${agentMessage.avatar_url}')` }"
            ></div>
          </div>
          <div
            class="name"
            @click="goHomePage"
          >
            {{ agentMessage.name }} <IconSvgArrowRightJump />
          </div>
        </div>
      </div>
      <div class="right-btn">
        <BgmSwitchBtn ref="bgmSwitchRef" />
      </div>
    </div>
    <GlobalProgress
      v-show="btnShowing.globalProgress"
      :options="progressOptions"
      :style="chatElementStyle"
    />
    <div
      class="chat-page"
      ref="chatPageRef"
      :style="chatElementStyle"
    >
      <div
        ref="chatRef"
        id="chatPage"
        class="chat"
        :style="{
          '--chat-mask-start': flagStatus.halfScreen ? '50%' : btnShowing.globalProgress ? '120px' : '50px',
          '--chat-mask-end': flagStatus.halfScreen ? '55%' : btnShowing.globalProgress ? '145px' : '75px',
          'padding-top': btnShowing.globalProgress ? '128px' : '64px'
        }"
      >
        <div
          class="whole-chat-item"
          v-for="(item, index) in chatRecord"
          :key="item.message_id + item.type + String(index)"
        >
          <div
            ref="replayItemRef"
            class="chat-item-container"
          >
            <div class="chat-item">
              <component
                :is="ComponentReflect[item.type]"
                :record="item"
                :index="index"
                @submit="blockSendQuestion"
                @go-content-game="goContentGame"
              />
              <template v-if="index === chatRecord.length - 1 && item.buttonType === 'button_no_blocking_short'">
                <AttachShortButton
                  v-show="btnShowing.attachShort"
                  v-model="btnShowing.attachShort"
                  @submit="blockSendQuestion"
                  @restart="restartChat"
                  @web_search="webSearch"
                  :options="noBlockBtnList"
                />
              </template>
            </div>
          </div>
        </div>
        <div
          class="whole-chat-item pl-12 pr-12"
          v-if="loadingStatus.chat"
        >
          <div class="chat-item-loading">
            <div class="chat-record">
              <ChatLoading />
            </div>
          </div>
        </div>
      </div>
      <div
        class="accordion"
        v-show="normalInput"
      >
        <div
          class="scroll-top flex-center-center"
          v-show="flagStatus.showScrollDown"
          @click="scrollToBottom(chatRef, 'smooth', false)"
        >
          <IconSvgScrollArrow class="fsize-24" />
        </div>

        <div
          class="undress-gift"
          v-if="agentMessage.resident_type !== 3"
        >
          <div
            v-if="agentMessage.resident_function.includes('gift')"
            class="gift"
            @click="
              () => {
                if (!userStore.checkLoginStatus()) return
                giftVisible = true
              }
            "
          >
            <img
              src="@/assets/images/agentChat/gift-chat.webp"
              alt=""
            />
          </div>
          <div class="content-game-list">
            <div
              v-for="item in contentGameInPageList"
              class="content-game"
              :key="item.id"
              @click="goContentGame(item)"
            >
              <SvgIcon
                v-show="item.lock_status !== ContentGameStatusEnum.LOCKED"
                icon-class="content-game-open"
                class="fsize-14"
              />
              <SvgIcon
                v-show="item.lock_status === ContentGameStatusEnum.LOCKED"
                icon-class="content-game-lock"
                class="fsize-14"
              />
              {{ item.name }}
            </div>
          </div>
        </div>
        <div
          class="talk-mask"
          v-if="loadingStatus.record"
        >
          <div
            class="mask-text"
            :style="isCancelDistance ? 'color: #F62A5A' : 'color: #fff'"
          >
            {{ isCancelDistance ? t('releaseToCancelSend') : t('releaseToSendSwipeUpCancel') }}
          </div>
        </div>
        <div class="input-container">
          <SvgIcon
            @click="changeInputting"
            :icon-class="flagStatus.inputting ? 'microphone' : 'keyboard'"
            class="fsize-28"
            :style="flagStatus.lock && 'opacity: 0.1'"
          />
          <div
            v-show="flagStatus.lock"
            class="lock-layer"
            @click="handleCollapse('attachLong')"
          >
            <img
              src="@/assets/images/chat/chat.gif"
              alt="chat"
            />
            <div>{{ t('waitAction') }}</div>
          </div>
          <van-field
            type="textarea"
            v-show="flagStatus.inputting && !flagStatus.lock"
            class="flex-1 input"
            v-model="question"
            @focus="handleCloseExpand"
            :autosize="{
              maxHeight: 100
            }"
            rows="1"
            :placeholder="t('freeInput')"
            @keydown.enter.prevent="debounceSendMessage({})"
          >
            <template
              #button
              v-if="attachBtnList.length"
            >
              <div
                class="options-btn"
                @click="handleCollapse('attachLong')"
              >
                <SvgIcon icon-class="ai-options-btn" />
              </div>
            </template>
          </van-field>
          <div
            v-show="!flagStatus.inputting && !flagStatus.lock"
            ref="talkBtnRef"
            :style="loadingStatus.record && (isCancelDistance ? 'background: #FF5B4D' : 'background: #EBDFAC')"
            class="flex-1 talk-btn flex-center-center"
          >
            <div v-show="!loadingStatus.record">{{ t('holdToSpeak') }}</div>
            <SoundWave
              v-show="loadingStatus.record"
              :cancel="isCancelDistance"
              class="mr-8"
            />
            <van-count-down
              v-show="loadingStatus.record"
              ref="countDownRef"
              millisecond
              :time="60000"
              :auto-start="false"
              @finish="countDownFinish"
            >
              <template #default="timeData">
                <span
                  class="block"
                  :style="isCancelDistance ? 'color: #fff' : 'color: #2f2101'"
                  >{{ timeData.seconds }}s
                </span>
              </template>
            </van-count-down>
          </div>
          <IconSvgSend
            v-if="question"
            class="fsize-36"
            @click="!flagStatus.lock && debounceSendMessage({})"
            :style="flagStatus.lock && 'opacity: 0.1'"
          />
          <SvgIcon
            v-else
            :icon-class="expandOptionsStatus.agentOptions ? 'cross-button' : 'add-plus-button'"
            class="fsize-28 switch-mode"
            @click="!flagStatus.lock && handleCollapse('agentOptions')"
            :style="flagStatus.lock && 'opacity: 0.1'"
          />
        </div>
        <div
          class="expand-option-container"
          ref="collapseContentRef"
          style="height: 0"
        >
          <Transition
            name="fade"
            mode="out-in"
          >
            <div v-if="expandOptionsStatus.agentOptions">
              <div class="crystal-add">
                <div
                  class="crystal-balance"
                  @click="getCrystalAd"
                >
                  <SvgIcon
                    icon-class="crystal"
                    class="fsize-18"
                  />
                  <div>{{ userStore.userInformation.crystal_amount }}</div>
                  <SvgIcon
                    icon-class="chat-add"
                    class="fsize-16"
                  />
                </div>
                <div
                  class="ads-tips"
                  v-android-only
                >
                  <SvgIcon
                    icon-class="ad-color"
                    class="fsize-16"
                  />
                  <div class="tips-text">{{ t('adsToCrystal') }}</div>
                </div>
              </div>
              <div class="agent-options">
                <div
                  v-if="agentMessage.resident_function.includes('call')"
                  class="collapse-item"
                  :style="!agentMessage.ai_chat_setting?.intimacy_interest_tags?.includes('phone_call') && { opacity: 0.5 }"
                  @click="enterPhoneCall"
                >
                  <IconSvgPhone class="fsize-32" />
                  <div class="fsize-12">{{ t('makeCall') }}</div>
                </div>
                <div
                  v-if="agentMessage.resident_function.includes('gift')"
                  class="collapse-item"
                  @click="giftVisible = true"
                >
                  <IconSvgGift class="fsize-32" />
                  <div class="fsize-12">{{ t('giftItems') }}</div>
                </div>
                <div
                  class="collapse-item"
                  @click="restartChat"
                >
                  <IconSvgAgentRestart class="fsize-32" />
                  <div class="fsize-12">{{ t('startNewChat') }}</div>
                </div>
                <div
                  v-if="agentMessage.resident_function.includes('dress')"
                  class="collapse-item"
                  @click="skinVisible = true"
                >
                  <IconSvgSkin class="fsize-32" />
                  <div class="fsize-12">{{ t('outfits') }}</div>
                </div>
                <div
                  v-if="agentMessage.resident_function.includes('history')"
                  class="collapse-item"
                  @click="goStory"
                >
                  <IconSvgStory class="fsize-32" />
                  <div class="fsize-12">{{ t('story') }}</div>
                </div>
                <div
                  class="collapse-item"
                  @click="feedbackVisible = true"
                >
                  <IconSvgReport class="fsize-32" />
                  <div class="fsize-12">{{ t('report') }}</div>
                </div>
              </div>
            </div>
            <AttachLongButton
              v-else-if="expandOptionsStatus.attachLong"
              v-model="expandOptionsStatus.attachLong"
              v-model:loading="loadingStatus.attachLongBtn"
              :options="attachBtnList"
              @submit="blockSendQuestion"
            />
          </Transition>
        </div>
      </div>
      <GlobalShortButton
        v-show="btnShowing.globalShort"
        v-model="btnShowing.globalShort"
        @submit="blockSendQuestion"
        @restart="restartChat"
        :options="blockBtnList"
      />
    </div>
    <div
      v-if="flagStatus.live2d"
      ref="live2dClear"
      class="live2d-clear"
    ></div>
    <ChapterAnimation
      :chapter="chapterContent"
      v-model="btnShowing.chapterAnimate"
      v-if="btnShowing.chapterAnimate"
    />
    <IntimacyAnimation
      v-if="intimacyVisible"
      v-model="intimacyVisible"
    />
    <IntimacyLevelPopup
      v-model="intimacyLevelVisible"
      :current-level="agentMessage.ai_chat_setting.intimacy_level"
      :current-value="agentMessage.ai_chat_setting.intimacy_now"
      @send-gift="giftVisible = true"
    />
    <SkinPopup
      v-model="skinVisible"
      @get-dress="getDress"
      @switch="handleSkinSwitch"
    />
    <RecordPermissionDialog
      v-model="permissionVisible"
      @get-permission="phoneCallPermission"
    />
    <SendGiftPopup
      v-model="giftVisible"
      @trigger-gift-message="triggerGiftMessage"
    />
    <KeepAlive>
      <Live2D
        ref="l2dRef"
        v-if="flagStatus.live2d"
        :emo="l2dEmo"
        :style="!flagStatus.live2d && { opacity: 0 }"
        :is-clear-mode="isClearMode"
        :asset-url="live2dResource.model_json"
        :config-parameter="live2dResource.parameters"
        :change-url="live2dResource.change_model_json"
        :action-list="live2dResource.action_list"
        :back-pic="live2dResource.back_pic"
        :params="{
          scale: live2dResource.scale,
          rotate: live2dResource.rotate / 360,
          anchor: {
            x: live2dResource.vertical,
            y: live2dResource.horizontal
          }
        }"
      />
    </KeepAlive>
    <PhoneCall
      :key="Date.now()"
      v-model="flagStatus.phoneCall"
      v-if="flagStatus.phoneCall"
      :audio-context="runningContextAudioForPhone"
      @get-record="permissionInit"
    />
    <CallTimeTopUpPopup v-model="topUpVisible" />
    <ContentGamePopup v-model="contentGameVisible" />
  </div>
</template>
<style>
p {
  margin: 0;
}

em {
  margin-right: 4px;
  opacity: 0.5;
}

code {
  white-space: pre-wrap;
}
</style>
<style scoped lang="scss">
@import 'src/assets/styles/chatbox';

@property --chat-mask-start {
  syntax: '<length-percentage>';
  inherits: false;
  initial-value: 50px;
}

@property --chat-mask-end {
  syntax: '<length-percentage>';
  inherits: false;
  initial-value: 75px;
}

.no-selected {
  pointer-events: none;
}

// 添加过渡动画样式
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.whole-chat-container {
  position: relative; // 添加相对定位
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden; // 改为hidden，不再使用滚动
  overscroll-behavior: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .video-bg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;

    :global(.chat-video) {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .top-bar {
    position: fixed;
    top: 0;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 57px;
    padding: 5px 12px 14px 6px;
    overscroll-behavior: none;
    background: linear-gradient(180deg, rgba(24, 24, 24, 50%) 0%, rgba(24, 24, 24, 0%) 100%);
    will-change: transform, opacity; // 优化GPU加速
    backface-visibility: hidden; // 减少重绘

    .home {
      margin-left: 4px;
      background: #18181899;
      backdrop-filter: blur(13px);
      border-radius: 10px;
    }

    .right-btn {
      display: flex;
      column-gap: 10px;
      align-items: center;
      justify-content: center;
    }

    .switch-btn {
      width: 32px;
      height: 32px;
      background: rgba(24, 24, 24, 60%);
      backdrop-filter: blur(16px);
      border-radius: 12px;
    }

    .head-message {
      position: relative;
      display: flex;
      column-gap: 8px;
      align-items: center;
      justify-content: center;
      background: rgba(24, 24, 24, 60%);
      backdrop-filter: blur(16px);
      border-radius: 28px;

      .heart-down {
        position: absolute;
        bottom: 0;
        left: 50%;
        z-index: 4;
        display: flex;
        align-items: center;
        justify-content: center;
        //width: 36px;
        //height: 56px;
        width: 16px;
        height: 16px;
        font-size: 12px;
        font-weight: bold;
        transform: translate(-50%, 40%);

        .num {
          position: absolute;
          bottom: 0;
          z-index: 4;
          font-size: 10px;
          color: $livCoTextColor;
          transform: translateY(-20%);
        }
      }

      .avatar-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        margin: 3px;
        background: conic-gradient(from -180deg, $livCoThemeColor var(--percentage), rgba(235, 223, 172, 30%) var(--percentage) 100%);
        border-radius: 50%;

        .avatar {
          width: 28px;
          height: 28px;
          background: url('@/assets/images/normal-holder.png') no-repeat center / cover;
          border-radius: 50%;
        }
      }

      .name {
        display: flex;
        align-items: center;
        margin-right: 8px;
        font-family: IBMSerif, Roboto, serif;
        font-size: 14px;
      }
    }
  }

  .exit-l2d {
    position: fixed;
    top: 8px;
    z-index: 3;
    width: 32px;
    height: 32px;
    margin-left: 16px;
    font-size: 14px;
    background: #18181899;
    backdrop-filter: blur(14px);
    border-radius: 12px;
  }

  .chat-page {
    z-index: 2;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    align-items: flex-end;
    width: 100%;
    height: 100%;
    overscroll-behavior: none;
    will-change: transform, opacity; // 优化GPU加速
    backface-visibility: hidden; // 减少重绘
  }

  .chat {
    position: relative;
    z-index: 1;
    display: flex;
    flex: 1;
    flex-direction: column;
    row-gap: 16px;
    width: 100%;
    height: 100%;
    padding: 64px 0 20px;
    overflow: auto;
    transition:
      --chat-mask-start 0.2s linear,
      --chat-mask-end 0.2s linear;
    mask-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0%) var(--chat-mask-start),
      rgba(0, 0, 0, 100%) var(--chat-mask-end),
      rgba(0, 0, 0, 100%) calc(100% - 20px),
      rgba(0, 0, 0, 0%) 100%
    );

    .half-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 50%;
      background: transparent;
    }

    :deep(.van-popover__wrapper) {
      display: block;
    }

    .whole-chat-item {
      .chat-item-loading {
        display: flex;
        flex-direction: column;
        column-gap: 8px;
        align-items: flex-start;
        align-self: start;
        width: fit-content;
        max-width: 95%;
        color: #2d2d2d;
        background: rgba(255, 255, 255, 95%);
        backdrop-filter: blur(32px);
        border-radius: 12px;

        .chat-record {
          flex: 1;
          width: 100%;
          padding: 12px;
          overflow-wrap: break-word;

          :deep(.van-popover__content) {
            border-radius: 12px;
          }

          :deep(img) {
            width: 100%;
            min-width: 300px;
            min-height: 200px;
          }
        }
      }
    }
  }

  .undress-gift {
    z-index: 3;
    display: flex;
    column-gap: 8px;
    align-items: center;
    width: 100%;
    padding: 0 0 8px;

    .undress {
      display: flex;
      column-gap: 8px;
      align-items: center;
      justify-content: center;
      padding: 7px 12px;
      font-weight: 400;
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 20%);
      border-radius: 12px;
    }

    .gift {
      display: flex;
      column-gap: 8px;
      align-items: center;
      justify-content: center;
      padding: 3px;
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 20%);
      border-radius: 12px;

      img {
        width: 24px;
        height: 24px;
      }
    }

    .content-game-list {
      display: flex;
      flex: 1;
      column-gap: 8px;
      height: fit-content;
      padding: 0;
      overflow: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      .content-game {
        display: flex;
        flex-shrink: 0;
        column-gap: 2px;
        align-items: center;
        justify-content: center;
        padding: 8px;
        font-size: 12px;
        font-weight: 400;
        line-height: 12px;
        color: #fff;
        text-align: left;
        background: #ffffff14;
        border-radius: 11px;
      }
    }
  }

  .accordion {
    position: relative;
    z-index: 4;
    width: 100%;
    padding: 12px 0 12px 12px;
    background: rgba(24, 24, 24, 40%);
    backdrop-filter: blur(32px);
    border-radius: 8px 8px 0 0;

    .scroll-top {
      position: absolute;
      top: -58px;
      right: 12px;
      width: 42px;
      height: 42px;
      background: rgba(62, 62, 62, 70%);
      backdrop-filter: blur(64px);
      border: 1px solid rgba(255, 255, 255, 10%);
      border-radius: 52px;
    }

    .talk-mask {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 208px;
      pointer-events: none;
      background: linear-gradient(360deg, #000 0%, #000 55%, rgba(0, 0, 0, 0%) 100%);

      .mask-text {
        position: absolute;
        bottom: 84px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        font-size: 12px;
      }
    }

    .input-container {
      display: flex;
      column-gap: 12px;
      align-items: center;
      width: 100%;
      padding-right: 12px;

      :deep(.van-field) {
        min-height: 42px;
        padding-top: 4px;
        padding-right: 8px;
        padding-bottom: 4px;

        &::after {
          display: none;
        }

        textarea::placeholder {
          color: #ffffff80;
        }
      }

      .lock-layer {
        display: flex;
        flex: 1;
        column-gap: 8px;
        align-items: center;
        justify-content: center;

        img {
          width: 20px;
          height: 20px;
        }
      }
    }

    .input {
      background: rgba(255, 255, 255, 15%);
      border: 1px solid rgba(223, 219, 255, 20%);
      border-radius: 16px;

      .options-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        font-size: 18px;
        background: #ffffff1f;
        border-radius: 12px;
      }
    }

    .talk-btn {
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 42px;
      padding: 0 12px;
      user-select: none;
      background: #ffffff4d;
      border-radius: 16px;

      .wave {
        flex: 1;
        height: 100%;
      }
    }

    .expand-option-container {
      overflow: hidden;
      transition: height 0.2s ease-out;
    }

    .crystal-add {
      display: flex;
      column-gap: 8px;
      align-items: center;
      justify-content: flex-start;
      padding: 8px 12px 8px 0;
      margin-top: 16px;
      margin-bottom: 8px;

      .crystal-balance {
        display: flex;
        column-gap: 4px;
        align-items: center;
        padding: 4px 8px;
        font-size: 14px;
        background: #ffffff1a;
        border-radius: 24px;
      }

      .ads-tips {
        display: flex;
        column-gap: 4px;
        align-items: center;
        padding: 4px 12px;
        background: url('@/assets/images/chat/ad-tips-bg.png') left / cover no-repeat;
        border-radius: 6px;

        .tips-text {
          font-size: 11px;
          color: #ffb485;
        }
      }
    }

    .agent-options {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      row-gap: 18px;
      width: 100%;
      padding-right: 12px;

      .collapse-item {
        position: relative;
        display: flex;
        flex-direction: column;
        row-gap: 12px;
        align-items: center;
        justify-content: flex-start;
        min-height: 78px;
        padding: 8px;
        overflow: hidden;

        svg {
          flex-shrink: 0;
        }

        div {
          flex-shrink: 0;
          text-align: center;
        }

        .used-act {
          position: absolute;
          top: 0;
          left: 0;
          padding: 4px 6px;
          font-size: 8px;
          background: linear-gradient(140deg, #ff35f2 0%, #98f 100%);
          border-radius: 12px 12px 12px 4px;
        }
      }
    }
  }

  .content-game-btn {
    z-index: 3;
    width: 100%;
    padding: 12px 16px;
    background: rgba(24, 24, 24, 40%);
    backdrop-filter: blur(88px);
    border-radius: 12px 12px 0 0;

    .go {
      display: flex;
      column-gap: 4px;
      align-items: center;
      justify-content: center;
      padding: 16px 0;
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      color: #fff;
      text-align: center;
      background: rgba(255, 255, 255, 15%);
      border-radius: 16px;
    }

    .later {
      padding: 16px 0;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      color: #fff;
      text-align: center;
      background: rgba(255, 255, 255, 15%);
      border-radius: 16px;
    }
  }

  .live2d-clear {
    position: absolute; // 改为绝对定位
    top: 0;
    left: 0;
    z-index: 2;
    display: flex;
    flex-shrink: 0;
    align-items: flex-end;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
}
</style>
