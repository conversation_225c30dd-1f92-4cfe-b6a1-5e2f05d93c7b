<script setup lang="ts">
import { useModal } from '@/hooks/useModal.ts'
import { getGift } from '@/api/agentChat'
import { GiftListResType } from '@/api/agentChat/types.ts'
import { getDress } from '@/api/propertyStore'
import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'
import PurchaseSkinPopUp from '@/components/SkinGiftGroup/PurchaseSkinPopUp.vue'
import { useBackHistory } from '@/hooks/useCommon.ts'
import { usePopupMemoryStore } from '@/stores/modules/popupMemory.ts'
const { t } = useI18n()

const tabMap = [
  {
    name: 'skin',
    label: t('outfits')
  },
  {
    name: 'gift',
    label: t('giftItems')
  }
]

const route = useRoute()
const router = useRouter()
const popupMemoryStore = usePopupMemoryStore()
const active = ref('skin')

const purchaseVisible = ref(false)
const purchaseSkinVisible = ref(false)
const threeDVisible = ref(false)
const previewVisible = ref(false)
const previewSkinData = ref()

const giftList = ref<GiftListResType[]>([])
const dressList = ref([])
const purchaseGiftData = ref()
const purchaseSkinData = ref()
const keyword = ref('')
const openPurchaseGift = (item: GiftListResType) => {
  if (item.is_weekly_limit_buy || item.is_day_limit_buy) {
    useModal({
      message: item.is_weekly_limit_buy ? t('weeklyPurchaseLimit') : t('dailyPurchaseLimit')
    })
    return
  }
  purchaseGiftData.value = item
  purchaseVisible.value = true
}

const openPurchaseSkin = (item: DressType, aiID: number) => {
  if (item.is_buy) {
    previewVisible.value = true
    previewSkinData.value = { ai_id: aiID, ...item }
  } else {
    if (item.type === SkinTypeEnum.Normal) {
      purchaseSkinData.value = item
      purchaseSkinVisible.value = true
    } else if (item.type === SkinTypeEnum.ThreeD) {
      threeDVisible.value = true
      purchaseSkinData.value = item
    }
  }
}

const getGiftList = () => {
  const popup = popupMemoryStore.getPopupMemory()
  if (route.query.tab === 'gift') active.value = 'gift'
  const { close } = useModal({
    message: t('loading'),
    loading: true,
    autoClose: false
  })
  getGift({
    limit: 9999,
    page: 1
  })
    .then((res) => {
      const { code, data } = res
      if (code === 200) {
        giftList.value = data.list
        if (popup) {
          openPurchaseGift(data.list.find((item) => item.id === popup.id))
          popupMemoryStore.resetAllPopupMemory()
        }
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      close()
    })
}

const getDressList = (name?: string) => {
  if (route.query.tab === 'skin') active.value = 'skin'
  const popup = popupMemoryStore.getPopupMemory()
  const { close } = useModal({
    message: t('loading'),
    loading: true,
    autoClose: false
  })
  getDress({
    limit: 9999,
    page: 1,
    name
  })
    .then((res) => {
      const { code, data } = res
      if (code === 200) {
        console.log(data)
        dressList.value = data
        if (popup) {
          let result: { outerId: number; dressItem: DressType } = {
            dressItem: {} as DressType,
            outerId: undefined
          }
          for (let item of data) {
            const foundItem = item.dress_list.find((dress) => dress.id === popup.id)
            if (foundItem) {
              result = { outerId: item.id, dressItem: foundItem }
              break // 找到后直接退出循环
            }
          }

          openPurchaseSkin(result.dressItem, result.outerId)
          popupMemoryStore.resetAllPopupMemory()
        }
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      close()
    })
}

getGiftList()
getDressList()

watch(keyword, (val) => {
  getDressList(val)
})
</script>

<template>
  <div class="store-container">
    <div class="top-bar flex-between-center">
      <div class="flex-center-center cg-6">
        <SvgIcon
          icon-class="left-arrow"
          class="fsize-24"
          @click="useBackHistory"
        />
        <div>{{ t('store') }}</div>
      </div>
      <div class="flex-center-center cg-6">
        <CrystalBalance />
        <!--        <CoinBalance />-->
      </div>
    </div>

    <div class="property-tab">
      <div class="flex-center-center cg-24">
        <div
          class="tab"
          v-for="(tab, index) in tabMap"
          :key="index"
          :class="{ 'active-tab': active === tab.name }"
          @click="active = tab.name"
        >
          {{ tab.label }}
        </div>
      </div>
      <SvgIcon
        @click="router.push('/propertyStore/backPack')"
        icon-class="backpack"
        class="fsize-22"
      />
    </div>
    <div
      class="property-list"
      v-show="active === 'gift'"
    >
      <div
        v-for="item in giftList"
        :key="item.id"
      >
        <GiftItem
          :style="
            (item.is_day_limit_buy || item.is_weekly_limit_buy) && {
              opacity: 0.5
            }
          "
          :gift="item"
          @click="openPurchaseGift(item)"
        />
      </div>
    </div>
    <div
      class="dress-list"
      v-show="active === 'skin'"
    >
      <div class="agent-list">
        <van-field
          @keydown.enter="getDressList(keyword)"
          class="search-bar"
          v-model="keyword"
          clearable
          clear-icon="close"
          :placeholder="t('searchAgentName')"
          @clear="getDressList()"
        >
          <template #left-icon>
            <SvgIcon
              @click="getDressList(keyword)"
              icon-class="blur-search"
              class="fsize-18"
          /></template>
        </van-field>
        <div
          class="agent-item"
          v-for="item in dressList"
          :key="item.id"
        >
          <div class="flex-start-center cg-8">
            <div
              class="avatar"
              :style="{ background: `url('${item.avatar_url}') no-repeat center / cover` }"
              @click="router.push(`/chat/${item.id}`)"
            ></div>
            {{ item.name }}
          </div>

          <div class="skin-container">
            <SkinItem
              v-for="dressItem in item.dress_list"
              :key="dressItem.id"
              :skin-item="dressItem"
              class="skin-item"
              @click="openPurchaseSkin(dressItem, item.id)"
            />
          </div>
        </div>
      </div>
    </div>
    <PurchaseGiftPopup
      v-model="purchaseVisible"
      :gift-item="purchaseGiftData"
      @get-list="getGiftList"
    />
    <PurchaseSkinPopUp
      v-model="purchaseSkinVisible"
      :skin-item="purchaseSkinData"
      @get-list="getDressList(keyword)"
    />
    <Unlock3DPopup
      v-model="threeDVisible"
      :skin-item="purchaseSkinData"
      @get-list="getDressList"
    />
    <PhotoPreviewDialog
      v-model="previewVisible"
      :photo-item="previewSkinData"
      is-background
    />
  </div>
</template>

<style scoped lang="scss">
.store-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .top-bar {
    padding: 16px 12px;
  }

  .property-tab {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;

    .tab {
      position: relative;
      font-weight: 400;
      color: rgba(255, 255, 255, 50%);
    }

    .active-tab {
      position: relative;
      font-weight: 600;
      color: rgba(255, 255, 255, 100%);
    }
  }

  .property-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 0 12px 24px;
    overflow-y: auto;
  }

  .dress-list {
    display: flex;
    flex-direction: column;
    padding: 0 12px 24px;
    overflow-y: hidden;

    .search-bar {
      display: flex;
      flex-shrink: 0;
      width: 100%;
      padding: 8px 16px;
      margin-bottom: -4px;
      background: #232222;
      border-radius: 12px;

      :deep(.van-field__left-icon) {
        display: flex;
        align-items: center;
        margin-right: 12px;
      }
    }

    .agent-list {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 24px;
      overflow-y: auto;

      .agent-item {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .avatar {
          width: 28px;
          height: 28px;
          border-radius: 50%;
        }
      }

      .skin-container {
        display: flex;
        flex-wrap: nowrap;
        column-gap: 12px;
        overflow-x: auto;

        .skin-item {
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
