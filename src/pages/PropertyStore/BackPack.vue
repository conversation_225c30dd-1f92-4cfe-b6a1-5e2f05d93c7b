<script setup lang="ts">
import { getMyDressList, getMyGiftList } from '@/api/propertyStore'
import { useBackHistory } from '@/hooks/useCommon.ts'
const { t } = useI18n()

// const tabMap = [
//   {
//     name: 'gift',
//     label: 'Character Gifts'
//   },
//   {
//     name: 'skin',
//     label: 'DressUp'
//   }
// ]
const active = ref('gift')
const loading = ref(true)
const isEmptyList = computed(() => {
  if (active.value === 'gift') {
    return !giftList.value.length
  } else {
    return !dressList.value.length
  }
})

const giftList = ref([])
const dressList = ref([])

const getGiftList = () => {
  loading.value = true
  getMyGiftList({
    limit: 9999,
    page: 1
  })
    .then((res) => {
      const { code, data } = res
      if (code === 200) {
        giftList.value = data.list
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      loading.value = false
    })
}

const getDressList = () => {
  getMyDressList({
    limit: 9999,
    page: 1
  })
    .then((res) => {
      const { code, data } = res
      if (code === 200) {
        dressList.value = data.map((item) => {
          return {
            ...item,
            is_buy: 1
          }
        })
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {})
}

getGiftList()
getDressList()
</script>

<template>
  <div class="store-container">
    <div class="top-bar flex-center-center">
      <div class="flex-center-center cg-6">
        <SvgIcon
          icon-class="left-arrow"
          class="arrow fsize-24"
          @click="useBackHistory"
        />
        <div>{{ t('backpack') }}</div>
      </div>
    </div>

    <div class="property-tab">
      <!--      <div class="flex-center-center cg-24">-->
      <!--        <div-->
      <!--          class="tab"-->
      <!--          v-for="(tab, index) in tabMap"-->
      <!--          :key="index"-->
      <!--          :class="{ 'active-tab': active === tab.name }"-->
      <!--          @click="active = tab.name"-->
      <!--        >-->
      <!--          {{ tab.label }}-->
      <!--        </div>-->
      <!--      </div>-->
      <van-tabs
        class="order-tabs"
        v-model:active="active"
        line-width="35%"
      >
        <van-tab
          :title="$t('giftItems')"
          name="gift"
        />
        <van-tab
          :title="$t('outfits')"
          name="skin"
        />
      </van-tabs>
    </div>
    <TailChaseLoading v-if="loading" />
    <Empty
      v-else-if="isEmptyList"
      :text="active === 'gift' ? t('nothing') : t('nothing')"
    />
    <div class="property-list">
      <div
        v-show="active === 'gift'"
        v-for="item in giftList"
        :key="item.id"
      >
        <OwnGiftItem :gift="item" />
      </div>
      <div
        v-show="active === 'skin'"
        v-for="item in dressList"
        :key="item.id"
        class="flex-center-center"
      >
        <SkinItem :skin-item="item" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.store-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .top-bar {
    position: relative;
    padding: 16px 12px;

    .arrow {
      position: absolute;
      left: 12px;
    }
  }

  :deep(.van-tabs__nav) {
    background: linear-gradient(180deg, rgba(24, 24, 24, 50%) 0%, rgba(24, 24, 24, 0%) 100%);
  }

  :deep(.van-tab--active) {
    color: #fff;
  }

  :deep(.van-tabs__line) {
    width: 50% !important;
    height: 1px !important;
    background: $livCoThemeColor;
  }

  :deep(.van-tabs) {
    .van-tabs__content {
      height: calc(100% - 46px);
      overflow: hidden auto;
    }
  }

  .property-tab {
    width: 100%;
    margin-bottom: 16px;
  }

  .property-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 0 12px;
  }
}
</style>
