<template>
  <div v-if="show">
    <slot> </slot>
  </div>
</template>

<script setup lang="ts">
// paypal开发文档：https://developer.paypal.com/sdk/js/reference/
import { loadScript } from '@/utils/tools.ts'
// import { confirmPayPalResult } from '@/api/purchase/index.ts'
// import { payment } from '@/api/mine/index.ts'
import { payment } from '@/api/mine/index'
import { confirmPayPalResult } from '@/api/purchase'
import router from '@/router'

const props = defineProps({
  dataObj: {
    type: Object,
    default: () => ({})
  },
  order: {
    type: Object,
    default: () => ({})
  }
})

console.log(props.dataObj)

// const emits = defineEmits(['fail', 'success'])
const sdk = {
  id: 'eros_paypalSDK',
  url: `https://www.paypal.com/sdk/js?client-id=${props.dataObj?.merchant_info?.client_id}&currency=USD&components=buttons,funding-eligibility,marks`
}
// https://www.paypal.com https://sandbox.paypal.com
// const clientId = 'AX0NU4mOaqpYare372sxUeEEtL3rwT_PYpx-cKdaTJXdETnp9bRR0q6HvoHcRtIix4nYa2ptfmnMHyk3'
const show = ref<boolean>(true)
const params = {
  order_id: '',
  paypal_order_id: ''
}
function createBtn(isSuccess: number) {
  console.log('paypalSDK', isSuccess)
  if (isSuccess) {
    // @ts-ignore
    paypal
      .Buttons({
        // https://developer.paypal.com/sdk/js/reference/#style
        style: {
          layout: 'vertical',
          color: 'white',
          shape: 'rect',
          // height: 32, // 25~55
          label: 'paypal',
          tagline: false
        },
        commit: false,
        createOrder() {
          return payment({
            amount_id: props.order?.amount_id,
            pay_channel: 'paypal'
          })
            .then((res) => {
              const { code, data } = res
              if (code === 200) {
                params.order_id = data.order_id
                params.paypal_order_id = data.paypal_order_id
              }
              return data
            })
            .then((data) => data.paypal_order_id)
            .catch(() => {
              handleTo('fail')
            })
        },
        onApprove() {
          return confirmPayPalResult({
            ...params
          })
            .then(() => {
              handleTo('success')
            })
            .catch(() => {
              handleTo('fail')
              // return actions.restart()
            })
        },
        onInit() {
          console.log('onInit')
          // 按钮的禁用和启用
          // actions.disable()
          // actions.enable()
        },
        // onClick() {
        //   console.log('onClick')
        // },
        onCancel(data: any) {
          handleTo('fail')
          console.log('onCancel', data)
        },
        onError(err: string) {
          handleTo('fail')
          console.log('onError', err)
        }
      })
      .render('#paypal-button')
  } else {
    show.value = false
  }
}

function handleTo(type: string) {
  console.log(type, '支付状态')
  if (type === 'success') {
    // window.location.href = window.location.origin + `/Order?order_id=${params?.order_id}`
    router.push({ name: 'Order', query: { order_id: params?.order_id } })
  } else {
    goBack()
  }
}

function goBack() {
  const buyType = sessionStorage.getItem('buyType') || ''
  if (buyType === 'Member') {
    router.push({ name: 'Member' })
  } else {
    router.push({ name: 'crystal' })
  }
}

onMounted(() => {
  loadScript(sdk.url, sdk.id, createBtn, {
    defer: true,
    async: true
  })
})
</script>

<style lang="scss">
.paypal-button {
  position: fixed;
  bottom: 54px;
  left: 5%;
  // width: 100px;
  // position: absolute;
  z-index: 2;
  width: 90%;
  height: 54px;
  opacity: 0;
}
</style>
