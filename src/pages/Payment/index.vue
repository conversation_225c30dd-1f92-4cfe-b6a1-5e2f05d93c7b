<script setup lang="ts">
import { country_list } from '@/api/mine/index'
import { useModal } from '@/hooks/useModal'
import PayPal from './components/PayPal.vue'
import { CountryItem, PaymentMethodItem, SupportItem } from '@/api/mine/type.ts'
import { payment } from '@/api/mine/index'
definePage({
  name: 'Payment',
  meta: {
    level: 2
  }
})
interface IPayParams {
  amount_id: string
  pay_channel: string
}
const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const loading = ref<boolean>(false)
// const amount_id = ref<string>('')
const showPicker = ref<boolean>(false)
const country_columns = ref<any[]>([])
const customFieldName = {
  text: 'country_name',
  value: 'area_code'
}
const amountInfo = reactive({
  amount_id: route.query.amount_id,
  total_amount: 0,
  total_amount_discount: 0,
  config_name: '',
  network_name: '',
  time_str: ''
})
const orderInfo = reactive({
  googlePay: {},
  payPal: {
    amount_id: amountInfo.amount_id
  },
  order_id: ''
})
const activeCountryObj = ref<CountryItem>({})
const activePayMethodObj = ref<PaymentMethodItem>({})
const activeSupportObj = ref<SupportItem>({})
const payWayList = ref<String[]>(['free_test'])

function submit() {
  if (!activePayMethodObj.value.pay_channel) return useModal({ message: t('selectPaymentMethod') })
  handleSubmit()
}

function handleSubmit() {
  if (payWayList.value.includes(activePayMethodObj.value.pay_channel)) {
    let data = {
      amount_id: amountInfo.amount_id as string,
      pay_channel: activePayMethodObj.value.pay_channel
    }
    console.log(data)
    handlePay(data)
  }
}

function handlePay(data: IPayParams) {
  const { close } = useModal({
    message: t('loading'),
    loading: true,
    autoClose: false
  })
  payment(data)
    .then((res) => {
      orderInfo.order_id = res.data.order_id
      useModal({
        message: t('paySuccess'),
        duration: 1500,
        onClose: () => {
          router.replace({
            name: 'Order',
            query: { order_id: orderInfo.order_id }
          })
        }
      })
    })
    .finally(() => {
      close()
    })
}

const onConfirm = (e: { selectedOptions: { country_name: string; area_code: string; payment_method: [] }[] }) => {
  showPicker.value = false
  activeCountryObj.value = e.selectedOptions[0]
  activeSupportObj.value = {}
  activePayMethodObj.value = {}
}

function get_country_list() {
  country_list().then((res) => {
    country_columns.value = res.data.list
    activeCountryObj.value = country_columns.value[0] || {}
    if (activeCountryObj.value.payment_method[0].method == 'PayPal' || activeCountryObj.value.payment_method.length != 1) return
    activeSupportObj.value = activeCountryObj.value.payment_method[0].support[0]
    activePayMethodObj.value = activeCountryObj.value.payment_method[0]
  })
}

function checkPayChannel(paymentItem: PaymentMethodItem, supportItem: SupportItem) {
  activePayMethodObj.value = paymentItem
  activeSupportObj.value = supportItem
}

get_country_list()
</script>

<template>
  <div class="payment-container">
    <van-nav-bar
      :title="t('cashRegister')"
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-24"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <div class="content">
      <div class="pt-16 pb-16 title">{{ t('payAmount') }}</div>
      <div class="money mb-18">
        <span class="fsize-24 money1">$</span>
        <span class="fsize-38 money2">{{ route.query.price }}</span>
      </div>
      <div class="pt-24 pb-16 title2">{{ t('selectPayCountry') }}</div>
      <div
        class="countrybox flex-between-center pl-16 pr-16"
        @click="showPicker = true"
      >
        <div class="flex-center-center">
          <SvgIcon
            icon-class="location"
            class="fsize-18 mr-4"
          />
          <div>{{ activeCountryObj.country_name }}</div>
        </div>
        <SvgIcon
          icon-class="r-arrow"
          class="fsize-20"
        />
      </div>
      <div class="pt-24 pb-16 title2">{{ t('selectPaymentWay') }}</div>
      <div class="paybox">
        <template v-for="payItem in activeCountryObj.payment_method ?? []">
          <template v-for="supportItem in payItem?.support">
            <pay-pal
              v-if="payItem.pay_channel === 'paypal'"
              :key="supportItem?.id + 'paypal'"
              :dataObj="{ ...supportItem }"
              :order="orderInfo.payPal"
              :class="{
                'pay-item-box': true,
                lightPayitem: activeSupportObj.id === supportItem.id && activeSupportObj.name === supportItem.name
              }"
              @click="checkPayChannel(payItem, supportItem)"
            >
              <div class="pay-item">
                <div class="pay-img">
                  <img
                    :src="supportItem?.logo"
                    :alt="supportItem?.name"
                  />
                </div>
                <p class="pay-name">
                  {{ supportItem?.name || supportItem?.support_key }}
                </p>
              </div>
              <div
                :style="{
                  'z-index': activePayMethodObj.pay_channel === 'paypal' ? 2 : -1,
                  'pointer-events': activePayMethodObj.pay_channel === 'paypal' ? 'all' : 'none'
                }"
                id="paypal-button"
                class="paypal-button"
              ></div>
            </pay-pal>
            <div
              v-else
              :key="supportItem?.id"
              :class="{
                'pay-item-box': true,
                lightPayitem: activeSupportObj.id === supportItem.id && activeSupportObj.name === supportItem.name
              }"
              @click="checkPayChannel(payItem, supportItem)"
            >
              <div class="pay-item">
                <div class="pay-img">
                  <van-image
                    class="img"
                    fit="contain"
                    :src="supportItem.logo"
                    :alt="supportItem.name"
                    lazy-load
                  >
                    <template #loading>
                      <van-loading
                        type="spinner"
                        size="20"
                      />
                    </template>
                    <template #error>
                      <van-icon
                        name="photo-fail"
                        size="20"
                      />
                    </template>
                  </van-image>
                </div>
                <p class="pay-name">
                  {{ supportItem.name || supportItem.support_key }}
                </p>
              </div>
            </div>
          </template>
        </template>
      </div>
    </div>
    <div class="btn-banner flex-center-center">
      <div
        class="buling flex-center-center"
        @click="submit"
      >
        {{ t('immediatePayment') }}
        <van-loading
          v-if="loading"
          type="spinner"
          color="white"
          size="24px"
        />
      </div>
    </div>

    <van-popup
      v-model:show="showPicker"
      round
      position="bottom"
    >
      <van-picker
        :columns="country_columns"
        @cancel="showPicker = false"
        @confirm="onConfirm"
        :columns-field-names="customFieldName"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="scss">
:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
  background: $livCoBackColor;
}

.payment-container {
  height: 100%;
  padding-top: 48px;
}

.content {
  height: 100%;
  padding: 8px 16px;
  // padding-top: 54px;
  background: $livCoBackColor;

  .title {
    width: 100%;
    font-size: 14px;
    font-weight: 400;
    color: rgba(255, 255, 255, 50%);
    text-align: center;
  }

  .money {
    width: 100%;
    font-weight: 800;
    text-align: center;
  }

  .title2 {
    font-size: 14px;
    font-weight: 600;
  }

  .countrybox {
    width: 100%;
    height: 52px;
    background: #232222;
    border-radius: 16px;
  }

  .paybox {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 4px 10px;
    padding-bottom: 100px;

    .pay-item-box {
      box-sizing: border-box;
      font-size: 10px;
      font-weight: 500;
      color: #8b8889;
    }

    .pay-item {
      box-sizing: border-box;
      width: 100%;
      text-align: center;

      .pay-img {
        box-sizing: border-box;
        height: 56px;
        padding: 6px 0;
        background: #232222;
        background-repeat: no-repeat;
        background-position: 50%;
        background-size: 70%;
        border: 2px solid transparent;
        border-radius: 12px;
        transition: all 0.28s;
      }

      .img,
      img {
        max-width: 100%;
        height: 100%;
        margin: 0 auto;
        border-radius: 6px;
      }

      .pay-name {
        margin-top: 5px;
        font-size: 11px;
        transition: all 0.28s;
      }
    }

    .lightPayitem {
      .pay-img {
        background: rgba(235, 223, 172, 20%);
        border: 1px solid #ebdfac;
      }

      .pay-name {
        color: $livCoThemeColor;
      }
    }
  }
}

.btn-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  padding: 8px 0 32px;
  background-color: $livCoBackColor;
}

.buling {
  width: 327px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: $livCoTextColor;
  background: $livCoThemeColor;
  border-radius: 40px;
}

.paypal-button {
  bottom: 32px;
}
</style>
