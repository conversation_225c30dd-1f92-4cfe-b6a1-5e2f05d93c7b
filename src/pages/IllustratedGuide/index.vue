<script setup lang="ts">
import { useBackHistory } from '@/hooks/useCommon.ts'
const { t } = useI18n()

const keyword = ref('')
const getDressList = (keywords: string) => {
  console.log(keywords)
}
</script>

<template>
  <div class="skin-container">
    <div class="top-bar flex-center-center">
      <IconSvgLeftArrow
        class="fsize-24 back"
        @click="useBackHistory"
      />
      <div>{{ t('我的图鉴') }}</div>
    </div>
    <div class="scroll-content">
      <van-field
        @keydown.enter="getDressList(keyword)"
        class="search-bar"
        v-model="keyword"
        clearable
        clear-icon="close"
        :placeholder="t('searchAgentName')"
        @clear="getDressList('')"
      >
        <template #left-icon>
          <SvgIcon
            @click="getDressList(keyword)"
            icon-class="blur-search"
            class="fsize-18"
        /></template>
      </van-field>

      <div class="skeleton"></div>

      <div class="agent-tag">
        <div class="agent-item active">全部</div>
        <div
          v-for="item in 10"
          :key="item"
          class="agent-item"
        >
          Rin {{ item }}
        </div>
      </div>

      <div class="agent-skin">
        <div class="series-container">
          <div class="flex-between-center mb-16">
            <div class="series-title">
              校园日常
              <span class="series-num">(4/6)</span>
            </div>
            <div class="gift">
              <div class="gift-inside">
                <IconSvgSkinGift class="fsize-12" />
              </div>
            </div>
          </div>
          <div class="series-skins">
            <div
              v-for="item in 10"
              :key="item"
              class="relative"
            >
              <div
                class="series-skin"
                :class="item === 3 && 'lock'"
              >
                <img
                  src="@/assets/images/mine/49b66793ce076fe5a88ddf758359650563fc73b7.png"
                  alt=""
                  class="skin"
                />
                <img
                  src="@/assets/images/skin/skin-top-mask.png"
                  alt="mask"
                  class="mask-top"
                />
                <div class="mask-text"></div>
                <img
                  src="@/assets/images/skin/skin-bottom-mask.png"
                  alt="mask"
                  class="mask-bottom"
                />
                <div class="skin-describe">This is skin describe 1 2 但是多少度是的是的</div>
                <div class="overlay-radius"></div>
              </div>
              <div
                class="lock"
                v-if="item === 3"
              >
                <IconSvgStoryLock class="fsize-24" />
                <div>未获得</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '@/assets/styles/mixin';

.skeleton {
  @include skeleton;
  width: 100%;
  height: 300px;
}

.skin-container {
  mask-image: linear-gradient(to bottom, #000 0%, #000 calc(100% - 52px), transparent 100%);
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 8px;
  background: url('@/assets/images/skin/bg.png') no-repeat top / contain;

  .top-bar {
    width: 100%;
    padding: 16px 12px;

    .back {
      position: absolute;
      left: 12px;
    }
  }

  .scroll-content {
    flex: 1;
    overflow-y: auto;
  }

  .search-bar {
    display: flex;
    flex-shrink: 0;
    width: 100%;
    padding: 8px 16px;
    background: #232222;
    border-radius: 12px;

    :deep(.van-field__left-icon) {
      display: flex;
      align-items: center;
      margin-right: 12px;
    }
  }

  .agent-tag {
    position: sticky;
    top: 0;
    background: #000;
    z-index: 4;
    display: flex;
    flex-wrap: nowrap;
    gap: 8px 8px;
    margin: 8px 0 14px;
    padding: 8px 0 8px;
    overflow-x: auto;

    .agent-item {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      padding: 6px 12px;
      font-size: 14px;
      color: #fff;
      background: rgba(255, 255, 255, 10%);
      border-radius: 10px;

      &.active {
        color: #ebdfac;
        background: #ebdfac29;
      }
    }
  }

  .series-num {
    font-family: 'DIN Light', serif;
  }

  .gift {
    --gift-progress: 0.25;

    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    padding: 2px;
    background: conic-gradient(from 180deg, #ebdfac 0 calc(var(--gift-progress) * 360deg), #303030 calc(var(--gift-progress) * 360deg));
    border-radius: 50%;
    //box-shadow: 0 0 16px 0 #ebdfac;

    .gift-inside {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #000;
      border-radius: 50%;
    }
  }

  .agent-skin {
    flex: 1;
  }

  .series-skins {
    padding-bottom: 52px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .lock {
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #fff4db;
    opacity: 0.5;
    transform: translate(-50%, -50%);
  }

  .series-skin {
    position: relative;
    aspect-ratio: 114 / 196;
    overflow: hidden;
    border-radius: 24px 0;

    @include gradation-border-with-radius-and-transparent-bg(1px, linear-gradient(161.44deg, #fff4db -0.56%, #ffdd91 100%));

    .overlay-radius {
      @include gradation-border-with-radius-and-transparent-bg(1px, linear-gradient(161.44deg, #fff4db -0.56%, #ffdd91 100%));

      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      border-radius: 24px 16px;
    }

    &.lock {
      opacity: 0.3;
    }

    .mask-top {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }

    .mask-bottom {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 100%;
    }

    .mask-text {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 24%;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0%) 0%, rgba(0, 0, 0, 60%) 29.85%, #000 100%);
      border-radius: 0 20px;
    }

    .skin-describe {
      position: absolute;
      bottom: 8px;
      left: 0;
      width: 100%;
      padding: 0 8px;
      font-size: 10px;
      color: #fff;
    }

    .skin {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style>
