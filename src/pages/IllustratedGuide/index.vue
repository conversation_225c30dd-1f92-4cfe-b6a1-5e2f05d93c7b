<script setup lang="ts">
import { useBackHistory } from '@/hooks/useCommon.ts'
import SkinCard from '@/components/IllustratedGuide/SkinCard.vue'
const { t } = useI18n()

const skinDeailVisible = ref(true)
const loading = ref(false)
const keyword = ref('')
const getDressList = (keywords: string) => {
  console.log(keywords)
}

const selectAgent = (id: number) => {
  console.log(id)
}
</script>

<template>
  <div class="skin-container">
    <div class="top-bar flex-center-center">
      <IconSvgLeftArrow
        class="fsize-24 back"
        @click="useBackHistory"
      />
      <div>{{ t('我的图鉴') }}</div>
    </div>
    <div class="scroll-content">
      <van-field
        @keydown.enter="getDressList(keyword)"
        class="search-bar"
        v-model="keyword"
        clearable
        clear-icon="close"
        :placeholder="t('searchAgentName')"
        @clear="getDressList('')"
      >
        <template #left-icon>
          <SvgIcon
            @click="getDressList(keyword)"
            icon-class="blur-search"
            class="fsize-18"
        /></template>
      </van-field>
      <!--      <Empty class="h-full" />-->
      <div class="agent-tag">
        <div class="agent-item active all">全部</div>
        <div
          v-for="item in 10"
          :key="item"
          class="agent-item"
          @click="selectAgent(item)"
        >
          <img
            src="@/assets/images/mine/49b66793ce076fe5a88ddf758359650563fc73b7.png"
            alt=""
            class="agent-tag-avatar"
          />
          Rin {{ item }}
        </div>
      </div>
      <div
        class="skeleton-container"
        v-if="loading"
      >
        <div class="skeleton-title"></div>
        <div class="flex-center-center cg-8">
          <div class="skeleton-img"></div>
          <div class="skeleton-img"></div>
          <div class="skeleton-img"></div>
        </div>
      </div>

      <div class="agent-skin">
        <SeriesContainer
          :gift-progress="0.5"
          :series-title="'校园日常'"
          :series-num="10"
        >
          <SkinCard
            v-for="item in 10"
            :key="item"
            :skin="item"
          />
        </SeriesContainer>
      </div>
    </div>
    <Transition name="from-bottom">
      <SkinDetail v-model:show="skinDeailVisible" />
    </Transition>
  </div>
</template>

<style scoped lang="scss">
@import '@/assets/styles/mixin';

.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 8px;
  margin-bottom: 16px;

  .skeleton-title {
    @include skeleton;
    width: 30%;
    height: 20px;
    border-radius: 8px;
  }

  .skeleton-img {
    @include skeleton;
    flex: 1;
    height: 200px;
    border-radius: 8px;
  }
}

.skin-container {
  mask-image: linear-gradient(to bottom, #000 0%, #000 calc(100% - 52px), transparent 100%);
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 8px;
  background: url('@/assets/images/skin/bg.png') no-repeat top / contain;

  .top-bar {
    width: 100%;
    padding: 16px 12px;

    .back {
      position: absolute;
      left: 12px;
    }
  }

  .scroll-content {
    flex: 1;
    overflow-y: auto;
  }

  .search-bar {
    display: flex;
    flex-shrink: 0;
    width: 100%;
    padding: 8px 16px;
    background: #232222;
    border-radius: 12px;

    :deep(.van-field__left-icon) {
      display: flex;
      align-items: center;
      margin-right: 12px;
    }
  }

  .agent-tag {
    position: sticky;
    top: 0;
    background: #000;
    z-index: 4;
    display: flex;
    flex-wrap: nowrap;
    gap: 8px 8px;
    margin: 8px 0 14px;
    padding: 8px 0 8px;
    overflow-x: auto;

    .agent-item {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      padding: 6px 12px 6px 8px;
      font-size: 14px;
      color: #fff;
      background: rgba(255, 255, 255, 10%);
      border-radius: 10px;

      .agent-tag-avatar {
        width: 20px;
        height: 20px;
        margin-right: 6px;
        border-radius: 50%;
      }

      &.active {
        color: #ebdfac;
        background: #ebdfac29;
      }

      &.all {
        padding: 6px 12px;
      }
    }
  }

  .agent-skin {
    flex: 1;
  }

  .series-skins {
    padding-bottom: 52px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
}
</style>
