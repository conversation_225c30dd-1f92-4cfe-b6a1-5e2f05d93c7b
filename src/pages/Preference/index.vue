<script setup lang="ts">
import { ResultEnum } from '@/enums/httpEnum.ts'

const { t } = useI18n()
const router = useRouter()
import { preference } from '@/api/login'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
const step = ref(1)
const gender = ref('')
const interest = ref('')
const loading = ref(false)

const selectGender = (value: string) => {
  gender.value = value
  setTimeout(() => {
    step.value = 2
  }, 500)
}

const selectInterest = (value: string) => {
  if (loading.value) return
  loading.value = true
  interest.value = value
  setTimeout(() => {
    sendAnswer()
  }, 500)
}

const sendAnswer = () => {
  preference({
    question1: gender.value === '其他' ? '' : gender.value,
    question2: interest.value
  })
    .then(({ code, data }) => {
      if (code === ResultEnum.SUCCESS) {
        router.replace(`/chat/${data}`)
        eventReport({
          event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
          front_address: ForwardAddressEnum.ENTER_APP,
          ai_id: data
        }).catch((err) => {
          console.warn(err)
        })
        eventReport({
          event_type: EventTypeEnum.QUESTION,
          question_sex: gender.value,
          question_interaction: interest.value,
          question_close: !gender.value || !interest.value ? '跳过' : '提交',
          fenpei_ai_id: data
        })
      }
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<template>
  <div class="preference-container">
    <div class="step">
      <div
        class="step-box"
        :class="{ 'step-active': step >= 1 }"
      ></div>
      <div
        class="step-box"
        :class="{ 'step-active': step >= 2 }"
      ></div>
      <div
        class="jump"
        @click="sendAnswer"
      >
        {{ t('skipOption') }}
      </div>
    </div>
    <div v-show="step === 1">
      <div class="title">
        <div class="big">{{ t('welcomeToEros') }}</div>
        <div class="small">{{ t('selectYourGender') }}</div>
      </div>
      <div class="gender">
        <div class="single">
          <div
            class="circle"
            :class="{ active: gender === '男' }"
            @click="selectGender('男')"
          >
            <IconSvgPreferenceMan class="fsize-48" />{{ t('maleOption') }}
          </div>
        </div>
        <div
          class="circle"
          :class="{ active: gender === '女' }"
          @click="selectGender('女')"
        >
          <IconSvgPreferenceWoman class="fsize-48" />{{ t('femaleOption') }}
        </div>
        <div
          class="circle"
          :class="{ active: gender === '其他' }"
          @click="selectGender('其他')"
        >
          <IconSvgPreferenceOther class="fsize-48" />{{ t('other') }}
        </div>
      </div>
    </div>
    <div v-show="step === 2">
      <div class="title">
        <div class="big">{{ t('wayYouLike') }}</div>
        <div class="small">{{ t('selectYourPreference') }}</div>
      </div>
      <div class="interest">
        <div
          class="interest-item"
          :class="{ active: interest === '闲聊' }"
          @click="selectInterest('闲聊')"
        >
          <div class="content">
            <div class="interest-title">{{ t('preference1') }}</div>
            <div class="tips">{{ t('preference2') }}</div>
          </div>
          <img
            src="@/assets/images/newer/chat-interest.png"
            alt="chat-interest"
          />
        </div>
        <div
          class="interest-item"
          :class="{ active: interest === '剧情' }"
          @click="selectInterest('剧情')"
        >
          <div class="content">
            <div class="interest-title">{{ t('preference3') }}</div>
            <div class="tips">{{ t('preference4') }}</div>
          </div>
          <img
            src="@/assets/images/newer/drama-interest.png"
            alt="chat-interest"
          />
        </div>
        <div
          class="interest-item"
          :class="{ active: interest === '游戏' }"
          @click="selectInterest('游戏')"
        >
          <div class="content">
            <div class="interest-title">{{ t('preference5') }}</div>
            <div class="tips">{{ t('preference6') }}</div>
          </div>
          <img
            src="@/assets/images/newer/game-interest.png"
            alt="chat-interest"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.preference-container {
  width: 100%;

  .step {
    display: flex;
    column-gap: 8px;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 16px;
    margin-top: 20px;

    .step-box {
      flex: 1;
      height: 6px;
      background: #141414;
      border-radius: 8px;
    }

    .step-active {
      background: $livCoThemeColor;
    }

    .jump {
      margin-left: 17px;
      font-size: 14px;
      color: $livCoThemeColor;
    }
  }

  .title {
    padding: 0 12px;
    margin-top: 64px;
    text-align: center;
    animation: up 1s ease;

    .big {
      margin-bottom: 4px;
      font-size: 26px;
      font-weight: 600;
    }

    .small {
      font-size: 14px;
      color: #969696;
    }
  }

  .gender {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 28px;
    margin-top: 60px;

    .circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 48%;
      aspect-ratio: 1;
      font-size: 14px;
      cursor: pointer;
      background: #232222;
      border-radius: 50%;
      opacity: 0;
      transition:
        transform,
        scale 0.3s ease;
      animation: up 1s ease 0.6s forwards;

      &:active {
        scale: 0.9;
      }
    }

    .single {
      flex: 1 1 100%;

      .circle {
        margin: 0 auto;
        opacity: 0;
        animation: up 1s ease 0.3s forwards;
      }
    }

    .circle.active {
      background: #ebdfac1a;
      border: 2px solid $livCoThemeColor;
    }
  }

  .interest {
    padding: 0 16px;
    margin-top: 64px;

    .interest-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 96px;
      padding: 26px 155px 26px 20px;
      margin-bottom: 20px;
      background: #141414;
      border-radius: 12px;
      opacity: 0;
      transition: scale 0.3s ease;
      animation: up 1s ease forwards;

      &:nth-child(1) {
        animation-delay: 0.3s;
      }

      &:nth-child(2) {
        animation-delay: 0.6s;
      }

      &:nth-child(3) {
        animation-delay: 0.9s;
      }

      &:active {
        scale: 0.9;
      }

      .interest-title {
        font-size: 15px;
        font-weight: 600;
      }

      .tips {
        margin-top: 4px;
        font-size: 13px;
        color: #6a6a6a;
        word-break: break-word;
      }

      img {
        position: absolute;
        right: 0;
        display: block;
        height: 100%;
      }
    }

    .interest-item.active {
      background: #ebdfac26;
      border: 1px solid $livCoThemeColor;
    }
  }
}

@keyframes up {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
