<script setup lang="ts">
import { getUserTerms } from '@/api/agreement'

definePage({
  name: 'User',
  meta: {
    level: 2
  }
})
const termsContent = ref('')
const termsTitle = ref('')
const { t } = useI18n()
function getUserTermsHandler() {
  getUserTerms().then((res) => {
    if (res.code !== 200) return
    termsContent.value = res.data?.content
    termsTitle.value = res.data?.title
  })
}

getUserTermsHandler()
</script>

<template>
  <div class="agreementDialog">
    <div class="dialog-content">
      <div class="title flex-center-center">
        <van-icon
          name="arrow-left"
          class="arrow-left"
          @click="$router.go(-1)"
        />
        <div>{{ t('userAgreement') }}</div>
      </div>
      <div class="content">
        <div class="termsTitle">
          <strong>{{ termsTitle }}</strong>
        </div>
        <div v-html="termsContent"></div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.agreementDialog {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 50%);

  .dialog-content {
    width: 100%;
    height: 100%;
    background: #fff;

    .title {
      position: relative;
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      color: #000;

      .arrow-left {
        position: absolute;
        left: 12px;
        font-size: 16px;
        font-weight: 600;
        color: #000;
      }
    }

    .content {
      height: calc(100% - 48px);
      padding: 8px 16px;
      overflow: hidden auto;
      line-height: 20px;
      color: #000;
    }
  }
}
</style>
