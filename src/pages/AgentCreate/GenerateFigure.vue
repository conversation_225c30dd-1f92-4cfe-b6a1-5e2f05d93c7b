<script setup lang="ts">
import router from '@/router'
import Popup from '../../components/Popup.vue'
import Dialog from '../../components/Dialog.vue'
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { getComfyUIApi } from '@/api/agentCreate/index'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
import type { FigureItem } from '@/api/agentCreate/types'
const agentCreateStore = useAgentCreateStore()

definePage({
  name: 'GenerateFigure',
  path: 'GenerateFigure/:prompt_id'
})

const { t } = useI18n()
const route = useRoute()
const promptId = computed(() => {
  if ('prompt_id' in route.params) {
    return route.params.prompt_id as string
  }
})
const getComfyUI = () => {
  getComfyUIApi({ prompt_id: promptId.value }).then((res) => {
    if (res.data.length !== 0) {
      showProgress.value = false
      figureList.value = res.data
      showFigure.value = figureList.value[0].img_url
      figureIndex.value = 0
      clearInterval(timer)
    }
  })
}
let timer = setInterval(() => {
  getComfyUI()
}, 1000)
const requireFigure = () => {
  timer = setInterval(() => {
    getComfyUI()
  }, 1000)
  getNum()
  showFigure.value = ''
  figureList.value = []
  cancelDialogRef.value.closeDiaog()
}
const navBarClose = () => {
  clearInterval(timer)
  router.go(-1)
}
onBeforeUnmount(() => {
  clearInterval(timer)
})
const showProgress = ref(true)
const showFigure = ref('')
const figureList = ref<FigureItem[]>([])
const getFigure = (index: number) => {
  agentCreateStore.img_url = showFigure.value = figureList.value[index].img_url
  figureIndex.value = index
}
const figureIndex = ref(0)

const dialogRef = ref<InstanceType<typeof Dialog> | null>(null)
const cancelDialogRef = ref<InstanceType<typeof Dialog> | null>(null)
const popupRef = ref<InstanceType<typeof Popup> | null>(null)

const closePopup = () => {
  eventReport({
    event_type: EventTypeEnum.CANCEL_EDIT_AI_AVATAR,
    front_address: ForwardAddressEnum.NEW_CREATION,
    ai_id: figureList.value[figureIndex.value].id
  })

  popupRef.value.closePopup()
}
const showCroppertTips = () => {
  dialogRef.value.showDialog()
}
const showCropper = () => {
  agentCreateStore.img_url = showFigure.value
  eventReport({ event_type: EventTypeEnum.EDIT_AI_AVATAR, front_address: ForwardAddressEnum.NEW_CREATION, ai_id: figureList.value[figureIndex.value].id })
  dialogRef.value.closeDiaog()
  popupRef.value.showPopup()
}
const cancelCropper = () => {
  dialogRef.value.closeDiaog()
}
// const goBack = () => {
//   router.go(-1)
// }
const goAgentCteate = () => {
  router.push({ name: 'AdvancedCreate' })
}
const num = ref(0)
const getNum = () => {
  const interval = setInterval(() => {
    // 在 1 和 9 之间随机生成增量
    const increment = Math.floor(Math.random() * 9) + 1
    // 将增量加到当前值
    num.value += increment
    // 打印当前值
    // 如果值大于等于 100，清除定时器
    if (num.value >= 100) {
      num.value = 99
      clearInterval(interval)
      console.log('Reached 100, stopping the increment.')
    }
  }, 1000) // 每秒执行一次
}
onMounted(() => {
  getNum()
})
</script>

<template>
  <div
    class="createFigure gradient-bg"
    id="nav-create-agent-container"
  >
    <CreateAgentPageNavBar
      :title="t('customizeImage')"
      :zIndex="10"
      @nav-bar-close="navBarClose"
    />
    <div class="figure-img">
      <img
        v-if="showFigure !== ''"
        :src="showFigure"
        alt=""
      />
      <!-- <div class="line">
        <p>正在排队中<br />第1位 / 共1位</p>
      </div> -->
      <div
        class="progress"
        v-if="num < 100 && showProgress"
      >
        <p>{{ num }}%</p>
        <span>{{ t('creatingCharacter') }}...</span>
      </div>
    </div>
    <div class="figure-list">
      <div
        :class="['figure-item', index === figureIndex ? 'figure-item-active' : '']"
        v-for="(item, index) in figureList"
        :key="item.id"
        @click="getFigure(index)"
      >
        <img
          :src="item.img_url"
          alt=""
        />
      </div>
    </div>
    <div class="btn">
      <div
        class="cancle-btn"
        @click="cancelDialogRef.showDialog()"
      >
        {{ t('regenerateOption') }}
      </div>
      <div
        class="cropper-btn"
        @click="showCroppertTips"
      >
        {{ t('cropAvatar') }}
      </div>
    </div>
    <Popup
      ref="popupRef"
      style="width: 100%; max-width: 100%; padding: 15px 0; background-color: transparent"
    >
      <Cropper
        :closePopup="closePopup"
        :goPage="goAgentCteate"
      />
    </Popup>
    <Dialog ref="dialogRef">
      <template #title>
        <span class="dialog-title">{{ t('continueCurrentSetting') }}</span>
      </template>
      <div class="dialog-content flex-center-center">
        <span>{{ t('modifySettingNotice') }}</span>
        <div class="dialog-tips">
          <span>{{ t('savedInDrafts') }}</span>
        </div>
      </div>

      <template #footer>
        <div class="flex-between-center dialog-footer">
          <div
            class="cancel-btn dialog-footer-btn"
            @click="cancelCropper"
          >
            {{ t('cancelButton') }}
          </div>
          <div
            class="confirm-btn dialog-footer-btn"
            @click="showCropper"
          >
            {{ t('confirmSelection') }}
          </div>
        </div>
      </template>
    </Dialog>
    <Dialog ref="cancelDialogRef">
      <template #title>
        <span class="dialog-title">{{ t('regenerateOption') }}？</span>
      </template>
      <div class="dialog-content flex-center-center">
        <div class="dialog-tips">
          <span>{{ t('abandonFigureConfirm') }}</span>
        </div>
      </div>

      <template #footer>
        <div class="flex-between-center dialog-footer">
          <div
            class="cancel-btn dialog-footer-btn"
            @click="cancelDialogRef.closeDiaog()"
          >
            {{ t('cancelButton') }}
          </div>
          <div
            class="confirm-btn dialog-footer-btn"
            @click="requireFigure"
          >
            {{ t('confirmButton') }}
          </div>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<style scoped lang="scss">
.createFigure {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 100px;

  .figure-img {
    position: relative;
    width: calc(279px - 4px);
    height: 495px;
    margin-top: 18px;
    margin-bottom: 18px;
    font-size: 0;
    border-radius: 12px;

    img {
      width: 100%;
      height: 100%;
      border-radius: 12px;
    }

    .line {
      position: absolute;
      top: 50%;
      left: 50%;
      text-align: center;
      transform: translate(-50%, -50%);

      p {
        margin: 0;
        margin-bottom: 4px;
        font-size: 18px;
      }
    }

    .progress {
      position: absolute;
      top: 50%;
      left: 50%;
      text-align: center;
      transform: translate(-50%, -50%);

      p {
        margin: 0;
        margin-bottom: 4px;
        font-size: 24px;
      }

      span {
        font-size: 14px;
      }
    }
  }

  .figure-img::before {
    position: absolute;
    inset: -4px;
    z-index: -1;
    content: '';
    background-image: url('@/assets/images/agentCreate/generate.png');
    background-repeat: no-repeat;
    background-size: contain;
    // background-image: linear-gradient(180deg, rgba(255, 53, 242, 100%), rgba(255, 109, 89, 100%));
    border-radius: 12px;
  }

  .figure-list {
    display: flex;
    justify-content: center;
    width: 100%;

    .figure-item {
      box-sizing: border-box;
      width: 47px;
      height: 71px;
      margin-right: 28px;
      overflow: hidden;
      border: 2px solid transparent;
      border-radius: 6px;

      img {
        width: 100%;
        border-radius: 6px;
      }
    }

    .figure-item:last-child {
      margin-right: 0;
    }

    .figure-item-active {
      background-image: linear-gradient(180deg, rgba(255, 53, 242, 100%), rgba(255, 109, 89, 100%));
    }
  }

  .btn {
    position: fixed;
    bottom: 4%;
    display: flex;
    justify-content: space-evenly;
    width: 100%;

    .cancle-btn {
      width: 165px;
      height: 52px;
      line-height: 52px;
      text-align: center;
      background: #2b2b2b;
      border-radius: 9999px;
    }

    .cropper-btn {
      width: 165px;
      height: 52px;
      line-height: 52px;
      text-align: center;
      background: #ff35f2;
      border-radius: 9999px;
    }
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  padding: 0 0 20px;
  text-align: center;

  p {
    margin-bottom: 4px;
    font-size: 14px;
  }
}

.dialog-tips {
  width: 100%;
  font-size: 12px;
  color: #c1c1c1;
  text-align: center;
}

.dialog-title {
  font-size: 18px;
  color: #fff;
}

.dialog-content {
  padding: 24px 10%;

  span {
    font-size: 14px;
    line-height: 1.5;
    color: #c1c1c1;
    text-align: center;
  }
}

.dialog-footer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 14px;
  justify-content: center;
  width: 90%;
  padding-bottom: 20px;
  margin: 0 auto;
}

.dialog-footer-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 0;
  border-radius: 16px;
}

.cancel-btn {
  background: #3d3d3d;
}

.confirm-btn {
  background: #ff35f2;
}
</style>
