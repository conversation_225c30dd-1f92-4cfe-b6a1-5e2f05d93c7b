<script setup lang="ts">
import { getVoicePreList } from '@/api/agentCreate'
import useAgentCreateStore from '@/stores/modules/agentCreate'

const agentCreateStore = useAgentCreateStore()

definePage({
  name: 'SoundSetting'
})
const router = useRouter()
const navBarClose = () => {
  router.back()
}
const { t } = useI18n()
const loading = ref(false)
const checked = ref([])
const checkboxRefs = ref([])
const toggle = (index: number, voice_id: number) => {
  checkboxRefs.value[index].toggle()
  checked.value = [voice_id]
}
const tabsList = ref({})
const soundList = ref([])
const active = ref(0)
const lang = ref('')
const getPage = (e: { name: string; title: string; event: MouseEvent; disabled: boolean }) => {
  lang.value = e.name
  handleVoicePreList()
}

const submit = () => {
  if (checked.value[0]) {
    agentCreateStore.sound = soundList.value.filter((item) => {
      if (item.voice_id === checked.value[0]) {
        return item
      }
    })[0].voice_id
    console.log(agentCreateStore.sound)
  } else {
    agentCreateStore.sound = 0
  }
  router.back()
}

const handleVoicePreList = () => {
  loading.value = true
  getVoicePreList({ lang: lang.value || 'cn', sex: 0 }).then((res) => {
    if (res.code === 200) {
      soundList.value = res.data.list.map((item) => {
        item.isPlay = false
        return item
      })
      if (soundList.value.length > 0) {
        checked.value = [soundList.value[0].voice_id]
      }
      tabsList.value = res.data.lang_arr
      loading.value = false
    }
  })
}
handleVoicePreList()
</script>

<template>
  <div
    class="gradient-bg contain"
    id="nav-create-agent-container"
  >
    <CreateAgentPageNavBar
      :title="`${t('advancedCreation')}(1/2)`"
      :zIndex="10"
      @nav-bar-close="navBarClose"
    />
    <div class="sound-title">{{ t('presetSoundLibrary') }}</div>
    <van-tabs
      type="card"
      swipe-threshold="3"
      v-model:active="active"
      @click-tab="getPage($event)"
      class="tabs-container"
    >
      <van-tab
        v-for="(value, key) in tabsList"
        :title="value"
        :name="key"
        :key="key"
      />
    </van-tabs>
    <div
      class="sound-list"
      v-if="soundList.length > 0"
    >
      <div
        class="sound-item"
        v-for="(item, index) in soundList"
        :key="item.voice_id"
      >
        <div class="left-box">
          <!-- <div class="play">
            <van-icon
              v-if="!item.isPlay"
              name="play-circle-o"
              @click="play(item, index)"
            />
            <van-icon
              v-else
              name="pause-circle-o"
              @click="play(item, index)"
            />
          </div>
          <audio
            :src="item.demo_url"
            ref="playerRefs"
          ></audio> -->
          <SoundPlay sound-url="https://storage.googleapis.com/eleven-public-prod/premade/voices/EXAVITQu4vr4xnSDxMaL/01a3e33c-6e99-4ee7-8543-ff2216a32186.mp3">
            <template #default="{ play, isPlaying, loading, stop }">
              <div
                class="sound-btn"
                @click="
                  () => {
                    if (isPlaying) {
                      console.log(1)
                      stop()
                    } else {
                      console.log(2)
                      play()
                    }
                  }
                "
              >
                <van-loading
                  v-if="loading"
                  size="18"
                  color="#8d005d"
                />
                <template v-else>
                  <SoundLoading
                    v-if="isPlaying"
                    style="height: auto"
                  />
                  <SvgIcon
                    v-else
                    icon-class="playon_fill"
                    style="margin-top: -4px; font-size: 24px"
                  />
                </template>
              </div>
            </template>
          </SoundPlay>
          <div class="title">{{ item.name }}</div>
        </div>
        <div class="check">
          <van-checkbox-group v-model="checked">
            <van-checkbox
              :name="item.voice_id"
              :ref="(el) => (checkboxRefs[index] = el)"
              @click.stop
              @click="toggle(index, item.voice_id)"
          /></van-checkbox-group>
        </div>
      </div>
    </div>
    <van-empty
      v-if="soundList.length === 0 && !loading"
      :description="t('audioEmpty')"
    />
    <div class="sound-sitting-btn">
      <div
        class="return"
        @click="navBarClose"
      >
        {{ t('return') }}
      </div>
      <div
        class="submit"
        @click="submit"
      >
        {{ t('confirmButton') }}
      </div>
    </div>
  </div>
  <Loading v-if="loading" />
</template>

<style scoped lang="scss">
:deep(.van-tab) {
  font-size: 12px;
}

:deep(.van-tabs) {
  height: 48px;

  .van-tabs__wrap {
    height: 100%;
    padding-right: 31.5px;

    .van-tabs__nav {
      height: 100%;
      padding: 9px 0;
      background: none;
    }
  }

  .van-tabs__nav {
    margin: 0;
    border: none;

    .van-tab--card {
      height: 32px;
      margin-right: 12px;
      color: rgba($color: #fff, $alpha: 80%);
      background-color: #262626;
      border: 1px solid rgba(255, 255, 255, 5%);
      border-radius: 16px;

      &:last-child {
        margin-right: 0;
      }

      &.van-tab--active {
        background: #98f;
        border: 1px solid rgba(255, 255, 255, 40%);
      }
    }
  }
}

.contain {
  padding: 16px;

  :deep(.sound-btn) {
    position: relative !important;
    display: flex;
    margin-right: 8px;
  }

  .sound-title {
    margin-bottom: 8px;
  }

  .sound-list {
    margin-top: 16px;

    .sound-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      margin-bottom: 16px;
      background: #464044;
      border-radius: 16px;
      opacity: 0.7;

      .left-box {
        display: flex;

        .play {
          margin-top: 2px;
          margin-right: 16px;
          font-size: 16px;

          .van-icon {
            margin-top: -8px;
            vertical-align: middle;
          }
        }
      }
    }
  }

  .sound-sitting-btn {
    position: fixed;
    bottom: 5%;
    display: flex;
    width: 100%;

    .return {
      width: calc(50% - 24px);
      height: 52px;
      margin-right: 16px;
      line-height: 52px;
      text-align: center;
      background-color: #3f3743;
      border-radius: 32px;
    }

    .submit {
      width: calc(50% - 24px);
      height: 52px;
      line-height: 52px;
      text-align: center;
      background-color: #ff35f2;
      border-radius: 32px;
    }
  }
}

:deep(.van-tabs) {
  height: 48px;

  .van-tabs__wrap {
    height: 100%;
    padding-right: 31.5px;

    .van-tabs__nav {
      height: 100%;
      padding: 9px 0;
    }
  }

  .van-tabs__nav {
    margin: 0;
    border: none;

    .van-tab--card {
      height: 32px;
      margin-right: 12px;
      border: 1px solid rgba(255, 255, 255, 5%);
      border-radius: 16px;

      &:last-child {
        margin-right: 0;
      }

      &.van-tab--active {
        background: linear-gradient(90deg, #ff35f2 0%, #98f 100%);
        border: 1px solid rgba(255, 255, 255, 40%);
      }
    }
  }
}
</style>
