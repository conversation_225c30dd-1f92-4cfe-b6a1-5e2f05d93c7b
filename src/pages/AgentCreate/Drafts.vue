<script setup lang="ts">
const router = useRouter()
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { getClientImageListApi } from '@/api/agentCreate'
import type { DraftItem } from '@/api/agentCreate/types'
const agentCreateStore = useAgentCreateStore()
definePage({
  name: 'Drafts'
})
const navBarClose = () => {
  router.back()
}

const { t } = useI18n()
const loading = ref(false)
const activeIndex = ref(0)
const agentList = ref<DraftItem[]>([])
const getIndex = (index: number) => {
  activeIndex.value = index
}
const goAdvancedCreate = () => {
  agentCreateStore.showAvatar = true
  agentCreateStore.img_url = agentList.value[activeIndex.value].img_url
  agentCreateStore.avatar = agentList.value[activeIndex.value].avatar_url
  router.push({ name: 'AdvancedCreate', params: { id: agentList.value[activeIndex.value].id } })
}
const getClientImageList = () => {
  loading.value = true
  getClientImageListApi()
    .then((res) => {
      if (res.code === 200) {
        agentList.value = res.data.list
        loading.value = false
      }
    })
    .catch((err) => console.log(err))
}
getClientImageList()
</script>

<template>
  <div
    class="gradient-bg contain"
    id="nav-create-agent-container"
  >
    <CreateAgentPageNavBar
      :title="t('draftsFolder')"
      :zIndex="10"
      :isShowClose="false"
    >
      <template #left>
        <img
          src="@/assets/images/agentCreate/arrow-right.png"
          style="width: 24px; height: 24px; transform: rotate(180deg)"
          @click="navBarClose"
        />
      </template>
    </CreateAgentPageNavBar>
    <div
      class="agent-list"
      v-if="agentList.length > 0"
    >
      <div
        :class="['agent-item', index === activeIndex ? 'active' : '']"
        v-for="(item, index) in agentList"
        :key="item.id"
        @click="getIndex(index)"
      >
        <img
          :src="item.img_url"
          alt=""
        />
      </div>
    </div>
    <van-empty
      v-else-if="!loading && agentList.length === 0"
      :description="t('diyFigureSave')"
    />
    <CreateAgentBtn
      :btnDisabled="false"
      :loading="loading"
      class="btn"
      @submit="goAdvancedCreate"
    >
      <template #left>
        <span>{{ t('useImage') }}</span>
      </template>
    </CreateAgentBtn>
  </div>
</template>

<style scoped lang="scss">
.contain {
  padding: 12px;
  padding-bottom: 100px;

  .agent-list {
    display: flex;
    flex-wrap: wrap;

    .agent-item {
      box-sizing: border-box;
      flex: 0 0 calc(33.3% - 10px);
      margin: 4px;
      overflow: hidden;
      font-size: 0;
      border: 4px solid transparent;
      border-radius: 8px;

      img {
        width: calc(100%);
      }
    }

    .active {
      background: linear-gradient(180deg, rgba(255, 53, 242, 100%), rgba(255, 109, 89, 100%));
    }
  }

  .submit-btn {
    position: fixed;
    bottom: 5%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100% - 32px);
    height: 50px;
    padding: 16px;
    background-color: #ff35f2;
    border-radius: 32px;

    img {
      height: 24px;
    }
  }
}
</style>
