<script setup lang="ts">
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { useModal } from '@/hooks/useModal.ts'
import Dialog from '@/components/Dialog.vue'
import { getAIInfo } from '@/api/agentHomePage'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'

const { t } = useI18n()
definePage({
  name: 'AdvancedCreate',
  path: 'AdvancedCreate/:id?'
})

const agentCreateStore = useAgentCreateStore()
const dialogRef = ref<InstanceType<typeof Dialog> | null>(null)

const router = useRouter()
const route = useRoute()
const aiID = computed(() => {
  if ('id' in route.params) {
    agentCreateStore.id = Number(route.params.id)
    return Number(route.params.id)
  }
})
watch(
  () => agentCreateStore.type,
  () => {
    const getAgentMessage = () => {
      getAIInfo({ ai_id: aiID.value }).then((res) => {
        const { code, data } = res
        if (code === 200) {
          agentCreateStore.showAvatar = true
          agentCreateStore.name = data.name
          agentCreateStore.sex = data.sex
          agentCreateStore.sound = Number(data.voice_id)
          agentCreateStore.avatar = data.avatar_url
          agentCreateStore.img_url = data.image_url
          agentCreateStore.role_setting = data.role_setting
          agentCreateStore.opening_statement = data.opening_statement
          agentCreateStore.back_story = data.back_story
          agentCreateStore.synopsis = data.synopsis
          agentCreateStore.talk_example = data.talk_example
          agentCreateStore.tags = data.tag_list.map((item) => {
            item.isCheck = true
            return item
          })
        }
      })
    }
    if (agentCreateStore.type === 'edit') {
      getAgentMessage()
    }
  },
  {
    immediate: true
  }
)
onMounted(() => {
  if (!aiID.value) {
    eventReport({ event_type: EventTypeEnum.ADVANCED_CREATE_AI, front_address: ForwardAddressEnum.NAV_CREATE_BUTTON })
  } else {
    eventReport({ event_type: EventTypeEnum.EDIT_ADVANCED_AI, ai_id: aiID.value })
  }
})

const navBarClose = () => {
  dialogRef.value.showDialog()
}
const goBack = () => {
  agentCreateStore.showAvatar = false
  agentCreateStore.name = ''
  agentCreateStore.sound = 0
  agentCreateStore.role_setting = ''
  agentCreateStore.opening_statement = ''
  agentCreateStore.back_story = ''
  agentCreateStore.synopsis = ''
  agentCreateStore.talk_example = []
  agentCreateStore.tags = []
  agentCreateStore.id = 0
  if (!aiID.value) {
    eventReport({ event_type: EventTypeEnum.CANCEL_ADVANCED_CREATE_AI })
  } else {
    eventReport({ event_type: EventTypeEnum.CANCEL_EDIT_AI_BODY })
  }
  router.go(-1)
}
function goBackground() {
  if (agentCreateStore.showAvatar === false || agentCreateStore.name === '' || agentCreateStore.sound === 0) {
    useModal({
      message: t('agentInfoTips'),
      duration: 1500
    })
    return
  } else {
    router.push({ name: 'agentBackground', params: { id: aiID.value } })
  }
}
function goDrafts() {
  router.push({ name: 'Drafts' })
}

const soundAndNameRef = ref()
</script>
<template>
  <div
    id="nav-create-agent-container"
    class="gradient-bg"
  >
    <CreateAgentPageNavBar
      :title="`${t('advancedCreation')}(1/2)`"
      :zIndex="10"
      @nav-bar-close="navBarClose"
    >
      <template #left>
        <img
          src="@/assets/images/agentCreate/draft.png"
          style="width: 24px; height: 24px"
          @click="goDrafts"
        />
      </template>
    </CreateAgentPageNavBar>
    <!-- <div style="width: 200px; height: 200px;">
      <img
        ref="imgRef"
        src="https://eros.wujialin.top/eros-mvp/assets/erosback-HByJAA6p.png"
        style="width: 200px; height: 200px;"
      />
    </div> -->
    <!-- 形象类型 -->
    <FigureItem v-if="!agentCreateStore.showAvatar" />
    <!-- 已选择的形象 -->
    <Avatar v-else />
    <!-- 声音和昵称 -->
    <SoundAndName ref="soundAndNameRef" />

    <!-- 完善背景故事按钮 -->
    <CreateAgentBtn
      :btnDisabled="false"
      class="btn"
      @submit="goBackground"
    >
      <template #left>
        <span>{{ t('completeBackground') }}</span>
      </template>
    </CreateAgentBtn>
  </div>
  <Dialog ref="dialogRef">
    <template #title>
      <span class="dialog-title">{{ t('completeBackground') }}</span>
    </template>
    <div class="dialog-content flex-center-center">
      <span>{{ t('cancelCreateTips') }}</span>
      <span>{{ t('cancelCreateConfirm') }}</span>
    </div>
    <template #footer>
      <div class="flex-between-center dialog-footer">
        <div
          class="cancel-btn dialog-footer-btn"
          @click="dialogRef.closeDiaog()"
        >
          {{ t('cancelButton') }}
        </div>
        <div
          class="confirm-btn dialog-footer-btn"
          @click="goBack"
        >
          {{ t('confirmButton') }}
        </div>
      </div>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">
.test-box {
  position: relative;
  width: 200px;
  height: 200px;

  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.5;
  }

  .cut {
    position: absolute;
    top: 20px;
    left: 60px;
    width: 30px;
    height: 30px;
    cursor: move;
    background: #fff;
    opacity: 0.1;
    mask-image: linear-gradient();
  }
}

.btn {
  position: fixed;
  bottom: 32px;
  width: calc(100% - 32px);
  height: 52px;
  margin: 0 16px;
}

.dialog-content {
  padding: 0 0 20px;
  text-align: center;

  p {
    margin-bottom: 4px;
    font-size: 14px;
  }
}

.dialog-title {
  font-size: 18px;
  color: #fff;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  padding: 24px 10%;

  span {
    font-size: 14px;
    line-height: 1.5;
    color: #c1c1c1;
    text-align: center;
  }
}

.dialog-footer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 14px;
  justify-content: center;
  width: 90%;
  padding-bottom: 20px;
  margin: 0 auto;
}

.dialog-footer-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 0;
  border-radius: 16px;
}

.cancel-btn {
  background: #3d3d3d;
}

.confirm-btn {
  background: #ff35f2;
}
</style>
