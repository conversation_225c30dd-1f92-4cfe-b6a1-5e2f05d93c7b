<script setup>
import { getBuildPic<PERSON><PERSON>, createComfyUIApi, getCreateConfigApi } from '@/api/agentCreate/index'
import { getPriceCrystal, paymentCrystal } from '@/api/mine'
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { useModal } from '@/hooks/useModal.ts'

definePage({
  name: 'DiyFigure'
})

const { t } = useI18n()
const router = useRouter()
const agentCreateStore = useAgentCreateStore()
const loading = ref(false)
const figureList = ref([])
const imgList = ref([])

const getBuildPic = () => {
  getBuildPicApi({ sex: agentCreateStore.sex }).then((res) => {
    figureList.value = res.data
    if (res.data.length > 0) {
      imgList.value = res.data[0].build_pic
    }
  })
}
getBuildPic()

//获取消耗钻石数
const diamond = ref(0)
const getCreateConfig = () => {
  getCreateConfigApi().then((res) => {
    diamond.value = res.data.create_image
  })
}
getCreateConfig()

const iconIndex = ref(0)
const getIconIndex = (index) => {
  iconIndex.value = index
}
watch(
  () => iconIndex.value,
  () => {
    if (figureList.value.length > 0) {
      imgList.value = figureList.value[iconIndex.value].build_pic
    }
  }
)
const comfyList = ref({})
const getIndex = (item, pic, index) => {
  comfyList.value[item.name] = pic.name
}
const navBarClose = () => {
  router.go(-1)
}
const goGenerateFigure = async () => {
  if (comfyList.value.length < imgList.value.length) {
    useModal({
      message: t('agentFeatureTips'),
      duration: 1500
    })
    return
  }
  loading.value = true
  const res = await getPriceCrystal({ product_type: 'create_image' })
  const amount_id = res.data.amount_id
  await paymentCrystal({ amount_id })
  const data = {
    action: 'add',
    sex: agentCreateStore.sex,
    comfy_list: comfyList.value
  }
  createComfyUIApi(data)
    .then((res) => {
      router.push({ name: 'GenerateFigure', params: { prompt_id: res.data.prompt_id } })
    })
    .catch((err) => {
      console.log(err)
    })
}
</script>

<template>
  <div
    class="diyFigure"
    id="nav-create-agent-container"
  >
    <CreateAgentPageNavBar
      :title="t('customizeImage')"
      :zIndex="10"
      @nav-bar-close="navBarClose"
    />
    <div class="swiper">
      <div
        class="icon-swiper"
        space-between="10"
        slides-per-view="4"
        free-mode="true"
        watch-slides-progress="true"
      >
        <swiper-slide
          v-for="(item, index) in figureList"
          :key="index"
          :class="['icon-swiper-item', iconIndex === index ? 'icon-swiper-item-active' : '']"
          @click="getIconIndex(index)"
        >
          <!-- <svg-icon
            :icon-class="item.icon"
            :class-name="`swiper-item-icon icon-${item.icon}`"
          /> -->
          <img
            :src="item.icon"
            alt=""
            style="width: 48px"
          />
        </swiper-slide>
      </div>
      <div
        style="

--swiper-navigation-color: #fff; --swiper-pagination-color: #fff"
        class="mySwiper"
        thumbs-swiper=".icon-swiper"
        space-between="10"
        navigation="true"
      >
        <swiper-slide
          v-for="(swiper, index) in figureList"
          :key="index"
        />
      </div>
    </div>
    <div
      v-for="(item, index) in imgList"
      :key="index"
    >
      <div
        class="swipe-title-box"
        v-if="!item.pic_item.every((pic) => pic.pic_url === '')"
      >
        <h3 class="swipe-title">{{ item.name }}</h3>
        <img
          v-if="comfyList[item.name]"
          src="@/assets/images/agentCreate/check-yes.png"
          alt=""
        />
      </div>
      <div class="swipe-cell">
        <div
          v-for="(pic, index) in item.pic_item"
          :key="pic.id"
        >
          <div
            v-if="pic.pic_url !== ''"
            @click="getIndex(item, pic, index)"
            :class="['swipe-item', pic.name === comfyList[item.name] ? 'swipe-item-active' : '']"
          >
            <div class="swipe-img">
              <img
                :src="pic.pic_url"
                alt=""
              />
            </div>
            <p>{{ pic.name }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="btn">
      <div
        class="submit-btn"
        @click="goGenerateFigure"
      >
        {{ t('createCharacter') }}
        <div class="diamond">
          <img
            src="@/assets/images/agentCreate/diamond.png"
            alt=""
          />
          <span>{{ diamond }}{{ t('diamond') }}</span>
        </div>
      </div>
    </div>
  </div>
  <Loading v-if="loading" />
</template>

<style lang="scss" scoped>
.diyFigure {
  padding: 16px;
  padding-bottom: 100px;
  overflow: auto;

  .swiper {
    .icon-swiper {
      display: flex;
      justify-content: space-around;
      padding: 10px;
      margin: 0 auto;
      background: rgba(163, 163, 163, 20%);
      border-radius: 30px;

      .icon-swiper-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        font-size: 48px;
        background-image: none;
        border-radius: 100%;
        box-shadow: none;

        .swiper-item-icon {
          width: 40px;
          height: 40px;
          font-size: 40px;
        }
      }

      .icon-swiper-item-active {
        width: 48px;
        height: 48px;
        background: linear-gradient(180deg, #fd9ef6 0%, #ff35f2 42%, #b51eab 100%);
        border-radius: 100%;
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 10%);
      }
    }
  }

  .swipe-cell {
    display: flex;
    flex-direction: row;
    width: auto;
    overflow: auto;

    .swipe-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 5px;
      margin-right: 8px;
      background: rgba(163, 163, 163, 20%);
      border-radius: 8px;

      .swipe-img {
        img {
          width: 80px;
          border-radius: 8px;
        }
      }

      p {
        margin: 10px 0 4px;
      }
    }
  }

  .swipe-title-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .swipe-title {
      font-size: 16px;
    }

    img {
      width: 24px;
      height: 24px;
      vertical-align: middle;
    }
  }

  .swipe-item-active {
    background: #d211c6 !important;
  }

  .btn {
    position: fixed;
    bottom: 0;
    width: 100vw;
    height: 110px;
    padding-left: 16px;
    margin-left: -16px;
    background-color: #181818;

    .submit-btn {
      position: fixed;
      bottom: 4%;
      width: calc(100% - 32px);
      height: 52px;
      font-size: 16px;
      line-height: 52px;
      text-align: center;
      background: linear-gradient(90deg, #d311c6 0%, #9521fe 100%), rgba(0, 0, 0, 30%);
      border-radius: 32px;

      .diamond {
        position: absolute;
        top: -24px;
        right: 0;
        padding: 4px 8px;
        font-size: 12px;
        line-height: 16px;
        background-color: #d311c6;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        border-bottom-right-radius: 12px;

        img {
          width: 16px;
          margin-right: 4px;
          vertical-align: middle;
        }
      }
    }
  }
}
</style>
