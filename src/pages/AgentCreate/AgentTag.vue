<script setup lang="ts">
import { getTagList } from '@/api/agentCreate'
import useAgentCreateStore from '@/stores/modules/agentCreate'
import type { InnerTag } from '@/api/agentCreate/types'

definePage({
  name: 'agentTag'
})

const router = useRouter()
const { t } = useI18n()
const agentCreateStore = useAgentCreateStore()
const navBarClose = () => {
  router.back()
}
const tagList = ref<any>([])

let collectedIds = ref<number[]>([])
const collectId = (item: InnerTag) => {
  const id = item.id
  if (!collectedIds.value.includes(id)) {
    collectedIds.value.push(id)
  } else {
    collectedIds.value = collectedIds.value.filter((item) => item !== id)
  }
  item.isCheck = !item.isCheck
}

const handleTagList = () => {
  getTagList().then((res) => {
    if (res.code === 200) {
      setTimeout(() => {
        tagList.value = res.data.map((item: any) => {
          item.tag_list.map((tag: any) => {
            tag.isCheck = false
            return tag
          })
          return item
        })
        console.log(agentCreateStore.tags)

        tagList.value.forEach((item: any) => {
          const tag_ids = agentCreateStore.tags.map((item) => {
            return item.id
          })
          item.tag_list.forEach((tag: any) => {
            if (tag_ids.includes(tag.id)) {
              tag.isCheck = true
            }
          })
        })
      }, 100)
    }
  })
}
const submit = () => {
  let filterArr: any = []
  tagList.value.forEach((item: any) => {
    if (item.tag_list.filter((tag: any) => tag.isCheck).length > 0) {
      filterArr = [...filterArr, ...item.tag_list.filter((tag: any) => tag.isCheck)]
    }
  })
  agentCreateStore.tags = filterArr
  router.back()
}
onMounted(() => {
  handleTagList()
})
</script>

<template>
  <div
    class="contain gradient-bg"
    id="nav-create-agent-container"
  >
    <CreateAgentPageNavBar
      :title="t('tag')"
      :zIndex="10"
      @nav-bar-close="navBarClose"
    />
    <div class="tag-card">
      <div
        class="tag-list"
        v-for="item in tagList"
        :key="item.id"
      >
        <span>{{ item.name }}</span>
        <van-tag
          v-for="tag in item.tag_list"
          :key="tag.id"
          :id="tag.id"
          @click="collectId(tag)"
          plain
          type="primary"
          :class="[tag.isCheck ? 'active' : '']"
          style="margin-bottom: 10px"
          >{{ tag.name }}</van-tag
        >
      </div>
    </div>

    <!-- <div class="total">({{ collectedIds.length }}/{{ tagList.length }})</div> -->
    <div
      class="submit-btn"
      @click="submit"
    >
      {{ t('saveButton') }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.contain {
  padding: 16px;

  .tag-card {
    padding: 16px;
    background-color: #2f2a2e;
    border-radius: 8px;

    .tag-list {
      span {
        display: inline-block;
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    .van-tag {
      padding: 6px 10px;
      margin-right: 8px;
      color: #fff;
      background-color: #262626;
      border-color: #383636;
      border-radius: 12px;
    }

    .active {
      background-color: rgba(237, 59, 230, 50%);
      border-color: rgba(237, 59, 230, 100%);
    }
  }

  .total {
    margin-top: 8px;
    text-align: right;
  }

  .submit-btn {
    position: fixed;
    bottom: 5%;
    width: calc(100% - 32px);
    height: 46px;
    font-size: 16px;
    line-height: 46px;
    color: #fff;
    text-align: center;
    background-color: #ff35f2;
    border-radius: 32px;
  }
}
</style>
