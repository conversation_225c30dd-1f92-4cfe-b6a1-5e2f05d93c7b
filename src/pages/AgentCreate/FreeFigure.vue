<script setup lang="ts">
import { register } from 'swiper/element/bundle'
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { getPresetImageList } from '@/api/agentCreate'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import type { PresetImageItem } from '@/api/agentCreate/types'
register()

definePage({
  name: 'FreeFigure'
})

const { t } = useI18n()
const loading = ref(false)
const activeSlide = ref(0)
const router = useRouter()
const agentCreateStore = useAgentCreateStore()
const navBarClose = () => {
  router.back()
}
const submit = () => {
  loading.value = true
  agentCreateStore.showAvatar = true
  agentCreateStore.img_url = agentList.value.map((item, index) => {
    if (index === activeSlide.value) {
      return item.pic_url
    }
  })[0]
  agentCreateStore.avatar = agentList.value.map((item, index) => {
    if (index === activeSlide.value) {
      return item.avatar_url
    }
  })[0]
  agentCreateStore.showAvatar = true
  eventReport({ event_type: EventTypeEnum.CHOOSE_FREE_AVATAR, tag_name: agentList.value[activeSlide.value].name })
  router.back()
}
const goBack = () => {
  router.back()
}

const swiperSlideChange = (e: any) => {
  activeSlide.value = e.detail[0].realIndex
}

const agentList = ref<PresetImageItem[]>([])
const handlePresetImageList = () => {
  getPresetImageList({ sex: agentCreateStore.sex }).then((res) => {
    if (res.code === 200) {
      agentList.value = res.data
    }
  })
}
handlePresetImageList()
</script>
<template>
  <div
    id="nav-create-agent-container"
    class="gradient-bg free-container"
  >
    <CreateAgentPageNavBar
      :title="t('freeImage')"
      :zIndex="10000"
      @nav-bar-close="navBarClose"
    />
    <div class="swiper-box">
      <swiper-container
        :init="true"
        slides-per-view="auto"
        :centered-slides="true"
        :space-between="24"
        :looped-slides="5"
        :pagination="{
          el: '.pagination'
        }"
        @swiperslidechange="swiperSlideChange"
      >
        <swiper-slide
          v-for="(item, index) in agentList"
          :key="item.id"
          class="swiper-slide"
        >
          <img
            :src="item.pic_url"
            class="swiper-img"
          />
          <div
            v-show="index === activeSlide"
            class="avatar-box"
          >
            <img
              object-fit="cover"
              :src="item.avatar_url"
              class="avatar"
            />
          </div>
        </swiper-slide>
      </swiper-container>
      <div class="pagination"></div>
    </div>
    <div class="btn-box">
      <div
        class="btn"
        @click="goBack"
      >
        {{ t('cancelButton') }}
      </div>
      <div
        class="btn btn-primary"
        @click="submit"
      >
        <span v-if="!loading">{{ t('confirmButton') }}</span>
        <BtnLoading v-else />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.free-container {
  padding-bottom: 150px;

  .swiper-box {
    margin-top: 24px;

    .swiper-slide {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80vw;
      height: 65vh;
      font-size: 18px;
      text-align: center;
      border-radius: 12px;

      .swiper-img {
        width: 96%;
        height: 97%;
        object-fit: cover;
        border-radius: 12px;
      }

      .avatar-box {
        position: absolute;
        right: 17px;
        bottom: 17px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 69px;
        height: 69px;
        background: linear-gradient(180deg, rgba(255, 53, 242, 100%), rgba(255, 109, 89, 100%));
        border-radius: 100px;

        .avatar {
          width: 61px;
          height: 61px;
          object-fit: cover;
          border-radius: 100px;
        }
      }
    }

    .swiper-slide-active,
    .swiper-slide-duplicate-active {
      background: linear-gradient(180deg, rgba(255, 53, 242, 100%), rgba(255, 109, 89, 100%));
    }
  }

  .pagination {
    margin-top: 28px;
    text-align: center;

    :deep(.swiper-pagination-bullet) {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin: 0 12px;
      background: #a3a3a3;
      border-radius: 100px;
    }

    :deep(.swiper-pagination-bullet-active) {
      background: #ff35f2;
    }
  }

  .btn-box {
    position: fixed;
    bottom: 30px;
    display: flex;
    justify-content: space-evenly;
    width: 100%;

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 164px;
      height: 52px;
      color: #fff;
      background: #2b2b2b;
      border-radius: 100px;
    }

    .btn-primary {
      background: #ff35f2;
    }
  }
}
</style>
