<script setup lang="ts">
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { ExampleItem } from '@/api/agentCreate/types'
definePage({
  name: 'dialogueExample'
})

const { t } = useI18n()
const router = useRouter()
const agentCreateStore = useAgentCreateStore()
const navBarClose = () => {
  router.back()
}
const chatList = ref<ExampleItem[]>([{ Q: '明天能和我约会吗', A: '好呀好呀' }])
const addChat = () => {
  chatList.value.push({ Q: '', A: '' })
}
const submit = () => {
  agentCreateStore.talk_example = chatList.value
  router.back()
}
onMounted(() => {
  console.log(agentCreateStore.talk_example)

  if (agentCreateStore.talk_example !== null && agentCreateStore.talk_example.length > 0) {
    chatList.value = agentCreateStore.talk_example
  }
})
</script>

<template>
  <div
    class="gradient-bg contain"
    id="nav-create-agent-container"
  >
    <CreateAgentPageNavBar
      :title="t('dialogueExample')"
      :zIndex="10"
      @nav-bar-close="navBarClose"
    />
    <div class="dialog-card">
      <div
        v-for="(item, index) in chatList"
        :key="index"
      >
        <div class="chat-item">
          <van-field
            :placeholder="t('clickToAddUserContent')"
            v-model="item.Q"
            clearable
            autosize
            type="textarea"
            rows="1"
          />
        </div>
        <div class="chat-item-ai">
          <van-field
            :placeholder="t('clickToAddAIResponses')"
            v-model="item.A"
            clearable
            autosize
            type="textarea"
            rows="1"
          />
        </div>
      </div>
    </div>
    <div
      class="dialog-card add-btn"
      @click="addChat"
    >
      <img
        src="/src/assets/images/agentCreate/add3.png"
        class="right-img"
      />
    </div>
    <div
      class="submit-btn"
      @click="submit"
    >
      {{ t('saveButton') }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.contain {
  padding: 16px;
  padding-bottom: 80px;

  .dialog-card {
    padding: 16px;
    margin-bottom: 16px;
    background: rgba(255, 255, 255, 10%);
    border-radius: 16px;

    .chat-item,
    .chat-item-ai {
      margin-bottom: 16px;
      overflow: hidden;
      color: #fff;
    }

    .chat-item {
      display: flex;
      justify-content: right;

      :deep(.van-field) {
        width: 70%;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        border-bottom-left-radius: 16px;
      }

      :deep(.van-field__control::placeholder) {
        font-size: 16px;
        color: #fff;
      }
    }

    .chat-item-ai {
      :deep(.van-field) {
        width: 70%;
        background-color: #a31899;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        border-bottom-right-radius: 16px;
      }

      :deep(.van-field__control::placeholder) {
        font-size: 16px;
        color: #fff;
      }
    }
  }

  .add-btn {
    font-size: 18px;
    text-align: center;
  }

  .submit-btn {
    position: fixed;
    bottom: 5%;
    width: calc(100% - 32px);
    height: 46px;
    font-size: 16px;
    line-height: 46px;
    color: #fff;
    text-align: center;
    background-color: #ff35f2;
    border-radius: 32px;
  }

  .right-img {
    width: 24px;
    height: 24px;
  }
}
</style>
