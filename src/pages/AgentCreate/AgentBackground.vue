<script setup lang="ts">
import { normalCreate, getCreateConfigApi } from '@/api/agentCreate'
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { useModal } from '@/hooks/useModal.ts'
import type { AgentCreateQuery } from '@/api/agentCreate/types'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'

definePage({
  name: 'agentBackground'
})

onMounted(() => {
  eventReport({ event_type: EventTypeEnum.COMPLETE_AI_BACKSTORY, front_address: ForwardAddressEnum.NEW_CREATION })
  getCreateConfig()
})
const { t } = useI18n()
const router = useRouter()
const agentCreateStore = useAgentCreateStore()
const loading = ref(false)
const navBarClose = () => {
  agentCreateStore.role_setting = formData.role_setting
  agentCreateStore.opening_statement = formData.opening_statement
  agentCreateStore.back_story = formData.back_story
  agentCreateStore.synopsis = formData.synopsis
  router.back()
}

onMounted(() => {
  formData.role_setting = agentCreateStore.role_setting
  formData.opening_statement = agentCreateStore.opening_statement
  formData.back_story = agentCreateStore.back_story
  formData.synopsis = agentCreateStore.synopsis || ''
  formData.talk_example = agentCreateStore.talk_example
  formData.tags = agentCreateStore.tags
})

//获取消耗钻石数
const diamond = ref(0)
const getCreateConfig = () => {
  getCreateConfigApi().then((res) => {
    diamond.value = Number(res.data.ai_size_high)
  })
}
getCreateConfig()

const formData = reactive<AgentCreateQuery>({
  id: 0,
  avatar_url: '',
  image_url: '',
  name: '',
  voice_id: 0,
  role_setting: '',
  opening_statement: '',
  back_story: '',
  synopsis: '',
  talk_example: [],
  tags: [],
  action: '',
  sex: 0,
  is_show: false
})

const tags = agentCreateStore.tags
const formRef = ref()

const goExample = () => {
  agentCreateStore.role_setting = formData.role_setting
  agentCreateStore.opening_statement = formData.opening_statement
  agentCreateStore.back_story = formData.back_story
  agentCreateStore.synopsis = formData.synopsis
  router.push('dialogueExample')
}
const goTag = () => {
  agentCreateStore.role_setting = formData.role_setting
  agentCreateStore.opening_statement = formData.opening_statement
  agentCreateStore.back_story = formData.back_story
  agentCreateStore.synopsis = formData.synopsis
  router.push('agentTag')
}

const backStoryRef = ref()
const submit = async () => {
  await formRef.value.validate()
  formData.tags = agentCreateStore.tags
  if (formData.tags.length === 0) {
    useModal({
      message: t('tagEmptyTips'),
      duration: 1500
    })
    return
  }
  if (agentCreateStore.id !== 0) {
    formData.id = agentCreateStore.id
    formData.action = 'edit'
  } else {
    formData.action = 'add'
  }
  loading.value = true
  formData.name = agentCreateStore.name
  formData.talk_example = agentCreateStore.talk_example
  formData.voice_id = agentCreateStore.sound
  formData.sex = agentCreateStore.sex
  formData.is_show = agentCreateStore.is_show
  const regex = /(\/uploads\/admin\/.*)/ // 正则表达式匹配以"/uploads/admin/"开头的后续路径
  const match_avatar = agentCreateStore.avatar.match(regex)
  const avatar_url = match_avatar ? match_avatar[0] : '' // 如果匹配到了，取第一个匹配的分组
  const match_image = agentCreateStore.img_url.match(regex)
  const image_url = match_image ? match_image[0] : '' // 如果匹配到了，取第一个匹配的分组
  formData.avatar_url = avatar_url
  formData.image_url = image_url
  normalCreate(formData)
    .then((res) => {
      loading.value = false
      useModal({
        message: t('createAgentSuccess'),
        duration: 1500
      })
      agentCreateStore.showAvatar = false
      agentCreateStore.name = ''
      agentCreateStore.sound = 0
      agentCreateStore.role_setting = ''
      agentCreateStore.opening_statement = ''
      agentCreateStore.back_story = ''
      agentCreateStore.synopsis = ''
      agentCreateStore.talk_example = []
      agentCreateStore.tags = []
      router.push({ path: `/chat/${res.data}`, query: { path: 'create' } })
    })
    .catch((err) => {
      loading.value = false
      if (err.code === 248141001) {
        router.push(`/Mine/Crystal`)
      }
      console.log(err)
    })
}

const roleRows = computed(() => {
  const row = Math.max(Math.ceil(formData.role_setting.length / 20), 3)
  if (row > 3) {
    roleShow.value = true
  }
  return row
})
const roleCollapsed = ref(false)
const roleShow = ref(false)
const roleToggleExpand = () => {
  roleCollapsed.value = !roleCollapsed.value
}

const backStoryRows = computed(() => {
  const row = Math.max(Math.ceil(formData.back_story.length / 20), 3)
  if (row > 3) {
    backStoryShow.value = true
  }
  return row
})
const backStoryCollapsed = ref(false)
const backStoryShow = ref(false)
const backStoryToggleExpand = () => {
  backStoryCollapsed.value = !backStoryCollapsed.value
}

const profileRows = computed(() => {
  const row = Math.max(Math.ceil(formData.synopsis.length / 20), 3)
  if (row > 3) {
    profileShow.value = true
  }
  return row
})
const profileCollapsed = ref(false)
const profileShow = ref(false)
const profileToggleExpand = () => {
  profileCollapsed.value = !profileCollapsed.value
}
</script>

<template>
  <div
    id="nav-create-agent-container"
    class="gradient-bg"
  >
    <CreateAgentPageNavBar
      :title="`${t('advancedCreation')}(2/2)`"
      :zIndex="10"
      @nav-bar-close="navBarClose"
    />
    <van-form
      class="form-box"
      ref="formRef"
    >
      <p class="item-title">{{ t('characterSettingRequired') }}</p>
      <div class="item">
        <van-field
          ref="backStoryRef"
          v-model="formData.role_setting"
          :rows="roleCollapsed ? 3 : roleRows"
          type="textarea"
          class="inp"
          name="pattern"
          maxlength="300"
          show-word-limit
          :placeholder="t('characterSetTips')"
          :rules="[{ pattern: /^.+$/, message: t('characterSetTips') }]"
        />
        <div class="toggleBtn">
          <img
            v-if="roleCollapsed && roleShow"
            src="@\assets\images\agentCreate\expand.png"
            alt=""
            style="width: 24.25px; height: 13px"
            @click="roleToggleExpand"
          />
          <img
            v-else-if="!roleCollapsed && roleShow"
            src="@\assets\images\agentCreate\fold.png"
            alt=""
            style="width: 24.25px; height: 13px"
            @click="roleToggleExpand"
          />
        </div>
      </div>
      <p class="item-title">{{ t('openingLine') }}</p>
      <div class="item">
        <van-field
          v-model="formData.opening_statement"
          type="text"
          :center="true"
          class="inp"
          name="pattern"
          :placeholder="t('openingLineTips')"
          maxlength="50"
          show-word-limit
          :rules="[{ pattern: /^.+$/, message: t('openingLineTips') }]"
        />
      </div>
      <p class="item-title">{{ t('backgroundStoryRequired') }}</p>
      <div class="item">
        <van-field
          ref="backStoryRef"
          v-model="formData.back_story"
          clearable
          :rows="backStoryCollapsed ? 3 : backStoryRows"
          type="textarea"
          class="inp"
          name="pattern"
          maxlength="300"
          show-word-limit
          :placeholder="t('backgroundStoryTips')"
          :rules="[{ pattern: /^.+$/, message: t('backgroundStoryTips') }]"
        />
        <div class="toggleBtn">
          <img
            v-if="backStoryCollapsed && backStoryShow"
            src="@\assets\images\agentCreate\expand.png"
            alt=""
            style="width: 24.25px; height: 13px"
            @click="backStoryToggleExpand"
          />
          <img
            v-else-if="!backStoryCollapsed && backStoryShow"
            src="@\assets\images\agentCreate\fold.png"
            alt=""
            style="width: 24.25px; height: 13px"
            @click="backStoryToggleExpand"
          />
        </div>
      </div>
      <p class="item-title">{{ t('characterIntroRequired') }}</p>
      <div class="item">
        <van-field
          ref="backStoryRef"
          v-model="formData.synopsis"
          :rows="profileCollapsed ? 3 : profileRows"
          type="textarea"
          class="inp"
          name="pattern"
          maxlength="300"
          show-word-limit
          :placeholder="t('characterIntroTips')"
          :rules="[{ pattern: /^.+$/, message: t('characterIntroTips') }]"
        />
        <div class="toggleBtn">
          <img
            v-if="profileCollapsed && profileShow"
            src="@\assets\images\agentCreate\expand.png"
            alt=""
            style="width: 24.25px; height: 13px"
            @click="profileToggleExpand"
          />
          <img
            v-else-if="!profileCollapsed && profileShow"
            src="@\assets\images\agentCreate\fold.png"
            alt=""
            style="width: 24.25px; height: 13px"
            @click="profileToggleExpand"
          />
        </div>
      </div>
      <div class="item">
        <van-cell class="cell">
          <template #title>
            <van-icon
              name="chat-o"
              style="margin-right: 8px"
            />
            <span class="item-title">{{ t('dialogueExampleOptional') }}</span>
          </template>
          <template #right-icon>
            <img
              src="/src/assets/images/agentCreate/add3.png"
              class="right-img"
              @click="goExample"
            />
          </template>
        </van-cell>
      </div>
      <div class="item">
        <van-cell class="cell">
          <template #title>
            <van-icon
              name="orders-o"
              style="margin-right: 8px"
            />
            <span class="item-title">{{ t('tagRequired') }}</span>
          </template>
          <template #right-icon>
            <img
              src="/src/assets/images/agentCreate/add3.png"
              class="right-img"
              @click="goTag"
            />
          </template>
          <template #label>
            <div class="tag-box">
              <van-tag
                v-for="item in tags"
                :key="item.id"
                plain
                type="primary"
                >{{ item.name }}</van-tag
              >
            </div>
          </template>
        </van-cell>
      </div>
      <CreateAgentBtn
        :btnDisabled="false"
        :loading="loading"
        class="btn"
        @submit="submit"
      >
        <template #left>
          <span v-if="agentCreateStore.id === 0">{{ t('createAndStartChat') }}</span>
          <span v-else>{{ t('saveButton') }}</span>
          <div
            v-if="agentCreateStore.id === 0"
            class="diamond"
          >
            <img
              src="@/assets/images/agentCreate/diamond.png"
              alt=""
            />
            <span>{{ diamond }}{{ t('diamond') }}</span>
          </div>
        </template>
      </CreateAgentBtn>
    </van-form>
  </div>
  <!-- <Loading v-if="loading" /> -->
</template>

<style scoped lang="scss">
.form-box {
  padding-bottom: 50px;
  margin: 40px 16px;

  .item-title {
    margin-bottom: 4px;

    i {
      color: #b3a6af;
    }
  }

  .item {
    display: flex;
    flex-direction: column;
    padding: 0 13px;
    margin-bottom: 16px;
    background: rgba(255, 255, 255, 10%);
    border-radius: 16px;

    .label {
      color: #888;
    }

    .inp {
      width: 100%;
      padding: 8px;
      line-height: 24px;
      background: none;
      border: none;

      :deep(.van-field__control::placeholder) {
        font-size: 16px;
        color: #888;
      }
    }

    .cell {
      padding: 0;
      line-height: 50px;
      background: none;
      border: none;

      :deep(.van-field__control::placeholder) {
        font-size: 16px;
        color: #888;
      }

      .van-icon {
        line-height: 50px;
      }

      .tag-box {
        padding-bottom: 16px;

        .van-tag {
          padding: 6px 10px;
          margin-right: 8px;
          color: #fff;
          background-color: rgba(237, 59, 230, 50%);
          border-color: rgba(237, 59, 230, 100%);
          border-radius: 12px;
        }
      }
    }
  }

  .gender {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .right-img {
    width: 24px;
    height: 24px;
    margin-top: 11px;
    vertical-align: middle;
  }
}

.submit-btn {
  position: fixed;
  bottom: 5%;
  left: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 32px);
  height: 52px;
  padding: 0 24px;
  overflow: hidden;
  background: #ff35f2;
  border-radius: 40px;

  .text {
    display: flex;
    align-items: center;

    .diamond-box {
      display: flex;
      align-items: center;
      font-size: 12px;

      .diamond-img {
        width: 20px;
        height: 20px;
        margin: 0 4px 0 8px;
      }
    }
  }

  img {
    height: 24px;
  }
}

.diamond {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;

  img {
    margin-right: 4px;
    vertical-align: middle;
  }
}

.toggleBtn {
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}

textarea {
  transition: height 1s ease-in-out;
}
</style>
