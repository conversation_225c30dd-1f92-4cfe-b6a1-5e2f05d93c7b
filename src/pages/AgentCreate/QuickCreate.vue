<script setup lang="ts">
import { register } from 'swiper/element/bundle'
import Dialog from '@/components/Dialog.vue'
import type { PickerConfirmEventParams } from 'vant'
import { getPresetImageList, getTemplateListApi, quickCreate, getCreateConfigApi } from '@/api/agentCreate'
import { useModal } from '@/hooks/useModal.ts'
import useAgentCreateStore from '@/stores/modules/agentCreate'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
import type { PresetImageItem, TemplateItem } from '@/api/agentCreate/types'
register()
definePage({
  name: 'QuickCreate'
})

onMounted(() => {
  eventReport({ event_type: EventTypeEnum.QUICK_CREATE_AI, front_address: ForwardAddressEnum.NAV_CREATE_BUTTON })
  // activeSlide.value = agentCreateStore.preset_image_id
  formData.name = agentCreateStore.name
  setTimeout(() => {
    if (agentCreateStore.template_id !== 0) {
      formData.template_id = agentCreateStore.template_id
      template_name.value = templateList.value.filter((item) => {
        console.log(item.id, agentCreateStore.template_id)
        if (item.id === agentCreateStore.template_id) {
          return item
        }
      })[0].name
    }
  }, 1000)
})

const { t } = useI18n()
const agentCreateStore = useAgentCreateStore()
const router = useRouter()
const loading = ref(false)
const formRef = ref()
const dialogRef = ref<InstanceType<typeof Dialog> | null>(null)
// 快速创建表单
const formData = reactive({
  preset_image_id: 0,
  template_id: null,
  name: '',
  sex: 0,
  is_show: false
})
const activeSlide = ref(0)
const showGenderPicker = ref(false)
const swiperSlideChange = (e: any) => {
  activeSlide.value = e.detail[0].realIndex
  agentCreateStore.preset_image_id = activeSlide.value
  eventReport({ event_type: EventTypeEnum.SELECT_QUICK_AI_PROFILE, ai_id: e.detail[0].realIndex })
}
const selectGender = () => {
  showGenderPicker.value = true
}

//获取消耗钻石数
const diamond = ref(0)
const getCreateConfig = () => {
  getCreateConfigApi().then((res) => {
    diamond.value = Number(res.data.ai_size_quick)
  })
}
getCreateConfig()

//提交创建
const submit = async () => {
  if (formData.template_id === 0) {
    useModal({
      message: t('agentTemplateTips'),
      duration: 1500
    })
    return
  }
  await formRef.value.validate()
  formData.preset_image_id = agentList.value[activeSlide.value].id
  formData.sex = agentCreateStore.sex
  formData.is_show = agentCreateStore.is_show
  if (formData.template_id === null || formData.name === '') {
    useModal({
      message: t('agentInfoTips'),
      duration: 1500
    })
    return
  }
  loading.value = true
  quickCreate(formData)
    .then((res) => {
      if (res.code === 200) {
        loading.value = false
        useModal({
          message: t('createAgentSuccess'),
          duration: 1500
        })
        router.replace(`/chat/${res.data}`)
      }
    })
    .catch((err) => {
      loading.value = false
      if (err.code === 248141001) {
        router.push(`/Mine/Crystal`)
      }
      console.log(err)
    })
}

const template_name = ref(t('selectRequest'))
const onGenderConfirm = ({ selectedValues }: PickerConfirmEventParams) => {
  formData.template_id = Number(selectedValues[0])
  agentCreateStore.template_id = formData.template_id
  template_name.value = templateList.value.filter((item) => {
    if (item.id === Number(selectedValues[0])) {
      return item
    }
  })[0].name

  showGenderPicker.value = false
}

const onGenderCancel = () => {
  showGenderPicker.value = false
}
const navBarClose = () => {
  dialogRef.value.showDialog()
}

// 获取预设图
const agentList = ref<PresetImageItem[]>([])
const handlePresetImageList = () => {
  getPresetImageList({ sex: agentCreateStore.sex }).then((res) => {
    if (res.code === 200) {
      agentList.value = res.data
    }
  })
}
handlePresetImageList()

// 获取模板
const templateList = ref<TemplateItem[]>([])
const customFieldName = {
  text: 'name',
  value: 'id'
}
const getTemplateList = () => {
  getTemplateListApi({ sex: agentCreateStore.sex }).then((res) => {
    if (res.code === 200) {
      templateList.value = res.data
    }
  })
}
getTemplateList()

const goBack = () => {
  agentCreateStore.showAvatar = false
  agentCreateStore.name = ''
  agentCreateStore.sound = 0
  agentCreateStore.role_setting = ''
  agentCreateStore.opening_statement = ''
  agentCreateStore.back_story = ''
  agentCreateStore.synopsis = ''
  agentCreateStore.talk_example = []
  agentCreateStore.tags = []
  agentCreateStore.sex = 0
  agentCreateStore.is_show = false
  agentCreateStore.template_id = 0
  agentCreateStore.preset_image_id = 0
  router.push('/')
}
</script>

<template>
  <div
    id="nav-create-agent-container"
    class="container gradient-bg"
  >
    <CreateAgentPageNavBar
      :zIndex="10"
      @nav-bar-close="navBarClose"
    />
    <div class="swiper-box">
      <swiper-container
        :init="true"
        slides-per-view="auto"
        :centered-slides="true"
        :looped-slides="5"
        @swiperslidechange="swiperSlideChange"
        :initialSlide="activeSlide"
      >
        <swiper-slide
          v-for="(item, index) in agentList"
          :key="item.id"
          class="swiper-slide"
        >
          <img
            :src="item.pic_url"
            class="swiper-img"
          />
          <div
            v-show="index === activeSlide"
            class="avatar-box"
          >
            <img
              object-fit="cover"
              :src="item.avatar_url"
              class="avatar"
            />
          </div>
        </swiper-slide>
      </swiper-container>
      <!-- <div class="pagination"></div> -->
    </div>
    <van-form
      ref="formRef"
      class="form-box"
    >
      <div
        class="item gender"
        @click="selectGender"
      >
        <span class="label">{{ t('template') }}</span>
        <span>{{ template_name }}</span>
        <img
          src="@/assets/images/agentCreate/arrow-right.png"
          style="width: 24px; height: 24px"
        />
      </div>
      <div class="item">
        <van-field
          v-model="formData.name"
          type="text"
          :placeholder="t('enterNickname')"
          :center="true"
          class="inp"
          @change="agentCreateStore.name = formData.name"
        />
        <!-- 
          :rules="[{ pattern: /^.+$/, message: '请输入昵称' }]"
         -->
      </div>
    </van-form>
    <CreateAgentBtn
      :btnDisabled="false"
      :loading="loading"
      class="btn"
      @submit="submit"
    >
      <template #left>
        <div class="text">
          <span>{{ t('createAndStartChat') }}</span>
          <div class="diamond-box">
            <img
              src="@/assets/images/agentCreate/diamond.png"
              class="diamond-img"
            />
            <span>{{ diamond }}{{ t('diamond') }}</span>
          </div>
        </div>
      </template>
    </CreateAgentBtn>
    <van-popup
      v-model:show="showGenderPicker"
      position="bottom"
    >
      <van-picker
        :title="t('template')"
        :columns="templateList"
        :columns-field-names="customFieldName"
        @confirm="onGenderConfirm"
        @cancel="onGenderCancel"
      />
    </van-popup>
    <Dialog ref="dialogRef">
      <template #title>
        <span class="dialog-title">{{ t('cancelCreate') }}</span>
      </template>
      <div class="dialog-content flex-center-center">
        <span>{{ t('cancelCreateTips') }}</span>
        <span>{{ t('cancelCreateConfirm') }}</span>
      </div>
      <template #footer>
        <div class="flex-between-center dialog-footer">
          <div
            class="cancel-btn dialog-footer-btn"
            @click="dialogRef.closeDiaog()"
          >
            {{ t('cancelButton') }}
          </div>
          <div
            class="confirm-btn dialog-footer-btn"
            @click="goBack"
          >
            {{ t('confirmButton') }}
          </div>
        </div>
      </template>
    </Dialog>
  </div>
  <Loading v-if="loading" />
</template>

<style scoped lang="scss">
.container {
  padding-bottom: 50px;

  .swiper-box {
    margin-top: 24px;

    .swiper-slide {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 224px;
      height: 380px;
      font-size: 18px;
      text-align: center;
      filter: brightness(50%);
      transition: 300ms;
      transform: scale(0.8);

      .swiper-img {
        width: 224px;
        height: 380px;
        border: 10px solid rgba(255, 255, 255, 10%);
        border-radius: 16px;
      }

      .avatar-box {
        position: absolute;
        right: 17px;
        bottom: 17px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        background: linear-gradient(180deg, rgba(255, 53, 242, 100%), rgba(255, 109, 89, 100%));
        border-radius: 100px;

        .avatar {
          width: 48px;
          height: 48px;
          border-radius: 100px;
        }
      }
    }

    .swiper-slide-active,
    .swiper-slide-duplicate-active {
      filter: brightness(100%);
      transform: scale(1);
    }
  }

  .form-box {
    margin: 40px 16px;

    .item {
      height: 50px;
      padding: 0 13px;
      margin-bottom: 16px;
      background: rgba(255, 255, 255, 10%);
      border-radius: 16px;

      .label {
        color: #888;
      }

      .inp {
        width: 100%;
        height: 100%;
        padding: 0;
        background: none;
        border: none;

        :deep(.van-field__control::placeholder) {
          font-size: 16px;
          color: #888;
        }
      }
    }

    .textarea {
      height: 178px;
      padding-top: 16px;
    }

    .gender {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .submit-btn {
    position: fixed;
    right: 16px;
    bottom: 32px;
    left: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 52px;
    padding: 0 24px;
    overflow: hidden;
    background: #ff35f2;
    border-radius: 40px;

    .text {
      display: flex;
      align-items: center;

      .diamond-box {
        display: flex;
        align-items: center;
        font-size: 12px;

        .diamond-img {
          width: 20px;
          height: 20px;
          margin: 0 4px 0 8px;
        }
      }
    }
  }
}

.dialog-title {
  font-size: 18px;
  color: #fff;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  padding: 24px 10%;

  span {
    font-size: 14px;
    line-height: 1.5;
    color: #c1c1c1;
    text-align: center;
  }
}

.dialog-footer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 14px;
  justify-content: center;
  width: 90%;
  padding-bottom: 20px;
  margin: 0 auto;
}

.dialog-footer-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 0;
  border-radius: 16px;
}

.cancel-btn {
  background: #3d3d3d;
}

.confirm-btn {
  background: #ff35f2;
}
</style>
