import { DirectiveBinding } from 'vue'
import { transformAspectRadio } from '@/utils'

type ScrollGradationBgValue = {
  scrollElement?: HTMLElement
  completedHeight?: number
  color?: string
  startHeight?: number
  font?: string | false
  func?: (scrollTop: number) => void
}

export const scrollGradationBg = {
  updated(el: HTMLElement, binding: DirectiveBinding<ScrollGradationBgValue>) {
    const { scrollElement = el, completedHeight = 50, color = '0,0,0', startHeight = 0, font = false, func } = binding.value || {}

    const onScroll = () => {
      const scrollTop = scrollElement.scrollTop
      const opacity = Math.min(
        (scrollTop - transformAspectRadio(startHeight) > 0 ? scrollTop - transformAspectRadio(startHeight) : 0) /
          (transformAspectRadio(completedHeight) - transformAspectRadio(startHeight)),
        1
      )

      el.style.backgroundColor = `rgba(${color}, ${opacity})`
      if (font) {
        el.style.color = `rgba(${font}, ${opacity})`
      }
      func && func(scrollTop)
    }

    scrollElement.addEventListener('scroll', onScroll)

    // @ts-ignore
    el.__onScrollHandler__ = onScroll
    // @ts-ignore
    el.__scrollElement__ = scrollElement
  },

  unmounted(el: HTMLElement) {
    // @ts-ignore
    el.__scrollElement__?.removeEventListener('scroll', el.__onScrollHandler__)
    // @ts-ignore
    delete el.__onScrollHandler__
    // @ts-ignore
    delete el.__scrollElement__
  }
}
