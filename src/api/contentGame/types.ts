import { AgentMessageType } from '@/api/agentHomePage/types.ts'

export interface IContentRes {
  api_key: number
  id: number
  type: number
  ai_id: number
  ai_info: AgentMessageType
  level: number
  name: string
  opening_statement: string
  opening_statement_outside: string
  cover_url: string
  cover_url_vertical: string
  status: number
  lock_status: number
  create_time: string
  update_time: string
  prefix_url: string
}

export type IContentListRes = IContentRes[]

export enum ContentGameStatusEnum {
  LOCKED,
  UNLOCKED,
  COMPLETED
}

export interface ContentGameRecordType {
  input_type: number
  input_query: string
  output_answer_event: EventRecordType[]
}

export interface EventRecordType {
  event: string
  answer: string
  material_id?: number
  video_id?: number
  npc_id?: number
  pass?: string
  end_id?: number
}

export interface StoryRecordType {
  id: number
  story_name: string
  create_time: string
  content_id: number
  content_info: {
    cover_url: string
    cover_url_vertical: string
    prefix_url: string
  }
}
