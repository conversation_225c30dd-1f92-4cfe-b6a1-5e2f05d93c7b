import service from '@/utils/service.ts'
import { IContentListRes, StoryRecordType } from '@/api/contentGame/types.ts'

export const getAgentContentGameList = (data: { ai_id: number }) => {
  return service.post<IContentListRes>({
    url: '/apiv1/ContentV2/list',
    method: 'post',
    data
  })
}

export const getSingleAgentContentGameInChatPage = (data: { ai_id: number }) => {
  return service.post<IContentListRes>({
    url: '/apiv1/ContentV2/chat_page_list',
    method: 'post',
    data
  })
}

export const getAgentContentGameOfMyList = (data: { ai_id: number }) => {
  return service.post<StoryRecordType[]>({
    url: '/apiv1/ContentV2/story_list',
    method: 'post',
    data
  })
}

export const base64ToUrl = (data: { b64: string }) => {
  return service.post<string>({
    url: '/apiv1/tool/b64_to_img',
    method: 'post',
    data
  })
}
