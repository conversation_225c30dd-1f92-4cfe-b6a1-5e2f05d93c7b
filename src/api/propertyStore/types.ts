import { ICurrencyTypeEnum } from '@/enums'

export interface IUserGiftListType {
  id: number
  goods_name: string
  balance_count: number
  intimacy: number
  icon: string
}

export interface IDressListType {
  id: number
  name: string
  avatar_url: string
  dress_list: DressType[]
}

export interface DressType {
  id: number
  dress_id: number
  type: SkinTypeEnum
  name: string
  set_id: number
  ai_id?: number
  currency_type: ICurrencyTypeEnum
  value: number
  create_time: string
  set_bg_time: string
  pic_url: string
  show_pic?: string
  is_buy: number
  isSelected?: boolean
}

export enum SkinTypeEnum {
  Video = 3,
  ThreeD = 2,
  Normal = 1
}
