import service from '@/utils/service.ts'
import { INormalList, IRequestPageNormalParams } from '@/types'
import { DressType, IDressListType, IUserGiftListType } from '@/api/propertyStore/types.ts'

export const getMyGiftList = (data: IRequestPageNormalParams) => {
  return service.post<INormalList<IUserGiftListType>>({
    url: '/apiv1/user/my_gift',
    data
  })
}

export const getMyDressList = (data: IRequestPageNormalParams) => {
  return service.post<DressType[]>({
    url: '/apiv1/dress/getPackDress',
    data
  })
}

export const getDress = (data: IRequestPageNormalParams & { name?: string }) => {
  return service.post<IDressListType[]>({
    url: '/apiv1/dress/getDressList',
    data
  })
}
