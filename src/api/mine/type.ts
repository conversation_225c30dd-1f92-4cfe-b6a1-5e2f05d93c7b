import { SexEnum } from '@/enums'

export interface userQuery {
  name?: string
  avatar_url?: string
  birthday?: string
  desc?: string
  sex?: number
  nsfw?: number
  invite_code?: string
}
export interface logoff {
  email: string
  code: string
}
export interface QueryPrice {
  product_type: string
  product_id: string | number
  order_type: string
  front_address?: string
}
export interface QueryPay {
  amount_id: string
  pay_channel: string
}
export interface cardItem {
  goods_name: string
  discount: number
  newpay_price: string
  renew_price: string
  id: string
  interest_list: { name: string; icon: string }[]
  original_price: string | number
  original_newpay_price: string
  unit: string
}
export interface goodItem {
  id: number
  name: string
  type: number
}
export interface payItem {
  order_id: string
  paypal_order_id: string
}

export interface IUserInfo {
  client_id?: number
  name?: string
  email?: string
  create_time?: string
  update_time?: string
  last_login_ip?: string
  last_login_time?: string
  now_login_ip?: string
  now_login_time?: string
  status?: number
  area_code?: string
  phone?: string
  first_login_time?: string
  sex?: number
  is_set_preference?: number
  vip_goods_id?: number
  gold_amount?: number
  avatar_url?: string
  birthday?: string
  desc?: string
  vip_expiration_time?: string
  my_invite_code?: string
  invite_client_id?: number
  is_delete?: number
  delete_date?: string
  client_type?: number
  register_ip?: string
  country?: string
  register_channel?: string
  nsfw?: number
  image_id?: number
  fans_count?: number
  like_ai_count?: number
  follow_count?: number
  crystal_amount?: number
  vip_name?: string
  vip_type?: number
  current_image?: string
  is_follow?: number
}

interface IPage {
  page: number
  limit: number
}

export interface IAiListParams extends IPage {
  other_client_id?: number | null
}

export interface IFollowList<T> {
  count: number
  list: T[]
}

export interface IFollowListItem {
  client_id: number
  name: string
  email: string
  create_time: string
  update_time: string
  last_login_ip: string
  last_login_time: string
  now_login_ip: string
  now_login_time: string
  status: number
  area_code: string
  phone: string
  first_login_time: string
  sex: number
  is_set_preference: number
  vip_goods_id: number
  gold_amount: number
  avatar_url: string
  birthday: string
  desc: string
  vip_expiration_time: string
  my_invite_code: string
  invite_client_id: number
  is_delete: number
  delete_date: string
  client_type: number
  register_ip: string
  country: string
  register_channel: string
  nsfw: number
  image_id: number
  is_follow?: number
  is_me?: number
}

export interface IListParams extends IPage {
  client_id?: number
}

export interface ICrystalPromotion {
  id: number
  price: string
  create_time: string
  update_time: string
  discount: number
  amount: number
  is_sell: number
  standard_id: number
  type: number
  start_time: string
  end_time: string
  delete_time: string
  desc: string
  original_price: string
}

export interface ICrystalParams {
  product_type: string
  count?: number
  product_id?: number
  gold_amount?: number
  front_address?: string
}

export interface IGoldPriceId {
  amount_id: string
}

export interface IFaq {
  count: number
  list: IFaqItem[]
}

export interface IFaqItem {
  id: number
  title: string
  status: number
  sort: number
  create_time: string
  update_time: string
  content: string
}

export interface Pagination {
  page?: number
  limit?: number
  total?: number
}

export interface FigureItem extends Pagination {
  id?: number
  client_id?: number
  create_time?: Date
  update_time?: Date
  nickname?: string
  sex?: SexEnum
  birthday?: string
  desc?: string
  // 相对路径
  avatar_url?: string
  // 完整路径
  full_avatar_url?: string
  image_url?: string
  is_choose: number
}

export interface FigureListReq extends Pagination {
  action?: string
  nickname?: string
  sex?: SexEnum
  birthday?: string
  desc?: string
  avatar_url?: string
  full_avatar_url?: string
  image_id?: number
  ai_id?: number
}

export interface FigureListRes {
  list?: FigureItem[]
  count?: number
  default_image_id?: number
}

export interface UserUpdateReq {
  name?: string
  avatar_url?: string
  birthday?: string
  desc?: string
  sex?: SexEnum
  // NSFW开关:0-关;1-是
  nsfw?: 0 | 1
  invite_code?: string
  image_id?: number
}
export interface UserUpdateRes {}

export interface SupportItem {
  id?: string
  logo?: string
  name?: string
  support_key?: string
  merchant_info?: {
    client_id?: string
  }
}

export interface PaymentMethodItem {
  method?: string
  pay_channel?: string
  support?: SupportItem[]
}

export interface CountryItem {
  area_code?: string
  country_code?: string
  country_name?: string
  payment_method?: PaymentMethodItem[]
}

export interface CountryListRes {
  list?: CountryItem[]
}
