import service from '@/utils/service.ts'
export const newerStoryTextChat = (data: any) => {
  return service.stream({
    url: '/apiv1/Welcome/chat',
    data: {
      type: 1,
      ...data
    }
  })
}

export const newerStoryAudioChat = (data: FormData) => {
  return service.stream({
    url: '/apiv1/Welcome/chat',
    data
  })
}

export const newerStoryInit = () => {
  return service.stream({
    url: '/apiv1/Welcome/chat',
    data: {
      type: 3
    }
  })
}

export const newerTtsMessage = (data: { text: string }) => {
  return service.stream({
    url: '/apiv1/Welcome/tts',
    data
  })
}

export const buildAgentConnection = () => {
  return service.post({
    url: '/apiv1/Welcome/add_to_msg_list'
  })
}
