export interface ISearchRecord {
  count: number
  list: ISearchRecordItem[]
}

export interface ISearchRecordItem {
  id: number
  client_id: number
  create_time: string
  update_time: string
  content: string
}

export interface ICreaterParams {
  keyword?: string
  page?: number
  limit?: number
}

export interface ICreaterList {
  count: number
  list: ICreaterListItem[]
}

export interface ICreaterListItem {
  client_id: number
  name: string
  email: string
  create_time: string
  update_time: string
  last_login_ip: string
  last_login_time: string
  now_login_ip: string
  now_login_time: string
  status: number
  area_code: string
  phone: string
  first_login_time: string
  sex: number
  is_set_preference: number
  vip_goods_id: number
  gold_amount: number
  avatar_url: string
  birthday: string
  desc: string
  vip_expiration_time: string
  my_invite_code: string
  invite_client_id: number
  is_delete: number
  delete_date: string
  client_type: number
  register_ip: string
  country: string
  register_channel: null | string
  nsfw: number
  image_id: number
  fans_count: number
  show_ai_count: number
  is_follow: number
  is_me: number
}

export interface IHotSearch {
  count: number
  list: IHotSearchItem[]
}

export interface IHotSearchItem {
  ai_id: number
  terminal: string
  ai_info: {
    tag_list: string
    like_status: number
    ai_chat_setting: {
      show_disclaimer: number
      show_character: number
      show_prologue: number
      show_muse: number
      intimacy_now: number
      intimacy_upgrade: number
      intimacy_level: number
    }
    id: number
    name: string
    sound_ray_url: string
    client_id: number
    follow: string
    chat_times: string
    talk_num: number
    image_url: string
    create_time: string
    update_time: string
    api_key: string
    dataset_id: string
    app_id: string
    role_setting: string
    opening_statement: string
    back_story: string
    talk_example: string
    synopsis: string
    voice_id: null | string
    bgm_id: null | string
    is_show: number
    status: number
    avatar_url: string
    client_image_id: number
    terminal: string[]
    type: number
    rank: number
    client_info: {
      client_id: number
      name: string
      email: string
    }
  }
}
