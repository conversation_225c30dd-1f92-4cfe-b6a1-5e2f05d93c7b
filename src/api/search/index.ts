import service from '@/utils/service.ts'
import { ISearchRecord, ICreaterList, ICreaterParams, IHotSearch } from './types'

export const getSearchRecord = () => {
  return service.get<ISearchRecord>({
    url: '/apiv1/info/get_search_record'
  })
}

export const cleanSearchRecord = () => {
  return service.get({
    url: '/apiv1/info/clean_search_record'
  })
}

export const getCreaterList = (params: ICreaterParams) => {
  return service.post<ICreaterList>({
    url: '/apiv1/user/creater_list',
    data: params
  })
}

export const setFollow = (follow_client_id: number) => {
  return service.post({
    url: '/apiv1/user/set_follow',
    data: {
      follow_client_id
    }
  })
}

export const getHotSearch = () => {
  return service.get<IHotSearch>({
    url: '/apiv1/info/hot_search'
  })
}
