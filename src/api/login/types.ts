export interface MqttInfo {
  mqtt_url?: string
  mqtt_username?: string
  mqtt_password?: string
  mqtt_client_id?: string
  mqtt_ws_url?: string
  mqtt_wss_url?: string
  mqtt_ssl_url?: string
  mqtt_topic?: string
}

export interface ApiRes<T> {
  code?: number
  msg?: string
  data?: T
}

export interface UserInfo {
  is_set_preference: number
  login_id: string
  access_token: string
  is_delete: number
  avatar_url: string
  first_login: number
  vip_info: IMember[]
  vip_count: number
}

export interface TagList {
  tag_list: []
}

export interface IPreferenceParams {
  sexs: number[]
  interest: number[]
}

export interface ILoginParams {
  code: string
  email: string
  invite_code?: string
}

export interface IUgPhoneLoginParams {
  ugphone_user_id: string
  ug_visitor_id: string
  small_id?: string
  game_id?: string
  timestamp?: string
  user_id?: string
  token?: string
  screen_type?: string
  lang?: string
  sign?: string
  key?: string
}

export interface ILoginMsg {
  login_id: string
  access_token: string
  mqtt_client_id: string
  mqtt_password: string
  mqtt_topic: string
  mqtt_username: string
  mqtt_wss_url: string
}

export interface IPreference {
  sex: number[]
  tag_ids: number[]
}
export interface IRecommendPreference {
  id: number
  name: string
  tag_list: ITag_ids[]
}

export interface ITag_ids {
  id: number
  name: string
  light: boolean
}

export interface IGoogleLoginParams {
  credential: string
  req_id?: string
  olink?: number
  is_cancel?: number
  client_id?: number
}

export interface IFaceBookParams {
  access_token: string
  req_id?: string
  is_cancel?: number
  client_id?: number
}

export interface LoginRes extends MqttInfo {
  login_id?: string
  access_token?: string
}

export interface IAppleLoginParams {
  code?: string
  id_token?: string
  state?: string
  is_cancel?: number
  client_id?: number
}

export interface ICallBackParams {
  credential?: string
  access_token?: string
  accessToken?: string
  code?: string
  state?: string
  id_token?: string
}

export interface IMemberInfo {
  vip_info: IMember[]
  vip_count: number
}

export interface IMember {
  vip_name: string
  vip_expiration_time: string
  vip_type: number
}

export interface IAbTestType {
  test_code: string
  is_first_begin: number
  ai_id: number
}

export enum AbTestEnum {
  HOMEPAGE = 2
}

export interface PreferenceReqType {
  question1: string
  question2: string
}
