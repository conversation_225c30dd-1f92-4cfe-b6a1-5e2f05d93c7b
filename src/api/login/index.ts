import service from '@/utils/service.ts'
import {
  ILoginMsg,
  UserInfo,
  IPreferenceParams,
  ILoginParams,
  IPreference,
  IGoogleLoginParams,
  IFaceBookParams,
  IAppleLoginParams,
  IRecommendPreference,
  IUgPhoneLoginParams,
  IAbTestType,
  AbTestEnum,
  PreferenceReqType
} from './types'

export const send_auth_code = (data: any) => {
  return service.post({
    url: '/apiv1/login/send_auth_code',
    data
  })
}

export const login = (data: ILoginParams) => {
  return service.post({
    url: '/apiv1/login/login',
    data
  })
}

export const ugPhoneLogin = (data: IUgPhoneLoginParams) => {
  return service.post({
    url: '/apiv1/login/ugphone_login',
    data
  })
}

export const get_tag_list = <T>() => {
  return service.get<T>({
    url: '/apiv1/info/get_tag_list'
  })
}
export const set_preference = (data: IPreferenceParams) => {
  return service.post({
    url: '/apiv1/user/set_preference',
    data
  })
}

export const user_info = () => {
  return service.post<UserInfo>({
    url: '/apiv1/user/user_info'
  })
}
export const logout = () => {
  return service.get({
    url: '/apiv1/login/logout'
  })
}

export const facebookLogin = (data: IFaceBookParams) => {
  return service.post<ILoginMsg>({
    url: '/apiv1/login/facebook_login',
    data
  })
}
export const googleLogin = (data: IGoogleLoginParams) => {
  return service.post<ILoginMsg>({
    url: '/apiv1/login/google_login',
    data
  })
}
export const appleLogin = (data: IAppleLoginParams) => {
  return service.post<ILoginMsg>({
    url: '/apiv1/login/apple_login',
    data
  })
}

export const getPreference = () => {
  return service.get<IPreference>({
    url: '/apiv1/user/get_preference'
  })
}
export const getRecommendPreference = () => {
  return service.get<IRecommendPreference[]>({
    url: '/apiv1/info/get_recommend_tags'
  })
}

export const cancelLogoff = () => {
  return service.get({
    url: '/apiv1/login/close_delete'
  })
}

export const abTest = (data: AbTestEnum[]) => {
  return service.post<Record<AbTestEnum, IAbTestType>>({
    url: '/apiv1/tool/uuid_ab_test',
    data: {
      mission_ids: data
    }
  })
}

export const preference = (data: PreferenceReqType) => {
  return service.post<number>({
    url: '/apiv1/info/question_to_ai',
    data
  })
}
