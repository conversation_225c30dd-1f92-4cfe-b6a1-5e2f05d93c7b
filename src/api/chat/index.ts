import service from '@/utils/service.ts'
import { INormalList, IRequestPageNormalParams } from '@/types'
import { AdvInfoType, ChatRequestType, IChatRecordType, IChatResponseType, IDialogueRecord, ReportType } from '@/api/chat/types.ts'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'

export const reportAI = (data: ReportType) => {
  return service.post({
    url: '/apiv1/info/report',
    data
  })
}

export const chat = (data: ChatRequestType) => {
  return service.post<IChatResponseType>({
    url: '/apiv1/chat/chat_v3',
    data
  })
}

export const getChatRecord = (data: IRequestPageNormalParams & { ai_id: number }) => {
  return service.post<INormalList<IChatRecordType> & { double_get_crystal: number }>({
    url: '/apiv1/chat/chat_record_v3',
    data
  })
}

export const updateRecord = (data: { record_id: number; output_text: IDialogueRecord }) => {
  return service.post({
    url: '/apiv1/chat/update_record',
    data
  })
}

export const getAdvInfo = () => {
  return service.post<AdvInfoType>({
    url: '/apiv1/info/adv_info'
  })
}

export const getCrystalCode = (data: { tag: 'get_crystal' | 'double_get_crystal' }) => {
  return service.post<string>({
    url: '/apiv1/wallet/get_adv_code',
    data
  })
}

export const getCrystal = (data: { code: string }) => {
  return service.post({
    url: '/apiv1/wallet/code_to_crystal',
    data
  })
}

export const playGetCrystal = () => {
  return service.post({
    url: '/apiv1/wallet/play_get_crystal'
  })
}

export const searchTool = (data: { ai_id: number; tool_type: string; tool_content: string }) => {
  return service.post<IChatResponseType>({
    url: '/apiv1/chat/tool_v2',
    data
  })
}

export const updateUserInfo = (data: { sound?: number; bgm?: number }) => {
  return service.post({
    url: '/apiv1/user/update',
    data
  })
}

export const getVideoMapByName = (data: { name: string; ai_id: number }) => {
  return service.post<AgentMessageType['video_map']>({
    url: '/apiv1/dress/get_video_map',
    data
  })
}
