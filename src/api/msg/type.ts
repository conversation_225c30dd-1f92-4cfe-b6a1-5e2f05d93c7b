import { Pagination } from '@/api/mine/type.ts'
import { IChatResponseType } from '@/api/chat/types.ts'

export interface msgList {
  list: []
  count: number
}

// 1 - 聊过 2- 喜欢 3 -我的创建
export interface ImsgParams {
  page: number
  limit: number
  type: number
}
export interface UnReadMsgRes {
  list?: UnReadMsgItem[]
  count?: number
  no_read_count?: number
}

export interface GiftItem {}

export interface UnReadMsgItem {
  // 0-普通消息;1-发放水晶;2-发放金币;3-关注消息;4-点赞消息;5-;领取礼物;
  type: number
  // 是否已读:0-否 ; 1- 是
  is_read: number
  // 角色id 是否为AI角色发出
  ai_id: number
  ai_info: {
    name: string
    image_url: string
    avatar_url?: string
  }
  amount?: number
  content?: string | IChatResponseType
  create_time?: Date
  gift_id?: string
  gift_info?: GiftItem[]
  id: number
  title?: string
  // 消息发起人 为0则是系统
  promoter_client_id: number
  promoter_client_info: {
    avatar_url: string
    name: string
  }
  recipient_client_id?: number
  update_time?: Date
  redirect_url?: string
  sort?: number
}

export interface BannerItem {
  content?: string
  create_time?: string
  update_time?: string
  title?: string
  sort?: number
  user_pay_type?: number
  notice_type?: number
  show_lang?: any
  poster?: string
  redirect_url?: string
  status?: number
  start_time?: string
  end_time?: string
  frequency?: number
  delete_time?: any
}

export interface PosterListReq {
  is_login: number
}

export interface PosterListRes {
  list?: BannerItem[]
  count?: number
}

export interface MsgReadReq {
  msg_id: number
}

export interface MsgNoReadNumReq {
  ai_id?: number
  mqtt_type?: string
  no_read_count?: number
}

export interface SystemListReq extends Pagination {}

export interface InteractionListReq extends Pagination {}
