import service from '@/utils/service.ts'
import { msgList, UnReadMsgRes, PosterListRes, MsgReadReq, UnReadMsgItem, ImsgParams, SystemListReq, InteractionListReq } from './type'

export const like_list = () => {
  return service.post<msgList>({
    url: '/apiv1/ai/like_list'
  })
}
export const collect_list = () => {
  return service.post<msgList>({
    url: '/apiv1/ai/collect_list'
  })
}
export const talk_list = () => {
  return service.post<msgList>({
    url: '/apiv1/ai/talk_list'
  })
}

export const deleteAi = (ai_id: number) => {
  return service.post({
    url: '/apiv1/ai/delete',
    data: {
      ai_id
    }
  })
}

export const getMsgList = (data: ImsgParams) => {
  return service.post<any>({
    url: '/apiv1/ai/msg_list',
    data
  })
}

// 即时信息通知
export const unReadMsg = (data: { limit: number; page: number }) => {
  return service.post<UnReadMsgRes>({
    url: '/apiv1/msg/no_read_list',
    data
  })
}

export const posterList = () => {
  return service.post<PosterListRes>({
    url: '/apiv1/msg/no_read_poster_list'
  })
}

export const systemList = (data: SystemListReq) => {
  return service.post<UnReadMsgRes>({
    url: '/apiv1/msg/system_list',
    data
  })
}

export const interactionList = (data: InteractionListReq) => {
  return service.post<UnReadMsgRes>({
    url: '/apiv1/msg/interaction_list',
    data
  })
}

export const msgRead = (data: MsgReadReq) => {
  return service.post({
    url: '/apiv1/msg/read',
    data
  })
}

export const msgSetTop = (data: { ai_id: number; is_top: number }) => {
  return service.post<any>({
    url: '/apiv1/ai/msg_set_top',
    data
  })
}

export const deleteTalk = (data: { ai_id: number }) => {
  return service.post({
    url: '/apiv1/ai/delete_talk',
    data
  })
}

export const msgDetail = (data: MsgReadReq) => {
  return service.post<UnReadMsgItem>({
    url: '/apiv1/msg/info',
    data
  })
}

// 开屏信息通知
export const noReadTextList = () => {
  return service.post<UnReadMsgRes>({
    url: '/apiv1/msg/no_read_text_list'
  })
}

export const allRead = (data: { type: string }) => {
  return service.post({
    url: '/apiv1/msg/all_read',
    data
  })
}

export const readMsg = (data: { ai_id: number }) => {
  return service.post({
    url: '/apiv1/chat/read_msg',
    data
  })
}
