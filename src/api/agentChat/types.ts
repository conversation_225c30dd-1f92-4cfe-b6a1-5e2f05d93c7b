import { ICurrencyTypeEnum } from '@/enums'

export interface AgentTextChatType {
  ai_id: number
  query: string
  interactive_model?: number
  conversation_id?: string
}

export interface AgentAudioChatType {
  ai_id: number
  audio_file: File
}

export interface AgentSendGiftType {
  ai_id: number
  gift_id: number
  gift_count: number
  interactive_model?: number
}

export interface PhoneCallExtension {
  phone_call_id: string
}

export type PhoneCallOpenReq = {
  ai_id: number
  interactive_model?: number
} & PhoneCallExtension

export interface PhoneCallTimeGoodsType {
  id: number
  phone_time: number
  crystal_amount: number
}

export interface HumePhoneConfigType {
  access_token: string
  chat_group_id: string
  config_id: string
}

export type AgentPhoneTextChatType = AgentTextChatType & PhoneCallExtension
export type AgentPhoneAudioChatType = AgentAudioChatType & PhoneCallExtension

export interface AgentChatResType {
  message_id: string
  pic_id?: number
  pic_info?: SelfieType
  input_query: string
  input_type: number
  output_type: number
  output_text: string
  is_bad: number
  is_later: number
  is_like: number
  is_phone_call: number
  session_id: string
  content_id?: number
  content_info: {
    name: string
    prefix_url: string
    level: string
    cover_url_vertical: string
    cover_url: string
    opening_statement: string
    opening_statement_outside: string
    into_button: string
    close_button: string
  }
  gift_info?: IGiftListType
  send_gift: number
  audio_file_url: string
  audio_length: number
  call_time: number
}
;[]

export interface LikeOrUnlikeType {
  message_id: string | number
  is_like?: number
  is_bad?: number
}

export interface GiftListResType {
  id: number
  name: string
  price: number
  intimacy: number
  icon: string
  prompt: string
  currency_type: ICurrencyTypeEnum
  is_day_limit_buy: number
  day_limit_buy: number
  is_weekly_limit_buy: number
  weekly_limit_buy: number
  client_have_count: number
}

export interface IGiftListType extends GiftListResType {
  selected: boolean
}
export interface SelfieType {
  id: number
  name: string
  pic_url: string
  vague_pic_url: string
  currency_type: ICurrencyTypeEnum
  value: number
  status: number
  create_time: string
  level: number
  rank: number
  level_int: number
}

export interface ActionType {
  id: number
  motion: string
  pic_url: string
  emotion_name?: string
  type: number
  rank: number
}

export interface ThreeDSourceType {
  model_json: string
  action_list: ActionType[]
  rotate: number
  change_model_json?: string
  parameters?: Record<string, number>
  back_pic?: string
  horizontal?: number
  vertical?: number
  scale?: number
}

export interface BlockChatResponseType {
  pjh_resp: {
    conversation_id: string
    frontend_answer: string
    tts_answer: string
    llm_emotion: string
    status: string
    tools?: ToolsType[]
  }
  voice_path: string
}

export interface ToolsType {
  tool_type: string
  tool_content: string
}
