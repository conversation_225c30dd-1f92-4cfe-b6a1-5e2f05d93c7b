import service from '@/utils/service.ts'
import {
  DraftItem,
  DraftList,
  TagItem,
  SoundItem,
  SoundList,
  AgentCreateQuery,
  QuickCreateQuery,
  PresetImageItem,
  FigureItem,
  TemplateItem,
  CreateConfigRes,
  RandomAiRes
} from './types'

export const getClientImageListApi = () => {
  return service.post<DraftList<DraftItem>>({
    url: '/apiv1/ai/getClientImageList'
  })
}

export const getTagList = () => {
  return service.post<TagItem[]>({
    url: '/apiv1/ai/getTagList'
  })
}

export const getVoicePreList = (data: { lang: string; sex: number }) => {
  return service.post<SoundList<SoundItem>>({
    url: '/apiv1/ai/getVoicePreList',
    data
  })
}

export const getPresetImageList = (data: { sex: number }) => {
  return service.post<PresetImageItem[]>({
    url: '/apiv1/ai/getPresetImageList',
    data
  })
}

export const normalCreate = (data: AgentCreateQuery) => {
  return service.post({
    url: '/apiv1/ai/normalCreate',
    data
  })
}

export const getTemplateListApi = (data: { sex: number }) => {
  return service.post<TemplateItem[]>({
    url: '/apiv1/ai/getTemplateList',
    data
  })
}

export const quickCreate = (data: QuickCreateQuery) => {
  return service.post({
    url: '/apiv1/ai/quickCreate',
    data
  })
}

export const getRandomAiApi = (data: { action: string }) => {
  return service.post<RandomAiRes>({
    url: '/apiv1/ai/getRandomAi',
    data
  })
}

export const getBuildPicApi = (data: { sex: number }) => {
  return service.post({
    url: '/apiv1/ai/getBuildPic',
    data
  })
}

//发起生图任务
export const createComfyUIApi = (data: any) => {
  return service.post({
    url: '/apiv1/dress/createComfyUI',
    data
  })
}

//获取生图信息
export const getComfyUIApi = (data: { prompt_id: string }) => {
  return service.post<FigureItem[]>({
    url: '/apiv1/dress/getComfyUI',
    data
  })
}

//获取钻石
export const getCreateConfigApi = () => {
  return service.post<CreateConfigRes>({
    url: '/apiv1/ai/createConfig'
  })
}
