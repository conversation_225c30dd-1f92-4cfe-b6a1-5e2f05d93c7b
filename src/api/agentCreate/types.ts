export interface AgentTextChatType {
  ai_id: number
  query: string
}

export interface PresetImageItem {
  id: number
  name: string
  sex: number
  avatar_url: string
  pic_url: string
  status: number
  create_user: number
  create_time: string
  update_time: string
  rank: number
}

export interface PresetImageList<T> {
  list: T[]
}
export interface DraftItem {
  id: number
  client_id: number
  name: string
  sex: number
  clothing: null
  pose: null
  backdrop: null
  avatar_url: string
  img_url: string
  create_user: number
  create_time: string
  update_time: string
}

export interface TemplateItem {
  id: number
  name: string
}

export interface TemplateList<T> {
  list: T[]
}

export interface DraftList<T> {
  list: T[]
  count: number
}

export interface InnerTag {
  id: number
  name: string
  isCheck: boolean
}

export interface TagItem {
  tag_list: InnerTag[]
}

export interface TalkItem {
  Q: string
  A: string
}

export interface SoundItem {
  voice_id: number
  name: string
  demo_url: string
  tts: string
  isPlay: boolean
}
export interface Lang {
  cht: string
  cn: string
  de: string
  en: string
  es: string
  fr: string
  ind: string
  jp: string
  ko: string
  pt: string
  ru: string
  th: string
  vi: string
}

export interface SoundList<T> {
  lang_arr: Lang
  list: T[]
}

export interface AgentCreateQuery {
  id: number
  avatar_url: string
  image_url: string
  voice_id: number
  name: string
  role_setting: string
  opening_statement: string
  back_story: string
  synopsis: string
  talk_example: TalkItem[]
  tags: TagItem[]
  action: string
  sex: number
  is_show: boolean
}

export interface QuickCreateQuery {
  preset_image_id: number
  template_id: number
  name: string
}

export interface PriceConfig {
  ai_size_quick: string
  ai_size_high: string
  create_image: string
}

export interface CreateConfigRes {
  ai_size_quick: string
  ai_size_high: string
  create_image: string
}

export interface ExampleItem {
  Q: string
  A: string
}

export interface FigureItem {
  client_id: number
  id: number
  img_url: string
  prompt_id: string
}

export interface RandomAiRes {
  name: string
  sex: string
  role_setting: string
  opening_statement: string
  back_story: string
  synopsis: string
  talk_example: ExampleItem[]
}
