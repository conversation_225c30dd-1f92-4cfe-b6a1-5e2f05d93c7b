import service from '@/utils/service.ts'
import { AgentMessageType, AlbumType, AlbumTypeEnum } from '@/api/agentHomePage/types.ts'
import { SelfieType } from '@/api/agentChat/types.ts'

export const getAIInfo = (data: { ai_id: number }) => {
  return service.post<AgentMessageType>({
    url: '/apiv1/ai/info',
    data
  })
}

export const setAgentLike = (data: { ai_id: number }) => {
  return service.post<string>({
    url: '/apiv1/ai/set_like',
    data
  })
}

export const deleteAgent = (data: { ai_id: number }) => {
  return service.post({
    url: '/apiv1/ai/delete',
    data
  })
}

export const getPictureList = (data: { ai_id: number; type: AlbumTypeEnum }) => {
  return service.post<AlbumType>({
    url: '/apiv1/ai/pic_list',
    data
  })
}

export const getPictureInfo = (data: { pic_id: number }) => {
  return service.post<SelfieType>({
    url: '/apiv1/ai/pic_info',
    data
  })
}

export const getOpeningVoice = (data: { ai_id: number }) => {
  return service.stream({
    url: '/apiv1/chat/opening_statement_voice',
    data
  })
}
