import { SelfieType } from '@/api/agentChat/types.ts'
import { SkinTypeEnum } from '@/api/propertyStore/types.ts'

export interface AgentMessageType {
  id: number
  name: string
  image_url: string
  bg_info: {
    bg_url: string
    set_id: number
    dress_id: number
    type: SkinTypeEnum
  }
  default_background: SkinTypeEnum
  default_live2d: {
    bg_url: string
    set_id: number
    dress_id: number
    type: SkinTypeEnum
  }
  default_video_map: VideoMapType
  description: string
  init_plot: string
  chat_times: string
  has_3d: number
  last_set_id: number
  bgm: string
  is_repeat: number
  api_key: string
  dataset_id: string
  camera_pic_status: number
  app_id: string
  sex: number
  role_setting: string
  opening_statement: string
  back_story: string
  talk_example: string
  synopsis: string
  voice_id: string
  sound_ray_url: string
  opening_statement_voice: string
  is_show: number
  is_quick: number
  follow: string
  status: number
  avatar_url: string
  client_image_id: string
  opening_option: string[]
  terminal: string[]
  update_time: string
  intimacy_level_info: {
    level: number
    interest_names: string[]
    interest_ids: number[]
    experience: number
  }[]
  client_info: {
    client_id: number
    name: string
    email: string
    avatar_url: string
  }
  admin_info: {
    admin_id: number
    email: string
    name: string
    avatar_url: string
  }
  tag_list: {
    id: number
    name: string
    isCheck: boolean
  }[]
  like_status: number
  ai_chat_setting: {
    image_id: number
    show_disclaimer: number
    show_character: number
    show_prologue: number
    show_muse: number
    intimacy_now: number
    intimacy_upgrade: number
    intimacy_level: number
    intimacy_interest_tags: string[]
  }
  video_map?: VideoMapType
  video_list?: IVideoType[]
  image_list?: IImageType[]
  resident_function: ResidentFunction[]
  resident_type: number
}

export interface VideoMapType {
  name: string
  url_prefix: string
  standby_url: string
  transition_url: string
  action_urls: {
    emotion: string
    url: string
  }[]
}

interface IVideoType {
  rank: string
  video_id: string
  path: string
}

interface IImageType {
  rank: string
  id: string
  path: string
}

export type ResidentFunction = 'dress' | 'call' | 'intimacy' | 'history' | 'gift'

export enum AlbumTypeEnum {
  NORMAL = 1,
  INTIMACY = 2
}

export interface AlbumType {
  name: string
  pic_list: SelfieType[]
}
