export interface IRankingParams {
  sort_type: number
  page: number
  limit: number
  album_id: number
}

export interface IRankingList {
  count: number
  list: IRankingItem[]
}

export interface AlbumList {
  id: number
  name: string
}

export interface IRankingItem {
  image_url: string
  id: number
  name: string
  avatar_url: string
  synopsis: string
  chat_times: string
  tag_list: {
    id: number
    name: string
  }[]
  received_gift_count: number
  client_info: {
    avatar_url: string
    name: string
  }
}
