import service from '@/utils/service.ts'
import { IRankingParams, IRankingList, AlbumList } from './types'
export const getRankingList = (data: IRankingParams) => {
  console.trace('ssd')
  return service.post<IRankingList>({
    url: '/apiv1/ai/list',
    data
  })
}

export const getAlbumList = () => {
  console.trace('sdsds')
  return service.post<AlbumList[]>({
    url: '/apiv1/info/get_album_list'
  })
}
