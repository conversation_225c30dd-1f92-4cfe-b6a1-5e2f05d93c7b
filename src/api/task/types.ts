export interface ITaskCompleteInfo {
  client_id: number
  create_time: string
  update_time: string
  task_id: number
  complete_number: number
  is_get: number
  is_complete: number
}

export interface ITask {
  id: number
  name: string
  type: string
  create_time: string
  update_time: string
  desc: string | null
  need_number: number
  gold_amount: number
  crystal_amount: number
  status: number
  tag: string
  reward_type: string | null
  gift_amount: number
  icon: string | null
  sort: number
  remark: string | null
  gift_id: number
  reward_times: string | null
  complete_info: ITaskCompleteInfo
  amount: number
  complete_condition: number
}
