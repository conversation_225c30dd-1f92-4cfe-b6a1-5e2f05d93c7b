import service from '@/utils/service.ts'
import { ITask } from './types'
export const getTaskList = () => {
  return service.get<ITask[]>({
    url: '/apiv1/task/list'
  })
}

export const getConfig = () => {
  return service.get<any>({
    url: '/apiv1/tool/config'
  })
}

export const getAward = (task_id: number) => {
  return service.post({
    url: '/apiv1/task/get_award',
    data: {
      task_id
    }
  })
}

export const signIn = () => {
  return service.post({
    url: '/apiv1/task/sign_in'
  })
}
