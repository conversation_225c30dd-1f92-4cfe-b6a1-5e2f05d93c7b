import service from '@/utils/service.ts'
import { IAiListParams, Ibannner, ITagListItem, IAiList, IAlbumListItem, bannerLogParams, bannerSettingRes } from './types'
export const queryProse = () => {
  return service.get<{ prose: string }>({
    url: '/project/prose'
  })
}

export const queryBanner = () => {
  return service.get<Ibannner[]>({
    url: '/apiv1/info/banner'
  })
}

export const queryTagList = () => {
  return service.get<ITagListItem[]>({
    url: '/apiv1/info/get_tag_list'
  })
}

export const queryAlbumList = () => {
  return service.get<IAlbumListItem[]>({
    url: '/apiv1/info/get_album_list'
  })
}

export const queryAiList = (params: IAiListParams) => {
  return service.post<IAiList>({
    url: '/apiv1/ai/list',
    data: params
  })
}

export const bannerLog = (data: bannerLogParams) => {
  return service.post({
    url: '/apiv1/info/bannerLog',
    data
  })
}
export const bannerSetting = () => {
  return service.get<bannerSettingRes>({
    url: '/apiv1/info/banner_setting'
  })
}

export const getOpeningStatementVoice = (data: { ai_id: number }) => {
  return service.post({
    url: '/apiv1/chat/opening_statement_voice',
    data
  })
}
