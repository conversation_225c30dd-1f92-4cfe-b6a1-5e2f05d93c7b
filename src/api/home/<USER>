import type { AgentMessageType } from '@/api/agentHomePage/types'
export interface Ibannner {
  ai_info: AgentMessageType
  banner_img_lang: string
  end_time: string
  id: number
  name: string
  redirect_url_lang: string
  start_time: string
  subtitle: string
  type: number
  is_outside: number
}

export interface ITagListItem {
  create_time: string
  id: number
  name: string
  rank?: number
  status: number
  update_time: string
  prompt: string
  title?: string
  light?: boolean
}

export interface IAiListParams {
  tag_id?: number
  page?: number
  limit?: number
  sort_type?: number
  keyword?: string
  is_recommend?: number
  album_id?: number
}

export interface IAiListItem {
  tag_list: {
    id: number
    name: string
  }[]
  like_status: number
  ai_chat_setting: string
  id: number
  name: string
  sound_ray_url: string
  cover_url: string
  cover_img_url: string
  client_id: number
  follow: string
  chat_times: string
  input_chat_times: string
  input_follow: string
  talk_num: number
  image_url: string
  create_time: string
  update_time: string
  api_key: string
  dataset_id: string
  app_id: string
  role_setting: string
  opening_statement: string
  back_story: string
  talk_example: string
  synopsis: string
  voice_id: string
  bgm_id: string
  is_show: number
  status: number
  avatar_url: string
  client_image_id: number
  terminal: string
  type: number
  rank: number
  client_info: {
    client_id: number
    name: string
    email: string
  }
  top?: number
  left?: number
  height?: number
  is_love?: number
  opening_statement_voice: string
  is_repeat: number
  has_3d: number
}

export interface IAiList {
  count: number
  list: IAiListItem[]
}

export interface IAlbumListItem {
  create_time: string
  end_time: string
  id: number
  name: string
  rank: number
  show_people: number
  show_rule: number
  start_time: string
  status: number
  terminal: string
  title?: string
}

export interface bannerLogParams {
  banner_id: number
  action: number
}

export interface bannerSettingRes {
  roll: number
  time: number
}
