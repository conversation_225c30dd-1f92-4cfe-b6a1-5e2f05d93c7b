import { ICurrencyType<PERSON>num, IPurchaseTypeEnum, OrderStatusEnum, WalletProductTypeEnum } from '@/enums'
import { Pagination } from '@/api/mine/type.ts'

export interface PriceAmountIDReqType {
  currency_type: ICurrencyTypeEnum
  product_type: IPurchaseTypeEnum
  count?: number
  product_id?: number
  gold_amount?: number
  phone_time?: number
  front_address?: string
  ai_id?: number
}

export interface PriceAmountIDResType {
  amount_id: string
}

export interface PurchaseCrystalReq {
  amount_id: string
}

export interface PurchaseReq extends PurchaseCrystalReq {
  currency_type: ICurrencyTypeEnum
}

interface OrderData {
  client_info?: {
    now_ip?: string
  }
  order_type?: string
  product_id?: string
  promotion_type?: number
  standard_id?: number
  terminal?: string
  total_amount?: string
  total_amount_discount?: number
}

export interface PurChaseUnit {
  amount?: {
    currency_code?: string
    value?: number
  }
  reference_id?: string
}

export interface RequestData {
  intent?: string
  purchase_units?: PurChaseUnit[]
}
export interface link {
  href?: string
  method?: 'GET' | 'POST'
  rel?: string
}

export interface PayRes {
  approve?: number
  id?: string
  status?: string
  links?: link[]
}

export interface OrderItem {
  budan?: number
  id?: string
  create_time?: string
  update_time?: string
  outside_order_id?: string
  client_id?: number
  cash_amount?: string
  order_id: number
  order_name?: string
  order_type: string
  order_status: OrderStatusEnum
  order_amount?: string
  order_data?: OrderData
  pay_channel?: string
  wallet_type?: string
  request_data?: RequestData
  pay_res?: PayRes
  pay_time?: string
  product_type?: string
  product_id?: number
  terminal?: string
  first_pay?: number
  standard_id?: number
  promotion_type?: number
}

export type OrderProductType = 'vip' | 'crystal'

export interface OrderListReq extends Pagination {
  // vip 会员  crystal 水晶
  product_type?: OrderProductType
  order_id?: string
}

export interface OrderListRes {
  count?: number
  list?: OrderItem[]
}

export type WalletType = 'gold' | 'crystal'

export interface WalletItem {
  id: number
  client_id?: number
  gold_amount?: number
  crystal_amount?: number
  create_time?: Date
  // 1 增加 2 减少
  type: 1 | 2
  order_id: string
  product_type: keyof typeof WalletProductTypeEnum
  product_id?: number
  count?: number
  log_type?: string
  product_type_text?: string
}

export interface WalletListReq extends Pagination {
  // gold 金币  crystal 水晶
  type?: WalletType
}

export interface WalletListRes {
  count?: number
  list?: WalletItem[]
}

export interface confirmPayPalResultReq {
  paypal_order_id?: string
  order_id?: string
}

export interface confirmPayPalResultRes {}
