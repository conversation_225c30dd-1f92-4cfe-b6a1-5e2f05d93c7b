import service from '@/utils/service.ts'
import {
  PriceAmountIDReqType,
  PriceAmountIDResType,
  PurchaseCrystalReq,
  PurchaseReq,
  OrderListReq,
  OrderListRes,
  WalletListReq,
  WalletListRes,
  confirmPayPalResultReq,
  confirmPayPalResultRes
} from '@/api/purchase/types.ts'
import { ICurrencyTypeEnum } from '@/enums'

export const getCrystalAmountID = (data: PriceAmountIDReqType) => {
  return service.post<PriceAmountIDResType>({
    url: '/apiv1/wallet/query_price_crystal',
    data
  })
}

export const getCoinAmountID = (data: PriceAmountIDReqType) => {
  return service.post<PriceAmountIDResType>({
    url: '/apiv1/wallet/query_price_gold',
    data
  })
}

export const purchaseByCrystal = (data: PurchaseCrystalReq) => {
  return service.post({
    url: '/apiv1/wallet/payment_crystal',
    data
  })
}

export const purchaseByCoin = (data: PurchaseCrystalReq) => {
  return service.post({
    url: '/apiv1/wallet/payment_gold',
    data
  })
}

export const getAmountID = (data: PriceAmountIDReqType) => {
  const url = data.currency_type === ICurrencyTypeEnum.Coin ? '/apiv1/wallet/query_price_gold' : '/apiv1/wallet/query_price_crystal'
  const { currency_type, ...settleData } = data
  return service.post<PriceAmountIDResType>({
    url,
    data: settleData
  })
}

export const purchase = (data: PurchaseReq) => {
  const url = data.currency_type === ICurrencyTypeEnum.Coin ? '/apiv1/wallet/payment_gold' : '/apiv1/wallet/payment_crystal'
  const { currency_type, ...settleData } = data
  return service.post({
    url,
    data: settleData
  })
}

export const orderList = (data: OrderListReq) => {
  return service.post<OrderListRes>({
    url: '/apiv1/fee/order',
    data
  })
}

export const walletDetail = (data: WalletListReq) => {
  return service.post<WalletListRes>({
    url: '/apiv1/wallet/detail',
    data
  })
}

export const confirmPayPalResult = (data: confirmPayPalResultReq) => {
  return service.post<confirmPayPalResultRes>({
    url: '/apiv1/fee/confirmPayPalResult',
    data
  })
}

export const orderPayStatus = (data: OrderListReq) => {
  return service.post<number>({
    url: '/apiv1/fee/order_status',
    data
  })
}

export const cancelOrder = (data: { order_id: number }) => {
  return service.post<any>({
    url: '/apiv1/fee/cancel_order',
    data
  })
}
