import service from '@/utils/service.ts'

export enum EventTypeEnum {
  ENTER_APP = 20001, // 进入应用
  EXIT_APP = 20002, // 退出应用
  SHOW_PERSON_GUIDE_POPUP = 20003, // 弹出人设指引框
  SHOW_3D_TUTORIAL = 20004, // 弹出3D引导教程
  SHOW_CLEAR_SCREEN_TUTORIAL = 20005, // 弹出清屏教程
  SHOW_TOUCH_ACTION_TUTORIAL = 20006, // 弹出触摸动作教程
  ENTER_CHAT_SCREEN = 20007, // 进入聊天画面
  START_CHAT = 20008, // 开始聊天
  CONTINUE_CHAT = 20009, // 继续聊天
  EXIT_CHAT = 20010, // 退出聊天
  ENTER_3D_BACKGROUND = 20011, // 进入3D背景
  EXIT_3D_BACKGROUND = 20012, // 退出3D背景
  ENTER_IMMERSE_MODE = 20013, // 进入沉浸模式
  EXIT_IMMERSE_MODE = 20014, // 退出沉浸模式
  TOUCH_INTERACTION = 20015, // 触摸互动
  OPEN_AI_PROFILE = 20016, // 打开AI档案
  QUICK_CREATE_AI = 20017, // 点击快速创建AI体
  SELECT_QUICK_AI_PROFILE = 20018, // 点击选择快速AI人设
  ADVANCED_CREATE_AI = 20019, // 点击高级创建AI体
  EDIT_ADVANCED_AI = 20020, // 点击编辑高级AI体
  CREATE_CUSTOM_AI_AVATAR = 20021, // 点击创建自定义AI形象
  SELECT_FREE_AVATAR = 20022, // 点击选择免费形象
  CHOOSE_FREE_AVATAR = 20023, // 选择免费形象
  CANCEL_AI_AVATAR_CREATION = 20024, // 取消AI形象创建
  EDIT_AI_AVATAR = 20025, // AI形象剪辑
  CANCEL_EDIT_AI_AVATAR = 20026, // 取消AI形象剪辑
  COMPLETE_EDIT_AI_AVATAR = 20027, // 完成AI形象剪辑
  RE_EDIT_AI_AVATAR = 20028, // 点击重新裁剪AI形象
  COMPLETE_RE_EDIT_AI_AVATAR = 20029, // 完成重新裁剪AI形象
  COMPLETE_AI_BACKSTORY = 20030, // 完善AI背景故事
  CANCEL_ADVANCED_CREATE_AI = 20031, // 取消高级创建AI体
  CANCEL_EDIT_AI_BODY = 20032, // 取消编辑AI体
  OPEN_MY_AVATAR_PAGE = 20033, // 打开我的形象页
  OPEN_MESSAGE_CHAT_HISTORY = 20034, // 打开消息-聊过
  OPEN_MESSAGE_LIKE_PAGE = 20035, // 打开消息-喜欢页
  OPEN_MESSAGE_MY_CREATION_PAGE = 20036, // 打开消息-我的创建页
  CLICK_SEARCH_BOX = 20037, // 点击搜索框
  CLICK_TAG = 20038, // 点击标签
  OPEN_FANS_LIST = 20039, // 打开粉丝列表
  OPEN_LIKE_LIST = 20040, // 打开喜欢列表
  OPEN_FOLLOW_LIST = 20041, // 打开关注列表
  VISIT_OTHER_PROFILE = 20042, // 访问他人主页
  CLICK_SHARE = 20043, // 点击分享
  ENTER_NEWBIE_TUTORIAL = 20044, // 进入新手接待教程
  EXIT_NEWBIE_TUTORIAL = 20045, // 退出新手引导教程
  END_NEWBIE_TUTORIAL_STORY = 20046, // 结束新手教程故事
  GENERATE_STORY_CARD = 20047, // 生成故事卡片
  RESTART_NEWBIE_TUTORIAL = 20048, // 重新开始新手引导教程
  ENTER_LIVE2D_NEWBIE_TUTORIAL = 20049, // 进入live2d新手接待教程
  LIVE2D_NEWBIE_TUTORIAL_TOUCH_INTERACTION = 20050, // 新手接待教程-触摸互动
  EXIT_LIVE2D_NEWBIE_TUTORIAL = 20051, // 退出live2d新手接待教程
  TALK_TO_HER = 20052, // 点击与智能体聊天
  ENTER_STORY = 20053, // 进入专属故事
  CLICK_STORY_DETAIL = 20054, // 点击查看故事详情
  RESTART_STORY = 20055, // 重新开始专属故事聊天
  ENTER_STORY_PAGE = 20056, // 点击进入故事页
  CLICK_CONTINUE_BUTTON = 20057, // 点击继续按钮
  CLICK_SKIP_BUTTON = 20058, // 点击跳过按钮
  SHOW_SETTLEMENT_PAGE = 20059, // 展示结算页
  CLICK_SAVE_IMAGE = 20060, // 点击保存图片
  OPEN_MINE = 20061, // 打开我的
  OPEN_HOME = 20062, // 打开首页
  OPEN_LEADERBOARD = 20063, // 打开排行榜
  CLICK_POPULARITY_CHART = 20064, // 点击人气榜
  CLICK_FLOWER_CHART = 20065, // 点击鲜花榜
  SHOW_HOME_PAGE = 20066, // 展示首页
  SHOW_LOGIN_PAGE = 20067, // 展示登录页
  SHOW_AD_BUTTON, // 广告按钮展示
  CLICK_AD_BUTTON, // 点击观看广告
  SHOW_AD_SUCCESS, // 成功展示广告
  CLOSE_AD_MIDDLE, // 中途关闭广告
  AD_FULL_PLAY, // 完整播放广告
  SHOW_AD_VIP, // 展示转化弹窗
  SHOW_BUY_CRYSTAL, // 展示购买水晶
  SHOW_BUY_VIP, // 展示购买会员
  L2D_LOADED, // 2D模型加载成功
  REPLY_WITHOUT_LOGIN, // 未登录回复
  USER_FEEDBACK, // 用户反馈
  QUESTION // 问卷
}

export enum ForwardAddressEnum {
  HOME_BANNER = '首页banner', // 首页banner
  HOME_AI_LIST = '首页ai列表', // 首页ai列表
  CREATE_AI_BODY = '创建ai体', // 创建ai体
  MY_CHARACTER_PAGE = '我的-角色页', // 我的-角色页
  MESSAGE_CHAT_HISTORY = '消息-聊过', // 消息-聊过
  MESSAGE_LIKE_PAGE = '消息-喜欢', // 消息-喜欢
  MESSAGE_MY_CREATION_PAGE = '消息-我的创建', // 消息-我的创建页
  SEARCH_PAGE = '搜索界面', // 搜索界面
  AI_PROFILE = 'ai体档案', // ai体档案
  NAV_CREATE_BUTTON = '导航栏创建按键', // 导航栏创建按键
  NEW_CREATION = '新创', // 新创
  EDIT_AI_PROFILE = 'ai档案编辑', // ai档案编辑
  MY_PAGE = '我的界面',
  CHAT_PAGE = '聊天界面',
  TASK_PAGE = '任务界面',
  POPULARITY_CHART = '人气榜',
  FLOWER_LIST = '鲜花榜',
  CHAT_BOX_BTN = '聊天框推荐',
  STORY_PAGE = '故事页',
  STORY_CHAT_PAGE_BACK_CHAT = '故事聊天界面返回',
  CLICK_BACK_AGENT_FROM_STORY = '点击返回智能体',
  HOME_PAGE = '首页',
  NOVICE_GUIDE_TUTORIAL = '新手引导教程',
  RANKING_PAGE = '排行榜',
  MINE = '我的',
  ALBUM = '专辑',
  QUESTION = '问卷',
  ENTER_APP = '进入应用'
}

export enum InteractiveTypeEnum {
  CHAT = 1, //'回复对话'
  CALL = 2, //'打电话',
  SEND_GIFT = 3 //'送礼'
}

export interface IEventReportType {
  event_type: EventTypeEnum
  ai_id?: number
  content?: string
  interactive_type?: InteractiveTypeEnum
  interactive_way?: string
  action_id?: number
  action_name?: string
  front_address?: ForwardAddressEnum | string
  tag_name?: string
  other_client_id?: number
  story_tag?: number
  exit_story?: string
  story_get_way?: string
  trigger_model?: string
  content_id?: number
  adv_id?: string
  recall_content?: string
  trigger_position?: string
  complete_play?: number
  adv_reward?: number
  adv_type?: string
  response_way?: string
  feedback_type?: string
  feedback_content?: string
  feedback_msg?: string
  question_sex?: string
  question_interaction?: string
  question_close?: string
  fenpei_ai_id?: unknown
}

export const eventReport = (data: IEventReportType) => {
  return service.post({
    url: '/apiv1/tool/event_report',
    data
  })
}
