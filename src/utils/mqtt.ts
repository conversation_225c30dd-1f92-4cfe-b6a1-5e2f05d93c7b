import mqtt from 'mqtt'
import { isJsonStr } from '@/utils/index.ts'
import { readMsg } from '@/api/msg'
import useUserStore from '@/stores/modules/user.ts'

export interface MqttConfig {
  mqtt_username?: string
  mqtt_password?: string
  mqtt_client_id?: string
  mqtt_url?: string
  mqtt_ssl_url?: string
  mqtt_ws_url?: string
  mqtt_wss_url?: string
  mqtt_topic?: string
  connectTimeout?: number
  reconnectPeriod?: number
  clean?: boolean
}

export interface MqttClientConfig {
  config: MqttConfig
  onMessage?: Function
  onConnect?: Function
  onError?: Function
  onClose?: Function
}

export type MqttInstanceType = InstanceType<typeof MqttClient>

class MqttClient {
  instance: any | null = null
  mqtt_topic: string = ''
  connectTimeout = 40000
  reconnectPeriod = 1000
  clean = true

  constructor({ config = {}, onMessage = () => {}, onError = () => {}, onClose = () => {} }: MqttClientConfig) {
    const {
      mqtt_wss_url,
      mqtt_username,
      mqtt_password,
      mqtt_client_id,
      mqtt_topic,
      connectTimeout = this.connectTimeout,
      reconnectPeriod = this.reconnectPeriod,
      clean = this.clean
    } = config
    this.mqtt_topic = mqtt_topic
    console.log('mqtt_wss_url、mqtt_topic', mqtt_wss_url, mqtt_topic)
    this.instance = mqtt.connect(mqtt_wss_url, {
      username: mqtt_username,
      password: mqtt_password,
      connectTimeout,
      clientId: mqtt_client_id,
      reconnectPeriod, //重连间隔时间，单位为毫秒，默认为 1000 毫秒，注意：当设置为 0 以后将取消自动重连
      clean //是否清除会话
    })
    this.instance?.on('close', onClose)
    this.instance?.on('error', onError)
    this.instance?.on('message', (topic: string, message: any) => {
      let data: any = {}
      if (message instanceof Uint8Array) {
        data = this.handleUint8Array(message)
      } else if (isJsonStr(message)) {
        data = JSON.parse(message)
      } else {
        data = message
      }
      console.log('message', topic, data)
      onMessage(data)
    })
    this.onConnect()
  }
  private onConnect() {
    this.instance?.on('connect', () => {
      console.log('mqtt Connection succeeded!')
      this.subscribe()
    })
  }
  subscribe(topic = this.mqtt_topic) {
    this.instance.subscribe(topic, (error: Error) => {
      const userStore = useUserStore()
      if (userStore.isLogin && !sessionStorage.getItem('readMsg')) {
        readMsg({
          ai_id: 0
        }).then(() => {
          sessionStorage.setItem('readMsg', '1')
        })
      }
      if (error) {
        console.log('Subscribe to topics error', error)
        return
      }
    })
  }
  handleUint8Array(uint8Array: Uint8Array) {
    const decoder = new TextDecoder('utf-8')
    const str = decoder.decode(uint8Array)
    return JSON.parse(str)
  }
  disconnect(topic = this.mqtt_topic) {
    sessionStorage.removeItem('livco_notification')
    try {
      this.instance.unsubscribe(topic, {}, (error: Error) => {
        if (error) {
          console.log('Unsubscribe to topics error', error)
        }
      })
    } catch (e) {
      console.log(e)
    }
  }
}

export default MqttClient
