import request from './request.ts'
import { AxiosRequestConfig } from 'axios'

export default {
  get: <T>(option: AxiosRequestConfig): Promise<ResultData<T>> => {
    return request({ method: 'GET', ...option })
  },
  post: <T>(option: AxiosRequestConfig): Promise<ResultData<T>> => {
    return request({ method: 'POST', ...option })
  },
  stream: (option: AxiosRequestConfig): Promise<ReadableStream> => {
    return request({ method: 'POST', responseType: 'stream', ...option })
  },
  delete: <T>(option: AxiosRequestConfig): Promise<ResultData<T>> => {
    return request({ method: 'DELETE', ...option })
  },
  put: <T>(option: AxiosRequestConfig): Promise<ResultData<T>> => {
    return request({ method: 'PUT', ...option })
  },
  download: <T>(option: AxiosRequestConfig): Promise<T> => {
    return request({ method: 'GET', responseType: 'blob', ...option })
  },
  upload: <T = any>(option: any): Promise<T> => {
    option.headersType = 'multipart/form-data'
    return request({ method: 'POST', ...option })
  }
}
