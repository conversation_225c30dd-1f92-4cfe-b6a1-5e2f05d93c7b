import FingerprintJS from '@fingerprintjs/fingerprintjs'
/**
 * @param {String} url 加载文件链接
 * @param {String} id 标签id
 * @param {Function} callback 回调函数 1-加载成功 0-加载失败
 * @param {Object} setAttribute 需要额外配置的属性
 * @return {*}
 */
export function loadScript(url: string, id: string, callback: Function = () => {}, setAttribute: any) {
  let script: any = document.getElementById(id)
  if (script) {
    callback(1)
    return
  }
  script = document.createElement('script')
  script.setAttribute('id', id)
  script.setAttribute('type', 'text/javascript')
  script.setAttribute('src', url)
  // script.async = true
  // script.defer = true
  if (setAttribute && typeof setAttribute === 'object') {
    for (const key in setAttribute) {
      script.setAttribute(key, setAttribute[key])
    }
  }
  if (script?.readyState) {
    script.onreadystatechange = function () {
      if (script.readyState === 3 || script.readyState == 4) {
        script.onreadystatechange = null
        callback && callback(1)
      } else {
        callback && callback(0)
        document.getElementsByTagName('head')[0].removeChild(script)
      }
    }
  } else {
    //其他浏览器
    script.onload = function () {
      callback && callback(1)
    }
    script.onerror = function () {
      callback && callback(0)
      document.getElementsByTagName('head')[0].removeChild(script)
    }
  }
  document.getElementsByTagName('head')[0].appendChild(script)
}

export const getDeviceFingerprint = async () => {
  const fpPromise = await FingerprintJS.load()
  // 调用FingerprintJS.load()返回一个Promise对象，用于加载和初始化FingerprintJS库。传参{debug: true}开启调试模式
  const result = await fpPromise.get()
  // 调用fpPromise.get()获取访问者指纹信息，并返回一个包含指纹ID和其他信息的结果对象。
  const fingerprintId = result.visitorId
  localStorage.setItem('browserId', fingerprintId)
  return fingerprintId
}
