import type { AxiosError, AxiosRequestHeaders, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'
import { useModal } from '@/hooks/useModal.ts'
import { getLoginID, getToken } from '@/utils/auth.ts'
import { ResultEnum } from '@/enums/httpEnum.ts'
import qs from 'qs'
import router from '@/router'
import useUserStore from '@/stores/modules/user'
import useAppStore from '@/stores/modules/app.ts'
// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: import.meta.env.VITE_APP_API_BASE_URL,
  timeout: 600000, // 请求超时时间
  adapter: 'fetch'
})

export type RequestError = AxiosError<{
  message?: string
  result?: any
  errorMessage?: string
}>

const selfHandleCode = [ResultEnum.ABSENT_AGENT, ResultEnum.CRYSTAL_DEFICIENCY, ResultEnum.COIN_DEFICIENCY, ResultEnum.NOVOICE, ResultEnum.UN_EXISTENCE]

const pendingRequests = new Map<string, AbortController>()
const generateRequestKey = (config: InternalAxiosRequestConfig): string => {
  return `${config.method}-${config.url}-${JSON.stringify(config.params)}-${JSON.stringify(config.data)}`
}
const addPendingRequest = (config: InternalAxiosRequestConfig): void => {
  const requestKey = generateRequestKey(config)
  // // 如果已经存在，先取消之前的请求
  // if (pendingRequests.has(requestKey)) {
  //   const controller = pendingRequests.get(requestKey)
  //   controller?.abort()
  // }

  // 创建新的 AbortController 并添加到列表中
  const controller = new AbortController()
  config.signal = controller.signal
  pendingRequests.set(requestKey, controller)
}
// 从 pending 列表中移除请求
const removePendingRequest = (config: InternalAxiosRequestConfig): void => {
  const requestKey = generateRequestKey(config)
  if (pendingRequests.has(requestKey)) {
    pendingRequests.delete(requestKey)
  }
}
export const cancelAllRequests = (): void => {
  pendingRequests.forEach((controller, key) => {
    if (key.includes('/event_report')) return
    controller.abort()
  })
  pendingRequests.clear()
}

// 异常拦截处理器
function errorHandler(error: RequestError): Promise<any> {
  if (axios.isCancel(error) || (error as RequestError).code === 'ERR_CANCELED') {
    // 请求被取消，不显示错误提示，直接拒绝Promise
    return Promise.reject(error)
  }
  useModal({
    message: (error as RequestError).message,
    duration: 1500
  })
  return Promise.reject(error)
}

// 请求拦截器
function requestHandler(config: InternalAxiosRequestConfig): InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig> {
  // 添加取消令牌支持
  const appStore = useAppStore()
  addPendingRequest(config)
  config.headers['access-token'] = getToken()
  config.headers['login-id'] = getLoginID()
  config.headers['lang'] = localStorage.getItem('language') || 'en'
  config.headers['voice-lang'] = localStorage.getItem('audioLanguage') || 'en'
  config.headers['terminal'] = 'web'
  config.headers['tourist'] = 1
  config.headers['uuid'] = localStorage.getItem('browserId') || ''
  if (localStorage.getItem('ug-lang')) {
    config.headers['ug-lang'] = localStorage.getItem('ug-lang')
  }
  if (config.headers['access-token']) {
    delete config.headers['tourist']
  }
  if (appStore.isUg) {
    config.headers['terminal'] = 'ugphone_web'
  }
  const data = config.data || false
  if (config.method?.toUpperCase() === 'POST' && (config.headers as AxiosRequestHeaders)['Content-Type'] === 'application/x-www-form-urlencoded') {
    config.data = qs.stringify(data)
  }
  return config
}

// Add a request interceptor
request.interceptors.request.use(requestHandler, errorHandler)

// 响应拦截器
function responseHandler(response: AxiosResponse) {
  removePendingRequest(response.config)
  const userStore = useUserStore()
  const { data } = response
  if (!data) {
    // 返回“[HTTP]请求没有返回值”;
    throw new Error()
  }
  // 未设置状态码则默认成功状态
  const code = data.code || 200
  // 二进制数据则直接返回
  if (response.request.responseType === 'blob' || response.request.responseType === 'arraybuffer') {
    return response
  }

  // 登录过期 || 顶号
  if (code === ResultEnum.OVERDUE || code === ResultEnum.SINGLE_POINT) {
    userStore.removeAllUserInfo()
    useModal({
      message: data.msg,
      duration: 1500,
      onClose: () => {
        window.location.href = '/'
      }
    })
    return Promise.reject(data)
  } else if (code === ResultEnum.AGENT_REMOVED) {
    useModal({
      message: data.msg,
      duration: 1500,
      onClose: () => {
        router.push('/')
      }
    })
    return
  } else if (code !== ResultEnum.SUCCESS && !selfHandleCode.includes(code)) {
    useModal({
      message: data.msg,
      duration: 1500
    })
    return Promise.reject(data)
  } else {
    return data
  }
}

// Add a response interceptor
request.interceptors.response.use(responseHandler, errorHandler)

export default request
