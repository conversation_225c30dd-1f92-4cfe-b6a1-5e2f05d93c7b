import { MQTT_LOCALSTORAGE_KEY } from '@/constant'

export const TokenKey = 'access_token'
const LoginIdKey = 'login_id'
const UserInfoKey = 'userInformation'
export function getToken() {
  return localStorage.getItem(TokenKey)
}

export function getLoginID() {
  return localStorage.getItem(LoginIdKey)
}

export function setToken(token: string) {
  return localStorage.setItem(TokenKey, token)
}

export function setLoginId(loginId: string) {
  return localStorage.setItem(LoginIdKey, loginId)
}

export function setUserInfo(userInfo: string) {
  return localStorage.setItem(UserInfoKey, userInfo)
}
export function removeUserInfo() {
  return localStorage.removeItem(UserInfoKey)
}

export function removeToken() {
  return localStorage.removeItem(TokenKey)
}

export function removeLoginId() {
  return localStorage.removeItem(LoginIdKey)
}

export const removeMqttConfig = () => {
  return localStorage.removeItem(MQTT_LOCALSTORAGE_KEY)
}

export function setTourist() {
  return localStorage.setItem('tourist', '1')
}

export function removeTourist() {
  return localStorage.removeItem('tourist')
}

export function getTourist() {
  return localStorage.getItem('tourist')
}
