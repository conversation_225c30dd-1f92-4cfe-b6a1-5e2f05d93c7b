import { i18n } from '@/utils/i18n.ts'
import { useModal } from '@/hooks/useModal.ts'
import { Howl } from 'howler'
import html2canvas from 'html2canvas'
import { marked } from 'marked'
import useUserStore from '@/stores/modules/user.ts'

// 全局音频实例，用于跟踪和控制当前播放的音频
let globalHowlInstance: Howl | null = null

/**
 * 播放音频URL，如果有正在播放的音频则会打断
 * @param url 音频URL
 * @param saveSound
 * @param func
 * @returns 返回Howl实例
 */
export const playChatAudio = (url: string, saveSound?: boolean, func?: { onPlay?: () => void; onEnd?: () => void; onStop?: () => void }): Howl => {
  const userStore = useUserStore()
  if (!userStore.userInformation.bgm) {
    return
  }
  // 如果有正在播放的音频，停止它
  if (globalHowlInstance) {
    if (!saveSound) {
      globalHowlInstance.stop()
      globalHowlInstance.unload()
      globalHowlInstance = null
    } else {
      const sound = new Howl({
        src: [url],
        html5: true, // 使用HTML5 Audio，更好地支持流媒体
        preload: true,
        onplay: () => {
          func?.onPlay && func.onPlay()
        },
        onend: () => {
          func?.onEnd && !saveSound && func.onEnd()
          // 播放结束后清除全局引用
          if (globalHowlInstance === sound) {
            globalHowlInstance = null
          }
        },
        onstop: () => {
          func?.onStop && func.onStop()
          // 播放停止后清除全局引用
          if (globalHowlInstance === sound) {
            globalHowlInstance = null
          }
        },
        onloaderror: (id, error) => {
          console.error('音频加载错误:', id, error)
          if (globalHowlInstance === sound) {
            globalHowlInstance = null
          }
        },
        onplayerror: (id, error) => {
          console.error('音频播放错误:', id, error)
          if (globalHowlInstance === sound) {
            globalHowlInstance = null
          }
        }
      })
      globalHowlInstance.on('end', () => {
        // 创建新的Howl实例
        // 保存到全局引用
        globalHowlInstance = sound
        // 开始播放
        sound.play()
      })
      return sound
    }
  }

  // 创建新的Howl实例
  const sound = new Howl({
    src: [url],
    html5: true, // 使用HTML5 Audio，更好地支持流媒体
    preload: true,
    onplay: () => {
      func?.onPlay && func.onPlay()
    },
    onend: () => {
      func?.onEnd && !saveSound && func.onEnd()
      // 播放结束后清除全局引用
      if (globalHowlInstance === sound) {
        globalHowlInstance = null
      }
    },
    onstop: () => {
      func?.onStop && func.onStop()
      // 播放停止后清除全局引用
      if (globalHowlInstance === sound) {
        globalHowlInstance = null
      }
    },
    onloaderror: (id, error) => {
      console.error('音频加载错误:', id, error)
      if (globalHowlInstance === sound) {
        globalHowlInstance = null
      }
    },
    onplayerror: (id, error) => {
      console.error('音频播放错误:', id, error)
      if (globalHowlInstance === sound) {
        globalHowlInstance = null
      }
    }
  })

  // 保存到全局引用
  globalHowlInstance = sound

  // 开始播放
  sound.play()
  return sound
}

// 监听页面可见性变化，在页面不可见时停止音频播放
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState !== 'visible' && globalHowlInstance) {
    globalHowlInstance.stop()
  }
})

const renderer = new marked.Renderer()

// 例如，忽略 "~" 的转换
renderer.del = (text) => {
  // 原样返回原始文本
  return text.raw
}

renderer.image = ({ href, title, text }) => {
  const placeholeder = (import.meta.env.VITE_APP_PUBLIC_PATH === '/' ? '' : import.meta.env.VITE_APP_PUBLIC_PATH) + '/setting/placeholder.png'
  return `
    <div class="markdown-image-container">
      <img 
        src="${placeholeder}"
        class="image-placeholder"
        alt=""
        onload="document.getElementById('chatPage').scrollTo({ top: document.getElementById('chatPage').scrollHeight })"

      />
      <img
        class="markdown-image"
        src="${href}"
        alt="${text}"
        title="${title || text}"
        onload="this.style.display = 'block'; this.previousElementSibling.style.display = 'none'; document.getElementById('chatPage').scrollTo({ top: document.getElementById('chatPage').scrollHeight })"
      />
    </div>
  `
}

// 配置 marked，使用自定义渲染器
marked.setOptions({
  renderer: renderer
})

export const markdownToHtml = (markdown: string) => {
  if (markdown) {
    return marked(markdown)
  } else {
    return ''
  }
}

export const copyText = (text: string | number) => {
  navigator.clipboard
    .writeText(String(text))
    .then(() => {
      useModal({ message: i18n.global.t('copied') })
    })
    .catch((err) => {
      console.warn(err)
    })
}

export const changeSecondToMMSS = (second: number) => {
  const minutes = Math.floor(second / 60)
  const seconds = second % 60
  return `${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`
}

export const soundUrlPlay = (url: string, handlePlay?: () => void, handleEnd?: () => void, handleLoad?: () => void, handleStop?: () => void) => {
  console.log('tts', url)
  const ttsSound = new Howl({
    src: url,
    onplay: () => {
      handlePlay && handlePlay()
      console.log('Audio is playing.')
    },
    onstop: () => {
      console.log('Audio has stopped.')
      handleStop && handleStop()
    },
    onload: () => {
      console.log('Audio has loaded.')
      ttsSound.play()
      handleLoad && handleLoad()
    },
    onend: () => {
      console.log('Audio has ended.')
      handleEnd && handleEnd()
      ttsSound.unload()
    }
  })
}

export const transformAspectRadio = (length: number) => {
  return length * ((window.innerWidth > 500 ? 500 : window.innerWidth) / 375)
}

export const getVideoHeight = () => {
  return (window.innerWidth * 1080) / 1980
}

export const isJsonStr = (val: any) => {
  if (typeof val === 'string') {
    try {
      const obj = JSON.parse(val)
      if (typeof obj === 'object' && obj) {
        return true
      } else {
        return false
      }
    } catch (e) {
      return false
    }
  }
  return false
}

export function downloadFile(href: string, fileName?: string) {
  const a = document.createElement('a')
  a.setAttribute('href', href)
  // 匹配最后一个斜杠/后面的字符
  const name = href.match(/[^/]+$/)![0]
  console.log('fileName', fileName || name)
  a.setAttribute('download', fileName || name)
  a.setAttribute('target', '_blank')
  a.click()
  a.remove()
}

// 按顺序替换 str 的 ** 字符
export function replaceStars(str: string, replacements: string[] = []): string {
  let result = str
  let starIndex = result.indexOf('**')
  while (starIndex !== -1) {
    if (replacements.length > 0) {
      result = result.replace('**', replacements.shift())
    } else {
      result = result.replace('**', '')
    }
    starIndex = result.indexOf('**')
  }
  return result
}

export const replaceBracketsWithAsterisks = (inputStr: string) => {
  // 正则表达式匹配各种括号类型及其中内容，并替换为 *内容*
  return inputStr?.replace(/（）()\[\]<>|(\*\()|(\*\))/g, '*')
}

// dom元素转为图片
export const handleDomToImg = async (domID: string) => {
  // 获取dom元素
  const graphImg = document.getElementById(domID)
  // 创建canvas元素
  const canvasDom = document.createElement('canvas')

  // 获取dom宽高
  const width = parseInt(window.getComputedStyle(graphImg).width, 10)
  const height = parseInt(window.getComputedStyle(graphImg).height, 10)

  // 设定 canvas 元素属性宽高为 DOM 节点宽高 * 像素比
  const scaleBy = 3 //也可以用window.devicePixelRatio，
  canvasDom.width = width * scaleBy
  canvasDom.height = height * scaleBy

  //scale:2 按比例增加分辨率，将绘制内容放大对应比例
  const canvas = await html2canvas(graphImg, { canvas: canvasDom, scale: scaleBy, useCORS: true, backgroundColor: 'transparent' })

  //将canvas转为base64
  return canvas.toDataURL()
}

export const removeBracketsAndContent = (inputStr: string): string => {
  // 正则表达式匹配各种括号类型及其中内容，并删除它们
  return inputStr.replace(/[()\[\]{}<>]([^()\[\]{}<>]*)[()\[\]{}<>]/g, '')
}

export const getMarkdownImageUrl = (url: string) => {
  const regex = /!\[.*?]\((.*?)\)/g
  const getUrls = (text: string) => [...text.matchAll(regex)].map((match) => match[1])
  return getUrls(url)
}

export const scrollToBottom = (element: HTMLElement, behavior: ScrollBehavior = 'smooth', isNextTick = true) => {
  if (element) {
    if (isNextTick) {
      nextTick(() => {
        element.scrollTo({ top: element.scrollHeight, behavior })
      })
    } else {
      element.scrollTo({ top: element.scrollHeight, behavior })
    }
  }
}

export function compareVersions(version1: string, version2: string) {
  console.log('version1', version1, 'version2', version2)
  // 将版本号字符串以点分割成数组，并补全较短版本号数组的尾部为0
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)

  // 获取最长的数组长度，用于补全较短的版本号数组
  const maxLength = Math.max(v1Parts.length, v2Parts.length)

  // 补全版本号数组到相同的长度，不足的部分用0填充
  while (v1Parts.length < maxLength) v1Parts.push(0)
  while (v2Parts.length < maxLength) v2Parts.push(0)

  // 比较版本号的每一部分
  for (let i = 0; i < maxLength; i++) {
    if (v1Parts[i] > v2Parts[i]) {
      return false // version1 大于 version2，无需更新
    } else if (v1Parts[i] < v2Parts[i]) {
      return true // version1 小于 version2，需要更新
    }
  }

  // 如果所有部分都相同，则不需要更新
  return false
}
