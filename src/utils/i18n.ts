import { createI18n } from 'vue-i18n'
import en from 'vant/es/locale/lang/en-US'
import cn from 'vant/es/locale/lang/zh-CN'
import cht from 'vant/es/locale/lang/zh-TW'
import jp from 'vant/es/locale/lang/ja-JP'
import es from 'vant/es/locale/lang/es-ES'
import vi from 'vant/es/locale/lang/vi-VN'
import th from 'vant/es/locale/lang/th-TH'
import ind from 'vant/es/locale/lang/id-ID'
import pt from 'vant/es/locale/lang/pt-BR'
import ru from 'vant/es/locale/lang/ru-RU'
import fr from 'vant/es/locale/lang/fr-FR'
import de from 'vant/es/locale/lang/de-DE'

/**
 * All i18n resources specified in the plugin `include` option can be loaded
 * at once using the import syntax
 */
import messages from '@intlify/unplugin-vue-i18n/messages'
import { Locale } from 'vant'

export const existLanguages = ['en', 'es', 'cn', 'jp', 'cht', 'fr', 'de', 'ru', 'pt', 'vi', 'th', 'ind']
/** 多语言 picker columns */
export const languageColumns = [
  { text: 'English', value: 'en' },
  { text: '日本語', value: 'jp' },
  { text: '繁體中文', value: 'cht' },
  { text: 'Español', value: 'es' },
  { text: '简体中文', value: 'cn' },
  { text: 'Français', value: 'fr' },
  { text: 'Deutsch', value: 'de' },
  { text: 'Русский', value: 'ru' },
  { text: 'Português', value: 'pt' },
  { text: 'Tiếng Việt', value: 'vi' },
  { text: 'ภาษาไทย', value: 'th' },
  { text: 'Bahasa Indonesia', value: 'ind' }
]

export const i18n = createI18n({
  locale: 'en',
  messages
})

/** 当前语言 */
export const locale = computed({
  get() {
    return (i18n.global.locale as unknown as Ref<string>).value
  },
  set(language: string) {
    localStorage.setItem('language', language)
    localStorage.setItem('languageText', languageColumns.find((item) => item.value === language)?.text)
    ;(i18n.global.locale as unknown as Ref<string>).value = language
    Locale.use(language)
  }
})

// 载入 vant 语言包
Locale.use('cn', cn)
Locale.use('en', en)
Locale.use('cht', cht)
Locale.use('jp', jp)
Locale.use('es', es)
Locale.use('vi', vi)
Locale.use('th', th)
Locale.use('ind', ind)
Locale.use('fr', fr)
Locale.use('pt', pt)
Locale.use('de', de)
Locale.use('ru', ru)

// 根据当前语言切换 vant 语言包
Locale.use(locale.value)
