interface ILang {
  en: string[]
  cht: string[]
  cn: string[]
  jp: string[]
  pt: string[]
  es: string[]
  de: string[]
  fr: string[]
  ru: string[]
  vi: string[]
  th: string[]
  ind: string[]
}

interface IlangText {
  [key: string]: string
}

const browserLanguage: ILang = {
  en: [
    'en',
    'en-US', //（美国）
    'en-EG',
    'en-AU', //（澳大利亚)
    'en-GB', //（英国）
    'en-CA', //（加拿大）
    'en-NZ', //（新西兰）
    'en-IE', //（爱尔兰）
    'en-ZA', //（南非）
    'en-JM', //（牙买加）
    'en-BZ', //（伯利兹）
    'en-TT' //（特立尼达和多巴哥）
  ],
  cn: [
    'zh',
    'zh-CN',
    'zh-SG' //(马新简体)
  ],
  cht: ['zh-TW', 'zh-HK', 'zh-MO', 'zh-CHT', 'zh-Hant'],
  jp: ['ja', 'ja-JP', 'jp'],
  pt: [
    'pt',
    'pt_PT',
    'pt-PT',
    'pt_PT_EURO',
    'pt-BR' //（巴西）
  ],
  es: [
    'es',
    'es-AR', //(阿根廷）
    'es-GT', //（危地马拉）
    'es-CR', //（哥斯达黎加）
    'es-PA', //（巴拿马）
    'es-DO', //（多米尼加共和国）
    'es-MX', //（墨西哥）
    'es-VE', //（委内瑞拉）
    'es-CO', //（哥伦比亚）
    'es-PE', //（秘鲁）
    'es-EC', //（厄瓜多尔）
    'es-ES', //（西班牙）
    'es-CL', //（智利）
    'es-UY', //（乌拉圭）
    'es-PY', //（巴拉圭）
    'es-BO', //（玻利维亚）
    'es-SV', //（萨尔瓦多）
    'es-HN', //（洪都拉斯）
    'es-NI', //（尼加拉瓜）
    'es-PR' //（波多黎各）
  ],
  de: [
    'de',
    'de-CH', //（瑞士）
    'de-AT', //（奥地利）
    'de-LU', //（卢森堡）
    'de-LI' //（列支敦士登）
  ],
  fr: [
    'fr',
    'fr-BE', //（比利时）
    'fr-FR', // (法国)
    'fr-CA', //（加拿大）
    'fr-CH', //（瑞士）
    'fr-LU', //（卢森堡）
    'fr-MC' // (摩纳哥)
  ],
  ru: [
    'ru',
    'ru-MI',
    'ru-mo', //（摩尔多瓦共和国）
    'ru-RU'
  ],
  vi: ['vi', 'vi-VN'], //（越南）
  th: ['th', 'th-TH'], //（泰国）
  ind: ['id', 'id-ID'] //（印尼）
}

const langText: IlangText = {
  cn: '简体中文',
  en: 'English',
  cht: '繁體中文',
  jp: '日本語',
  es: 'Español',
  fr: 'Français',
  pt: 'Português',
  de: 'Deutsch',
  ru: 'Русский',
  vi: 'Tiếng Việt',
  th: 'ภาษาไทย',
  ind: 'Bahasa Indonesia'
}

export const navigatorLanguage = function () {
  const language = localStorage.getItem('language')
  if (language) {
    return language
  } else {
    const navigatorLanguage = (navigator.language || (navigator as any).browserLanguage).toLowerCase() || ''
    let lang = 'en'
    try {
      for (const key in browserLanguage) {
        if (Object.hasOwnProperty.call(browserLanguage, key)) {
          const currentLang = browserLanguage[key as keyof ILang].map((i: string) => i.toLowerCase())
          if (currentLang.includes(navigatorLanguage)) {
            lang = key
            break
          }
        }
      }
      localStorage.setItem('language', lang)
      localStorage.setItem('languageText', langText[lang])
      return lang
    } catch (error) {
      return lang
    }
  }
}
