import { createApp } from 'vue'
import './assets/styles/index.scss'
import router from '@/router'
import pinia from '@/stores'
import { i18n } from '@/utils/i18n'
// import 'virtual:svg-icons-register'
import App from './App.vue'
// Vant 桌面端适配
import '@vant/touch-emulator'
import { Toast } from 'vant'
import { Lazyload } from 'vant'
/* --------------------------------
Vant 中有个别组件是以函数的形式提供的，
包括 Toast，Dialog，Notify 和 ImagePreview 组件。
在使用函数组件时，unplugin-vue-components
无法自动引入对应的样式，因此需要手动引入样式。
------------------------------------- */
import 'vant/es/toast/style'
import 'vant/es/dialog/style'
import 'vant/es/notify/style'
import 'vant/es/image-preview/style'
import { setupDirectives } from '@/directives'

const app = createApp(App)
setupDirectives(app)

app.use(router)
app.use(pinia)
app.use(i18n)
app.use(Toast)
app.use(Lazyload)
app.mount('#app')
const loading = document.getElementById('initial-loading')
if (loading) {
  loading.remove()
}
