// * 请求枚举配置
/**
 * @description：请求配置
 */
export enum ResultEnum {
  SUCCESS = 200,
  ERROR = 500,
  OVERDUE = 24781131,
  SINGLE_POINT = 247171552,
  TIMEOUT = 10000,
  TYPE = 'success',
  TTS_ERROR = 247191131,
  ABSENT_AGENT = 247160950,
  OVER_PHONE_TIME = 249241526,
  UNCLEAR_VOICE = 201,
  CRYSTAL_DEFICIENCY = 24821542,
  COIN_DEFICIENCY = 248211151,
  RISK_CHAT = 2410111154,
  AGENT_REMOVED = 2410251705,
  NOVOICE = 2410251846,
  REPLYING = 2412111028,
  UN_EXISTENCE = 400403
}

/**
 * @description：请求方法
 */
export enum RequestEnum {
  GET = 'GET',
  POST = 'POST',
  PATCH = 'PATCH',
  PUT = 'PUT',
  DELETE = 'DELETE'
}

/**
 * @description：常用的contentTyp类型
 */
export enum ContentTypeEnum {
  // json
  JSON = 'application/json;charset=UTF-8',
  // text
  TEXT = 'text/plain;charset=UTF-8',
  // form-data 一般配合qs
  FORM_URLENCODED = 'application/x-www-form-urlencoded;charset=UTF-8',
  // form-data 上传
  FORM_DATA = 'multipart/form-data;charset=UTF-8'
}
