export enum ICurrencyTypeEnum {
  Crystal = 'crystal',
  Coin = 'gold'
}

export enum IPurchaseTypeEnum {
  Gift = 'buy_gift',
  Coin = 'buy_gold',
  PhoneTime = 'buy_phone_time',
  SELFIE = 'get_a_selfie',
  NormalSkin = 'buy_skin',
  ThreeDSkin = 'buy_live2d',
  GENERATE_STORY = 'generate_story'
}

export enum PerSetVoiceEnum {
  UnclearVoice = 1
}

export enum OrderStatusEnum {
  'processingOrder' = 0,
  'payedOrder' = 1,
  'closedOrder' = 2,
  'orderCompleted' = 3,
  'refundOrder' = 4,
  'canceledOrder' = 5
}

export enum WalletProductTypeEnum {
  'task' = '任务',
  'vip_give' = '会员每日赠送',
  'recharge' = '水晶兑换',
  'buy_gift' = '购买皮肤',
  'buy_gif' = '购买礼物',
  'camera_pic' = '图集消耗',
  'buy_gold' = '金币兑换'
}

export enum SexEnum {
  'maleOption' = 1,
  'femaleOption' = 2,
  'nonBinaryOption' = 3
}

export enum StoryRecordTypeEnum {
  // 聊天消息类型
  USER,
  AGENT,
  BACKGROUND,
  NARRATION,
  VIDEO,
  IMAGE,
  NPC,
  CONTINUE,
  REWARD,
  END,
  END_CARD
}

export enum ChatRecordTypeEnum {
  // 聊天消息类型
  TIPS,
  GIFT,
  SYNOPSIS,
  WARN,
  USER,
  AI,
  PHONE,
  IMAGE,
  STORY_IMAGE
}

export enum RecordEnum {
  USER_COMMENT,
  AI_COMMENT,
  PICTURE_MARKDOWN,
  PICTURE_REQUEST,
  PICTURE_ADS,
  VIDEO_ADS,
  CHAPTER,
  NARRATION,
  VOICEOVER,
  GAME_INVITATION,
  TIPS,
  WARN,
  GAME_RESULT,
  SYNOPSIS,
  PHONE,
  STORY_IMAGE,
  GIFT,
  GAME_REWARD_TIPS,
  LOCK
}

export interface StoryRecordType {
  type: StoryRecordTypeEnum
  content: string
  message_id?: string
  avatar_url?: string
  video_id?: string
  skip_text?: string
  isContinue?: boolean
  isFreeTalk?: boolean
  giftType?: number
  giftNum?: number
  // audio_file_url?: string
  // audio_length?: number
  // ttsLoading?: boolean
  // ttsPlaying?: boolean
  textLoading?: boolean
  userTTS?: boolean
}

export enum StoryRewardGiftType {
  GIFT = 1,
  INTIMACY,
  COIN,
  CRYSTAL
}

export enum BannerShowEnum {
  SHOW = 1,
  CLICK = 2
}
