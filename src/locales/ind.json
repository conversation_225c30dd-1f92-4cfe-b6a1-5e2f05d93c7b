{"welcomeToEros": "Selamat datang di UGenie!", "termsConfirmation": "Hara<PERSON> konfirmasi syarat berikut sebelum menggunakan situs ini.", "ageConfirmation": "Say<PERSON> be<PERSON> 18 tahun ke atas.", "aiContentNotice": "<PERSON>a sudah tahu bahwa konten terkait AI di situs ini bersifat fiksi.", "guestLogin": "<PERSON><PERSON>", "registrationLogin": "Daftar/Masuk", "emailLogin": "Login email", "email": "Email", "getCaptcha": "<PERSON><PERSON><PERSON><PERSON>", "emailLoginOption": "<PERSON><PERSON>", "agreeToTerms": "Men<PERSON><PERSON><PERSON><PERSON>i dan <PERSON><PERSON> saat login", "loginIssueContact": "Tidak bisa login? Hubungi Layanan Pelanggan", "enterEmail": "<PERSON><PERSON> ma<PERSON>kkan email <PERSON><PERSON>", "enterCaptcha": "<PERSON><PERSON> masukkan kode verifikasi", "invalidEmailFormat": "Format email salah, harap masukkan kembali", "captchaError": "<PERSON><PERSON> veri<PERSON><PERSON>i salah, harap masukkan kembali", "captchaExpired": "Kode verifikasi telah <PERSON>, harap masukkan kembali", "accountSuspendedNotice": "<PERSON><PERSON><PERSON>a telah ditangguhkan...", "logout": "<PERSON><PERSON><PERSON>", "logoutOption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmLogout": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> t<PERSON> akan dapat be<PERSON>...", "readAndConfirmed": "<PERSON>a telah membaca dan meng<PERSON><PERSON><PERSON>", "privacyAgreement": "<PERSON><PERSON><PERSON><PERSON>", "userAgreement": "<PERSON><PERSON><PERSON>", "unableLogin": "Tidak bisa masuk?", "contactCustomer": "Hubung<PERSON>", "loginAndAgree": "Masuk untuk menyetujui", "welcomeToUgenie": "Selamat datang di UGenie", "CheckTerms": "<PERSON><PERSON><PERSON> per<PERSON>", "otherLoginWay": "<PERSON><PERSON><PERSON> dengan akun lain", "loadingFailed": "Pemuatan SDK gagal", "invalidSession": "<PERSON><PERSON> tidak valid, silakan login kembali", "verificationCode": "<PERSON><PERSON>", "store": "<PERSON><PERSON>", "searchAgentName": "<PERSON><PERSON> ka<PERSON>ter", "unableConnect": "Tidak dapat terhubung ke toko", "searchHistory": "Riwayat pencarian", "popularRoles": "Karakter populer", "noResultsFeedback": "Tidak dapat menemukan apa yang Anda inginkan? Klik umpan balik", "emptySearchSuggestion": "Di sini kosong, coba kata kunci lain?", "creatorSelection": "<PERSON><PERSON><PERSON>", "chatNow": "<PERSON><PERSON>lan", "followedNotice": "Diikuti", "characterOption": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "searchWantTips": "Tidak dapat menemukan apa yang Anda inginkan?", "tryUsingDifferentKeyword": "Coba lagi dengan kata kunci lain", "language": "Bahasa", "helpFAQ": "Bantuan FAQ", "feedbackOption": "<PERSON><PERSON><PERSON>", "selectNumber": "<PERSON><PERSON><PERSON> nomor <PERSON>", "aiFilter": "Penyaringan entitas cerdas", "sortOptions": "Metode penyortiran", "aiType": "<PERSON><PERSON> entitas cerdas", "mostPopular": "<PERSON><PERSON><PERSON><PERSON>", "latest": "Terbaru", "trending": "Populer", "searchRoleOrCreator": "<PERSON>i karakter atau kreator", "dynamicInteraction": "Rasakan interaksi dinamis dengan karakter...", "chatBasedOnPersona": "<PERSON><PERSON><PERSON> akan mengobrol dengan Anda berdasarkan pengaturan karakter Anda...", "userCharacterOption": "<PERSON><PERSON><PERSON><PERSON> karakter saya", "roleSpeakingDuration": "<PERSON><PERSON> u<PERSON>an karakter yang menghabiskan durasi ini", "repeatedClickNotice": "Anda telah berulang kali meng<PERSON>lik, silakan coba lagi nanti", "exitImmersiveMode": "<PERSON><PERSON>ar dari mode imersi", "myCreations": "<PERSON><PERSON><PERSON>", "loginToView": "<PERSON><PERSON><PERSON> untuk melihat", "createLover": "Ciptakan keka<PERSON>u sendiri", "goCreate": "<PERSON><PERSON>", "clickToSet": "Klik untuk Mengatur", "retainCharacterNotice": "<PERSON><PERSON><PERSON><PERSON> Anda ingin mempertahankan karakter <PERSON>a?", "dataClearWarning": "Semua data karakter akan di<PERSON>, harap lan<PERSON>kan dengan hati-hati", "clickToTouchSpace": "<PERSON><PERSON><PERSON> ke <PERSON>", "imageOption": "Penampilan", "noAIsNotice": "Belum ada entitas AI, klik + di bawah untuk memanggil entitas AI~", "myImageOption": "Penampila<PERSON>", "editImage": "<PERSON>", "defaultVoicePlayback": "<PERSON><PERSON>uta<PERSON>", "captchaInvalidRetry": "Aktifkan NSFW", "cost": "Biaya", "noHaveAi": "<PERSON><PERSON> lain tidak memiliki AI", "moreCharacterTime": "<PERSON><PERSON>h banyak waktu bicara karakter", "noMoreImage": "Tidak ada lagi pengaturan karakter untuk saat ini", "editFigure": "<PERSON> pengaturan karakter", "aiOnTheWay": "AI masih dalam perjalanan～", "searchWant": "<PERSON>i apa yang <PERSON>a inginkan...", "confirmDelFigure": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus avatar ini?", "personalDescriptionTips": "<PERSON><PERSON><PERSON> <PERSON> akan didasarkan pada deskripsi pribadi Anda dan o<PERSON>lan <PERSON>a...", "confirmExitStory": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin keluar dari cerita saat ini?", "interactWithHer": "Berinteraksi dengannya", "newerStoryName": "<PERSON><PERSON><PERSON><PERSON>", "flowerList": "Bunga", "popularityChart": "Popularitas", "inputFeedback": "<PERSON><PERSON> ma<PERSON>kkan umpan balik <PERSON>", "thankFeedback": "<PERSON><PERSON> kasih atas tanggapan Anda", "fillInFeedback": "<PERSON><PERSON> umpan balik", "storyIntroduction": "Pengantar", "missionObjectives": "<PERSON><PERSON><PERSON>", "missionRewards": "<PERSON><PERSON>", "leaderboard": "<PERSON><PERSON>", "ugPhoneToLivco": "", "skipOption": "<PERSON><PERSON>", "genderInterest": "<PERSON><PERSON> k<PERSON>min yang kamu suka", "interests": "<PERSON><PERSON>", "allOptions": "<PERSON><PERSON><PERSON>", "interestPreferences": "Preferensi Minat", "confirmSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelButton": "<PERSON><PERSON>", "confirmButton": "Se<PERSON><PERSON>", "editOption": "Sunting", "saveToLocal": "Simpan ke lokal", "addOption": "Tambah", "saveButton": "Simpan", "emptySpaceNotice": "<PERSON> Sin<PERSON>", "nextStep": "<PERSON><PERSON><PERSON>", "closeButton": "<PERSON><PERSON><PERSON>", "uncompleted": "Tidak lengkap", "completed": "Se<PERSON><PERSON>", "return": "Kembali", "contactSupport": "<PERSON><PERSON> <PERSON>a memiliki masalah, silakan hubungi layanan pelanggan", "maxAmount": "<PERSON><PERSON>", "purchaseOption": "Pembelian", "expand": "<PERSON><PERSON><PERSON>", "collapse": "<PERSON><PERSON><PERSON>", "none": "Tidak ada", "deleting": "<PERSON><PERSON><PERSON><PERSON>", "deleteSuccessful": "Penghapusan berhasil", "download": "<PERSON><PERSON><PERSON>", "owned": "<PERSON><PERSON><PERSON><PERSON>", "notOwned": "Tidak dimiliki", "noData": "Tidak ada data", "nothing": "Tidak ada apa-apa", "haveAnyQuestions": "<PERSON><PERSON> Anda memiliki per<PERSON>, silakan", "minutes": "Menit", "exit": "<PERSON><PERSON><PERSON>", "micPermissionRequired": "UGenie memerlukan per<PERSON>an Anda untuk izin berikut agar dapat menggunakan mikrofon secara normal", "loading": "Memuat", "clickFeedback": "Dapatkan Dukungan", "verify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "successfullySaved": "Disimpan", "uploadPictures": "Mengunggah gambar", "setup": "<PERSON><PERSON><PERSON><PERSON>", "noMore": "Tidak ada lagi", "noMoreContent": "Tidak ada lagi konten", "permissionRequire": "<PERSON><PERSON><PERSON><PERSON>", "pullToRefresh": "<PERSON><PERSON> ke bawah untuk menyegarkan", "looseToRefresh": "Lepaskan untuk menyegarkan", "pressAgain": "Tekan lagi untuk keluar dari aplikasi", "report": "<PERSON><PERSON><PERSON>", "generateSuccessfully": "Berhasil dibuat", "generateStoryBook": "<PERSON><PERSON><PERSON>", "copied": "Di<PERSON>in", "send": "<PERSON><PERSON>", "saveSuccessfully": "<PERSON><PERSON><PERSON><PERSON> disimpan", "saveFailed": "<PERSON><PERSON>", "featureCoding": "Fitur sedang dalam pengembangan", "getSuccessfully": "Dapatkan sukses", "feedback1": "Kesalahan konten atau respons tidak lengkap", "feedback2": "Ko<PERSON>n sensitif atau pornografi", "feedback3": "Informasi yang melibatkan kekerasan atau kebencian", "feedback4": "Informasi tidak pantas yang melibatkan anak di bawah umur", "feedback5": "<PERSON><PERSON>n yang mengganggu", "inputFeedbackMessage": "<PERSON><PERSON><PERSON> masukkan masalah yang Anda temui", "submit": "<PERSON><PERSON>", "feedbackSuccess": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, terima kasih atas masukan <PERSON>", "discoverUpdate": "Versi baru ditemukan!", "updateNow": "<PERSON><PERSON><PERSON>", "updateLater": "<PERSON><PERSON> dulu", "checkForUpdates": "Check for Updates", "currentVersion": "<PERSON><PERSON><PERSON>", "latestVersion": "Saat ini versi terbaru", "swipeToClear": "<PERSON><PERSON><PERSON> ke kiri untuk masuk ke mode imersi 3D", "triggerActions": "Klik pada area khusus untuk memicu tindakan!", "aiContentDisclaimer": "<PERSON><PERSON>-kata yang diucapkan oleh AI semuanya fiktif, harap berhati-hati", "changeTopicSuggestion": "<PERSON><PERSON>an ganti topik", "makeCall": "Panggil", "startNewChat": "<PERSON><PERSON>", "startTyping": "<PERSON><PERSON>", "resetCharacterNotice": "<PERSON><PERSON><PERSON> memu<PERSON>, ri<PERSON>at o<PERSON>lan <PERSON>a dengan karakter akan di<PERSON>pus", "newChatStartedNotice": "Percakapan baru telah dimulai, pesan baru tidak terpengaruh di atas", "emptyContentVoicePlayback": "Anda telah memulai topik baru", "recognitionFailedRetry": "Konten kosong, tidak dapat memutar suara", "unclearAudioRepeat": "<PERSON><PERSON><PERSON> gagal, silakan coba lagi", "voiceNotHeardNotice": "Saya tidak menden<PERSON>, bisakah Anda mengatakannya lagi?", "continueConversation": "<PERSON><PERSON><PERSON> tidak dapat mendengar suara Anda dan akan mengakhiri panggilan secara otomatis...", "exitOption": "Lanjutkan <PERSON>", "clearAudioEnvironment": "Harap usahakan menjaga lingkungan akustik yang bersih selama panggilan", "purchaseCallDuration": "<PERSON>or dan dapatkan lebih banyak waktu bicara karakter", "hangUp": "Tutup Telepon", "microphoneOn": "Mikrofon aktif", "showKeyboard": "<PERSON><PERSON> keyboard", "remainingDuration": "Waktu panggilan tersisa", "exchangeDuration": "Dapatkan lebih banyak waktu panggilan", "callDuration": "<PERSON><PERSON><PERSON>", "unlockSelfie": "<PERSON><PERSON> kunci selfie", "setAsChatBackground": "Atur sebagai latar belakang obrolan", "previousChats": "Mengobrol", "recordingPermissionError": "Tidak dapat memper<PERSON>h izin <PERSON>, silakan periksa pengat<PERSON>n", "recordingFailed": "Tidak dapat merekam", "speechTooShort": "<PERSON><PERSON><PERSON> bicara terlalu singkat", "recordingError": "<PERSON><PERSON><PERSON> gagal", "restarting": "<PERSON><PERSON><PERSON>", "restartSuccessful": "<PERSON><PERSON><PERSON> be<PERSON>", "likeCancelled": "Batalkan suka", "likeSuccessful": "<PERSON><PERSON>", "dislikeCancelled": "Batalkan tidak suka", "dislikeSuccessful": "Tidak suka berhasil", "conversationProcessing": "Agen sedang mengetik...", "voiceTooShort": "<PERSON><PERSON>tu suara terlalu singkat", "exitClearMode": "<PERSON><PERSON>ar dari mode layar bersih", "like": "<PERSON><PERSON>", "dislike": "Tidak suka", "copy": "<PERSON><PERSON>", "releaseToCancelSend": "Lepaskan untuk membatalkan pengiriman", "releaseToSendSwipeUpCancel": "Lepaskan untuk mengirim, geser ke atas untuk membatalkan", "holdToSpeak": "<PERSON><PERSON> untuk berb<PERSON>", "inUse": "Sedang digunakan", "levelLocked": "Level yang tidak terkunci", "levelUnlocked": "Level yang tidak terkunci", "modelLoading": "Memuat model", "waitToSend": "<PERSON><PERSON> tunggu pihak lain selesai berbicara sebelum mengirim", "connecting": "Menghubungkan", "microphoneOff": "Mikrofon mati", "sendingGift": "<PERSON><PERSON> hadiah", "giftSent": "<PERSON><PERSON><PERSON>", "settingUp": "Mengganti latar belakang", "setupSuccessful": "Ditetapkan dengan sukses", "picture": "Gambar", "message": "<PERSON><PERSON>", "intimacyUnlockFunc": "Tingkatkan kesukaan untuk membuka fitur eksklusif", "syncPhoneCallRecord": "Catatan panggilan suara telah disinkronkan.", "savePicture": "Simpan gambar", "scanQRCode": "Pindai kode QR untuk bermain lagi", "refuseGenerate": "Tidak, terima kasih", "rewardCongratulation": "<PERSON><PERSON>, selamat men<PERSON>", "generateCard": "Kumpulkan kartu", "backToChat": "Ke<PERSON>li ke obrolan", "playAgain": "Main lagi", "myStoryBook": "<PERSON><PERSON> ceritaku", "moreStories": "Lebih banyak cerita", "recheckStoryRecord": "Anda dapat melihatnya lagi di bagian Toolbar Halaman Dialog", "storyBookGenerateConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda ingin membuat buku cerita?", "storyBookGenerateTips": "<PERSON><PERSON><PERSON> ulang akan menghapus catatan cerita saat ini...", "restartStory": "<PERSON><PERSON><PERSON> ulang akan menghapus kemajuan cerita Anda saat ini. A<PERSON><PERSON>h Anda yakin ingin memulai ulang?", "moreIntimacyToUnlock": "Tingkatkan keintiman untuk membuka lebih banyak konten cerita", "story": "Cerita", "freeFirst": "Gratis pertama kali", "goToChat": "<PERSON><PERSON> ke obrolan", "syncGameMemory": "Memori Gameplay Eksklusif Lv{level} telah disinkronkan.", "clickToStory": "Klik kartu untuk memulai cerita eksklusif", "upgradeToUnlockStory": "Tingkatkan kesukaan untuk membuka kunci", "startNow": "<PERSON><PERSON>", "unableToSend": "<PERSON><PERSON> t<PERSON> valid", "freeInput": "<PERSON><PERSON><PERSON> be<PERSON>...", "selectOne": "<PERSON><PERSON>an pilih salah satu", "shareContent": "Bagikan Agent ke teman Anda!", "gameWin": "Kemenangan permainan", "gameLose": "Kegagalan permainan", "doubleRewards": "<PERSON><PERSON>", "unlockByAds": "<PERSON>uka kunci dengan menonton iklan", "rewardTip": "<PERSON><PERSON>at atas peneri<PERSON>an pen<PERSON>an", "unSupportRegion": "Fitur ini tidak didukung di", "giftItems": "<PERSON><PERSON>", "membershipBenefits": "<PERSON><PERSON>di anggota untuk mendapatkan lebih banyak kristal", "totalCrystalUsed": "Akumulasi konsumsi kristal", "giftReceivedNotice": "<PERSON><PERSON><PERSON><PERSON>", "sendGift": "<PERSON><PERSON> had<PERSON>", "itemPurchase": "Beli item", "unlockedFeatures": "Terkun<PERSON>", "purchaseOutfit": "<PERSON><PERSON>", "unlock3DOutfit": "<PERSON><PERSON> kunci pakaian 3D", "outfits": "<PERSON><PERSON><PERSON><PERSON>", "staticOutfit": "Pakaian Statis", "unlock3DOption": "Buka Kunci 3D", "unlockOutfitCost": "Membuka kunci pakaian menghabiskan kristal", "itemStore": "<PERSON>", "diamond": "<PERSON><PERSON><PERSON>", "crystalChangeDetails": "<PERSON><PERSON>", "goldChangeDetails": "Detail <PERSON>an <PERSON>", "purchaseSkin": "Beli Skin", "myCrystals": "<PERSON><PERSON>", "crystalDetails": "<PERSON><PERSON>", "selectPurchaseItem": "<PERSON><PERSON><PERSON> pilih produk", "goldExchange": "<PERSON><PERSON><PERSON><PERSON>", "exchangeOption": "<PERSON><PERSON><PERSON>", "backpack": "<PERSON><PERSON><PERSON>", "noMoreCrystalDetails": "Tidak ada detail perubahan kristal", "noMoreGoldDetails": "Tidak ada detail perubahan koin emas", "goldDetails": "Detail koin emas", "moreFreeCoins": "Lebih banyak koin gratis", "weeklyPurchaseLimit": "Batas pembelian minggu ini telah tercapai", "dailyPurchaseLimit": "Batas pembelian hari ini telah tercapai", "purchaseSuccessful": "Pembelian ber<PERSON>il", "totalCoinsSpent": "Akumulasi konsumsi koin emas", "free": "<PERSON><PERSON><PERSON>", "rechargeToUnlock": "Dapatkan dengan isi ulang", "remainingDiamonds": "Total Kristal", "exchangeCoins": "<PERSON><PERSON> koin emas", "redeemNow": "<PERSON><PERSON><PERSON>", "consumption": "Konsumsi", "gold": "<PERSON><PERSON> emas", "crystal": "<PERSON><PERSON>", "insufficientCrystals": "<PERSON><PERSON> tidak cukup", "exchangeSuccessful": "<PERSON><PERSON><PERSON>", "unableFindProduct": "Produk tidak ada", "adsToCrystal": "Tonton iklan untuk mendapatkan 50 kristal", "insufficientBalance": "<PERSON><PERSON> tida<PERSON>, si<PERSON>an setor", "currentLevel": "Level saat ini", "increaseIntimacy": "Tingkatkan keintiman untuk membuka lebih banyak gameplay", "intimacyLevelUp": "<PERSON><PERSON><PERSON> keintiman naik", "giftBoostsIntimacy": "<PERSON><PERSON> hadiah dapat mening<PERSON>kan keintiman ~", "emptyDiscover": "<PERSON> sini kosong, buka halaman <PERSON> untuk memeriksanya", "goDiscover": "Pergi ke Temukan", "msgDetail": "Detail pesan", "noMoreMsg": "Tidak ada pesan lagi", "followedYour": "<PERSON><PERSON><PERSON><PERSON> mi<PERSON>", "followedYouNotice": "<PERSON><PERSON><PERSON><PERSON>", "messageNotification": "Notif<PERSON><PERSON>", "systemMessages": "Pesan Sistem", "interactiveMessages": "Pesan Interaktif", "reportFeedback": "Laporkan Umpan Balik", "deleteRole": "<PERSON><PERSON>", "aboutRole": "Tentang TA", "photoAlbum": "Album", "introduction": "Pengantar", "openingLine": "Ucap<PERSON>", "startChat": "<PERSON><PERSON>", "improveAffection": "Tingkatkan kesukaan untuk membuka selfie eksklusif", "receiveSelfiesHere": "<PERSON><PERSON> yang diterima akan disimpan di sini", "selfieUnlocked": "<PERSON>ie tidak terkunci", "getSelfie": "Dapatkan Selfie", "purchaseCallTime": "Bel<PERSON> W<PERSON>", "purchaseGift": "<PERSON><PERSON>", "confirmDeleteCharacter": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus karakter tersebut?", "agentProfile": "Profil", "cancelCreate": "Batalkan pembuatan", "characterSetting": "<PERSON><PERSON>", "creationMethod": "Metode Pem<PERSON>", "quickCreate": "Buat Cepat", "selectGender": "<PERSON><PERSON><PERSON>", "maleOption": "Pria", "femaleOption": "<PERSON><PERSON>", "nonBinaryOption": "Non-Biner", "setPublic": "Atur ke Publik", "publicSettingWarning": "Jika diatur ke publik, tidak dapat dibuat pribadi", "personality": "Kepribadian", "createAndStartChat": "Buat dan <PERSON>", "advancedCreation": "<PERSON><PERSON><PERSON><PERSON>", "customizeImage": "Gambar Kustom", "freeImage": "<PERSON><PERSON><PERSON>", "enterNickname": "<PERSON><PERSON><PERSON><PERSON>", "completeBackground": "Lengkapi Latar Belakang Cerita", "createCharacter": "<PERSON><PERSON><PERSON>", "creatingCharacter": "Membuat", "regenerateOption": "Regenerasi", "cropAvatar": "Pangkas Avatar", "continueCurrentSetting": "Lanjutkan dengan Pengaturan Saat Ini?", "modifySettingNotice": "Anda dapat mengubah pengaturan dan membuat citra baru...", "savedInDrafts": "<PERSON><PERSON><PERSON> yang di<PERSON>kan akan disimpan di kotak draf", "exitAICharacterCreation": "<PERSON><PERSON><PERSON> dari Pembu<PERSON>n Entitas AI", "draftsFolder": "Kotak Draf", "useImage": "<PERSON><PERSON><PERSON>", "voiceOption": "<PERSON><PERSON>", "tag": "tag", "presetSoundLibrary": "Perpustaka<PERSON>", "characterSettingRequired": "<PERSON><PERSON><PERSON><PERSON> (wajib)", "openingLineRequired": "<PERSON><PERSON> (wajib)", "backgroundStoryRequired": "<PERSON><PERSON> (wajib)", "characterIntroRequired": "Pengen<PERSON> Karakter (wajib)", "dialogueExampleOptional": "<PERSON><PERSON><PERSON> (opsional)", "tagRequired": "Label (wajib)", "clickToAddUserContent": "Klik untuk Menambahkan Konten Dialog Pengguna", "clickToAddAIResponses": "Klik untuk Menambahkan Konten Respons AI", "customizeImageName": "Kon<PERSON>gu<PERSON>i <PERSON> yang <PERSON>ua<PERSON>n", "createCustomImage": "Buat Avatar Kustom", "createAI": "Buat Entitas AI", "openingLineTips": "<PERSON><PERSON> masukkan u<PERSON>an pembuka", "choiceFigureTips": "<PERSON><PERSON><PERSON><PERSON> ulang avatar tidak akan mempertahankan pengaturan saat ini", "selectFigureConfirm": "<PERSON><PERSON>h ulang avatar?", "createAgain": "B<PERSON>t <PERSON>", "inputContentTips": "<PERSON><PERSON> ma<PERSON>kkan konten (hingga 300 karakter)", "inputEmptyTips": "Input tidak boleh kosong!", "sexChoice": "<PERSON><PERSON><PERSON><PERSON> jeni<PERSON> k<PERSON>", "audioEmpty": "Tidak ada audio", "template": "Templat", "selectRequest": "<PERSON><PERSON><PERSON> pilih", "agentTemplateTips": "<PERSON><PERSON><PERSON> pilih template AI", "abandonFigureConfirm": "Membuat ulang avatar akan memakan kristal lagi...", "agentInfoTips": "Harap lengkapi informasi AI", "backgroundStoryTips": "<PERSON><PERSON> masukkan cerita latar belakang", "characterIntroTips": "<PERSON><PERSON> masukkan per<PERSON> singkat", "dialogueExample": "Contoh dialog", "agentFeatureTips": "Harap leng<PERSON>pi karakteristik AI", "diyFigureSave": "Avatar yang dibuat khusus akan disimpan di sini", "cancelCreateTips": "Membatalkan pembuatan tidak akan menyimpan pengaturan Anda saat ini", "cancelCreateConfirm": "<PERSON><PERSON><PERSON><PERSON> Anda ingin membatalkan pembuatan saat ini?", "createAgentSuccess": "AI berhasil dibuat", "characterSetTips": "<PERSON><PERSON> masukkan pengaturan karakter", "tagEmptyTips": "Silakan pilih label", "fansOption": "<PERSON><PERSON><PERSON><PERSON>", "likesOption": "<PERSON><PERSON>", "followOption": "<PERSON><PERSON><PERSON>", "nicknameOption": "<PERSON><PERSON>", "genderOption": "<PERSON><PERSON>", "birthdaySelection": "<PERSON><PERSON> Tahun", "personalDescription": "Deskripsi Pribadi", "youRoleChatPersonalDesc": "<PERSON><PERSON><PERSON> <PERSON> akan mengo<PERSON>l dengan Anda berdasarkan deskripsi pribadi Anda...", "selectBirthday": "<PERSON><PERSON><PERSON>", "unfollowOption": "<PERSON><PERSON><PERSON><PERSON>", "basicInfo": "<PERSON><PERSON>", "enterInviteCode": "Masukkan kode", "invalidInviteCode": "Kode tidak ada", "rewardAlreadyClaimed": "<PERSON><PERSON>", "accountDeactivation": "Hapus Akun", "deactivationTerms": "Syarat Pembatalan", "deactivationTermsContent1": "1.Akun yang akan dibatalkan adalah akun yang sedang login.", "deactivationTermsContent2": "2.<PERSON><PERSON><PERSON>, <PERSON><PERSON> t<PERSON> akan dapat login, menga<PERSON><PERSON><PERSON> kembali, men<PERSON><PERSON><PERSON>, atau memulihkan akun ini lagi.", "deactivationTermsContent3": "3.<PERSON><PERSON>, koi<PERSON>, atau berlian yang tidak terpakai di akun, se<PERSON><PERSON>a akan di<PERSON>pus setelah pembatalan berhasil. <PERSON><PERSON> lanjutkan dengan hati-hati.", "deactivationTermsContent4": "4.<PERSON><PERSON><PERSON>, informasi pribadi Anda dan data lainnya akan dihapus secara permanen dan tidak dapat dipulihkan.", "deactivationTermsContent5": "5.<PERSON><PERSON><PERSON> pem<PERSON>alan, akun akan disimpan untuk Anda selama 15 hari. Jika Anda login selama periode ini, pembatalan akan dibatalkan secara otomatis. Jika pembatalan tidak dicabut dalam 15 hari, akun akan dibatalkan secara otomatis.", "termsAgreement": "<PERSON>a telah membaca dan menyetujui syarat-syarat di atas", "accountVerification": "Verifi<PERSON><PERSON>", "otherVerificationOptions": "<PERSON><PERSON>", "deactivationConfirmation": "<PERSON><PERSON> baca dengan seksama dan konfirmasi lagi", "deactivationNotice1": "1. <PERSON><PERSON><PERSON>, informasi pribadi Anda dan data lainnya akan dihapus secara permanen dan tidak dapat dipulihkan.", "deactivationNotice2": "2. <PERSON><PERSON><PERSON> pem<PERSON>alan, akun akan disimpan untuk Anda selama 15 hari. Jika Anda login selama periode ini, pembatalan akan dibatalkan secara otomatis. Jika pembatalan tidak dicabut dalam 15 hari, akun akan dibatalkan secara otomatis.", "deactivationSuccessful": "Aplikasi Pembatalan Akun Berhasil", "verificationSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "networkErrorRetry": "<PERSON><PERSON><PERSON>, silakan coba lagi nanti.", "accountVerificationFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Tidak Valid", "invalidEmailVerification": "Kode verifikasi tidak valid, silakan coba lagi nanti.", "profile": "<PERSON><PERSON>", "choosePreference": "<PERSON><PERSON><PERSON><PERSON>", "recommend": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "abandonAccountCancellation": "Batalkan pembatalan akun", "abandonCancellation": "Batalkan pembatalan", "discardLogoutPrompt": "<PERSON><PERSON><PERSON>a telah mengajukan permintaan pembatalan...", "focusonSuccess": "<PERSON><PERSON><PERSON><PERSON>~", "unfollowed": "Tidak lagi mengikuti", "fansAmount": "xxx pengikut", "rolesAmount": "xxx karakter", "emailFormatError": "Format email salah", "inconsistentAccounts": "Akun yang diver<PERSON>ikasi bukan akun yang sedang login", "inviteCode": "<PERSON><PERSON>", "inviteAmount": "Telah mengundang total xx orang", "termTips": "Harap baca syarat di atas dengan seksama. Akun tidak dapat dipulihkan...", "invitationLink": "<PERSON><PERSON> und<PERSON>n", "day": "hari", "encounteredAlready": "<PERSON><PERSON><PERSON> UGenie selama X hari", "seeMore": "<PERSON><PERSON> lebih banyak", "upgradePlanBenefits": "<PERSON><PERSON>", "activateNow": "Tingkatkan", "logoutInfo": "<PERSON><PERSON>", "orderHistory": "<PERSON><PERSON>", "noMembershipActivated": "<PERSON><PERSON><PERSON>an VIP <PERSON><PERSON><PERSON>", "activateVIPBenefits": "<PERSON><PERSON>", "purchaseNow": "<PERSON><PERSON><PERSON>", "superMembershipOffer": "Isi Ulang Keanggotaan Super dan Dapatkan Hadiah Instan", "dailyGiftForSuperMembers": "<PERSON><PERSON> untuk Anggota Super", "chooseCombo": "<PERSON><PERSON>an pilih paket keanggotaan", "NonMember": "Tidak ada keanggotaan yang aktif saat ini", "subscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekSubscriptionDesc": "Batalkan kapan saja. Diperpanjang minggu depan seharga $/minggu.", "monthSubscriptionDesc": "Batalkan kapan saja. Diperpanjang bulan depan seharga $/bulan.", "maturity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "membershipCenter": "Konfirmasi Pembatalan Pesanan", "orderCancellationConfirm": "Perhatian: Membatal<PERSON> pesanan hanya mengembalikan diskon yang telah digunakan (mis: diskon pesanan pertama, voucher). <PERSON><PERSON> <PERSON><PERSON> su<PERSON> membayar, jangan batalkan pesanan, karena dapat menyebabkan kegagalan pemrosesan pesanan dan kerugian bagi Anda", "cancellationNotice": "Diskon berikut dapat dikembalikan untuk pembatalan pesanan ini", "cancellationRefundDetails": "Diskon P<PERSON>", "ensureNoPaymentBeforeCancel": "<PERSON><PERSON> kon<PERSON>si Anda belum membayar sebelum membatalkan!", "cancelUnpaidOrder": "<PERSON><PERSON> be<PERSON> bayar, segera batalkan pesanan", "orderRecords": "<PERSON>i<PERSON><PERSON> Pesanan", "orderType": "<PERSON><PERSON>", "creationTime": "W<PERSON><PERSON>", "productName": "<PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON>", "actualPayment": "<PERSON><PERSON><PERSON>", "processingOrder": "<PERSON><PERSON><PERSON><PERSON>", "closedOrder": "Ditutup", "cancelOrder": "<PERSON><PERSON><PERSON>", "changeTime": "<PERSON><PERSON><PERSON>", "records": "Catatan", "quantity": "Kuantitas", "awaitingOrderCompletion": "<PERSON><PERSON><PERSON>", "orderNumber": "<PERSON><PERSON>", "paymentProcessing": "Memp<PERSON><PERSON>", "watiForPayment": "<PERSON><PERSON> ma<PERSON> pem<PERSON>, pembay<PERSON> mungkin memerlukan waktu 5-10 menit...", "noMoreOrder": "Tidak Ada Catatan", "rechargeSuccessful": "<PERSON><PERSON> ul<PERSON> ber<PERSON>", "cashRegister": "<PERSON><PERSON>", "selectPaymentMethod": "<PERSON><PERSON>an pilih metode pembayaran", "selectPaymentWay": "<PERSON><PERSON><PERSON> metode pembayaran", "paySuccess": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>", "payAmount": "<PERSON><PERSON><PERSON> se<PERSON> yang <PERSON>", "selectPayCountry": "<PERSON><PERSON><PERSON> negara pembayaran", "immediatePayment": "<PERSON><PERSON>", "orderCompleted": "<PERSON><PERSON><PERSON>", "payedOrder": "<PERSON><PERSON><PERSON>", "refundOrder": "<PERSON><PERSON><PERSON>", "canceledOrder": "<PERSON><PERSON><PERSON>", "purchaseCancelled": "Pembelian <PERSON>", "buyFail": "P<PERSON><PERSON><PERSON> gagal", "taskForFreeGold": "Selesaikan tugas untuk mendapatkan lebih banyak koin gratis", "task": "Tugas", "dailyTasks": "<PERSON><PERSON>", "specialTasks": "<PERSON><PERSON>", "checkIn": "Check-in", "rewardClaimed": "<PERSON><PERSON><PERSON>", "goComplete": "<PERSON><PERSON><PERSON><PERSON>", "claimReward": "<PERSON><PERSON><PERSON>", "notAchieved": "Belum <PERSON>rca<PERSON>i", "inviteFriends": "<PERSON><PERSON><PERSON>", "currentInvitedCount": "Saat In<PERSON>", "taskAcquisition": "Tugas untuk mendapatkan", "weeklyTasks": "Weekly Tasks", "noAds": "Hilangkan Iklan", "from": "mulai", "unlockVipPower": "<PERSON>kses {vipNum} Fitur VIP", "subscribeNow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "waitAction": "<PERSON><PERSON><PERSON> tindakan...", "moreCallTime": "<PERSON><PERSON><PERSON>", "vipTips": "<PERSON>gan meng<PERSON><PERSON> be<PERSON>, <PERSON><PERSON> men<PERSON><PERSON> syarat layanan. Anda akan dikenakan biaya dan langganan akan otomatis diperpanjang dengan harga dan durasi yang sama. Anda dapat membatalkan langganan kapan saja di Play Store. Ini adalah harga promosi terbatas waktu. Kami akan memberi tahu Anda 30 hari sebelum periode promosi berakhir. <PERSON>gguna yang sudah membeli dapat terus menikmati perpanjangan dengan harga promosi.", "onlineTips": "<PERSON><PERSON>", "onlineDesc": "Unduh UGenie untuk lebih seru dengan kesempatan main tanpa iklan!", "onlineJump": "<PERSON><PERSON>", "community": "Komunitas", "communityCrystal": "<PERSON><PERSON>", "globalSoundSwitch": "Sakelar Volume Global", "bgmSwitch": "Sakelar Musik Latar Belakang", "buyCrystal": "<PERSON><PERSON>", "loadAdFail": "Iklan gagal dimuat. Silakan coba lagi nanti.", "selectYourGender": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>", "wayYouLike": "<PERSON><PERSON>aks<PERSON>", "selectYourPreference": "<PERSON><PERSON><PERSON>", "preference1": "Ng<PERSON><PERSON>", "preference2": "<PERSON><PERSON>, <PERSON><PERSON> pilihan", "preference3": "Cerita", "preference4": "Teks panjang, Narasi", "preference5": "Permainan", "preference6": "Aturan, Statistik", "intimacyUpgrade": "Tingkat keintiman ditingkatkan ke LV{level}", "contentGameUnlock": "Gameplay eksklusif LV{level} telah dibuka"}