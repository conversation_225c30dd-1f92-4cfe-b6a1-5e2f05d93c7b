{"welcomeToEros": "Ugenieへようこそ！", "termsConfirmation": "本サイトの利用前に以下の条項を確認してください。", "ageConfirmation": "私は18歳以上です。", "aiContentNotice": "当サイトのAI関連コンテンツは架空であることを承知しています。", "guestLogin": "ゲストログイン", "registrationLogin": "登録/ログイン", "emailLogin": "メールログイン", "email": "メールアドレス", "getCaptcha": "取得", "emailLoginOption": "メールログイン", "agreeToTerms": "ログインするとプライバシーポリシーとユーザーポリシーに同意する", "loginIssueContact": "ログインできないですか？カスタマーサービスに連絡してください。", "enterEmail": "メールアドレスを入力してください", "enterCaptcha": "認証コードを入力してください", "invalidEmailFormat": "メールアドレスの形式が正しくないため、もう一度入力してください。", "captchaError": "認証コードが正しくないため、もう一度入力してください。", "captchaExpired": "認証コードが失効しました。再度入力してください。", "accountSuspendedNotice": "アカウントは一時停止されています。詳細については、ヘルプセンターで確認またはカスタマーサービスにお問い合わせください。", "logout": "ログアウト", "logoutOption": "ログアウトを確認", "confirmLogout": "ログアウト後はAIエンティティとチャットできなくなりますが、引き続きこのアカウントにログインできます", "readAndConfirmed": "私は読み取りを確認しました", "privacyAgreement": "プライバシーポリシー", "userAgreement": "利用規約", "unableLogin": "ログインできませんか？", "contactCustomer": "顧客サポートに連絡する", "loginAndAgree": "サインインして同意する", "welcomeToUgenie": "Ugenieへようこそ", "CheckTerms": "条項にチェックしてください", "otherLoginWay": "他のアカウントでログイン", "loadingFailed": "SDKの読み込みは失敗しました", "invalidSession": "無効なセッションです。再度ログインしてください", "verificationCode": "確認コード", "store": "ストア", "searchAgentName": "キャラクターを検索する", "unableConnect": "ストアに接続できません", "searchHistory": "検索履歴", "popularRoles": "人気キャラクター", "noResultsFeedback": "欲しいものがない？フィードバックをクリック", "emptySearchSuggestion": "ここは空いているので、別のキーワードを試してみてください", "creatorSelection": "クリエイター", "chatNow": "チャット", "followedNotice": "フォロー中", "characterOption": "キャラクター", "search": "検索", "searchWantTips": "欲しいものが見つかりませんか?", "tryUsingDifferentKeyword": "キーワードを変更して再試行してください", "language": "言語", "helpFAQ": "ヘルプFAQ", "feedbackOption": "会社概要", "selectNumber": "あなたのナンバーを選択してください", "aiFilter": "インテリジェントエンティティの選別", "sortOptions": "並べ替え方", "aiType": "インテリジェントエンティティのタイプ", "mostPopular": "人気", "latest": "最新", "trending": "人気", "searchRoleOrCreator": "キャラクターまたはクリエイターを検索", "dynamicInteraction": "キャラとのダイナミックな交流を体験して、コミュニケーションをより生き生きとさせましょう。", "chatBasedOnPersona": "キャラクターはあなたのキャラ設置に基づいて話しますので、キャラクターカードを選択して、彼女/彼があなたをよりよく理解できるようにしてください", "userCharacterOption": "私のキャラ", "roleSpeakingDuration": "キャラが話している言葉だけでこの時間を消費する", "repeatedClickNotice": "何度もクリックしましたので、後でもう一度お試しください", "exitImmersiveMode": "浸透モード終了", "myCreations": "私の作成", "loginToView": "ログインしてチェック", "createLover": "専用恋人を作ろう", "goCreate": "作成に行く", "clickToSet": "クリックして設定", "retainCharacterNotice": "あなたのキャラクターを保留しますか？", "dataClearWarning": "キャラデータすべて削除しますので、慎重に操作してください", "clickToTouchSpace": "タッチスペースに入る", "imageOption": "外観", "noAIsNotice": "AIエンティティがまだない場合は、下の+をクリックしてAIエンティティを召喚してください~", "myImageOption": "私の外観", "editImage": "外観を編集", "defaultVoicePlayback": "デフォルトでボイス再生", "captchaInvalidRetry": "NSFWを有効", "cost": "費用", "noHaveAi": "相手にAIがありません", "moreCharacterTime": "より多くのキャラ会話時間", "noMoreImage": "今のところもっとキャラクター設定はありません", "editFigure": "キャラクター設定を編集", "aiOnTheWay": "AIはまだ道中です～", "searchWant": "あなたが望むものを検索してください...", "confirmDelFigure": "このアバターを削除してもよろしいですか？", "personalDescriptionTips": "あなたのキャラクターはあなたの個人説明とあなたのチャットに基づいており、あなたの好みや嫌いなものなどの情報入力が可能です～", "confirmExitStory": "現在のストーリーを終了してよろしいですか？", "interactWithHer": "彼女と交流する", "newerStoryName": "リバース：リリーからの脱出", "flowerList": "ギフトランキング", "popularityChart": "人気ランキング", "inputFeedback": "フィードバック内容を入力してください", "thankFeedback": "ご意見ありがとうございます", "fillInFeedback": "フィードバック記入", "storyIntroduction": "ストーリー紹介", "missionObjectives": "目標", "missionRewards": "報酬", "leaderboard": "ランキング", "ugPhoneToLivco": "", "skipOption": "スキップ", "genderInterest": "興味ある性別", "interests": "興味", "allOptions": "すべて", "interestPreferences": "興味", "confirmSelection": "確認", "cancelButton": "キャンセル", "confirmButton": "完了", "editOption": "編集", "saveToLocal": "ローカルに保存", "addOption": "追加", "saveButton": "保存", "emptySpaceNotice": "ここは空いてる", "nextStep": "次のステップ", "closeButton": "閉じる", "uncompleted": "未完了", "completed": "完了しました", "return": "戻る", "contactSupport": "もし何か問題があれば、カスタマーサービスに連絡してください", "maxAmount": "最大", "purchaseOption": "購入", "expand": "展開する", "collapse": "折りたたむ", "none": "なし", "deleting": "削除中", "deleteSuccessful": "削除成功", "download": "ダウンロード", "owned": "所有する", "notOwned": "所有していない", "noData": "データなし", "nothing": "何もありません", "haveAnyQuestions": "ご質問がございましたら、お気軽にお問い合わせください", "minutes": "分", "exit": "退出", "micPermissionRequired": "Ugenieはマイクを正常に使用するために以下の権限を許可する必要があります", "loading": "読み込み中", "clickFeedback": "サポートを受ける", "verify": "検証", "successfullySaved": "保存しました", "uploadPictures": "ピクチャをアップロード中", "setup": "設定", "noMore": "これ以上ありません", "noMoreContent": "これ以上内容はありません", "permissionRequire": "許可申請", "pullToRefresh": "引っ張ってリフレッシュ", "looseToRefresh": "放してリフレッシュ", "pressAgain": "もう一度押すとアプリを終了します", "report": "報告", "generateSuccessfully": "生成に成功しました", "generateStoryBook": "生成する", "copied": "コピー済み", "send": "", "saveSuccessfully": "", "saveFailed": "", "featureCoding": "機能開発中", "getSuccessfully": "取得成功", "feedback1": "コンテンツエラーまたは不完全な返信", "feedback2": "敏感またはポルノコンテンツ", "feedback3": "暴力やヘイトに関する情報", "feedback4": "未成年者に関する不適切な情報", "feedback5": "不快なコンテンツ", "inputFeedbackMessage": "遇到した問題を入力してください", "submit": "送信", "feedbackSuccess": "送信成功、フィードバックありがとうございます", "discoverUpdate": "新しいバージョンが見つかりました！", "updateNow": "すぐに更新", "updateLater": "暫く更新しない", "checkForUpdates": "アップデートを確認", "currentVersion": "現在のバージョン", "latestVersion": "現在は最新バージョンです", "swipeToClear": "左にスワイプして画面をクリアし、3D浸入モードに入る", "triggerActions": "スペシャルなエリアをクリックしてアクションをトリガーする！", "aiContentDisclaimer": "AIが言っていることはすべて架空なので、慎重に判断してください", "changeTopicSuggestion": "別の話題にしてください", "makeCall": "電話をかける", "startNewChat": "再起動", "startTyping": "入力開始", "resetCharacterNotice": "再起動後、キャラクターとのチャット履歴は消去されます。", "newChatStartedNotice": "新しい会話が開始されました。新しいメッセージは上記の影響を受けません", "emptyContentVoicePlayback": "新しい話題を開始しました", "recognitionFailedRetry": "コンテンツがブランクなので、音声を再生できません", "unclearAudioRepeat": "認識失敗、再試行してください", "voiceNotHeardNotice": "先ほどはよく聞こえませんでした。もう一度お願いできますか？", "continueConversation": "キャラクターはあなたの声を聞くことができず、カウントダウンの後自動的に通話が終了します", "exitOption": "会話を続ける", "clearAudioEnvironment": "通話中にできるだけ静かな音声環境を維持してください", "purchaseCallDuration": "チャージしてより長く会話時間をゲットできます", "hangUp": "電話を切る", "microphoneOn": "マイクオン", "showKeyboard": "キーボードを引き上げる", "remainingDuration": "残り通話時間", "exchangeDuration": "通話時間を追加", "callDuration": "通話時間", "unlockSelfie": "セルフィーをアンロック", "setAsChatBackground": "チャット背景に設定", "previousChats": "チャットした", "recordingPermissionError": "録音許可を取得できません。設定を確認してください", "recordingFailed": "録音できません", "speechTooShort": "話す時間が短すぎます", "recordingError": "録音失敗", "restarting": "再起動中", "restartSuccessful": "再起動成功", "likeCancelled": "いいね！をキャンセルしました", "likeSuccessful": "いいね！に成功しました", "dislikeCancelled": "マイナス評価をキャンセルしました", "dislikeSuccessful": "マイナス評価に成功しました", "conversationProcessing": "ダイアログを出力中", "voiceTooShort": "音声時間が短すぎます", "exitClearMode": "クリーンスクリーンモードを終了します", "like": "いいね！", "dislike": "マイナス評価", "copy": "コピー", "releaseToCancelSend": "手を離して送信をキャンセル", "releaseToSendSwipeUpCancel": "手を離して送信、上にスライドしてキャンセル", "holdToSpeak": "押し続けて話してください", "inUse": "使用中", "levelLocked": "ロックされていたレベル", "levelUnlocked": "アンロックされたレベル", "modelLoading": "モデルを読み込んでいます", "waitToSend": "相手が話した後に送信してください", "connecting": "接続中", "microphoneOff": "マイク<PERSON>", "sendingGift": "プレゼント中", "giftSent": "プレゼントに成功しました", "settingUp": "設定中", "setupSuccessful": "設定に成功しました", "picture": "ピクチャ", "message": "メッセージ", "intimacyUnlockFunc": "お気に入りを強化します排他的な機能のロックを解除します", "syncPhoneCallRecord": "音声通話記録が同期されました。", "savePicture": "画像を保存", "scanQRCode": "QRコードをスキャンして再度プレイする", "refuseGenerate": "いいえ、結構です", "rewardCongratulation": "ストーリーが完了しましたを獲得しました", "generateCard": "ストーリーカードを受け取る", "backToChat": "チャットに戻る", "playAgain": "もう一度遊ぶ", "myStoryBook": "私のストーリーブック", "moreStories": "もっとストーリー", "recheckStoryRecord": "“会話ページのツール欄”-“ストーリー”で再度確認できます", "storyBookGenerateConfirm": "ストーリーブックを生成しますか？", "storyBookGenerateTips": "再プレイすると今回のストーリー記録が消去されます。記録を保存するためにストーリーブックを生成しますか？", "restartStory": "", "moreIntimacyToUnlock": "親密度を上げて、さらに多くのストーリーを解放しましょう", "story": "ストーリー", "freeFirst": "初回無料", "goToChat": "チャットに行く", "syncGameMemory": "Lv{level}専用ゲームプレイ記憶が同期されました", "clickToStory": "カードをクリックして専用ストーリーを開く", "upgradeToUnlockStory": "エージェントとの親密度を上げてアンロックしましょう", "startNow": "すぐに開始", "unableToSend": "送信無効", "freeInput": "自由入力...", "selectOne": "1つ選択してください", "shareContent": "Agentを友達に共有しましょう！", "gameWin": "ゲーム勝利", "gameLose": "ゲーム失敗", "doubleRewards": "ダブル報酬", "unlockByAds": "広告を見てアンロック", "rewardTip": "受賞おめでとうございます", "unSupportRegion": "お住まいの地域ではこの機能はサポートされていません", "giftItems": "ギフト", "membershipBenefits": "会員登録してクリスタルをもっとゲット", "totalCrystalUsed": "クリスタルの累計消費", "giftReceivedNotice": "**があなたのものを受け取った", "sendGift": "プレゼントを贈る", "itemPurchase": "アイテムを購入", "unlockedFeatures": "ロック中", "purchaseOutfit": "格好を購入", "unlock3DOutfit": "3D格好をアンロック", "outfits": "衣装", "staticOutfit": "スタティックな格好", "unlock3DOption": "3Dをアンロック", "unlockOutfitCost": "1つの格好をアンロックするにはクリスタルが必要です", "itemStore": "アイテムショップ", "diamond": "ダイヤモンド", "crystalChangeDetails": "クリスタルの変更詳細", "goldChangeDetails": "ゴールドコインの変更詳細", "purchaseSkin": "スキンを購入", "myCrystals": "私のクリスタル", "crystalDetails": "クリスタルの詳細", "selectPurchaseItem": "商品を選択してください", "goldExchange": "ゴールド引換", "exchangeOption": "引換", "backpack": "かばん", "noMoreCrystalDetails": "クリスタル変更明細なし", "noMoreGoldDetails": "ゴールドコイン変更明細なし", "goldDetails": "ゴールドコイン明細", "moreFreeCoins": "さらに多くの無料コイン", "weeklyPurchaseLimit": "この商品は週に購入できる上限に達しました", "dailyPurchaseLimit": "この商品は日に購入できる上限に達しました", "purchaseSuccessful": "購入成功", "totalCoinsSpent": "ゴールドの累計消耗", "free": "無料", "rechargeToUnlock": "チャージで獲得", "remainingDiamonds": "水晶の合計", "exchangeCoins": "ゴールドコインを交換", "redeemNow": "すぐに交換", "consumption": "消費", "gold": "ゴールドコイン", "crystal": "クリスタル", "insufficientCrystals": "クリスタルが足りない", "exchangeSuccessful": "償還が成功しました", "unableFindProduct": "商品が存在しません", "adsToCrystal": "広告を見て50個のクリスタルを獲得", "insufficientBalance": "残高が足りません。チャージしてください", "currentLevel": "現在のレベル", "increaseIntimacy": "親密度を上げて、より多くの遊び方をアンロックしよう", "intimacyLevelUp": "親密度レベルアップ", "giftBoostsIntimacy": "プレゼントを贈ることで親密度を上げることができます~", "emptyDiscover": "ここは空いているので、発見ページでも見ようか", "goDiscover": "発見に行く", "msgDetail": "メッセージの詳細", "noMoreMsg": "これ以上メッセージはありません", "followedYour": "**があなたの**をフォローした", "followedYouNotice": "フォローしました", "messageNotification": "通知", "systemMessages": "システムメッセージ", "interactiveMessages": "対話メッセージ", "reportFeedback": "フィードバック報告", "deleteRole": "キャラクター削除", "aboutRole": "その人について", "photoAlbum": "アルバム", "introduction": "プロフィール", "openingLine": "挨拶", "startChat": "会話開始", "improveAffection": "好感度を上げてスペシャルセルフィーをアンロックしよう", "receiveSelfiesHere": "受け取ったセルフィーはここに保存されます", "selfieUnlocked": "セルフィーはアンロックしました", "getSelfie": "セルフィーをゲット", "purchaseCallTime": "通話時間を購入", "purchaseGift": "ギフトを購入", "confirmDeleteCharacter": "キャラクター削除を確認できますか？", "agentProfile": "プロフィール", "cancelCreate": "作成のキャンセル", "characterSetting": "キャラデザイン", "creationMethod": "作成方法", "quickCreate": "クイック作成", "selectGender": "キャラクターの性別を選択", "maleOption": "男性", "femaleOption": "女性", "nonBinaryOption": "ノンバイナリー", "setPublic": "公開に設定", "publicSettingWarning": "公開に設定すると、非公開にできません", "personality": "性格", "createAndStartChat": "作成してチャットを始めよう", "advancedCreation": "高級作成", "customizeImage": "カスタムアバター", "freeImage": "無料アバター", "enterNickname": "ニックネームを入力", "completeBackground": "バックストーリーを充実させる", "createCharacter": "キャラ作成", "creatingCharacter": "作成中", "regenerateOption": "再生成", "cropAvatar": "アバターをトリミング", "continueCurrentSetting": "現在の設定で続けますか？", "modifySettingNotice": "設定を変更して新しいアバターを作成できますが、追加のクリスタルが必要です", "savedInDrafts": "生成されたアバターは下書き箱に保存されます", "exitAICharacterCreation": "AIエンティティの作成を中止", "draftsFolder": "下書き箱", "useImage": "アバター使用", "voiceOption": "音声", "tag": "タグ", "presetSoundLibrary": "プリセット音源", "characterSettingRequired": "キャラ設定(必須)", "openingLineRequired": "挨拶(必須)", "backgroundStoryRequired": "バックストーリー (必須)", "characterIntroRequired": "プロフィール(必須)", "dialogueExampleOptional": "対話例 (任意)", "tagRequired": "タグ（必須）", "clickToAddUserContent": "クリックして対話内容を追加する", "clickToAddAIResponses": "クリックしてAI回復内容を追加する", "customizeImageName": "カスタムアバター名の詳細設定", "createCustomImage": "カスタムアバターを作成", "createAI": "AIエンティティを作成", "openingLineTips": "開場白を入力してください", "choiceFigureTips": "イメージを再選択すると現在の設定が保持されません", "selectFigureConfirm": "イメージを再選択しますか？", "createAgain": "再作成", "inputContentTips": "内容を入力してください（最大300文字）", "inputEmptyTips": "入力を空にできません！", "sexChoice": "性別選択", "audioEmpty": "オーディオなし", "template": "テンプレート", "selectRequest": "選択してください", "agentTemplateTips": "AIテンプレートを選択してください", "abandonFigureConfirm": "イメージを再生成するとクリスタルを再び消費するため、現在のイメージを放弃しますか？", "agentInfoTips": "AI情報を完成してください", "backgroundStoryTips": "バックグラウンドストーリーを入力してください", "characterIntroTips": "人物紹介を入力してください", "dialogueExample": "ダイアログ例", "agentFeatureTips": "AIの特徴を完成してください", "diyFigureSave": "カスタム作成のアバターはここに保存されます", "cancelCreateTips": "作成をキャンセルすると現在の設定が保持されません", "cancelCreateConfirm": "今の作成をキャンセルしますか？", "createAgentSuccess": "AIを作成しました", "characterSetTips": "キャラクター設定を入力してください", "tagEmptyTips": "ラベルを選択してください", "fansOption": "ファン", "likesOption": "いいね", "followOption": "フォロー", "nicknameOption": "ニックネーム", "genderOption": "性別", "birthdaySelection": "バースデー", "personalDescription": "プロフィール", "youRoleChatPersonalDesc": "キャラはプロフィールに基づいてあなたとチャットしますので、あなたの好み、嫌いなものなど情報を入力しますね~", "selectBirthday": "バースデーを選ぶ", "unfollowOption": "フォロー解除", "basicInfo": "マイプロフィール", "enterInviteCode": "コードを入力してください", "invalidInviteCode": "コードが存在しません", "rewardAlreadyClaimed": "報酬は既に受け取られました", "accountDeactivation": "アカウント削除", "deactivationTerms": "削除条項", "deactivationTermsContent1": "1.今ログインしているアカウントを削除します。", "deactivationTermsContent2": "2.アカウント削除成功の後、もう一度ログイン、アクティベーション、使用またはそのアカウントの復元ができなくなります。", "deactivationTermsContent3": "3.アカウントに未失効のメンバーシップ、未使用のゴールドやダイヤモンドが存在する場合、削除に成功するとすべてクリアされますので、慎重に操作してください。", "deactivationTermsContent4": "4.削除に成功すると、あなたの個人情報やその他のデータが永久に削除され、復元できません。", "deactivationTermsContent5": "5.削除を申請した後、アカウントは15日間保留されます。その期間中に再度ログインすると、削除が自動的にキャンセルされます。もし15日間に削除をキャンセルしない場合は、自動的にアカウントが削除されます。", "termsAgreement": "私は上記の条項を読み、同意しました", "accountVerification": "アカウントの検証", "otherVerificationOptions": "他の検証方法", "deactivationConfirmation": "もう一度注意深く読み、確認してください", "deactivationNotice1": "１.削除に成功すると、あなたの個人情報やその他のデータが永久に削除され、復元できません。", "deactivationNotice2": "２.削除を申請した後、アカウントは15日間保留されます。その期間中に再度ログインすると、削除が自動的にキャンセルされます。もし15日間に削除をキャンセルしない場合は、自動的にアカウントが削除されます。", "deactivationSuccessful": "アカウントの削除が成功しました", "verificationSuccess": "検証成功", "networkErrorRetry": "ネットワークエラー、後で再試してください", "accountVerificationFailed": "アカウントの検証に失敗しました、メールアカウントが無効です", "invalidEmailVerification": "無効な確認コード、後で再試してください", "profile": "個人プロフィール", "choosePreference": "あなたの好みを選択してください", "recommend": "おすすめ", "abandonAccountCancellation": "アカウントのキャンセルをやめます", "abandonCancellation": "キャンセルをやめます", "discardLogoutPrompt": "アカウントのキャンセル申請を提出しており、[时间]にキャンセルされる予定です。キャンセルが成功する前に、アカウントを復元する必要がある場合は、「キャンセルのキャンセル」を選択して再度ログインすると、申請がキャンセルされます。キャンセルをキャンセルする必要がなければ、「他のアカウントでログイン」をクリックしてください。", "focusonSuccess": "フォローに成功しました～", "unfollowed": "フォローをキャンセルしました", "fansAmount": "xxx のフォロワー", "rolesAmount": "xxx のキャラクター", "emailFormatError": "メールアドレスの形式が正しくありません", "inconsistentAccounts": "確認されたアカウントが現在のログインアカウントではありません", "inviteCode": "コード", "inviteAmount": "合計xx名を招待しました", "termTips": "上記の規約をよくお読みになり、キャンセルするとアカウントを復元することはできませんので、慎重に選択してください。", "invitationLink": "リンクを招待します", "day": "日", "encounteredAlready": "Ugenieと出会ってX日", "seeMore": "もっと見る", "upgradePlanBenefits": "サブスクリプションプラン", "activateNow": "アップグレード", "logoutInfo": "メンバーショップ", "orderHistory": "まだ会員登録していません。", "noMembershipActivated": "会員登録でVIP特典をゲット", "activateVIPBenefits": "すぐに購入", "purchaseNow": "メンバーセンター", "superMembershipOffer": "スーパーメンバーチャージで即返金", "dailyGiftForSuperMembers": "スーパーメンバーの毎日の贈り物", "chooseCombo": "メンバーシッププランを選択してください", "NonMember": "現在、いかなるメンバーシップもアクティブではありません", "subscribe": "サブスクリプション", "weekSubscriptionDesc": "", "monthSubscriptionDesc": "", "maturity": "", "membershipCenter": "注文キャンセルの確認", "orderCancellationConfirm": "注意：注文キャンセルするには、使用された割引（例えば初回注文割引、クーポン）の未返金します。もしすでに支払いを済ませていれば、注文をキャンセルしないでください。そうしないと、注文処理が失敗する可能性があり、損失を被るかもしれません。", "cancellationNotice": "今回注文キャンセルで返金される割引：", "cancellationRefundDetails": "**メンバーの初回注文割引", "ensureNoPaymentBeforeCancel": "キャンセルする前にまだ支払いしていないことを確認してください！", "cancelUnpaidOrder": "支払いがまだ済ませていない場合は、注文をすぐにキャンセルしてください", "orderRecords": "註文記録", "orderType": "注文タイプ", "creationTime": "作成時間", "productName": "商品名", "paymentMethod": "支払い方法", "actualPayment": "実質支払い", "processingOrder": "注文処理中", "closedOrder": "クローズ", "cancelOrder": "注文キャンセル", "changeTime": "変更時間", "records": "記録", "quantity": "数量", "awaitingOrderCompletion": "注文完了を待つ", "orderNumber": "注文番号", "paymentProcessing": "支払い中", "watiForPayment": "支払いチャネルの原因により、支払い処理には5〜10分の時間がかかる可能性があります。ご迷惑をおかけしますが、しばらくお待ちください。", "noMoreOrder": "記録なし", "rechargeSuccessful": "チャージに成功しました", "cashRegister": "レジカウンター", "selectPaymentMethod": "お支払い方法を選択してください", "selectPaymentWay": "お支払い方法を選択", "paySuccess": "支払いが成功しました", "payAmount": "実際に支払った金額", "selectPayCountry": "支払い国を選択してください", "immediatePayment": "今すぐお支払いください", "orderCompleted": "注文完了", "payedOrder": "支払い済み", "refundOrder": "返金済み", "canceledOrder": "キャンセル済み", "purchaseCancelled": "購入がキャンセルされました", "buyFail": "購入が失敗しました", "taskForFreeGold": "ミッションからもっと無料ゴールドをゲット", "task": "ミッション", "dailyTasks": "日常ミッション", "specialTasks": "スペシャルミッション", "checkIn": "チェックイン", "rewardClaimed": "受取済み", "goComplete": "完成に行く", "claimReward": "受取", "notAchieved": "未達成", "inviteFriends": "フレンドを招待", "currentInvitedCount": "現在招待済み：1/10", "taskAcquisition": "タスクの取得", "weeklyTasks": "毎週の任務", "noAds": "今すぐ広告なし", "from": "から", "unlockVipPower": "{vipNum} つのVIP機能にアクセス", "subscribeNow": "今すぐ購読", "waitAction": "アクション待ち...", "moreCallTime": "より多くの時間", "vipTips": "購読をクリックすることで、利用規約に同意したことになります。料金が請求され、購読は同じ価格と期間で自動更新されます。Play Storeでいつでも購読をキャンセルできます。これは期間限定の特別価格です。特別価格期間終了の30日前に通知いたします。既存の購入者は引き続き特別価格での更新をお楽しみいただけます。", "onlineTips": "フル体験をアンロック", "onlineDesc": "Ugenieをダウンロードしてもっと楽しもう、広告なしでプレイするチャンスも！", "onlineJump": "今すぐ行く", "community": "コミュニティ", "communityCrystal": "クリスタルを送る", "globalSoundSwitch": "グローバル音量スイッチ", "bgmSwitch": "BGMスイッチ", "buyCrystal": "クリスタルを購入する", "loadAdFail": "広告の読み込みに失敗しました。後で再試行してください。", "selectYourGender": "性別を選択", "other": "その他", "wayYouLike": "お好みのインタラクション方式", "selectYourPreference": "好みを選択", "preference1": "雑談", "preference2": "短いテキスト、選択肢なし", "preference3": "ストーリー", "preference4": "長いテキスト、ナレーション付き", "preference5": "ゲーム", "preference6": "ルール、統計あり", "intimacyUpgrade": "親密度レベルがLV{level}にアップグレードされました", "contentGameUnlock": "LV{level}専用のゲームプレイがアンロックされました"}