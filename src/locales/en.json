{"welcomeToEros": "Welcome to UGenie!", "termsConfirmation": "Please confirm the following terms before using this site.", "ageConfirmation": "I am 18 years old or above.", "aiContentNotice": "I already know that the AI-related content on this site is fictional.", "guestLogin": "Guest Login", "registrationLogin": "Register/Login", "emailLogin": "<PERSON><PERSON>", "email": "Email", "getCaptcha": "Obtain", "emailLoginOption": "<PERSON><PERSON>", "agreeToTerms": "Agreeing to the Privacy Policy and User Agreement upon logging in", "loginIssueContact": "Can`t log in? Contact Customer Service", "enterEmail": "Please enter your email", "enterCaptcha": "Please enter the verification code", "invalidEmailFormat": "The email format is incorrect, please re-enter", "captchaError": "The verification code is incorrect, please re-enter", "captchaExpired": "The verification code has expired, please re-enter", "accountSuspendedNotice": "Your account has been suspended, for more details, you can check the Help Center for reasons or contact customer service", "logout": "Log Out", "logoutOption": "Confirm <PERSON>ut", "confirmLogout": "After logging out, you will not be able to converse with the AI Entity, but you can still log in to this account.", "readAndConfirmed": "I have read and confirmed", "privacyAgreement": "Privacy Policy", "userAgreement": "Service Terms", "unableLogin": "Can`t log in?", "contactCustomer": "Contact Customer Service", "loginAndAgree": "Sign in to agree", "welcomeToUgenie": "Welcome to UGenie", "CheckTerms": "Please check the terms", "otherLoginWay": "Log in with another account", "loadingFailed": "SDK loading failed", "invalidSession": "Invalid session, please log in again", "verificationCode": "Verification Code", "store": "Store", "searchAgentName": "Search for characters", "unableConnect": "Unable to connect to store", "searchHistory": "Search history", "popularRoles": "Popular characters", "noResultsFeedback": "Can`t find what you want? Click feedback", "emptySearchSuggestion": "It`s empty here, try another keyword?", "creatorSelection": "Creator", "chatNow": "Cha<PERSON>", "followedNotice": "Followed", "characterOption": "Character", "search": "Search", "searchWantTips": "Can`t find what you want?", "tryUsingDifferentKeyword": "Try again with another keyword", "language": "Language", "helpFAQ": "Help FAQ", "feedbackOption": "About Us", "selectNumber": "Choose your number", "aiFilter": "Intelligent entity screening", "sortOptions": "Sorting method", "aiType": "Type of intelligent entity", "mostPopular": "Hottest", "latest": "Newest", "trending": "Popular", "searchRoleOrCreator": "Search for characters or creators", "dynamicInteraction": "Experience dynamic interaction with characters, making communication more vivid and interesting", "chatBasedOnPersona": "Characters will chat with you based on your character setup, choose your character setup card to make her/him understand you better", "userCharacterOption": "My character setup", "roleSpeakingDuration": "Only character speech consumes this duration", "repeatedClickNotice": "You have repeatedly clicked, please try again later", "exitImmersiveMode": "Exit immersion mode", "myCreations": "My Creations", "loginToView": "Log in to view", "createLover": "Create your own lover", "goCreate": "Go to Create", "clickToSet": "Click to Set", "retainCharacterNotice": "Do you want to keep your character?", "dataClearWarning": "All character data will be completely cleared, please proceed with caution", "clickToTouchSpace": "Enter to Touch Space", "imageOption": "Appearance", "noAIsNotice": "No AI entity yet, click the + below to summon an AI entity~", "myImageOption": "My Appearance", "editImage": "Edit Appearance", "defaultVoicePlayback": "De<PERSON><PERSON> Voice Playback", "captchaInvalidRetry": "Enable NSFW", "cost": "Cost", "noHaveAi": "The other party has no AI", "moreCharacterTime": "More character speaking time", "noMoreImage": "No more character settings for now", "editFigure": "Edit character settings", "aiOnTheWay": "The AI is still on its way～", "searchWant": "Search for what you want...", "confirmDelFigure": "Are you sure you want to delete this avatar?", "personalDescriptionTips": "Your character will be based on your personal description and your chats, you can input your likes, dislikes, and other information～", "confirmExitStory": "Are you sure you want to exit the current story?", "interactWithHer": "Interact with her", "newerStoryName": "Rebirth", "flowerList": "Flowers", "popularityChart": "Popularity", "inputFeedback": "Please enter your feedback", "thankFeedback": "Thank you for your feedback", "fillInFeedback": "Fill out feedback", "storyIntroduction": "Introduction", "missionObjectives": "Goal", "missionRewards": "<PERSON><PERSON>", "leaderboard": "Leaderboard", "ugPhoneToLivco": "New Year, New Friends.", "skipOption": "<PERSON><PERSON>", "genderInterest": "Gender you like", "interests": "Interest", "allOptions": "All", "interestPreferences": "Interest Preferences", "confirmSelection": "Confirm", "cancelButton": "Cancel", "confirmButton": "Done", "editOption": "Edit", "saveToLocal": "Save to local", "addOption": "Add", "saveButton": "Save", "emptySpaceNotice": "It`s Empty Here", "nextStep": "Next Step", "closeButton": "Close", "uncompleted": "Incomplete", "completed": "Completed", "return": "Back", "contactSupport": "If you have any issues, please contact customer service", "maxAmount": "Max", "purchaseOption": "Purchase", "expand": "Expand", "collapse": "Collapse", "none": "None", "deleting": "Deleting", "deleteSuccessful": "Deletion successful", "download": "Download", "owned": "Owned", "notOwned": "Not owned", "noData": "No data available", "nothing": "Nothing", "haveAnyQuestions": "If you have any questions, please", "minutes": "Minutes", "exit": "Exit", "micPermissionRequired": "UGenie needs your permission to use the microphone properly", "loading": "Loading", "clickFeedback": "Get Support", "verify": "Verify", "successfullySaved": "Saved", "uploadPictures": "Uploading picture", "setup": "Settings", "noMore": "No more", "noMoreContent": "No more content", "permissionRequire": "Permission application", "pullToRefresh": "Pull down to refresh", "looseToRefresh": "Release to refresh", "pressAgain": "Press again to exit the app", "report": "Report", "generateSuccessfully": "Generated successfully", "generateStoryBook": "Generate", "copied": "<PERSON>pied", "send": "Give", "saveSuccessfully": "Save successfully", "saveFailed": "Failed to save", "featureCoding": "Feature coming soon", "getSuccessfully": "Get successful", "feedback1": "Content error or incomplete response", "feedback2": "Sensitive or pornographic content", "feedback3": "Information involving violence or hatred", "feedback4": "Inappropriate information involving minors", "feedback5": "Content is disturbing", "inputFeedbackMessage": "Please enter the problem you encountered", "submit": "Submit", "feedbackSuccess": "Submission successful, thank you for your feedback", "discoverUpdate": "New version found!", "updateNow": "Update now", "updateLater": "Do not update yet", "checkForUpdates": "Check for Updates", "currentVersion": "Current Version", "latestVersion": "Currently the latest version", "swipeToClear": "Swipe left to enter 3D immersion mode", "triggerActions": "Click on special areas to trigger actions!", "aiContentDisclaimer": "Everything AI says is fictional, please carefully discern", "changeTopicSuggestion": "Please change the topic", "makeCall": "Make a call", "startNewChat": "<PERSON><PERSON>", "startTyping": "Start typing", "resetCharacterNotice": "After the restart, your chat history with the character will be cleared.", "newChatStartedNotice": "A new conversation has started, new messages are not affected above", "emptyContentVoicePlayback": "You have started a new topic", "recognitionFailedRetry": "Content is empty, cannot play voice", "unclearAudioRepeat": "Recognition failed, please retry", "voiceNotHeardNotice": "I didn`t catch that, can you say it again?", "continueConversation": "The character cannot hear your voice and will automatically end the call after the countdown", "exitOption": "Continue the conversation", "clearAudioEnvironment": "Please try to keep a clean acoustic environment during the call", "purchaseCallDuration": "Deposit and get more character speaking time", "hangUp": "Hang up", "microphoneOn": "Microphone on", "showKeyboard": "Pull up the keyboard", "remainingDuration": "Call time remains", "exchangeDuration": "Get more call time", "callDuration": "Call duration", "unlockSelfie": "Unlock selfie", "setAsChatBackground": "Set as chat background", "previousChats": "Chatted", "recordingPermissionError": "Unable to access recording permissions, please check settings", "recordingFailed": "Unable to record", "speechTooShort": "Speaking time is too short", "recordingError": "Recording failed", "restarting": "Restarting", "restartSuccessful": "Restart successful", "likeCancelled": "Unliked", "likeSuccessful": "Liked", "dislikeCancelled": "Disliked", "dislikeSuccessful": "Dislike successful", "conversationProcessing": "The agent is typing...", "voiceTooShort": "Voice time is too short", "exitClearMode": "Exit clean screen mode", "like": "Like", "dislike": "Dislike", "copy": "Copy", "releaseToCancelSend": "Release to cancel send", "releaseToSendSwipeUpCancel": "Release to send, slide up to cancel", "holdToSpeak": "Hold to talk", "inUse": "In use", "levelLocked": "Unlocked levels", "levelUnlocked": "Locked levels", "modelLoading": "Model loading", "waitToSend": "Please wait for the other party to finish speaking before sending", "connecting": "Connecting", "microphoneOff": "Microphone off", "sendingGift": "Gifting", "giftSent": "Gifting successful", "settingUp": "Replacing Background", "setupSuccessful": "Setting successful", "picture": "Picture", "message": "Message", "intimacyUnlockFunc": "Increase intimacy with the Agent to unlock", "syncPhoneCallRecord": "Voice call records have been synchronized.", "savePicture": "Save Image", "scanQRCode": "<PERSON>an the QR code to play again", "refuseGenerate": "No, thanks", "rewardCongratulation": "Story completed, congratulations on receiving", "generateCard": "Collect the card", "backToChat": "Back to chat", "playAgain": "Play again", "myStoryBook": "My storybook", "moreStories": "More stories", "recheckStoryRecord": "You can view it again in the section of the Dialogue Page Toolbar", "storyBookGenerateConfirm": "Would you like to create a storybook?", "storyBookGenerateTips": "Replaying will erase the current story record. Would you like to create a storybook to save this record?", "restartStory": "Restarting will erase your current story progress. Are you sure to restart?", "moreIntimacyToUnlock": "Increase intimacy to unlock more story content", "story": "Story", "freeFirst": "First free", "goToChat": "Go to chat", "syncGameMemory": "Lv{level} Exclusive Gameplay Memory has been synchronized.", "clickToStory": "Click the card to unlock the exclusive story", "upgradeToUnlockStory": "Increase intimacy with the Agent to unlock", "startNow": "Start now", "unableToSend": "Send invalid", "freeInput": "Free input...", "selectOne": "Please select one", "shareContent": "Share Agent to your friend!", "gameWin": "Game victory", "gameLose": "Game failure", "doubleRewards": "Double Rewards", "unlockByAds": "Unlock by watching ads", "rewardTip": "Congratulations on receiving the award", "unSupportRegion": "This feature is not supported in your region", "giftItems": "Gifts", "membershipBenefits": "Become a member to get more crystals", "totalCrystalUsed": "Accumulated crystal consumption", "giftReceivedNotice": "Claimed yours", "sendGift": "Give a gift", "itemPurchase": "Purchase items", "unlockedFeatures": "Locked", "purchaseOutfit": "Purchase outfit", "unlock3DOutfit": "Unlock 3D outfit", "outfits": "Outfit", "staticOutfit": "Static Outfit", "unlock3DOption": "Unlock 3D", "unlockOutfitCost": "Unlocking an outfit consumes crystals", "itemStore": "Item Mall", "diamond": "diamond", "crystalChangeDetails": "Crystal Change Details", "goldChangeDetails": "Gold Coin Change Details", "purchaseSkin": "Buy Skin", "myCrystals": "My Crystals", "crystalDetails": "Crystal Details", "selectPurchaseItem": "Please select a product", "goldExchange": "Coin Exchange", "exchangeOption": "Redeem", "backpack": "Backpack", "noMoreCrystalDetails": "No crystal change details", "noMoreGoldDetails": "No gold coin change details", "goldDetails": "Gold coin details", "moreFreeCoins": "More free coins", "weeklyPurchaseLimit": "Purchase limit reached for this week", "dailyPurchaseLimit": "Purchase limit reached for today", "purchaseSuccessful": "Purchase successful", "totalCoinsSpent": "Accumulated consumption of gold coins", "free": "Free", "rechargeToUnlock": "Obtain by recharge", "remainingDiamonds": "Total Crystals", "exchangeCoins": "Exchange gold coins", "redeemNow": "Exchange now", "consumption": "Consumption", "gold": "Gold coins", "crystal": "Crystals", "insufficientCrystals": "Insufficient crystals", "exchangeSuccessful": "Exchange successful", "unableFindProduct": "Product does not exist", "adsToCrystal": "Watch the ad to get 50 crystals", "insufficientBalance": "Insufficient balance, please deposit", "currentLevel": "Current level", "increaseIntimacy": "Increase intimacy to unlock more ways to play", "intimacyLevelUp": "Intimacy level up", "giftBoostsIntimacy": "Giving gifts can increase intimacy~", "emptyDiscover": "It`s empty here, go to the Discovery page to check it out", "goDiscover": "Go to Discover", "msgDetail": "Message details", "noMoreMsg": "No more messages", "followedYour": "Followed your", "followedYouNotice": "Followed you", "messageNotification": "Notification", "systemMessages": "System Messages", "interactiveMessages": "Interactive Messages", "reportFeedback": "Report Feedback", "deleteRole": "Delete Character", "aboutRole": "About TA", "photoAlbum": "Album", "introduction": "Introduction", "openingLine": "Opening Remarks", "startChat": "Start Conversation", "improveAffection": "Improve favorability to unlock exclusive selfies", "receiveSelfiesHere": "Received selfies will be saved here", "selfieUnlocked": "Unlocked selfies", "getSelfie": "Get a Selfie", "purchaseCallTime": "Buy Talk Time", "purchaseGift": "Buy Gift", "confirmDeleteCharacter": "Are you sure you want to delete the character?", "agentProfile": "Profile", "cancelCreate": "Cancel creation", "characterSetting": "Character Design", "creationMethod": "Creation Method", "quickCreate": "Quick Create", "selectGender": "Choose Character Gender", "maleOption": "Male", "femaleOption": "Female", "nonBinaryOption": "Non-Binary", "setPublic": "Set to Public", "publicSettingWarning": "If set to public, it cannot be made private", "personality": "Personality", "createAndStartChat": "Create and Start Chatting", "advancedCreation": "Advanced Creation", "customizeImage": "Custom Imagery", "freeImage": "Free Imagery", "enterNickname": "Enter Nickname", "completeBackground": "Complete the Backstory", "createCharacter": "Create Character", "creatingCharacter": "Creating", "regenerateOption": "Regenerate", "cropAvatar": "Crop Avatar", "continueCurrentSetting": "Continue with Current Settings?", "modifySettingNotice": "You can modify the settings and create a new imagery, but it will consume extra crystals", "savedInDrafts": "Generated images will be saved in the draft box", "exitAICharacterCreation": "Exit AI Entity Creation", "draftsFolder": "Draft Box", "useImage": "Use Imagery", "voiceOption": "Voice", "tag": "tag", "presetSoundLibrary": "Preset Voice Library", "characterSettingRequired": "Character Setup (required)", "openingLineRequired": "Opening Line (required)", "backgroundStoryRequired": "Backstory (required)", "characterIntroRequired": "Character Introduction (required)", "dialogueExampleOptional": "Dialogue Example (optional)", "tagRequired": "Label (required)", "clickToAddUserContent": "Click to Add User Dialogue Content", "clickToAddAIResponses": "Click to Add AI Response Content", "customizeImageName": "Detailed Configuration of Customized Image Name", "createCustomImage": "Create Custom Avatar", "createAI": "Create AI Entity", "openingLineTips": "Please enter an opening remark", "choiceFigureTips": "Reselecting the avatar will not retain current settings", "selectFigureConfirm": "Reselect avatar?", "createAgain": "Recreate", "inputContentTips": "Please enter content (up to 300 characters)", "inputEmptyTips": "Input cannot be empty!", "sexChoice": "Gender selection", "audioEmpty": "No audio available", "template": "Template", "selectRequest": "Please select", "agentTemplateTips": "Please select an AI template", "abandonFigureConfirm": "Regenerating the avatar will cost crystals again, do you want to abandon the current avatar image?", "agentInfoTips": "Please complete the AI information", "backgroundStoryTips": "Please enter a background story", "characterIntroTips": "Please enter a brief introduction", "dialogueExample": "Dialogue examples", "agentFeatureTips": "Please complete the AI characteristics", "diyFigureSave": "Custom created avatars will be saved here", "cancelCreateTips": "Canceling creation will not retain your current settings", "cancelCreateConfirm": "Do you want to cancel the current creation?", "createAgentSuccess": "AI created successfully", "characterSetTips": "Please enter character settings", "tagEmptyTips": "Please select the label", "fansOption": "Fans", "likesOption": "<PERSON>s", "followOption": "Follow", "nicknameOption": "Nickname", "genderOption": "Gender", "birthdaySelection": "Birthday", "personalDescription": "Personal Description", "youRoleChatPersonalDesc": "Your character will chat with you based on your personal description, you can enter your preferences, things you hate, and other information~", "selectBirthday": "Choose <PERSON>", "unfollowOption": "Unfollow", "basicInfo": "My Profile", "enterInviteCode": "Please Enter Redeem Code", "invalidInviteCode": "Redeem Code Does Not Exist", "rewardAlreadyClaimed": "Reward Already Claimed", "accountDeactivation": "Delete Account", "deactivationTerms": "Cancellation Terms", "deactivationTermsContent1": "1.The account to be canceled is the currently logged-in account.", "deactivationTermsContent2": "2.Once canceled successfully, you will not be able to log in, reactivate, use, or recover this account again.", "deactivationTermsContent3": "3.If there are any unused memberships, coins, or diamonds in the account, they will be cleared upon successful cancellation.Please proceed with caution.", "deactivationTermsContent4": "4.After successful cancellation, your personal information and other data will be permanently deleted and cannot be recovered.", "deactivationTermsContent5": "5.After applying for cancellation, the account will be reserved for you for 15 days. If you log in during this period, the cancellation will be automatically canceled. If the cancellation is not revoked within 15 days, the account will be automatically canceled.", "termsAgreement": "I have read and agree to the terms above", "accountVerification": "Account Verification", "otherVerificationOptions": "Other Verification Methods", "deactivationConfirmation": "Please read carefully and confirm again", "deactivationNotice1": "1. Once canceled successfully, your personal information and other data will be permanently deleted and cannot be recovered.", "deactivationNotice2": "2. After applying for cancellation, the account will be reserved for you for 15 days. If you log in during this period, the cancellation will be automatically canceled. If the cancellation is not revoked within 15 days, the account will be automatically canceled.", "deactivationSuccessful": "Account Cancellation Application Successful", "verificationSuccess": "Verification Successful", "networkErrorRetry": "Network error, please try again later.", "accountVerificationFailed": "Account Verification Failed, <PERSON><PERSON> Account Invalid", "invalidEmailVerification": "The verification code is invalid, please try again later.", "profile": "Personal profile", "choosePreference": "Choose your preference", "recommend": "Recommended", "abandonAccountCancellation": "Give up account cancellation", "abandonCancellation": "Give up cancellation", "discardLogoutPrompt": "Your account has submitted a cancellation request and will be successfully canceled at [时间]. Before the cancellation is successful, if you need to restore the account, please select <PERSON><PERSON> Cancellation to log in to the account again, and your request will be canceled. If you do not need to cancel the cancellation, you can click Log in with another account.", "focusonSuccess": "Followed successfully~", "unfollowed": "Unfollowed", "fansAmount": "xxx followers", "rolesAmount": "xxx characters", "emailFormatError": "Incorrect email format", "inconsistentAccounts": "The verified account is not the currently logged-in account", "inviteCode": "Redeem Code", "inviteAmount": "Have invited xx people in total", "termTips": "Please read the above terms carefully. Account cannot be restored after cancellation. Please choose carefully.", "invitationLink": "Invite link", "day": "days", "encounteredAlready": "Met UGenie for X days", "seeMore": "View more", "upgradePlanBenefits": "Subscription Plan", "activateNow": "Upgrade", "logoutInfo": "Member Shop", "orderHistory": "Not a Member Yet", "noMembershipActivated": "Enjoy VIP Benefits upon Registration", "activateVIPBenefits": "Buy Now", "purchaseNow": "Member Center", "superMembershipOffer": "Recharge Super Membership and Get Instant Rewards", "dailyGiftForSuperMembers": "Daily Gift for Super Members", "chooseCombo": "Please select a membership plan", "NonMember": "No membership is active at the moment", "subscribe": "Subscribe", "weekSubscriptionDesc": "Cancel anytime. Renew next week at $/week", "monthSubscriptionDesc": "Cancel anytime. Renew next month at $/month", "maturity": "expire", "membershipCenter": "Confirm Order Cancellation", "orderCancellationConfirm": "Notice: Canceling orders only returns used discounts (e.g., first-order discount, vouchers). If you have already paid, do not cancel the order, otherwise it may cause order processing failure and losses to you", "cancellationNotice": "The following discounts can be refunded for this order cancellation", "cancellationRefundDetails": "Member First-Order Discount", "ensureNoPaymentBeforeCancel": "Please confirm you have not paid before canceling!", "cancelUnpaidOrder": "I haven`t paid, cancel the order immediately", "orderRecords": "Order History", "orderType": "Order Type", "creationTime": "Creation Time", "productName": "Product Name", "paymentMethod": "Payment Method", "actualPayment": "Amount <PERSON>", "processingOrder": "Processing Order", "closedOrder": "Closed", "cancelOrder": "Cancel Order", "changeTime": "Change Time", "records": "Record", "quantity": "Quantity", "awaitingOrderCompletion": "Wait for Order Completion", "orderNumber": "Order Number", "paymentProcessing": "Processing Payment", "watiForPayment": "Due to payment channel issues, the payment may take 5-10 minutes to process. Please be patient.", "noMoreOrder": "No Records", "rechargeSuccessful": "Recharge successful", "cashRegister": "Cashier", "selectPaymentMethod": "Please select payment method", "selectPaymentWay": "Select payment method", "paySuccess": "Payment successful", "payAmount": "Actual amount paid", "selectPayCountry": "Select payment country", "immediatePayment": "Pay now", "orderCompleted": "Order completed", "payedOrder": "Order paid", "refundOrder": "Order refunded", "canceledOrder": "Order canceled", "purchaseCancelled": "Purchase canceled", "buyFail": "Purchase failed", "taskForFreeGold": "Complete tasks to earn more free coins", "task": "Tasks", "dailyTasks": "Daily Tasks", "specialTasks": "Special Tasks", "checkIn": "Check-in", "rewardClaimed": "Claimed", "goComplete": "Go Complete", "claimReward": "<PERSON><PERSON><PERSON>", "notAchieved": "Not Achieved", "inviteFriends": "Invite Friends", "currentInvitedCount": "Currently Invited", "taskAcquisition": "Task to obtain", "weeklyTasks": "Weekly Tasks", "noAds": "Go Ad-Free Now", "from": "from", "unlockVipPower": "Access {vipNum} VIP Features", "subscribeNow": "Subscribe Now", "waitAction": "Waiting for action...", "moreCallTime": "More Duration", "vipTips": "By clicking subscribe, you agree to the terms of service. You will be charged, and the subscription will automatically renew at the same price and duration. You can cancel your subscription anytime in the Play Store. This is a limited-time promotional price. We will notify you 30 days before the promotional period ends. Existing subscribers can continue to enjoy renewal at the promotional price.", "onlineTips": "Unlock Full Experience", "onlineDesc": "Download UGenie for more fun with a chance to play ad-free!", "onlineJump": "Go Now", "community": "Community", "communityCrystal": "Send Crystals", "globalSoundSwitch": "Global Volume Switch", "bgmSwitch": "Background Music Switch", "buyCrystal": "Buy Crystals", "loadAdFail": "Ad failed to load. Please try again later.", "selectYourGender": "Choose Your Gender", "other": "Other", "wayYouLike": "Your Preferred Interaction Style", "selectYourPreference": "Select Your Preference", "preference1": "Cha<PERSON>", "preference2": "Short text, No options", "preference3": "Story", "preference4": "Long text, Narration", "preference5": "Game", "preference6": "Rules, Stats", "intimacyUpgrade": "Intimacy level upgraded to LV{level}", "contentGameUnlock": "LV{level} exclusive gameplay has been unlocked"}