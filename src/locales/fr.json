{"welcomeToEros": "Bienvenue chez UGenie!", "termsConfirmation": "Veuillez confirmer les conditions suivantes avant d'utiliser ce site.", "ageConfirmation": "J'ai 18 ans ou plus.", "aiContentNotice": "Je reconnais que le contenu lié à l'IA sur ce site est fictif.", "guestLogin": "Connexion en tant qu'invité", "registrationLogin": "S'inscrire/Se connecter", "emailLogin": "Connexion email", "email": "E-mail", "getCaptcha": "<PERSON><PERSON><PERSON><PERSON>", "emailLoginOption": "Connexion", "agreeToTerms": "En vous connectant, vous acceptez la politique de confidentialité et l'accord utilisateur.", "loginIssueContact": "Connectez-vous avec le service client si vous ne pouvez pas vous connecter.", "enterEmail": "Veuillez entrer votre adresse e-mail.", "enterCaptcha": "Veuillez entrer le code de vérification.", "invalidEmailFormat": "L'adresse e-mail est mal formatée, veuillez la ressaisir.", "captchaError": "Le code de vérification est incorrect, veuillez le ressaisir.", "captchaExpired": "Le code de vérification a expiré, veuillez le ressaisir.", "accountSuspendedNotice": "Votre compte a été suspendu, pour plus de détails, vous pouvez consulter le Centre d'aide pour les raisons possibles ou contacter le service client.", "logout": "Se déconnecter", "logoutOption": "Confirmez la déconnexion", "confirmLogout": "Une fois déconnecté, vous ne pourrez plus discuter avec l'IA, mais vous pouvez toujours vous connecter avec ce compte", "readAndConfirmed": "J'ai lu et confirmé", "privacyAgreement": "Politique de confidentialité", "userAgreement": "Conditions d'Utilisation", "unableLogin": "Connexion impossible ?", "contactCustomer": "Contacter le service client", "loginAndAgree": "Connectez-vous pour accepter", "welcomeToUgenie": "Bienvenue chez UGenie", "CheckTerms": "Veuillez cocher les conditions", "otherLoginWay": "Se connecter avec un autre compte", "loadingFailed": "Le chargement du SDK a échoué", "invalidSession": "Session invalide, connectez-vous récemment", "verificationCode": "Code de vérification", "store": "Ma<PERSON><PERSON>", "searchAgentName": "Rechercher des personnages", "unableConnect": "Il n'est pas possible de se connecter à la pièce", "searchHistory": "Historique de recherche", "popularRoles": "Personnages populaires", "noResultsFeedback": "Cliquez sur le feedback si vous ne trouvez pas ce que vous voulez", "emptySearchSuggestion": "Il est vide ici, essayez un autre mot-clé ?", "creatorSelection": "<PERSON><PERSON><PERSON><PERSON>", "chatNow": "Discussion", "followedNotice": "<PERSON><PERSON><PERSON><PERSON>", "characterOption": "Personnage", "search": "Recherche", "searchWantTips": "Vous ne trouvez pas ce que vous voulez ?", "tryUsingDifferentKeyword": "<PERSON>z le mot-clé et réessayez", "language": "<PERSON><PERSON>", "helpFAQ": "Aide FAQ", "feedbackOption": "À propos", "selectNumber": "Choisissez votre numéro", "aiFilter": "Sélection intelligente d'entités", "sortOptions": "Mode de tri", "aiType": "Type d'entité intelligente", "mostPopular": "<PERSON><PERSON>", "latest": "<PERSON><PERSON><PERSON>", "trending": "Populaire", "searchRoleOrCreator": "Chercher des personnages ou des créateurs", "dynamicInteraction": "Expérimentez des interactions dynamiques avec les personnages, rendre la communication plus vivante et intéressante", "chatBasedOnPersona": "Les personnages bavarderont avec vous en fonction de votre configuration de personnage. Choisissez votre carte de configuration de personnage pour qu'elle/il vous comprenne mieux", "userCharacterOption": "Mon setup de personnage", "roleSpeakingDuration": "Seul le discours des personnages consomme cette durée", "repeatedClickNotice": "Vous avez cliqué plusieurs fois, ve<PERSON><PERSON><PERSON> réessayer plus tard", "exitImmersiveMode": "<PERSON><PERSON><PERSON> le mode immersif", "myCreations": "Mes créations", "loginToView": "Connectez-vous pour voir", "createLover": "<PERSON><PERSON><PERSON> votre propre amour", "goCreate": "<PERSON><PERSON>", "clickToSet": "Cliquez pour configurer", "retainCharacterNotice": "Voulez-vous conserver votre personnage?", "dataClearWarning": "Toutes les données du personnage seront complètement supprimées, s'il vous plaît, faites attention", "clickToTouchSpace": "Entrez dans l'espace tactile", "imageOption": "Apparence", "noAIsNotice": "Pas encore d'entité IA, cliquez sur le + ci-dessous pour invoquer une entité IA~", "myImageOption": "Mon apparence", "editImage": "Modifier l'apparence", "defaultVoicePlayback": "Lecture vocale par défaut", "captchaInvalidRetry": "<PERSON><PERSON> le NSFW", "cost": "Ausgeben", "noHaveAi": "L'autre partie n'a pas d'IA", "moreCharacterTime": "Plus de temps de parole pour les personnages", "noMoreImage": "Aucune autre configuration de personnage pour le moment", "editFigure": "Modifier la configuration du personnage", "aiOnTheWay": "L'IA est toujours en route～", "searchWant": "Recherchez ce que vous voulez...", "confirmDelFigure": "Êtes-vous sûr de vouloir supprimer cet avatar ?", "personalDescriptionTips": "Votre personnage sera basé sur votre description personnelle et vos discussions, vous pouvez entrer vos préférences, choses que vous n'aimez pas et autres informations～", "confirmExitStory": "Êtes-vous sûr de vouloir quitter l'histoire en cours ?", "interactWithHer": "Interagir avec elle", "newerStoryName": "Renaissance", "flowerList": "<PERSON><PERSON>", "popularityChart": "Popularité", "inputFeedback": "Veuillez entrer vos commentaires", "thankFeedback": "Merci pour vos commentaires", "fillInFeedback": "Remplir les commentaires", "storyIntroduction": "Introduction", "missionObjectives": "Objectif", "missionRewards": "Récompense", "leaderboard": "Classement", "ugPhoneToLivco": "", "skipOption": "Passer", "genderInterest": "<PERSON>e de votre in<PERSON>r<PERSON>", "interests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allOptions": "<PERSON>ut", "interestPreferences": "Préférences d'intérêt", "confirmSelection": "Confirmer", "cancelButton": "Annuler", "confirmButton": "<PERSON><PERSON><PERSON><PERSON>", "editOption": "Modifier", "saveToLocal": "Enregistrer localement", "addOption": "Ajouter", "saveButton": "<PERSON><PERSON><PERSON><PERSON>", "emptySpaceNotice": "Il est vide ici", "nextStep": "Prochaine étape", "closeButton": "<PERSON><PERSON><PERSON>", "uncompleted": "Incomplet", "completed": "<PERSON><PERSON><PERSON><PERSON>", "return": "Retour", "contactSupport": "Si vous rencontrez des problèmes, veuillez contacter le service client", "maxAmount": "Maximal", "purchaseOption": "<PERSON><PERSON><PERSON>", "expand": "Déplier", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "none": "Aucun", "deleting": "Suppression en cours", "deleteSuccessful": "Suppression réussie", "download": "Télécharger", "owned": "Possédé", "notOwned": "Non possédé", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "nothing": "Rien", "haveAnyQuestions": "Si vous avez des questions, s'il vous plaît", "minutes": "Minutes", "exit": "<PERSON><PERSON><PERSON>", "micPermissionRequired": "UGenie a besoin de votre autorisation pour utiliser le microphone correctement", "loading": "En cours de chargement", "clickFeedback": "Obtenir du soutien", "verify": "Vérifier", "successfullySaved": "Enregistré", "uploadPictures": "En cours d'envoi de l'image", "setup": "Paramètres", "noMore": "Plus rien", "noMoreContent": "Plus aucun contenu", "permissionRequire": "Demande d'autorisation", "pullToRefresh": "Déroulez pour actualiser", "looseToRefresh": "Relâchez pour actualiser", "pressAgain": "Pression nouvelle pour accéder à l'application", "report": "signalement", "generateSuccessfully": "Gén<PERSON><PERSON> avec succès", "generateStoryBook": "<PERSON><PERSON><PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "send": "", "saveSuccessfully": "", "saveFailed": "", "featureCoding": "Nouvelle fonction à venir", "getSuccessfully": "<PERSON><PERSON><PERSON><PERSON> le succès", "feedback1": "E<PERSON>ur de contenu ou réponse incomplète", "feedback2": "Contenu sensible ou pornographique", "feedback3": "Informations impliquant violence ou haine", "feedback4": "Informations inappropriées impliquant des mineurs", "feedback5": "Contenu d<PERSON>", "inputFeedbackMessage": "Veuillez saisir le problème que vous avez rencontré", "submit": "So<PERSON><PERSON><PERSON>", "feedbackSuccess": "Soumission ré<PERSON>ie, merci pour vos commentaires", "discoverUpdate": "Nouvelle version trouvée !", "updateNow": "Mettre à jour maintenant", "updateLater": "Ne pas mettre à jour pour l'instant", "checkForUpdates": "Vérifier les mises à jour", "currentVersion": "Version actuelle", "latestVersion": "Actuellement la dernière version", "swipeToClear": "Faites glisser vers la gauche pour effacer l'écran et entrer en mode d'immersion 3D", "triggerActions": "Cliquez sur les zones spéciales pour déclencher des actions !", "aiContentDisclaimer": "Tout ce que dit l'IA est fictif, veuillez discerner avec soin", "changeTopicSuggestion": "Veuillez changer de sujet", "makeCall": "Faire un appel", "startNewChat": "<PERSON><PERSON><PERSON><PERSON>", "startTyping": "Commencer à taper", "resetCharacterNotice": "Après le redémarrage, votre historique de chat avec le personnage sera effacé.", "newChatStartedNotice": "Un nouveau dialogue a commencé, les nouveaux messages ne sont pas affectés ci-dessus", "emptyContentVoicePlayback": "Vous avez commencé un nouveau sujet", "recognitionFailedRetry": "Le contenu est vide, impossible de lire la voix", "unclearAudioRepeat": "La reconnaissance a échoué, veuil<PERSON>z réessayer", "voiceNotHeardNotice": "Je n'ai pas compris, pouvez-vous le rép<PERSON>ter ?", "continueConversation": "Le personnage ne peut pas vous entendre et l'appel se terminera automatiquement après le compte à rebours", "exitOption": "Continuer la conversation", "clearAudioEnvironment": "Veuillez vous assurer qu'il y ait un environnement acoustique propre pendant l'appel", "purchaseCallDuration": "Recharger pour obtenir plus de temps de parole pour les personnages", "hangUp": "<PERSON><PERSON><PERSON><PERSON>", "microphoneOn": "Microphone allumé", "showKeyboard": "<PERSON><PERSON><PERSON> le clavier", "remainingDuration": "Temps d'appel restant", "exchangeDuration": "Obtenir plus de temps d'appel", "callDuration": "<PERSON><PERSON><PERSON> de l'appel", "unlockSelfie": "Déverrouiller une selfie", "setAsChatBackground": "Définir comme fond de discussion", "previousChats": "<PERSON><PERSON><PERSON>", "recordingPermissionError": "Die Aufnahmegenehmigung kann nicht erhalten werden, bitte überprüfen Sie die Einstellungen", "recordingFailed": "Enregistrement impossible", "speechTooShort": "Le temps de parole est trop court", "recordingError": "L'échec de l'enregistrement", "restarting": "Redémarrage en cours", "restartSuccessful": "Redémarrage réussi", "likeCancelled": "J'aime annulé avec succès", "likeSuccessful": "<PERSON>'aime r<PERSON>i", "dislikeCancelled": "J'aime non réussi", "dislikeSuccessful": "J'aime non réussi", "conversationProcessing": "Dialogue en cours de sortie", "voiceTooShort": "Le temps vocal est trop court", "exitClearMode": "<PERSON><PERSON><PERSON> le mode d'écran propre", "like": "<PERSON>'aime", "dislike": "J'aime non", "copy": "<PERSON><PERSON><PERSON>", "releaseToCancelSend": "<PERSON><PERSON><PERSON><PERSON> pour annuler l'envoi", "releaseToSendSwipeUpCancel": "<PERSON><PERSON><PERSON><PERSON> pour envoyer, glisser vers le haut pour annuler", "holdToSpeak": "Maintenir pour parler", "inUse": "En cours d'utilisation", "levelLocked": "Niveaux non déverrouillés", "levelUnlocked": "Niveaux verrouillés", "modelLoading": "Mod<PERSON>le en cours de chargement", "waitToSend": "Veuillez attendre que l'autre partie ait fini de parler avant d'envoyer", "connecting": "Connexion en cours", "microphoneOff": "Microphone éteint", "sendingGift": "En cours d'offre", "giftSent": "<PERSON><PERSON> ré<PERSON>ie", "settingUp": "En cours de configuration", "setupSuccessful": "Configuration réussie", "picture": "Image", "message": "Message", "intimacyUnlockFunc": "Améliorez votre favorabilité Déverrouiller les fonctions exclusives", "syncPhoneCallRecord": "Les enregistrements d'appels vocaux ont été synchronisés.", "savePicture": "Enregistrer l'image", "scanQRCode": "Scannez le code QR pour rejouer", "refuseGenerate": "Non, merci", "rewardCongratulation": "Histoire terminée, félicitations pour avoir reçu", "generateCard": "Récupérer la carte d'histoire", "backToChat": "Retourner au chat", "playAgain": "Jouer à nouveau", "myStoryBook": "Mon livre d'histoires", "moreStories": "Plus d'histoires", "recheckStoryRecord": "Vous pouvez le revoir dans la section Histoire de la Barre d'outils de la page de dialogue", "storyBookGenerateConfirm": "Souhait<PERSON>-vous créer un livre d'histoires ?", "storyBookGenerateTips": "Rejouer effacera l'enregistrement actuel de l'histoire. Souhaitez-vous créer un livre d'histoires pour enregistrer cet historique ?", "restartStory": "", "moreIntimacyToUnlock": "Augmentez l'intimité pour débloquer plus de contenu d'histoire", "story": "Histoire", "freeFirst": "Première gratuit", "goToChat": "<PERSON>er au chat", "syncGameMemory": "Mémoire de gameplay exclusive Lv{level} synchronisée", "clickToStory": "Cliquez sur la carte pour débloquer l'histoire exclusive", "upgradeToUnlockStory": "Augmentez l'intimité avec l'agent pour débloquer", "startNow": "Commencer maintenant", "unableToSend": "Envoi invalide", "freeInput": "Entrée libre...", "selectOne": "Veuillez en sélectionner un", "shareContent": "Partagez Agent avec votre ami !", "gameWin": "Vic<PERSON> du jeu", "gameLose": "Échec du jeu", "doubleRewards": "Récompenses Doublées", "unlockByAds": "Débloquer en regardant des publicités", "rewardTip": "Félicitations pour avoir reçu le prix", "unSupportRegion": "Cette fonctionnalité n'est pas prise en charge dans votre région", "giftItems": "<PERSON><PERSON>", "membershipBenefits": "Devenez membre pour obtenir plus de cristaux", "totalCrystalUsed": "Consommation cumulée de cristaux", "giftReceivedNotice": "<PERSON><PERSON><PERSON>", "sendGift": "Faire un cadeau", "itemPurchase": "Acheter des accessoires", "unlockedFeatures": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "purchaseOutfit": "Acheter un costume", "unlock3DOutfit": "Déverrouiller un costume 3D", "outfits": "<PERSON><PERSON>", "staticOutfit": "Tenue statique", "unlock3DOption": "Déverrouiller en 3D", "unlockOutfitCost": "Déverrouiller un costume consomme des cristaux", "itemStore": "Magasin d'articles", "diamond": "<PERSON><PERSON><PERSON>", "crystalChangeDetails": "Détails du changement de cristaux", "goldChangeDetails": "<PERSON>é<PERSON> du changement des pièces d'or", "purchaseSkin": "Acheter un skin", "myCrystals": "<PERSON><PERSON>", "crystalDetails": "Kristalldetails", "selectPurchaseItem": "Veuillez sélectionner un produit", "goldExchange": "Goldmünzen tauschen", "exchangeOption": "<PERSON><PERSON><PERSON><PERSON>", "backpack": "Sac à dos", "noMoreCrystalDetails": "Aucune d<PERSON> de modification de cristal", "noMoreGoldDetails": "Aucune dé<PERSON> de modification de pièce d'or", "goldDetails": "<PERSON><PERSON><PERSON> de la pièce d'or", "moreFreeCoins": "Plus de pièces gratuites", "weeklyPurchaseLimit": "Ce produit a atteint la limite d'achat hebdomadaire", "dailyPurchaseLimit": "Ce produit a atteint la limite d'achat quotidienne", "purchaseSuccessful": "<PERSON><PERSON><PERSON>", "totalCoinsSpent": "Consommation累计 de pièces d'or", "free": "Gratuitement", "rechargeToUnlock": "Obtenir par recharge", "remainingDiamonds": "Total de cristaux", "exchangeCoins": "Échanger des pièces d'or", "redeemNow": "<PERSON><PERSON><PERSON> maintenant", "consumption": "Consommation", "gold": "Pièces d'or", "crystal": "<PERSON><PERSON><PERSON>", "insufficientCrystals": "Pas assez de c<PERSON>aux", "exchangeSuccessful": "Rédemption réussie", "unableFindProduct": "Le produit n'existe pas", "adsToCrystal": "Regardez la publicité pour obtenir 50 cristaux", "insufficientBalance": "Solde insuffisant, veuillez recharger", "currentLevel": "Niveau actuel", "increaseIntimacy": "Augmenter l'intimité pour débloquer plus de façons de jouer", "intimacyLevelUp": "Niveau d'intimité", "giftBoostsIntimacy": "<PERSON><PERSON> des cadeaux, peut augmenter l'intimité~", "emptyDiscover": "Il est vide ici, allez voir la page de découverte", "goDiscover": "<PERSON><PERSON>", "msgDetail": "<PERSON><PERSON><PERSON> du message", "noMoreMsg": "Plus aucun message", "followedYour": "Vous a suivi", "followedYouNotice": "Vous a suivi", "messageNotification": "Notification", "systemMessages": "Messages système", "interactiveMessages": "Messages interactifs", "reportFeedback": "Signaler des commentaires", "deleteRole": "Supprimer le personnage", "aboutRole": "À propos de TA", "photoAlbum": "Album", "introduction": "Introduction", "openingLine": "Introduction", "startChat": "Commencer une conversation", "improveAffection": "Améliorez la popularité pour débloquer des selfies exclusifs", "receiveSelfiesHere": "Les selfies reçus seront enregistrés ici", "selfieUnlocked": "Déverrouillé des selfies", "getSelfie": "Obtenez un selfie", "purchaseCallTime": "Acheter du temps de parole", "purchaseGift": "Acheter un cadeau", "confirmDeleteCharacter": "Voulez-vous vraiment supprimer le personnage ?", "agentProfile": "Profil", "cancelCreate": "Annuler la création", "characterSetting": "Conception du personnage", "creationMethod": "Méthode de création", "quickCreate": "Création rapide", "selectGender": "Choisissez le sexe du personnage", "maleOption": "<PERSON><PERSON>", "femaleOption": "<PERSON>mme", "nonBinaryOption": "Non binaire", "setPublic": "Mettre en public", "publicSettingWarning": "S'il est défini sur public, il ne peut pas être rendu privé", "personality": "<PERSON><PERSON><PERSON>", "createAndStartChat": "<PERSON><PERSON><PERSON> et commencer à discuter", "advancedCreation": "Création avancée", "customizeImage": "<PERSON><PERSON>", "freeImage": "<PERSON><PERSON> gratuite", "enterNickname": "Entrez un surnom", "completeBackground": "Complétez l'histoire", "createCharacter": "<PERSON><PERSON><PERSON> un personnage", "creatingCharacter": "En cours de création", "regenerateOption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cropAvatar": "Couper l'avatar", "continueCurrentSetting": "Continuer avec les paramètres actuels?", "modifySettingNotice": "Vous pouvez modifier les paramètres et créer une nouvelle image, mais cela consommera des cristaux supplémentaires", "savedInDrafts": "Les images générées seront enregistrées dans la boîte de rédaction", "exitAICharacterCreation": "Quitter la création de l'entité IA", "draftsFolder": "<PERSON><PERSON><PERSON>", "useImage": "Utiliser l'imagerie", "voiceOption": "Voix", "tag": "Étiquettes", "presetSoundLibrary": "Bibliothèque de voix prédéfinies", "characterSettingRequired": "Paramétrage du personnage (obligatoire)", "openingLineRequired": "Phrase d'ouverture (obligatoire)", "backgroundStoryRequired": "Historique (obligatoire)", "characterIntroRequired": "Présentation du personnage (obligatoire)", "dialogueExampleOptional": "Exemple de dialogue (facultatif)", "tagRequired": "Étiquettes (requises)", "clickToAddUserContent": "Cliquez pour ajouter le contenu du dialogue utilisateur", "clickToAddAIResponses": "Cliquez pour ajouter le contenu de la réponse IA", "customizeImageName": "-", "createCustomImage": "<PERSON><PERSON>er un avatar personnalisé", "createAI": "Créer une entité IA", "openingLineTips": "V<PERSON><PERSON>z entrer une introduction", "choiceFigureTips": "La réélection de l'avatar ne conservera pas les paramètres actuels", "selectFigureConfirm": "Réélection de l'avatar ?", "createAgain": "<PERSON><PERSON><PERSON><PERSON>", "inputContentTips": "Veuillez entrer le contenu (jusqu'à 300 caractères)", "inputEmptyTips": "L'entrée ne peut pas être vide !", "sexChoice": "Sélection du sexe", "audioEmpty": "Aucun audio disponible", "template": "<PERSON><PERSON><PERSON><PERSON>", "selectRequest": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "agentTemplateTips": "Veuillez sélectionner un modèle d'IA", "abandonFigureConfirm": "La régénération de l'avatar coûtera à nouveau des cristaux, souhaitez-vous abandonner l'image actuelle de l'avatar ?", "agentInfoTips": "Veuillez compléter les informations sur l'IA", "backgroundStoryTips": "<PERSON>eu<PERSON>z entrer une histoire d'arrière-plan", "characterIntroTips": "<PERSON>eu<PERSON>z entrer une brève introduction", "dialogueExample": "Exemples de dialogue", "agentFeatureTips": "Veuillez compléter les caractéristiques de l'IA", "diyFigureSave": "Les avatars créés personnalisés seront enregistrés ici", "cancelCreateTips": "Annuler la création ne conservera pas vos paramètres actuels", "cancelCreateConfirm": "Voulez-vous annuler la création actuelle ?", "createAgentSuccess": "Création d'IA réussie", "characterSetTips": "Veuillez entrer les paramètres du personnage", "tagEmptyTips": "Veuillez sélectionner l'étiquette", "fansOption": "Fans", "likesOption": "<PERSON>'aime", "followOption": "Suivre", "nicknameOption": "Surnom", "genderOption": "<PERSON>e", "birthdaySelection": "Anniversaire", "personalDescription": "Description personnelle", "youRoleChatPersonalDesc": "Votre personnage bavarder avec vous en fonction de votre description personnelle, vous pouvez entrer vos préférences, les choses que vous détestez et autres informations~", "selectBirthday": "Choisir l'anniversaire", "unfollowOption": "Ne plus suivre", "basicInfo": "Mon profil", "enterInviteCode": "Entrez le code", "invalidInviteCode": "Code n'existe pas", "rewardAlreadyClaimed": "La récompense a déjà été réclamée", "accountDeactivation": "Supprimer le compte", "deactivationTerms": "Conditions d'annulation", "deactivationTermsContent1": "1.L'annulation du compte est pour le compte actuellement connecté.", "deactivationTermsContent2": "2.Après une annulation ré<PERSON>ie, vous ne pourrez plus vous connecter, r<PERSON><PERSON><PERSON>, utiliser ou récupérer ce compte.", "deactivationTermsContent3": "3.Si il y a des adhésions non utilisées, des pièces d'or et des diamants dans le compte, ils seront supprimés en cas d'annulation réussie. Veuillez agir avec prudence.", "deactivationTermsContent4": "4.Après une annulation réussie, vos informations personnelles et les autres données seront définitivement supprimées et ne peuvent pas", "deactivationTermsContent5": "5.申请注销后该账号为您保留15天，期间重新登录则自动取消注销，若15天内未取消注销，则自动注销该账号", "termsAgreement": "J'ai lu et j'accepte les conditions ci-dessus", "accountVerification": "Vérification de compte", "otherVerificationOptions": "Autres méthodes de vérification", "deactivationConfirmation": "Veuillez lire attentivement et confirmer à nouveau", "deactivationNotice1": "1. Après une annulation réussie, vos informations personnelles et les autres données seront définitivement annulées et ne peuvent pas être récupérées.", "deactivationNotice2": "2. Après avoir demandé l'annulation, votre compte sera conservé pendant 15 jours. Si vous vous reconnectez à nouveau pendant cette période, l'annulation sera automatiquement annulée. Si vous n'annulez pas l'annulation dans les 15 jours, le compte sera automatiquement annulé.", "deactivationSuccessful": "Demande d'annulation de compte réussie", "verificationSuccess": "Vérification réussie", "networkErrorRetry": "<PERSON><PERSON><PERSON>, ve<PERSON><PERSON><PERSON> rées<PERSON>er plus tard", "accountVerificationFailed": "La vérification du compte a échoué, le compte de messagerie électronique n'est pas valide", "invalidEmailVerification": "Code de vérification non valide, veuillez réessayer plus tard", "profile": "Profil personnel", "choosePreference": "Choisissez vos préférences", "recommend": "Recommandé", "abandonAccountCancellation": "Abandonner la désactivation du compte", "abandonCancellation": "Abandonner l'annulation", "discardLogoutPrompt": "Ihr Konto wurde für die Löschung eingereicht und wird am [时间] erfolgreich gelöscht. Bevor die Löschung erfolgreich ist, wenn Sie das Konto wiederherstellen müssen, wählen Sie bitte Löschung abbrechen, um erneut in das Konto einzuloggen, und Ihre Anfrage wird abgebrochen. Wenn Sie die Löschung nicht abbrechen möchten, können Si<PERSON> auf Mit anderem Konto anmelden klicken.", "focusonSuccess": "<PERSON><PERSON><PERSON>~", "unfollowed": "<PERSON><PERSON><PERSON><PERSON> du suivi", "fansAmount": "xxx abonnés", "rolesAmount": "xxx personnages", "emailFormatError": "Mauvaise adresse e-mail", "inconsistentAccounts": "Le compte vérifié n'est pas le compte connecté actuellement", "inviteCode": "Code", "inviteAmount": "Vous avez invité xx personnes au total", "termTips": "Veuillez lire attentivement les conditions ci-dessus. Votre compte ne peut pas être restauré après son annulation, veuillez donc choisir avec soin.", "invitationLink": "Inviter le lien", "day": "jours", "encounteredAlready": "Rencontré UGenie depuis X jours", "seeMore": "Voir plus", "upgradePlanBenefits": "Plan d'abonnement", "activateNow": "Mise à niveau", "logoutInfo": "Boutique des membres", "orderHistory": "Pas encore membre", "noMembershipActivated": "Profitez des avantages VIP dès l'inscription", "activateVIPBenefits": "Acheter maintenant", "purchaseNow": "Centre des membres", "superMembershipOffer": "Super membre remboursement immédiat", "dailyGiftForSuperMembers": "Cadeau quotidien pour les super membres", "chooseCombo": "Veuillez choisir un forfait d'adhésion", "NonMember": "Aucun abonnement actif pour le moment", "subscribe": "Abonnement", "weekSubscriptionDesc": "", "monthSubscriptionDesc": "", "maturity": "", "membershipCenter": "Confirmation de l'annulation de commande", "orderCancellationConfirm": "<PERSON><PERSON>", "cancellationNotice": "Remises remboursables pour l'annulation de cette commande", "cancellationRefundDetails": "Remise pour la première commande des membres", "ensureNoPaymentBeforeCancel": "Veu<PERSON>z confirmer que vous n'avez pas encore payé avant d'annuler !", "cancelUnpaidOrder": "Je n'ai pas encore payé, veuillez annuler la commande immédiatement", "orderRecords": "Historique des commandes", "orderType": "Type de commande", "creationTime": "Heure de création", "productName": "Nom du produit", "paymentMethod": "Mode de paiement", "actualPayment": "<PERSON><PERSON><PERSON> réel", "processingOrder": "Traitement de la commande", "closedOrder": "<PERSON><PERSON><PERSON>", "cancelOrder": "Annuler la commande", "changeTime": "<PERSON><PERSON> du changement", "records": "Enregistrement", "quantity": "Quantité", "awaitingOrderCompletion": "<PERSON><PERSON><PERSON> la fin de la commande", "orderNumber": "<PERSON><PERSON><PERSON><PERSON> de commande", "paymentProcessing": "Paiement difficile", "watiForPayment": "En raison des raisons liées aux canaux de paiement, le paiement peut prendre 5 à 10 minutes à traiter, veuil<PERSON><PERSON> patienter.", "noMoreOrder": "Aucun enregistrement", "rechargeSuccessful": "<PERSON><PERSON><PERSON> r<PERSON>", "cashRegister": "caisse", "selectPaymentMethod": "Veuillez sélectionner le mode de paiement", "selectPaymentWay": "Sélectionner le mode de paiement", "paySuccess": "<PERSON><PERSON><PERSON>", "payAmount": "<PERSON><PERSON> réel payé", "selectPayCountry": "Sélectionnez le pays de paiement", "immediatePayment": "<PERSON>ez maintenant", "orderCompleted": "Commande terminée", "payedOrder": "Commande payée", "refundOrder": "La commande a été remboursée", "canceledOrder": "Commande annulée", "purchaseCancelled": "<PERSON><PERSON><PERSON>", "buyFail": "Falha na compra", "taskForFreeGold": "Kostenlose Goldmünzen durch Aufgaben erhalten", "task": "Aufgaben", "dailyTasks": "Tägliche Aufgaben", "specialTasks": "Besondere Aufgaben", "checkIn": "Einchecken", "rewardClaimed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goComplete": "Fertigstellen", "claimReward": "Beanspruchen", "notAchieved": "<PERSON>cht erreicht", "inviteFriends": "Freunde einladen", "currentInvitedCount": "Derzeit eingeladen", "taskAcquisition": "Acquisition de tâches", "weeklyTasks": "Tâches hebdomadaires", "noAds": "Sans Pub Maintenant", "from": "à partir de", "unlockVipPower": "Accès à {vipNum} Fonctions VIP", "subscribeNow": "<PERSON>'abonner", "waitAction": "En attente d'action...", "moreCallTime": "Plus de Durée", "vipTips": "En cliquant sur s'abonner, vous acceptez les conditions de service. Vous serez facturé et l'abonnement se renouvellera automatiquement au même prix et pour la même durée. Vous pouvez annuler votre abonnement à tout moment dans le Play Store. Il s'agit d'un prix promotionnel à durée limitée. Nous vous informerons 30 jours avant la fin de la période promotionnelle. Les acheteurs existants peuvent continuer à bénéficier du renouvellement au prix promotionnel.", "onlineTips": "D<PERSON><PERSON>loquez l'Expérience Complète", "onlineDesc": "Téléchargez UGenie pour plus de fun avec une chance de jouer sans pub !", "onlineJump": "Y <PERSON>er <PERSON>ant", "community": "Communauté", "communityCrystal": "Envoyer des Cristaux", "globalSoundSwitch": "Interrupteur de Volume Global", "bgmSwitch": "Interrupteur de Musique de Fond", "buyCrystal": "Acheter des Cristaux", "loadAdFail": "Échec du chargement de la publicité. Veuillez réessayer plus tard.", "selectYourGender": "Choisissez Votre Genre", "other": "<PERSON><PERSON>", "wayYouLike": "Votre Style d`Interaction Préféré", "selectYourPreference": "Sélectionnez Votre Préférence", "preference1": "Discussion", "preference2": "Texte court, Sans options", "preference3": "Histoire", "preference4": "Texte long, Narration", "preference5": "<PERSON><PERSON>", "preference6": "Règles, Statistiques", "intimacyUpgrade": "Niveau d'intimité amélioré au niveau LV{level}", "contentGameUnlock": "Le gameplay exclusif LV{level} a été débloqué"}