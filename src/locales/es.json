{"welcomeToEros": "¡Bienvenido a UGenie!", "termsConfirmation": "Por favor, confirme los siguientes términos antes de utilizar este sitio.", "ageConfirmation": "Tengo 18 años o más.", "aiContentNotice": "Reconozco que el contenido relacionado con IA en este sitio es ficticio.", "guestLogin": "Inicio de sesión de invitado", "registrationLogin": "Registrarse/Iniciar se<PERSON>", "emailLogin": "Inicio de sesión por correo", "email": "Correo electrónico", "getCaptcha": "Obtener", "emailLoginOption": "Inicio de sesión", "agreeToTerms": "Al iniciar sesión, usted acepta la Política de Privacidad y el Acuerdo del Usuario.", "loginIssueContact": "¿No puedes iniciar sesión? Contacta al servicio al cliente.", "enterEmail": "Por favor, ingrese su correo electrónico.", "enterCaptcha": "Por favor, ingrese el código de verificación.", "invalidEmailFormat": "El formato de correo electrónico es incorrecto, por favor vuelva a ingresarlo.", "captchaError": "El código de verificación es incorrecto, por favor vuelva a ingresarlo.", "captchaExpired": "El código de verificación ha expirado, por favor vuelva a ingresarlo.", "accountSuspendedNotice": "Su cuenta ha sido suspendida, para detalles puede revisar el Centro de Ayuda para posibles razones o contactar al servicio al cliente.", "logout": "Salir", "logoutOption": "Confirmar sa<PERSON>a", "confirmLogout": "Una vez que sales, no podrás charlar con la IA, pero aún puedes iniciar sesión en esta cuenta", "readAndConfirmed": "<PERSON> leído y confirmado", "privacyAgreement": "Política de privacidad", "userAgreement": "Términos de Servicio", "unableLogin": "¿No se puede iniciar sesión?", "contactCustomer": "Contactar al servicio al cliente", "loginAndAgree": "Inicia sesión para aceptar", "welcomeToUgenie": "¡Bienvenido a UGenie", "CheckTerms": "Por favor, marque los términos", "otherLoginWay": "Iniciar sesión con otra cuenta", "loadingFailed": "Falló la carga de SDK", "invalidSession": "Sesión no válida, por favor inicia sesión nuevamente", "verificationCode": "Código de verificación", "store": "Tienda", "searchAgentName": "Buscar personajes", "unableConnect": "No se puede conectar a la tienda", "searchHistory": "Historial de búsqueda", "popularRoles": "Personajes populares", "noResultsFeedback": "¿No encuentras lo que quieres? Haz clic en retroalimentación", "emptySearchSuggestion": "¿Está vacío aquí, probar otra palabra clave?", "creatorSelection": "<PERSON><PERSON><PERSON>", "chatNow": "Cha<PERSON>", "followedNotice": "<PERSON><PERSON><PERSON><PERSON>", "characterOption": "<PERSON><PERSON><PERSON>", "search": "Buscar", "searchWantTips": "¿No encuentras lo que buscas?", "tryUsingDifferentKeyword": "Cambie la palabra clave e inténtelo de nuevo.", "language": "Idioma", "helpFAQ": "Ayuda FAQ", "feedbackOption": "Acerca de", "selectNumber": "Elija su número", "aiFilter": "Selección inteligente de entidades", "sortOptions": "Método de ordenación", "aiType": "Tipo de entidade inteligente", "mostPopular": "Popular", "latest": "Reciente", "trending": "Popular", "searchRoleOrCreator": "Buscar personajes o creadores", "dynamicInteraction": "Experimentar interacciones dinámicas con personajes, haciendo que la comunicación sea más vívida e interesante", "chatBasedOnPersona": "Los personajes charlan con usted basándose en su configuración de personaje. Elija su tarjeta de configuración de personaje para que ella/él lo comprenda mejor", "userCharacterOption": "Mi configuración de personaje", "roleSpeakingDuration": "Solo el discurso del personaje consumirá esta duración", "repeatedClickNotice": "Has hecho clic varias veces, inténtelo de nuevo más tarde", "exitImmersiveMode": "Salir del modo de inmersión", "myCreations": "Mis creaciones", "loginToView": "Inicia sesión para ver", "createLover": "<PERSON>rea tu propio amado", "goCreate": "Ir a crear", "clickToSet": "Haz clic para configurar", "retainCharacterNotice": "¿Quieres mantener tu personaje?", "dataClearWarning": "Todos los datos del personaje se borrarán por completo, por favor, tenga cuidado", "clickToTouchSpace": "Entrar en el espacio táctil", "imageOption": "Apariencia", "noAIsNotice": "Todavía no hay una entidad IA, haga clic en el + de abajo para invocar una entidad IA~", "myImageOption": "Mi apariencia", "editImage": "Editar a<PERSON>", "defaultVoicePlayback": "Reproducción de voz predeterminada", "captchaInvalidRetry": "Activar NSFW", "cost": "Gasto", "noHaveAi": "El otro lado no tiene IA", "moreCharacterTime": "Más tiempo de palabra para el personaje", "noMoreImage": "No hay más configuraciones de personaje por ahora", "editFigure": "Editar configuraciones de personaje", "aiOnTheWay": "El AI todavía está en el camino～", "searchWant": "Busca lo que deseas...", "confirmDelFigure": "¿Seguro que quieres eliminar este avatar?", "personalDescriptionTips": "Tu personaje se basará en tu descripción personal y tus conversaciones, puedes ingresar tus gustos, cosas que no te gustan y demás información～", "confirmExitStory": "¿Está seguro de que desea salir de la historia actual?", "interactWithHer": "Interactuar con ella", "newerStoryName": "Renacimiento", "flowerList": "Regalos", "popularityChart": "Popularidad", "inputFeedback": "Por favor, introduzca sus comentarios", "thankFeedback": "<PERSON><PERSON><PERSON> por sus comentarios", "fillInFeedback": "Rellenar los comentarios", "storyIntroduction": "Introducción", "missionObjectives": "Objetivo", "missionRewards": "Recompensa", "leaderboard": "Clasificación", "ugPhoneToLivco": "Año Nuevo, Nuevos Amigos.", "skipOption": "<PERSON><PERSON><PERSON>", "genderInterest": "Género de su interés", "interests": "Interés", "allOptions": "Todo", "interestPreferences": "Preferencias de interés", "confirmSelection": "Confirmar", "cancelButton": "<PERSON><PERSON><PERSON>", "confirmButton": "<PERSON><PERSON>", "editOption": "<PERSON><PERSON>", "saveToLocal": "Guardar localmente", "addOption": "<PERSON><PERSON><PERSON>", "saveButton": "Guardar", "emptySpaceNotice": "Está vacío aquí", "nextStep": "Siguiente paso", "closeButton": "<PERSON><PERSON><PERSON>", "uncompleted": "Incompleto", "completed": "Completado", "return": "Regresar", "contactSupport": "Si tienes algún problema, por favor contacta al servicio al cliente", "maxAmount": "Máximo", "purchaseOption": "<PERSON><PERSON><PERSON>", "expand": "Expandir", "collapse": "<PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "deleting": "Eliminando", "deleteSuccessful": "Eliminación exitosa", "download": "<PERSON><PERSON><PERSON>", "owned": "<PERSON><PERSON><PERSON><PERSON>", "notOwned": "No poseído", "noData": "Sin datos disponibles", "nothing": "<PERSON><PERSON>", "haveAnyQuestions": "Si tiene alguna pregunta, por favor", "minutes": "<PERSON><PERSON><PERSON>", "exit": "Salir", "micPermissionRequired": "UGenie necesita su permiso para usar el micrófono correctamente", "loading": "Cargando", "clickFeedback": "Obtener soporte", "verify": "Verificar", "successfullySaved": "Guardado", "uploadPictures": "Subiendo imagen", "setup": "Configuración", "noMore": "No más", "noMoreContent": "No más contenido", "permissionRequire": "Solicitud de permiso", "pullToRefresh": "Tira hacia abajo para actualizar", "looseToRefresh": "Suelte para actualizar", "pressAgain": "Presione nuevamente para salir de la aplicación.", "report": "<PERSON><PERSON><PERSON><PERSON>", "generateSuccessfully": "Generado con éxito", "generateStoryBook": "Generar", "copied": "Copiado", "send": "", "saveSuccessfully": "Guardar el éxito", "saveFailed": "Falla al guardar", "featureCoding": "Nueva función pronto", "getSuccessfully": "Obtener éxito", "feedback1": "Error de contenido o respuesta incompleta", "feedback2": "Contenido sensible o pornográfico", "feedback3": "Información que involucra violencia u odio", "feedback4": "Información inapropiada que involucra menores", "feedback5": "Contenido perturbador", "inputFeedbackMessage": "Por favor ingrese el problema que encontró", "submit": "Enviar", "feedbackSuccess": "<PERSON><PERSON><PERSON>, gracias por su comentario", "discoverUpdate": "¡Se encontró una nueva versión!", "updateNow": "Actualice ahora", "updateLater": "No actualizar todavía", "checkForUpdates": "Comprobar actualizaciones", "currentVersion": "Versión actual", "latestVersion": "Actualmente es la última versión", "swipeToClear": "Desliza hacia la izquierda para limpiar la pantalla y entrar en el modo de inmersión 3D", "triggerActions": "Haz clic en áreas especiales para desencadenar acciones.", "aiContentDisclaimer": "Lo que dice la IA es ficticio, por favor, preste atención y discerna con cuidado", "changeTopicSuggestion": "Por favor, cambie el tema", "makeCall": "<PERSON>cer una llamada", "startNewChat": "Reiniciar", "startTyping": "Comience a escribir", "resetCharacterNotice": "Después del reinicio, tu historial de chat con el personaje se borrará.", "newChatStartedNotice": "Se ha iniciado una nueva conversación, los nuevos mensajes no se ven afectados arriba", "emptyContentVoicePlayback": "Usted ha iniciado un nuevo tema", "recognitionFailedRetry": "El contenido está vacío, no se puede reproducir la voz", "unclearAudioRepeat": "El reconocimiento falló, por favor intente de nuevo", "voiceNotHeardNotice": "No escuché eso, ¿puedes decirlo de nuevo?", "continueConversation": "El personaje no puede escuchar tu voz y la llamada terminará automáticamente después de la cuenta atrás", "exitOption": "Continuar la conversación", "clearAudioEnvironment": "Por favor, manténgase en un entorno acústico limpio durante la llamada", "purchaseCallDuration": "Recargar para obtener más tiempo de habla del personaje", "hangUp": "<PERSON><PERSON>", "microphoneOn": "Activar el micrófono", "showKeyboard": "Levantar el teclado", "remainingDuration": "Tiempo de llamada restante", "exchangeDuration": "Obtener más tiempo de llamada", "callDuration": "Duración de la llamada", "unlockSelfie": "Desbloquear selfie", "setAsChatBackground": "Establecer como fondo de chat", "previousChats": "Hablado", "recordingPermissionError": "No se pueden obtener permisos de grabación, por favor revise la configuración", "recordingFailed": "No se puede grabar", "speechTooShort": "El tiempo de habla es demasiado corto", "recordingError": "La grabación ha fallado", "restarting": "<PERSON><PERSON><PERSON><PERSON>", "restartSuccessful": "Reiniciado con éxito", "likeCancelled": "Quitar me gusta", "likeSuccessful": "Me gusta", "dislikeCancelled": "Quitar No me gusta", "dislikeSuccessful": "No me gusta", "conversationProcessing": "El agente está escribiendo...", "voiceTooShort": "El tiempo de voz es demasiado corto", "exitClearMode": "Salir del modo de pantalla limpia", "like": "Me gusta", "dislike": "No me gusta", "copy": "Copiar", "releaseToCancelSend": "Soltar para cancelar el envío", "releaseToSendSwipeUpCancel": "Soltar para enviar, deslizar para cancelar", "holdToSpeak": "Mantén presionado para hablar", "inUse": "En uso", "levelLocked": "<PERSON><PERSON><PERSON> des<PERSON>", "levelUnlocked": "<PERSON><PERSON>es bloqueados", "modelLoading": "Cargando modelo", "waitToSend": "Por favor, espere que la otra parte termine de hablar antes de enviar", "connecting": "<PERSON><PERSON><PERSON><PERSON>", "microphoneOff": "Micró<PERSON><PERSON>", "sendingGift": "Regalando", "giftSent": "<PERSON><PERSON>", "settingUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setupSuccessful": "Configuración exitosa", "picture": "Imagen", "message": "Men<PERSON><PERSON>", "intimacyUnlockFunc": "Aumenta la intimidad con el Agente para desbloquear", "syncPhoneCallRecord": "Los registros de llamadas de voz han sido sincronizados.", "savePicture": "Guardar imagen", "scanQRCode": "Escanea el código QR para jugar de nuevo", "refuseGenerate": "No, gracias", "rewardCongratulation": "Historia completada, felicitaciones por obtener", "generateCard": "Recoger tarjeta de historia", "backToChat": "<PERSON><PERSON> al chat", "playAgain": "<PERSON>ver a jugar", "myStoryBook": "Mi libro de historias", "moreStories": "Más historias", "recheckStoryRecord": "Puedes verlo nuevamente en la sección Historia de la Barra de Herramientas de la Página de Diálogo", "storyBookGenerateConfirm": "¿Te gustaría crear un libro de historias?", "storyBookGenerateTips": "Volver a jugar borrará el registro actual de la historia. ¿Te gustaría crear un libro de historias para guardar este registro?", "restartStory": "Jugar de nuevo borrará el progreso de la historia actual. ¿Estás seguro de que deseas reiniciar?", "moreIntimacyToUnlock": "Aumenta la intimidad para desbloquear más contenido de la historia", "story": "Historia", "freeFirst": "Primera vez gratis", "goToChat": "<PERSON><PERSON> al chat", "syncGameMemory": "Memoria de juego exclusiva Lv{level} sincronizada", "clickToStory": "Haz clic en la carta para desbloquear la historia exclusiva", "upgradeToUnlockStory": "Aumenta la intimidad con el Agente para desbloquear", "startNow": "<PERSON><PERSON><PERSON> ahora", "unableToSend": "Env<PERSON>", "freeInput": "Entrada libre...", "selectOne": "Por favor, seleccione uno", "shareContent": "¡Comparte Agent con tu amigo!", "gameWin": "Victoria del juego", "gameLose": "Fracaso del juego", "doubleRewards": "Recompensas <PERSON>", "unlockByAds": "Desbloquear mirando anuncios", "rewardTip": "Felicitaciones por recibir el premio", "unSupportRegion": "Esta función no está disponible en tu región", "giftItems": "Regalos", "membershipBenefits": "Conviertase en miembro para obtener más cristales", "totalCrystalUsed": "Consumo acumulado de cristales", "giftReceivedNotice": "Recibido tuyo", "sendGift": "Regalar un regalo", "itemPurchase": "Comprar objetos", "unlockedFeatures": "Bloqueado", "purchaseOutfit": "<PERSON><PERSON><PERSON>", "unlock3DOutfit": "Desbloquear atuendo 3D", "outfits": "Atuendos", "staticOutfit": "Atuendo estático", "unlock3DOption": "Desbloquear 3D", "unlockOutfitCost": "Desbloquear un atuendo consume cristales", "itemStore": "Tienda de objetos", "diamond": "Diamantes", "crystalChangeDetails": "Detalles del cambio de cristales", "goldChangeDetails": "Detalles del cambio de monedas de oro", "purchaseSkin": "Comprar skin", "myCrystals": "<PERSON>s cristales", "crystalDetails": "Detalles de cristal", "selectPurchaseItem": "Por favor, selecciona un producto", "goldExchange": "Intercambio de diamantes", "exchangeOption": "Troca", "backpack": "<PERSON><PERSON><PERSON>", "noMoreCrystalDetails": "Sin detalles de cambio de cristal", "noMoreGoldDetails": "Sin detalles de cambio de moneda de oro", "goldDetails": "Detalles de moneda de oro", "moreFreeCoins": "<PERSON><PERSON> monedas gratis", "weeklyPurchaseLimit": "Límite de compras alcanzado para esta semana", "dailyPurchaseLimit": "Límite de compras alcanzado para hoy", "purchaseSuccessful": "Compra exitosa", "totalCoinsSpent": "Consumo acumulado de monedas de oro", "free": "<PERSON><PERSON><PERSON>", "rechargeToUnlock": "Obtener por recarga", "remainingDiamonds": "Total de Cristales", "exchangeCoins": "Cambiar monedas de oro", "redeemNow": "<PERSON><PERSON><PERSON>ora", "consumption": "Consu<PERSON>", "gold": "Monedas de oro", "crystal": "Cristales", "insufficientCrystals": "No hay suficientes cristales", "exchangeSuccessful": "Redención exitosa", "unableFindProduct": "El producto no existe", "adsToCrystal": "Mira el anuncio para obtener 50 cristales", "insufficientBalance": "<PERSON><PERSON> insuficiente, por favor recarge", "currentLevel": "<PERSON>vel actual", "increaseIntimacy": "Aumentar la intimidad para desbloquear más formas de jugar", "intimacyLevelUp": "Nivel de intimidad", "giftBoostsIntimacy": "<PERSON><PERSON><PERSON> regalos, puede aumentar la intimidad ~", "emptyDiscover": "Está vacío aquí, ve a la página de descubrimiento", "goDiscover": "Ir a des<PERSON><PERSON>r", "msgDetail": "Detalles del mensaje", "noMoreMsg": "No más mensajes", "followedYour": "Siguió a tu", "followedYouNotice": "<PERSON> siguieron", "messageNotification": "Notificación", "systemMessages": "Mensajes del sistema", "interactiveMessages": "Mensajes interactivos", "reportFeedback": "Reportar retroalimentación", "deleteRole": "Eliminar personaje", "aboutRole": "Sobre TA", "photoAlbum": "<PERSON>l<PERSON><PERSON>", "introduction": "Introducción", "openingLine": "Presentación", "startChat": "Iniciar conversación", "improveAffection": "Mejora la afinidad para desbloquear selfies exclusivos", "receiveSelfiesHere": "Las selfies recibidas se guardarán aquí", "selfieUnlocked": "Desbloqueado selfies", "getSelfie": "Consigue una selfie", "purchaseCallTime": "Comprar tiempo de llamada", "purchaseGift": "<PERSON><PERSON><PERSON> regalo", "confirmDeleteCharacter": "¿Seguro que quieres eliminar el personaje?", "agentProfile": "Perfil", "cancelCreate": "Cancelar a creación", "characterSetting": "Diseño del personaje", "creationMethod": "Método de creación", "quickCreate": "Creación rápida", "selectGender": "Elección del género del personaje", "maleOption": "Hombre", "femaleOption": "<PERSON><PERSON>", "nonBinaryOption": "No binario", "setPublic": "Establecer como público", "publicSettingWarning": "Si se establece como público, no se puede hacer privado", "personality": "Personalidad", "createAndStartChat": "<PERSON><PERSON><PERSON> y comenzar a charlar", "advancedCreation": "Creación avanzada", "customizeImage": "Imagen personalizada", "freeImage": "<PERSON>n gratuita", "enterNickname": "Ingresar apodo", "completeBackground": "Completa la historia", "createCharacter": "<PERSON><PERSON><PERSON> personaje", "creatingCharacter": "<PERSON><PERSON><PERSON>", "regenerateOption": "<PERSON><PERSON><PERSON>", "cropAvatar": "Recortar avatar", "continueCurrentSetting": "¿Continuar con la configuración actual?", "modifySettingNotice": "Puedes modificar la configuración y crear una nueva imagen, pero consumirá cristales adicionales", "savedInDrafts": "Las imágenes generadas se guardarán en la caja de borradores", "exitAICharacterCreation": "Salir de la creación de la entidad IA", "draftsFolder": "Caja de borradores", "useImage": "Usar imagen", "voiceOption": "Voz", "tag": "Etiquetas", "presetSoundLibrary": "Biblioteca de voces preestablecidas", "characterSettingRequired": "Configuración del personaje (requerido)", "openingLineRequired": "Línea de apertura (requerido)", "backgroundStoryRequired": "Historia de fondo (requerido)", "characterIntroRequired": "Introducción del personaje (requerido)", "dialogueExampleOptional": "Ejemplo de diálogo (opcional)", "tagRequired": "Etiquetas (requerido)", "clickToAddUserContent": "Haz clic para agregar contenido de diálogo de usuario", "clickToAddAIResponses": "Haz clic para agregar contenido de respuesta de la IA", "customizeImageName": "-", "createCustomImage": "Crear avatar personalizado", "createAI": "Crear entidad IA", "openingLineTips": "Por favor, ingrese un saludo", "choiceFigureTips": "Seleccionar un avatar nuevo no mantendrá la configuración actual", "selectFigureConfirm": "¿Seleccionar un avatar nuevo?", "createAgain": "Volver a crear", "inputContentTips": "Ingrese el contenido (hasta 300 caracteres)", "inputEmptyTips": "¡La entrada no puede estar vacía!", "sexChoice": "Selección de género", "audioEmpty": "No hay audio disponible", "template": "Plantilla", "selectRequest": "Por favor, seleccione", "agentTemplateTips": "Por favor, seleccione una plantilla de IA", "abandonFigureConfirm": "Regenerar el avatar costará cristales de nuevo, ¿desea abandonar la imagen actual del avatar?", "agentInfoTips": "Por favor, complete la información de la IA", "backgroundStoryTips": "Ingrese una historia de fondo", "characterIntroTips": "Ingrese una breve introducción", "dialogueExample": "Ejemplos de diálogo", "agentFeatureTips": "Por favor, complete las características de la IA", "diyFigureSave": "Los avatares creados personalmente se guardarán aquí", "cancelCreateTips": "Cancelar la creación no mantendrá la configuración actual", "cancelCreateConfirm": "¿Desea cancelar la creación actual?", "createAgentSuccess": "Creación de IA exitosa", "characterSetTips": "Ingrese la configuración del personaje", "tagEmptyTips": "Seleccione la etiqueta", "fansOption": "Fans", "likesOption": "Me gusta", "followOption": "<PERSON><PERSON><PERSON>", "nicknameOption": "A<PERSON>do", "genderOption": "<PERSON><PERSON><PERSON>", "birthdaySelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "personalDescription": "Descripción personal", "youRoleChatPersonalDesc": "Su personaje charlaba con usted basándose en su descripción personal, puede ingresar sus preferencias, cosas que odia y otra información~", "selectBirthday": "Elección de cumpleaños", "unfollowOption": "<PERSON><PERSON>", "basicInfo": "<PERSON>", "enterInviteCode": "Ingrese el código", "invalidInviteCode": "Código no existe", "rewardAlreadyClaimed": "El premio ya ha sido reclamado", "accountDeactivation": "Eliminar cuenta", "deactivationTerms": "Términos de cancelación", "deactivationTermsContent1": "1.La cancelación de la cuenta es para la cuenta actualmente iniciada en sesión.", "deactivationTermsContent2": "2.Después de una cancelación exitosa, ya no podrás iniciar sesión, reactivar, usar o recuperar esta cuenta.", "deactivationTermsContent3": "3.Si hay membresías sin usar, monedas de oro y diamantes en la cuenta, se eliminarán al cancelar exitosamente. Por favor, proceda con precaución.", "deactivationTermsContent4": "4.Después de una cancelación exitosa, su información personal y otros datos se eliminarán permanentemente y no se pueden recuperar.", "deactivationTermsContent5": "5.Después de solicitar la cancelación, su cuenta se mantendrá durante 15 días. Si vuelve a iniciar sesión durante este período, la cancelación se cancelará automáticamente. Si no cancela la cancelación en 15 días, la cuenta se cancelará automáticamente.", "termsAgreement": "He leído y acepto los términos anteriores", "accountVerification": "Verificación de cuenta", "otherVerificationOptions": "Otros métodos de verificación", "deactivationConfirmation": "Por favor, lea detenidamente y confirme nuevamente", "deactivationNotice1": "1. Después de una cancelación exitosa, su información personal y otros datos se cancelarán permanentemente y no podrán ser recuperados.", "deactivationNotice2": "2. Después de solicitar la cancelación, su cuenta se mantendrá por 15 días. Si vuelve a iniciar sesión durante este período, la cancelación se cancelará automáticamente. Si no cancela la cancelación en 15 días, la cuenta se cancelará automáticamente.", "deactivationSuccessful": "Solicitud de cancelación de cuenta exitosa", "verificationSuccess": "Verificación exitosa", "networkErrorRetry": "<PERSON><PERSON>r de red, por favor intente más tarde", "accountVerificationFailed": "La verificación de la cuenta ha fallado, la cuenta de correo electrónico no es válida", "invalidEmailVerification": "Código de verificación no válido, por favor intente más tarde", "profile": "Perfil personal", "choosePreference": "Elige tu preferencia", "recommend": "Recomendado", "abandonAccountCancellation": "Renunciar a la cancelación de cuenta", "abandonCancellation": "Renunciar a la cancelación", "discardLogoutPrompt": "Su cuenta ha enviado una solicitud de cancelación y se cancelará exitosamente en [时间]. Antes de que la cancelación tenga éxito, si necesita restaurar la cuenta, por favor seleccione Cancelar Cancelación para iniciar sesión de nuevo, y su solicitud será cancelada. Si no necesita cancelar la cancelación, puede hacer clic en Iniciar sesión con otra cuenta.", "focusonSuccess": "Segu<PERSON> con éxito~", "unfollowed": "<PERSON><PERSON><PERSON>", "fansAmount": "xxx seguidores", "rolesAmount": "xxx personajes", "emailFormatError": "Formato de correo electrónico <PERSON>o", "inconsistentAccounts": "La cuenta verificada no es la cuenta actualmente iniciada en sesión", "inviteCode": "Código", "inviteAmount": "Han invitado a xx personas en total", "termTips": "Lea atentamente los términos anteriores. Su cuenta no se puede restaurar después de cancelarla, así que elija con cuidado.", "invitationLink": "Enlace de invitación", "day": "días", "encounteredAlready": "Conocí a UGenie hace X días", "seeMore": "<PERSON>er más", "upgradePlanBenefits": "Plan de Suscripción", "activateNow": "Actualizar", "logoutInfo": "Tienda de miembros", "orderHistory": "No es miembro todavía", "noMembershipActivated": "Disfruta de los beneficios VIP al suscribirte", "activateVIPBenefits": "<PERSON><PERSON><PERSON> ahora", "purchaseNow": "Centro de miembros", "superMembershipOffer": "Super miembro reembolso inmediato", "dailyGiftForSuperMembers": "Regalo diario para super miembros", "chooseCombo": "Por favor, seleccione un plan de membresía", "NonMember": "Actualmente no hay membresía activa", "subscribe": "Suscribir", "weekSubscriptionDesc": "Cancele en cualquier momento. Recarga la próxima semana por $", "monthSubscriptionDesc": "Cancele en cualquier momento. Recarga el próximo mes por $", "maturity": "vencer", "membershipCenter": "Confirmación de cancelación de pedido", "orderCancellationConfirm": "Tenga en cuenta", "cancellationNotice": "Descuentos reembolsables por esta cancelación de pedido", "cancellationRefundDetails": "Descuento por primer pedido de miembro", "ensureNoPaymentBeforeCancel": "Por favor, confirme que aún no ha pagado antes de cancelar", "cancelUnpaidOrder": "No he pagado, por favor cancela el pedido de inmediato", "orderRecords": "Historial de pedidos", "orderType": "Tipo de pedido", "creationTime": "Hora de creación", "productName": "Nombre del producto", "paymentMethod": "Método de pago", "actualPayment": "Pago real", "processingOrder": "Procesando el pedido", "closedOrder": "<PERSON><PERSON><PERSON>", "cancelOrder": "Cancelar pedido", "changeTime": "Hora de cambio", "records": "Registro", "quantity": "Cantidad", "awaitingOrderCompletion": "Esperar la finalización del pedido", "orderNumber": "Número de pedido", "paymentProcessing": "Pagan con esfuerzo", "watiForPayment": "Debido a razones del canal de pago, el pago puede tomar de 5 a 10 minutos para procesarse, por favor espere con paciencia.", "noMoreOrder": "Sin registros", "rechargeSuccessful": "<PERSON><PERSON><PERSON> exitosa", "cashRegister": "contador de salidas", "selectPaymentMethod": "Por favor seleccione el método de pago", "selectPaymentWay": "Seleccione el método de pago", "paySuccess": "<PERSON><PERSON>oso", "payAmount": "Monto real pagado", "selectPayCountry": "Seleccione el país de pago", "immediatePayment": "Paga ahora", "orderCompleted": "<PERSON><PERSON> completada", "payedOrder": "<PERSON><PERSON> pagada", "refundOrder": "El pedido ha sido reembolsado", "canceledOrder": "Orden cancelada", "purchaseCancelled": "Compra cancelada", "buyFail": "Compra fallida", "taskForFreeGold": "Tareas para conseguir más oro gratis", "task": "<PERSON><PERSON><PERSON>", "dailyTasks": "<PERSON><PERSON><PERSON> diarias", "specialTasks": "<PERSON>reas especiales", "checkIn": "Check-in", "rewardClaimed": "Recibido", "goComplete": "<PERSON>r al completo", "claimReward": "Reciba", "notAchieved": "No alcanzado", "inviteFriends": "Invita a tus amigos", "currentInvitedCount": "Invitados actuales", "taskAcquisition": "Adquisición de tareas", "weeklyTasks": "<PERSON><PERSON><PERSON>", "noAds": "Sin Anuncios Ahora", "from": "desde", "unlockVipPower": "Accede a {vipNum} Funciones VIP", "subscribeNow": "Suscribirse Ahora", "waitAction": "Esperando acción...", "moreCallTime": "Más Duración", "vipTips": "Al hacer clic en suscribirse, acepta los términos de servicio. Se le cobrará y la suscripción se renovará automáticamente al mismo precio y duración. Puede cancelar su suscripción en cualquier momento en Play Store. Este es un precio promocional por tiempo limitado. Le notificaremos 30 días antes de que termine el período promocional. Los usuarios que ya compraron pueden continuar disfrutando de la renovación al precio promocional.", "onlineTips": "Desbloquea la Experiencia Completa", "onlineDesc": "¡Descarga UGenie para más diversión con la oportunidad de jugar sin anuncios!", "onlineJump": "<PERSON><PERSON>", "community": "Comunidad", "communityCrystal": "Enviar Cristales", "globalSoundSwitch": "Interruptor de Volumen Global", "bgmSwitch": "Interruptor de Música de Fondo", "buyCrystal": "Comprar <PERSON>", "loadAdFail": "Error al cargar el anuncio. Inténtelo de nuevo más tarde.", "selectYourGender": "<PERSON><PERSON>", "other": "<PERSON><PERSON>", "wayYouLike": "Tu Estilo de Interacción Preferido", "selectYourPreference": "Selecciona Tu Preferencia", "preference1": "Cha<PERSON>a", "preference2": "Texto corto, Sin opciones", "preference3": "Historia", "preference4": "Texto largo, Narración", "preference5": "Ju<PERSON>", "preference6": "Reglas, Estadísticas", "intimacyUpgrade": "Nivel de intimidad mejorado a LV{level}", "contentGameUnlock": "Se ha desbloqueado el modo de juego exclusivo de LV{level}"}