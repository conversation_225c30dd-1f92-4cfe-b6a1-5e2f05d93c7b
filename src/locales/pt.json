{"welcomeToEros": "Bem-vindo ao UGenie!", "termsConfirmation": "Por favor, confirme os seguintes termos antes de usar este site", "ageConfirmation": "Tenho 18 anos ou mais", "aiContentNotice": "Reconheço que o conteúdo relacionado à IA neste site é fictício", "guestLogin": "<PERSON><PERSON><PERSON><PERSON>", "registrationLogin": "Registrar-se/Fazer login", "emailLogin": "Login de e-mail", "email": "Email", "getCaptcha": "Obter", "emailLoginOption": "<PERSON><PERSON>", "agreeToTerms": "Ao efetuar login, você concorda com a Política de Privacidade e o Acordo do Usuário.", "loginIssueContact": "Não consegue fazer login? Entre em contato com o suporte", "enterEmail": "Digite seu email", "enterCaptcha": "Insira o código de verificação", "invalidEmailFormat": "O formato do email está incorreto, por favor, digite novamente", "captchaError": "O código de verificação está incorreto, por favor, insira novamente", "captchaExpired": "O código de verificação expirou, por favor, insira novamente", "accountSuspendedNotice": "Sua conta foi suspensa, para de<PERSON>hes, você pode verificar o Centro de Ajuda para possíveis razões ou contatar o suporte ao cliente.", "logout": "<PERSON><PERSON>", "logoutOption": "Confirme o logout", "confirmLogout": "<PERSON><PERSON><PERSON> de sair, você não poderá conversar com a IA, mas ainda pode fazer login nesta conta", "readAndConfirmed": "Eu li e confirmei", "privacyAgreement": "Política de privacidade", "userAgreement": "Termos de Serviço", "unableLogin": "Não consigo me logar?", "contactCustomer": "Contatar o suporte ao cliente", "loginAndAgree": "Faça login para concordar", "welcomeToUgenie": "Bem-vindo ao UGenie", "CheckTerms": "Por favor, marque os termos", "otherLoginWay": "Usar outra conta para fazer login", "loadingFailed": "O carregamento do SDK falhou", "invalidSession": "Sessão <PERSON>, faça login novamente", "verificationCode": "Código de verificação", "store": "<PERSON><PERSON>", "searchAgentName": "Procurar personagens", "unableConnect": "Não foi possível conectar à loja", "searchHistory": "Histórico de pesquisa", "popularRoles": "Personagens populares", "noResultsFeedback": "Não encontrou o que queria? Clique em feedback", "emptySearchSuggestion": "Está vazio aqui, tente outra palavra-chave?", "creatorSelection": "<PERSON><PERSON><PERSON>", "chatNow": "Cha<PERSON>", "followedNotice": "<PERSON><PERSON><PERSON>", "characterOption": "Personagem", "search": "<PERSON><PERSON><PERSON><PERSON>", "searchWantTips": "<PERSON><PERSON> consegue encontrar o que deseja?", "tryUsingDifferentKeyword": "Altere a palavra-chave e tente novamente", "language": "Idioma", "helpFAQ": "Apoio FAQ", "feedbackOption": "О нас", "selectNumber": "Escolha seu número", "aiFilter": "Seleção de IA", "sortOptions": "Método de ordenação", "aiType": "Tipo de IA", "mostPopular": "Mais like", "latest": "Novo", "trending": "Popular", "searchRoleOrCreator": "Procure por personagens ou criadores", "dynamicInteraction": "Experimente interações dinâmicas com personagens, tornando a comunicação mais vívida e interessante", "chatBasedOnPersona": "Os personagens farão chat com você com base em sua configuração de personagem. Escolha seu cartão de configuração de personagem para que ela/ele possa entender você melhor", "userCharacterOption": "Minha configuração de personagem", "roleSpeakingDuration": "<PERSON>nte o discurso do personagem consome esse tempo", "repeatedClickNotice": "<PERSON>oc<PERSON> clicou várias vezes, por favor, tente novamente mais tarde", "exitImmersiveMode": "Sair do modo de imersão", "myCreations": "Minhas criações", "loginToView": "Login para ver", "createLover": "Crie seu próprio amado", "goCreate": "<PERSON><PERSON> criar", "clickToSet": "Clique para configurar", "retainCharacterNotice": "Você quer manter seu personagem?", "dataClearWarning": "Todos os dados dos personagens serão completamente apagados, por favor, tome cuidado", "clickToTouchSpace": "Entre na área tátil", "imageOption": "Aparência", "noAIsNotice": "Ainda não há uma entidade IA, clique no + abaixo para convocar uma entidade IA~", "myImageOption": "Minha aparência", "editImage": "Editar <PERSON>", "defaultVoicePlayback": "Reprodução de voz padrão", "captchaInvalidRetry": "Ativar NSFW", "cost": "Gasto", "noHaveAi": "O outro lado não tem IA", "moreCharacterTime": "Mais tempo de fala para o personagem", "noMoreImage": "Não há mais configurações de personagem por enquanto", "editFigure": "Editar configurações de personagem", "aiOnTheWay": "O IA ainda está no caminho～", "searchWant": "Procure o que você deseja...", "confirmDelFigure": "Você tem certeza de que deseja excluir este avatar?", "personalDescriptionTips": "Seu personagem será baseado em sua descrição pessoal e suas conversas, você pode inserir seus gostos, coisas que não gosta e outras informações～", "confirmExitStory": "Tem certeza de que deseja sair da história atual?", "interactWithHer": "Interagir com ela", "newerStoryName": "Renascimento", "flowerList": "Presentes", "popularityChart": "Popularidade", "inputFeedback": "Digite seu feedback", "thankFeedback": "G<PERSON><PERSON> por su retroalimentación", "fillInFeedback": "<PERSON><PERSON><PERSON> os comentários", "storyIntroduction": "Introdução", "missionObjectives": "Objetivo", "missionRewards": "Recompensa", "leaderboard": "<PERSON><PERSON><PERSON>", "ugPhoneToLivco": "", "skipOption": "<PERSON><PERSON>", "genderInterest": "<PERSON><PERSON><PERSON><PERSON> que você gosta", "interests": "Interesse", "allOptions": "<PERSON><PERSON>", "interestPreferences": "Preferências de interesse", "confirmSelection": "Confirmar", "cancelButton": "<PERSON><PERSON><PERSON>", "confirmButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editOption": "<PERSON><PERSON>", "saveToLocal": "<PERSON><PERSON>", "addOption": "<PERSON><PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON>", "emptySpaceNotice": "Está vazio aqui", "nextStep": "Próximo passo", "closeButton": "<PERSON><PERSON><PERSON>", "uncompleted": "Incompleto", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "return": "Voltar", "contactSupport": "Se você tiver algum problema, por favor, contate o suporte ao cliente", "maxAmount": "Máximo", "purchaseOption": "<PERSON><PERSON><PERSON>", "expand": "Expandir", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "deleting": "Excluindo", "deleteSuccessful": "Exclusão bem-sucedida", "download": "Baixar", "owned": "Possu<PERSON><PERSON>", "notOwned": "Não possuido", "noData": "Sem dados disponíveis", "nothing": "<PERSON><PERSON>", "haveAnyQuestions": "Se você tiver alguma dúvida, por favor", "minutes": "<PERSON><PERSON><PERSON>", "exit": "<PERSON><PERSON>", "micPermissionRequired": "UGenie precisa da sua permissão para usar o microfone corretamente", "loading": "Carregando", "clickFeedback": "Obter Suporte", "verify": "Verificar", "successfullySaved": "Salvo", "uploadPictures": "Enviando imagem", "setup": "Configurações", "noMore": "<PERSON><PERSON> há mais", "noMoreContent": "<PERSON><PERSON> há mais conteúdo", "permissionRequire": "Aplicação de permissão", "pullToRefresh": "Puxe para baixo para atualizar", "looseToRefresh": "Solte para atualizar", "pressAgain": "Pressione novamente para sair do aplicativo", "report": "<PERSON><PERSON><PERSON><PERSON>", "generateSuccessfully": "Gerado com sucesso", "generateStoryBook": "<PERSON><PERSON><PERSON>", "copied": "Copiado", "send": "", "saveSuccessfully": "", "saveFailed": "", "featureCoding": "Novo recurso em breve", "getSuccessfully": "Obter sucesso", "feedback1": "Erro de conteúdo ou resposta incompleta", "feedback2": "<PERSON><PERSON><PERSON><PERSON> sens<PERSON> ou pornográfico", "feedback3": "Informações envolvendo violência ou ódio", "feedback4": "Informações inadequadas envolvendo menores", "feedback5": "<PERSON><PERSON><PERSON><PERSON>", "inputFeedbackMessage": "Por favor, insira o problema que você encontrou", "submit": "Enviar", "feedbackSuccess": "<PERSON><PERSON>, obri<PERSON> pelo <PERSON>", "discoverUpdate": "Nova versão encontrada!", "updateNow": "Atualize", "updateLater": "Não atualize", "checkForUpdates": "Verificar atualização", "currentVersion": "<PERSON><PERSON><PERSON> at<PERSON>", "latestVersion": "Atualmente a versão mais recente", "swipeToClear": "Deslize para a esquerda para limpar a tela e entrar no modo de imersão 3D", "triggerActions": "Clique em áreas especiais para ativar ações!", "aiContentDisclaimer": "Tudo o que a IA diz é fictício, por favor, discerna com cuidado", "changeTopicSuggestion": "Por favor, mude o tópico", "makeCall": "Ligar", "startNewChat": "Reiniciar", "startTyping": "Comece a digitar", "resetCharacterNotice": "Após a reinicialização, seu histórico de chat com o personagem será apagado.", "newChatStartedNotice": "Uma nova conversa foi iniciada, as novas mensagens não são afetadas acima", "emptyContentVoicePlayback": "Você iniciou um novo tópico", "recognitionFailedRetry": "O conteúdo está vazio, não é possível reproduzir áudio", "unclearAudioRepeat": "A reconhecimento falhou, por favor, tente novamente", "voiceNotHeardNotice": "Eu não ouvi bem, você pode dizer novamente?", "continueConversation": "O personagem não poderá ouvi-lo e encerrará automaticamente a chamada após uma contagem regressiva", "exitOption": "Continuar a conversa", "clearAudioEnvironment": "Manter um ambiente acústico limpo durante a chamada", "purchaseCallDuration": "Recarregue para obter mais tempo de fala do personagem", "hangUp": "<PERSON><PERSON><PERSON>", "microphoneOn": "Microfone ligado", "showKeyboard": "Levantar o teclado", "remainingDuration": "Tempo de chamada restante", "exchangeDuration": "Obter mais tempo de chamada", "callDuration": "Duração da chamada", "unlockSelfie": "Desbloquear selfie", "setAsChatBackground": "Definir como plano de fundo do chat", "previousChats": "<PERSON><PERSON><PERSON>", "recordingPermissionError": "Não é possível obter permissão de gravação, por favor, verifique as configurações", "recordingFailed": "Não é possível gravar", "speechTooShort": "O tempo de fala é muito curto", "recordingError": "A gravação falhou", "restarting": "<PERSON><PERSON><PERSON><PERSON>", "restartSuccessful": "Reinicialização bem-sucedida", "likeCancelled": "Curtida cancelada com sucesso", "likeSuccessful": "<PERSON><PERSON>ida bem-sucedida", "dislikeCancelled": "Não gosto cancelado com sucesso", "dislikeSuccessful": "<PERSON><PERSON> gosto bem-sucedida", "conversationProcessing": "Diálogo sa<PERSON>o", "voiceTooShort": "O tempo de áudio é muito curto", "exitClearMode": "Sair do modo de tela limpa", "like": "Curtir", "dislike": "<PERSON><PERSON> gosto", "copy": "Copiar", "releaseToCancelSend": "Soltar para cancelar o envio", "releaseToSendSwipeUpCancel": "Soltar para enviar, deslize para cancelar", "holdToSpeak": "Pressione e fale", "inUse": "Em uso", "levelLocked": "Níveis <PERSON>", "levelUnlocked": "Níveis blo<PERSON>", "modelLoading": "Carregando modelo", "waitToSend": "Por favor, espere que a outra parte termine de falar antes de enviar", "connecting": "<PERSON><PERSON><PERSON><PERSON>", "microphoneOff": "Microfone desligado", "sendingGift": "<PERSON><PERSON><PERSON>", "giftSent": "Doação bem-sucedida", "settingUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setupSuccessful": "Configuração bem-sucedida", "picture": "Imagem", "message": "Mensagem", "intimacyUnlockFunc": "Aprimore sua favorabilidade desbloquear funções exclusivas", "syncPhoneCallRecord": "Os registros de chamadas de voz foram sincronizados.", "savePicture": "<PERSON><PERSON> imagem", "scanQRCode": "Escaneie o código QR para jogar novamente", "refuseGenerate": "Não, obrigado", "rewardCongratulation": "História concluída, parabéns por receber", "generateCard": "Coletar cartão de história", "backToChat": "Voltar ao chat", "playAgain": "Jogar novamente", "myStoryBook": "Meu livro de histórias", "moreStories": "<PERSON><PERSON>", "recheckStoryRecord": "Você pode visualizá-lo novamente na seção História da Barra de Ferramentas da Página de Diálogo", "storyBookGenerateConfirm": "Deseja criar um livro de histórias?", "storyBookGenerateTips": "Jogar novamente apagará o registro atual da história. Deseja criar um livro de histórias para salvar este registro?", "restartStory": "", "moreIntimacyToUnlock": "Aumente a intimidade para desbloquear mais conteúdo da história", "story": "História", "freeFirst": "Primeira vez gr<PERSON><PERSON>", "goToChat": "Ir para o chat", "syncGameMemory": "Memória de jogo exclusiva Lv{level} sincronizada", "clickToStory": "Clique no cartão para desbloquear a história exclusiva", "upgradeToUnlockStory": "Aumente a intimidade com o Agente para desbloquear", "startNow": "<PERSON><PERSON><PERSON>ra", "unableToSend": "<PERSON><PERSON>", "freeInput": "Entrada livre...", "selectOne": "Por favor, selecione um", "shareContent": "Compartilhe o Agente com seu amigo!", "gameWin": "Vitória do jogo", "gameLose": "<PERSON><PERSON><PERSON> no jogo", "doubleRewards": "Recompensas <PERSON>", "unlockByAds": "Desbloquear assistindo a anúncios", "rewardTip": "Parabéns por receber o prêmio", "unSupportRegion": "Este recurso não é suportado em sua região", "giftItems": "Presentes", "membershipBenefits": "Tornar-se <PERSON> para obter mais cristais", "totalCrystalUsed": "<PERSON><PERSON><PERSON> acumula<PERSON> de <PERSON>ristais", "giftReceivedNotice": "**<PERSON><PERSON>u seu **", "sendGift": "<PERSON><PERSON> um presente", "itemPurchase": "<PERSON><PERSON><PERSON> itens", "unlockedFeatures": "Bloqueado", "purchaseOutfit": "Comprar visual", "unlock3DOutfit": "Desbloquear visual 3D", "outfits": "<PERSON><PERSON><PERSON>", "staticOutfit": "Visual estático", "unlock3DOption": "Desbloquear 3D", "unlockOutfitCost": "Desbloquear um visual consome cristais", "itemStore": "<PERSON>ja de itens", "diamond": "Diamantes", "crystalChangeDetails": "Detalhes da mudança de cristais", "goldChangeDetails": "Detalhes da mudança de ouros", "purchaseSkin": "Comprar skin", "myCrystals": "<PERSON><PERSON> cristais", "crystalDetails": "Detalhes de cristais", "selectPurchaseItem": "Por favor, selecione um produto", "goldExchange": "Troca de ouros", "exchangeOption": "Troca", "backpack": "<PERSON><PERSON><PERSON>", "noMoreCrystalDetails": "Sem detalhes de mudança de cristal", "noMoreGoldDetails": "Sem detalhes de mudança de moeda de ouro", "goldDetails": "Detalhes da moeda de ouro", "moreFreeCoins": "<PERSON><PERSON> moe<PERSON> gr<PERSON>", "weeklyPurchaseLimit": "Este produto alcançou o limite de compra semanal", "dailyPurchaseLimit": "Este produto alcançou o limite de compra diário", "purchaseSuccessful": "Compra bem-sucedida", "totalCoinsSpent": "Consu<PERSON> acumulado de moedas de ouro", "free": "<PERSON><PERSON><PERSON><PERSON>", "rechargeToUnlock": "Obter por recarga", "remainingDiamonds": "Total de Cristais", "exchangeCoins": "<PERSON><PERSON><PERSON> moedas de ouro", "redeemNow": "Trocar agora", "consumption": "Consu<PERSON>", "gold": "<PERSON><PERSON> de ouro", "crystal": "<PERSON><PERSON><PERSON>", "insufficientCrystals": "Cristais insuficientes", "exchangeSuccessful": "Resgate bem-sucedido", "unableFindProduct": "O produto não existe", "adsToCrystal": "Assista ao anúncio para obter 50 cristais", "insufficientBalance": "<PERSON><PERSON> insuficiente, por favor recarregue", "currentLevel": "<PERSON><PERSON><PERSON> atual", "increaseIntimacy": "Aumentar o nível de intimidade para desbloquear mais maneiras de jogar", "intimacyLevelUp": "Nível de intimidade", "giftBoostsIntimacy": "<PERSON><PERSON> presentes, pode aumentar a intimidade~", "emptyDiscover": "Está vazio aqui, vá para a página de descoberta", "goDiscover": "<PERSON><PERSON> <PERSON>", "msgDetail": "Detalhes da mensagem", "noMoreMsg": "Não há mais mensagens", "followedYour": "** seguiu seu **", "followedYouNotice": "Segu<PERSON> você", "messageNotification": "Notificação", "systemMessages": "Aviso do sistema", "interactiveMessages": "Aviso interativo", "reportFeedback": "Feedback sobre den<PERSON><PERSON><PERSON>", "deleteRole": "Excluir personagem", "aboutRole": "Sobre ele/ela", "photoAlbum": "<PERSON>l<PERSON><PERSON>", "introduction": "Introdução", "openingLine": "Apresentação", "startChat": "Iniciar conversa", "improveAffection": "Aumentar a afinidade, desbloquear selfies exclusivos", "receiveSelfiesHere": "As selfies recebidas serão salvas aqui", "selfieUnlocked": "Desbloqueado selfies", "getSelfie": "Tira uma selfie", "purchaseCallTime": "Comprar tempo de ligação", "purchaseGift": "<PERSON><PERSON><PERSON> presente", "confirmDeleteCharacter": "Você tem certeza de que deseja excluir o personagem?", "agentProfile": "Perfil", "cancelCreate": "Cancelar a criação", "characterSetting": "Design do personagem", "creationMethod": "Método de criação", "quickCreate": "Criar rapidamente", "selectGender": "Escolha o gênero do personagem", "maleOption": "<PERSON><PERSON><PERSON><PERSON>", "femaleOption": "Feminino", "nonBinaryOption": "<PERSON><PERSON>", "setPublic": "Definir como público", "publicSettingWarning": "Se definido como público, não pode ser tornado privado", "personality": "Personalidade", "createAndStartChat": "Criar e começar a conversar", "advancedCreation": "Criação avançada", "customizeImage": "Imagem personalizada", "freeImage": "<PERSON><PERSON> gratuita", "enterNickname": "Digite o nicknome", "completeBackground": "Complete a história", "createCharacter": "Criar personagem", "creatingCharacter": "<PERSON><PERSON><PERSON>", "regenerateOption": "Regerar", "cropAvatar": "Recortar avatar", "continueCurrentSetting": "Continuar com a configuração atual?", "modifySettingNotice": "Você pode modificar a configuração e criar uma nova imagem, mas isso consumirá cristais adicionais", "savedInDrafts": "As imagens geradas serão salvas na caixa de rascunhos", "exitAICharacterCreation": "Sair da criação da IA", "draftsFolder": "Caixa de rascunhos", "useImage": "Usar a imagem", "voiceOption": "Voz", "tag": "Etiquetas", "presetSoundLibrary": "Biblioteca de vozes predefinidas", "characterSettingRequired": "Configuração do personagem (obrigatório)", "openingLineRequired": "Apresentação (obrigatório)", "backgroundStoryRequired": "História de fundo (obrigatório)", "characterIntroRequired": "Introdução do personagem (obrigatório)", "dialogueExampleOptional": "Exemplo de diálogo (opcional)", "tagRequired": "Etiquetas (obrigatório)", "clickToAddUserContent": "Clique para adicionar o conteúdo da conversa do usuário", "clickToAddAIResponses": "Clique para adicionar o conteúdo da resposta da IA", "customizeImageName": "Configuração detalhada do nome de imagem personalizado", "createCustomImage": "Criar figura personalizada", "createAI": "Criar entidade IA", "openingLineTips": "Por favor, insira um saudação", "choiceFigureTips": "Selecionar um novo avatar não manterá as configuraç<PERSON><PERSON> atuais", "selectFigureConfirm": "¿Selecionar um avatar novo?", "createAgain": "<PERSON><PERSON><PERSON><PERSON>", "inputContentTips": "Por favor, insira o conteúdo (até 300 caracteres)", "inputEmptyTips": "Entrada não pode estar vazia!", "sexChoice": "Seleção de gênero", "audioEmpty": "Não há áudio disponível", "template": "<PERSON><PERSON>", "selectRequest": "Por favor, selecione", "agentTemplateTips": "Por favor, selecione um modelo de IA", "abandonFigureConfirm": "Regenerar o avatar vai custar cristais novamente, você deseja abandonar a imagem atual do avatar?", "agentInfoTips": "Por favor, preen<PERSON> as informações da IA", "backgroundStoryTips": "Insira uma história de fundo", "characterIntroTips": "Insira uma breve introdução", "dialogueExample": "Exemplos de diálogo", "agentFeatureTips": "Por favor, preen<PERSON> as características da IA", "diyFigureSave": "Os avatares criados personalmente serão salvos aqui", "cancelCreateTips": "Cancelar a criação não manterá as configurações atuais", "cancelCreateConfirm": "Você deseja cancelar a criação atual?", "createAgentSuccess": "Criação de IA bem-sucedida", "characterSetTips": "<PERSON><PERSON><PERSON> as configuraç<PERSON><PERSON> do personagem", "tagEmptyTips": "Selecione o rótulo", "fansOption": "<PERSON><PERSON><PERSON><PERSON>", "likesOption": "<PERSON>urt<PERSON>", "followOption": "<PERSON><PERSON><PERSON>", "nicknameOption": "Nicknome", "genderOption": "<PERSON><PERSON><PERSON><PERSON>", "birthdaySelection": "Aniversário", "personalDescription": "Descrição pessoal", "youRoleChatPersonalDesc": "Seu personagem fará chat com você com base em sua descrição pessoal, você pode inserir seus preferências, coisas que você odeia e outras informações~", "selectBirthday": "Escolher o aniversário", "unfollowOption": "<PERSON><PERSON><PERSON>", "basicInfo": "<PERSON><PERSON>", "enterInviteCode": "Digite o código", "invalidInviteCode": "<PERSON><PERSON><PERSON>", "rewardAlreadyClaimed": "A recompensa já foi resgatada", "accountDeactivation": "Excluir conta", "deactivationTerms": "Termos de cancelamento", "deactivationTermsContent1": "1. <PERSON><PERSON> da conta e altere-a para a conta de login atual", "deactivationTermsContent2": "2. <PERSON><PERSON><PERSON> de sair com sucesso, você não poderá fazer login novamente.<PERSON><PERSON><PERSON>, usar ou restaurar a conta", "deactivationTermsContent3": "3. Se houver membros não expirados na conta e eles não forem usadosMoedas de ouro e diamantes serão compensados ​​juntos após o cancelamento bem-sucedido.Por favor, opere com cuidado", "deactivationTermsContent4": "4. Ap<PERSON> o logout bem-sucedido, suas informações pessoais e outros dadosO conteúdo será desconectado permanentemente e não poderá ser restaurado.", "deactivationTermsContent5": "5. Ap<PERSON> solicitar o cancelamento, a conta será retida por 15 dias.Se você fizer login novamente dentro de 15 dias, o logout será automaticamente cancelado.Se o cancelamento não for cancelado, a conta será automaticamente cancelada.", "termsAgreement": "Eu li e concordo com os termos acima", "accountVerification": "Verificação de conta", "otherVerificationOptions": "Outros métodos de verificação", "deactivationConfirmation": "Leia cuidadosamente e confirme novamente", "deactivationNotice1": "1. Após o cancelamento bem-sucedido, suas informações pessoais e outros dados serão cancelados permanentemente e não poderão ser recuperados.", "deactivationNotice2": "2. Após solicitar o cancelamento, sua conta será mantida por 15 dias. Se você fizer login novamente durante esse período, o cancelamento será automaticamente cancelado. Se você não cancelar o cancelamento em 15 dias, a conta será automaticamente cancelada.", "deactivationSuccessful": "Solicitação de cancelamento de conta bem-sucedida", "verificationSuccess": "Verificação bem-sucedida", "networkErrorRetry": "<PERSON><PERSON> de rede, por favor tente novamente mais tarde", "accountVerificationFailed": "A verificação da conta falhou, a conta de e-mail é inválida", "invalidEmailVerification": "Código de verificação inválido, por favor tente novamente mais tarde", "profile": "Perfil pessoal", "choosePreference": "Escolha sua preferência", "recommend": "Recomendado", "abandonAccountCancellation": "Renunciar ao cancelamento da conta", "abandonCancellation": "Renunciar ao cancelamento", "discardLogoutPrompt": "Sua conta enviou um pedido de cancelamento e será cancelada com sucesso em [时间]. Antes que o cancelamento tenha sucesso, se você precisar restaurar a conta, por favor selecione Cancelar Cancelamento para fazer login novamente, e seu pedido será cancelado. Se você não precisar cancelar o cancelamento, pode clicar em Fazer login com outra conta.", "focusonSuccess": "Seguido com sucesso~", "unfollowed": "Deixado de seguir", "fansAmount": "xxx seguidores", "rolesAmount": "xxx personagens", "emailFormatError": "Formato de e-mail incorreto", "inconsistentAccounts": "A conta verificada não é a conta atualmente logada", "inviteCode": "Código", "inviteAmount": "Convidou um total de xx pessoas", "termTips": "Leia os termos acima com atenção. Sua conta não pode ser restaurada após ser cancelada, portanto, escolha com cuidado.", "invitationLink": "Convite link", "day": "dias", "encounteredAlready": "Conheci UGenie há X dias", "seeMore": "Ver mais", "upgradePlanBenefits": "Plano de Assinatura", "activateNow": "<PERSON><PERSON><PERSON><PERSON>", "logoutInfo": "Loja <PERSON>", "orderHistory": "Ainda não é VIP", "noMembershipActivated": "Abrir para desfrutar dos benefícios VIP", "activateVIPBenefits": "Compre", "purchaseNow": "Centro de VIP", "superMembershipOffer": "Recarregue o SVIP e ganhe prêmios instantâneos", "dailyGiftForSuperMembers": "Apresente diária para SVIP", "chooseCombo": "Por favor, selecione um plano de membros", "NonMember": "Atualmente, nenhum plano de membros está ativo", "subscribe": "Assinatura", "weekSubscriptionDesc": "", "monthSubscriptionDesc": "", "maturity": "", "membershipCenter": "Confirmação de cancelamento de pedido", "orderCancellationConfirm": "Observe", "cancellationNotice": "Descontos reembolsáveis para este cancelamento de pedido", "cancellationRefundDetails": "Desconto de primeira compra para VIP", "ensureNoPaymentBeforeCancel": "Confirme que você não pagou antes de cancelar!", "cancelUnpaidOrder": "Eu não paguei, cancele o pedido imediatamente", "orderRecords": "Registro de pedidos", "orderType": "Tipo de pedido", "creationTime": "Hor<PERSON><PERSON>", "productName": "Nome do produto", "paymentMethod": "Método de pagamento", "actualPayment": "Pagamento real", "processingOrder": "Processando o pedido", "closedOrder": "<PERSON><PERSON><PERSON>", "cancelOrder": "Cancelar o pedido", "changeTime": "<PERSON><PERSON><PERSON><PERSON>", "records": "Registro", "quantity": "Quantidade", "awaitingOrderCompletion": "Aguardar a conclusão do pedido", "orderNumber": "Número do pedido", "paymentProcessing": "Pagando com esforço", "watiForPayment": "Devido a razões dos canais de pagamento, o pagamento pode levar de 5 a 10 minutos para ser processado, por favor, aguarde pacientemente.", "noMoreOrder": "Sem Registros", "rechargeSuccessful": "<PERSON><PERSON><PERSON> bem-sucedida", "cashRegister": "caixa de pagamento", "selectPaymentMethod": "Selecione a forma de pagamento", "selectPaymentWay": "Selecione a forma de pagamento", "paySuccess": "Pagamento bem sucedido", "payAmount": "Valor real pago", "selectPayCountry": "Selecione o país de pagamento", "immediatePayment": "Pague agora", "orderCompleted": "Pedido concluído", "payedOrder": "Pedido pago", "refundOrder": "Pedido reembolsado", "canceledOrder": "Pedido cancelado", "purchaseCancelled": "Compra cancelada", "buyFail": "Falha na compra", "taskForFreeGold": "Tarefas para obter mais ouros gr<PERSON>tis", "task": "<PERSON><PERSON><PERSON><PERSON>", "dailyTasks": "<PERSON><PERSON><PERSON><PERSON>", "specialTasks": "<PERSON><PERSON><PERSON><PERSON> especiais", "checkIn": "Check-in", "rewardClaimed": "Recebido", "goComplete": "<PERSON>r completar", "claimReward": "<PERSON><PERSON><PERSON>", "notAchieved": "Não alcan<PERSON>", "inviteFriends": "Convidar amigos", "currentInvitedCount": "Convidados atualmente", "taskAcquisition": "Aquisição de tarefas", "weeklyTasks": "<PERSON><PERSON><PERSON><PERSON>", "noAds": "Remover <PERSON>", "from": "a partir de", "unlockVipPower": "Acesse {vipNum} Recursos VIP", "subscribeNow": "<PERSON><PERSON><PERSON>", "waitAction": "Aguardando ação...", "moreCallTime": "<PERSON><PERSON>", "vipTips": "Ao clicar em assinar, você concorda com os termos de serviço. Você será cobrado e a assinatura será renovada automaticamente pelo mesmo preço e duração. Você pode cancelar sua assinatura a qualquer momento na Play Store. Este é um preço promocional por tempo limitado. Notificaremos você 30 dias antes do término do período promocional. Usuários que já compraram podem continuar a desfrutar da renovação ao preço promocional.", "onlineTips": "Desbloqueie a Experiência Completa", "onlineDesc": "Baixe o UGenie para mais diversão com chance de jogar sem anúncios!", "onlineJump": "<PERSON><PERSON>", "community": "Comunidade", "communityCrystal": "<PERSON><PERSON><PERSON>", "globalSoundSwitch": "Interruptor de Volume Global", "bgmSwitch": "Interruptor de Música de Fundo", "buyCrystal": "<PERSON><PERSON><PERSON>", "loadAdFail": "Falha ao carregar o anúncio. Tente novamente mais tarde.", "selectYourGender": "Escolha <PERSON>", "other": "Outro", "wayYouLike": "Seu Estilo de Interação Preferido", "selectYourPreference": "Selecione Sua Preferência", "preference1": "Conversa", "preference2": "<PERSON>o curto, <PERSON><PERSON>ç<PERSON>", "preference3": "História", "preference4": "Texto longo, Narração", "preference5": "Jogo", "preference6": "Regras, Estatísticas", "intimacyUpgrade": "Nível de intimidade atualizado para LV{level}", "contentGameUnlock": "Jogabilidade exclusiva de LV{level} desbloqueada"}