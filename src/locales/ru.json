{"welcomeToEros": "Добро пожаловать в UGenie!", "termsConfirmation": "Перед использованием сайта, пожалуйста, ознакомьтесь с приведенными ниже условиями.", "ageConfirmation": "Я 18 лет или старше.", "aiContentNotice": "Я разрешаю, что AI-содержимое на сайте является выымышленным.", "guestLogin": "Гостевой вход", "registrationLogin": "Регистрация/Вход", "emailLogin": "Вход в почту", "email": "Электронная почта", "getCaptcha": "Получение", "emailLoginOption": "Вход", "agreeToTerms": "Вход в систему означает согласие с Политикой конфиденциальности и Пользовательским соглашением.", "loginIssueContact": "Пожалуйста, свяжитесь с поддержкой, если не можете войти.", "enterEmail": "Пожалуйста, введите ваш адрес электронной почты.", "enterCaptcha": "Пожалуйста, введите код подтверждения.", "invalidEmailFormat": "Формат адреса электронной почты неправильный, пожалуйста, введите заново.", "captchaError": "Код подтверждения неправильный, пожалуйста, введите заново.", "captchaExpired": "Код подтверждения истек, пожалуйста, введите заново.", "accountSuspendedNotice": "Ваш аккаунт был приостановлен. Подробности можно узнать в центре поддержки или обратитесь в службу поддержки.", "logout": "Выход", "logoutOption": "Подтвердите выход", "confirmLogout": "После выхода вы не сможете общаться с ИИ, но вы можете войти на этот аккаунт", "readAndConfirmed": "Я прочитал и подтвердил", "privacyAgreement": "Политика конфиденциальности", "userAgreement": "Условия Использования", "unableLogin": "Не могу войти?", "contactCustomer": "Связаться со службой поддержки", "loginAndAgree": "Войдите, чтобы согласиться", "welcomeToUgenie": "Добро пожаловать в UGenie", "CheckTerms": "Пожалуйста, отметьте условия", "otherLoginWay": "Войти с другой учетной записью", "loadingFailed": "SDK Загрузка не удалась", "invalidSession": "Недействительная сессия, пожалуйста, войдите снова", "verificationCode": "Код подтверждения", "store": "Мага<PERSON>ин", "searchAgentName": "Искать персонажей", "unableConnect": "Невозможно подключиться к магазину", "searchHistory": "История поиска", "popularRoles": "Популярные персонажи", "noResultsFeedback": "Не находишь того, что хочешь? Нажми обратную связь", "emptySearchSuggestion": "Здесь пусто, попробуйте другой ключевой слова?", "creatorSelection": "Создатель", "chatNow": "Чат", "followedNotice": "Подписаны", "characterOption": "Перс<PERSON><PERSON><PERSON>", "search": "Поиск", "searchWantTips": "Не можете найти то, что хотите?", "tryUsingDifferentKeyword": "Измените ключевое слово и повторите попытку.", "language": "Язык", "helpFAQ": "Помощь FAQ", "feedbackOption": "Свяжитесь с нами", "selectNumber": "Выберите свой номер", "aiFilter": "Интеллектуальное отбор(entities)", "sortOptions": "Сортировка", "aiType": "Тип интеллектуальной сущности", "mostPopular": "Г<PERSON><PERSON><PERSON><PERSON><PERSON>", "latest": "Новый", "trending": "Популярный", "searchRoleOrCreator": "Поиск персонажей или создателей", "dynamicInteraction": "Опыт динамного взаимодействия с персонажами, сделает общение более ярким и интересным", "chatBasedOnPersona": "Персонажи будут общаться с вами на основе вашей настройки персонажа. Выберите карту вашей настройки персонажа, чтобы она/он лучше вас понял", "userCharacterOption": "Моя настройка персонажа", "roleSpeakingDuration": "Только речь персонажей потребляет это время", "repeatedClickNotice": "Вы многократно нажали, попробуйте позже", "exitImmersiveMode": "Выход из непривычного погружения", "myCreations": "Мои создания", "loginToView": "Войдите для просмотра", "createLover": "Создайте своего ловеера", "goCreate": "Перейти к созданию", "clickToSet": "Нажмите для установки", "retainCharacterNotice": "Хотите ли вы сохранить персонажа?", "dataClearWarning": "Все данные персонажа будут полностью удалены, пожалуйста, аккуратно", "clickToTouchSpace": "Войти в пространство контактов", "imageOption": "Внешность", "noAIsNotice": "Еще нет интеллектуальной сущности, нажмите + ниже, чтобы призвать интеллектуальную сущность~", "myImageOption": "Моя внешность", "editImage": "Редактировать внешность", "defaultVoicePlayback": "По умолчанию воспроизведение голоса", "captchaInvalidRetry": "Включить NSFW", "cost": "Тратить", "noHaveAi": "У другого нет ИИ", "moreCharacterTime": "Больше времени для речи персонажа", "noMoreImage": "В настоящее время больше нет настроек персонажа", "editFigure": "Редактировать настройки персонажа", "aiOnTheWay": "Интеллектуальная система еще на пути～", "searchWant": "Ищите то, что вы хотите...", "confirmDelFigure": "Вы уверены, что хотите удалить этого персонажа?", "personalDescriptionTips": "Ваш персонаж будет основан на вашем личном описании и ваших чатах, вы можете вводить свои предпочтения, нелюбимые вещи и другую информацию～", "confirmExitStory": "Вы уверены, что хотите выйти из текущей истории?", "interactWithHer": "Взаимодействовать с ней", "newerStoryName": "Возрождение", "flowerList": "Подарки", "popularityChart": "Популярность", "inputFeedback": "Пожалуйста, введите отзыв", "thankFeedback": "Спасибо за ваш отзыв", "fillInFeedback": "Оставить отзыв", "storyIntroduction": "Введение", "missionObjectives": "Цель", "missionRewards": "Награда", "leaderboard": "Таблица лидеров", "ugPhoneToLivco": "", "skipOption": "Пропустить", "genderInterest": "Ваш интерес к полу", "interests": "Интерес", "allOptions": "Все", "interestPreferences": "Интересные предпочтения", "confirmSelection": "Подтвердить", "cancelButton": "Отменить", "confirmButton": "Готово", "editOption": "Редактировать", "saveToLocal": "Сохранить локально", "addOption": "Добавить", "saveButton": "Сохранить", "emptySpaceNotice": "Здесь пусто", "nextStep": "Следующий шаг", "closeButton": "Закрыть", "uncompleted": "Незавершено", "completed": "Завершено", "return": "Возврат", "contactSupport": "Если у вас возникнут проблемы, пожалуйста, обратитесь в службу поддержки", "maxAmount": "Максимум", "purchaseOption": "Купить", "expand": "Развернуть", "collapse": "Свернуть", "none": "Нет", "deleting": "Удаление", "deleteSuccessful": "Успешное удаление", "download": "Загрузить", "owned": "Владеет", "notOwned": "Не владеет", "noData": "Нет данных", "nothing": "Ничего нет", "haveAnyQuestions": "Если у вас есть вопросы, пожалуйста", "minutes": "Минуты", "exit": "Выход", "micPermissionRequired": "UGenie нуждается в вашем разрешении для использования микрофона", "loading": "Загрузка", "clickFeedback": "Получить поддержку", "verify": "Проверка", "successfullySaved": "Сохранено", "uploadPictures": "Загрузка изображения", "setup": "Настройки", "noMore": "Нет больше", "noMoreContent": "Уже нет контента", "permissionRequire": "Заявка разрешения", "pullToRefresh": "Потяните вниз, чтобы обновить", "looseToRefresh": "Отпустите, чтобы обновить", "pressAgain": "Нажмите еще раз, чтобы выйти из приложения", "report": "жалоба", "generateSuccessfully": "Успешно создано", "generateStoryBook": "Создать", "copied": "Скопировано", "send": "", "saveSuccessfully": "", "saveFailed": "", "featureCoding": "Функция скоро будет", "getSuccessfully": "Получить успех", "feedback1": "Ошибка контента или неполный ответ", "feedback2": "Деликатный или порнографический контент", "feedback3": "Информация, связанная с насилием или ненавистью", "feedback4": "Неподходящая информация, касающаяся несовершеннолетних", "feedback5": "Беспокоящий контент", "inputFeedbackMessage": "Пожалуйста, введите проблему, с которой вы столкнулись", "submit": "Отправить", "feedbackSuccess": "Отправка успешна, спасибо за отзыв", "discoverUpdate": "Новая версия найдена!", "updateNow": "Обновить сейчас", "updateLater": "Пока не обновлять", "checkForUpdates": "Проверить обновления", "currentVersion": "Текущая версия", "latestVersion": "На данный момент последняя версия", "swipeToClear": "Смахните влево, чтобы очистить экран и войти в режим непривычного погружения в 3D", "triggerActions": "Нажмите на особые зоны, чтобы активировать действия!", "aiContentDisclaimer": "Все, что говорит ИИ, является вымыслом, пожалуйста, внимательно отличайте", "changeTopicSuggestion": "Пожалуйста, смените тему", "makeCall": "Позвонить", "startNewChat": "Перезагрузить", "startTyping": "Начать ввод", "resetCharacterNotice": "После перезагрузки ваша история чатов с персонажем будет очищена.", "newChatStartedNotice": "Новый разговор начался, новые сообщения выше не затрагиваются", "emptyContentVoicePlayback": "Вы начали новый разговор", "recognitionFailedRetry": "Содержание пустое, голос не может быть воспроизведен", "unclearAudioRepeat": "Распознавание не удалось, попробуйте снова", "voiceNotHeardNotice": "Я не понял, можете повторить?", "continueConversation": "Персонаж не может слышать ваш голос, и после отсчета разговор будет автоматически завершен", "exitOption": "Продолжить разговор", "clearAudioEnvironment": "Пожалуйста, поддерживайте чистую акустическую обстановку во время разговора", "purchaseCallDuration": "Пополните счет, чтобы получить больше времени для разговора персонажей", "hangUp": "Повесить трубку", "microphoneOn": "Включить микрофон", "showKeyboard": "Поднять клавиатуру", "remainingDuration": "Оставшееся время разговора", "exchangeDuration": "Получить больше времени разговора", "callDuration": "Длительность разговора", "unlockSelfie": "Разблокировать selfie", "setAsChatBackground": "Установить как фон чата", "previousChats": "Поговорили", "recordingPermissionError": "Невозможно получить разрешение на запись, пожалуйста, проверьте настройки", "recordingFailed": "Невозможно записать", "speechTooShort": "Время разговора слишком короткое", "recordingError": "Запись не удалась", "restarting": "Перезагрузка", "restartSuccessful": "Перезагрузка прошла успешно", "likeCancelled": "Лайк успешно отменен", "likeSuccessful": "Лайк успешно", "dislikeCancelled": "Нелайк успешно отменен", "dislikeSuccessful": "Нелайк успешно", "conversationProcessing": "Диалог выводится", "voiceTooShort": "Время голоса слишком короткое", "exitClearMode": "Выход из режима чистого экрана", "like": "Лайк", "dislike": "Нелайк", "copy": "Копировать", "releaseToCancelSend": "Отпустите, чтобы отменить отправку", "releaseToSendSwipeUpCancel": "Отпустите, чтобы отправить, проведите вверх, чтобы отменить", "holdToSpeak": "Удерживайте, чтобы говорить", "inUse": "Используется", "levelLocked": "Уровни не разблокированы", "levelUnlocked": "Уровни заблокированы", "modelLoading": "Загрузка модели", "waitToSend": "Пожалуйста, подождите, когда другой человек закончит говорить, прежде чем отправлять", "connecting": "Подключение", "microphoneOff": "Микрофон выключен", "sendingGift": "Дарение", "giftSent": "Дарение прошло успешно", "settingUp": "Настройка", "setupSuccessful": "Настройка прошла успешно", "picture": "Изображение", "message": "Сообщение", "intimacyUnlockFunc": "Улучшите эксклюзивные функции разблокировать благоприятность", "syncPhoneCallRecord": "Записи голосового звонка были синхронизированы.", "savePicture": "Сохранить изображение", "scanQRCode": "Отсканируйте QR-код, чтобы сыграть снова", "refuseGenerate": "Нет, спасибо", "rewardCongratulation": "История завершена, поздравляем с получением", "generateCard": "Получить карточку истории", "backToChat": "Вернуться в чат", "playAgain": "Играть снова", "myStoryBook": "Моя книга", "moreStories": "Больше историй", "recheckStoryRecord": "Вы можете просмотреть это снова в разделе «История» на «Панели инструментов страницы диалога»", "storyBookGenerateConfirm": "Хотите создать книгу?", "storyBookGenerateTips": "Повторное прохождение удалит текущие записи истории. Хотите создать книгу, чтобы сохранить эту запись?", "restartStory": "", "moreIntimacyToUnlock": "Повышайте уровень близости, чтобы разблокировать больше сюжетного контента", "story": "История", "freeFirst": "Первый бесплатно", "goToChat": "Перейти в чат", "syncGameMemory": "Эксклюзивная память игрового процесса Lv{level} синхронизирована", "clickToStory": "Щелкните по карте, чтобы открыть эксклюзивную историю", "upgradeToUnlockStory": "Повышайте уровень близости с агентом, чтобы разблокировать", "startNow": "Начать сейчас", "unableToSend": "Отправка недействительна", "freeInput": "Свободный ввод...", "selectOne": "Пожалуйста, выберите один", "shareContent": "Поделись агентом со своим другом!", "gameWin": "Победа в игре", "gameLose": "Провал в игре", "doubleRewards": "Двойные награды", "unlockByAds": "Разблокировать, просматривая рекламу", "rewardTip": "Поздравляем с получением награды", "unSupportRegion": "Эта функция не поддерживается в вашем регионе", "giftItems": "Подарки", "membershipBenefits": "Активируйте членство, чтобы получить больше кристаллов", "totalCrystalUsed": "Количество потраченных кристаллов", "giftReceivedNotice": "Получил ваш", "sendGift": "Дарить подарки", "itemPurchase": "Купить предметы", "unlockedFeatures": "Заблокировано", "purchaseOutfit": "Купить скинки", "unlock3DOutfit": "Разблокировать 3D скин", "outfits": "Наряды", "staticOutfit": "Статический наряд", "unlock3DOption": "Разблокировать 3D", "unlockOutfitCost": "Разблокировка наряда потребует кристаллов", "itemStore": "Мага<PERSON>ин предметов", "diamond": "Алмазы", "crystalChangeDetails": "Детализация изменения криста<PERSON>лов", "goldChangeDetails": "Детализация изменения золотых монет", "purchaseSkin": "Купить скин", "myCrystals": "Мои кристаллы", "crystalDetails": "Детализация криста<PERSON><PERSON>ов", "selectPurchaseItem": "Пожалуйста, выберите товар", "goldExchange": "Золотой монеты обмен", "exchangeOption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backpack": "Рюкзак", "noMoreCrystalDetails": "Нет деталей изменения криста<PERSON>лов", "noMoreGoldDetails": "Нет деталей изменения золотых монет", "goldDetails": "Детали золотых монет", "moreFreeCoins": "Больше бесплатных монет", "weeklyPurchaseLimit": "Этот продукт достиг еженедельного лимита покупки", "dailyPurchaseLimit": "Этот продукт достиг ежедневного лимита покупки", "purchaseSuccessful": "Покупка прошла успешно", "totalCoinsSpent": "Акумулированное потребление золотых монет", "free": "Бесплатно", "rechargeToUnlock": "Получить пополниванием", "remainingDiamonds": "Всего криста<PERSON><PERSON>ов", "exchangeCoins": "Обмен золотых монет", "redeemNow": "立刻兑换", "consumption": "Расход", "gold": "Золотые монеты", "crystal": "Кристаллы", "insufficientCrystals": "Недостаточно кристаллов", "exchangeSuccessful": "Погашение успешно", "unableFindProduct": "Товар не существует", "adsToCrystal": "Посмотрите рекламу, чтобы получить 50 кристаллов", "insufficientBalance": "Недостаточно средств, пополните счет", "currentLevel": "Текущий уровень", "increaseIntimacy": "Повысьте близость, чтобы разблокировать больше способов игры", "intimacyLevelUp": "Уровень близости", "giftBoostsIntimacy": "Дарить подарки, можно повысить близость~", "emptyDiscover": "Здесь пусто, перейдите на страницу обнаружения", "goDiscover": "Перейти к обнаружению", "msgDetail": "Сообщение деталей", "noMoreMsg": "Уже нет сообщений", "followedYour": "Отслеживание вас", "followedYouNotice": "Подписался на вас", "messageNotification": "Уведомление", "systemMessages": "Системные сообщения", "interactiveMessages": "Интерактивные сообщения", "reportFeedback": "Сообщить об обратной связи", "deleteRole": "Удалить персонажа", "aboutRole": "О Та", "photoAlbum": "Альбом", "introduction": "Краткое описание", "openingLine": "Вступительные слова", "startChat": "Диал<PERSON>ги", "improveAffection": "Повысьте благосклонность, чтобы разблокировать эксклюзивные selfie", "receiveSelfiesHere": "Полученные selfies будут сохранены здесь", "selfieUnlocked": "Разблокированные selfies", "getSelfie": "Получить selfie", "purchaseCallTime": "Купить время разговора", "purchaseGift": "Купить подарок", "confirmDeleteCharacter": "Вы уверены, что хотите удалить персонажа?", "agentProfile": "Профиль", "cancelCreate": "Отменить создание", "characterSetting": "Перс<PERSON><PERSON><PERSON>", "creationMethod": "Создание", "quickCreate": "Быстрое создание", "selectGender": "Выбор пола персонажа", "maleOption": "Мужчина", "femaleOption": "Женщина", "nonBinaryOption": "бесполый", "setPublic": "Опубликовать", "publicSettingWarning": "Если опубликовать, то невозможно сделать частным", "personality": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createAndStartChat": "Создайте и начните общение", "advancedCreation": "Расширенное создание", "customizeImage": "Пользовательская графика", "freeImage": "Бесплатная графика", "enterNickname": "Введите псевдоним", "completeBackground": "Добавьте историю", "createCharacter": "Создание персонажа", "creatingCharacter": "Создание", "regenerateOption": "Перегенерировать", "cropAvatar": "Обрезать аватар", "continueCurrentSetting": "Продолжить с текущими настройками?", "modifySettingNotice": "Вы можете изменить настройки и создать новую картинку, но это потребует дополнительных кристаллов", "savedInDrafts": "Созданные изображения будут сохранены в корзине", "exitAICharacterCreation": "Выход из создания интеллектуальной сущности", "draftsFolder": "Черновики", "useImage": "Использовать изображение", "voiceOption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tag": "<PERSON><PERSON><PERSON><PERSON>", "presetSoundLibrary": "Предустановленная голосовая библиотека", "characterSettingRequired": "Персонаж (обязательно)", "openingLineRequired": "Участок (обязательно)", "backgroundStoryRequired": "Бэк-история (обязательно)", "characterIntroRequired": "Персонаж (обязательно)", "dialogueExampleOptional": "Диалог (опционально)", "tagRequired": "Тегi (обязательно)", "clickToAddUserContent": "Нажмите, чтобы добавить содержание диалога пользователя", "clickToAddAIResponses": "Нажмите, чтобы добавить содержание AI-ответа", "customizeImageName": "-", "createCustomImage": "Создать персональный аватар", "createAI": "Создать интеллектуальную сущность", "openingLineTips": "Пожалуйста, введите приветствие", "choiceFigureTips": "Перевыбор аватара не сохранит текущие настройки", "selectFigureConfirm": "Перевыбрать аватар?", "createAgain": "Пересоздать", "inputContentTips": "Пожалуйста, введите содержание (до 300 символов)", "inputEmptyTips": "Ввод не может быть пустым!", "sexChoice": "Выбор пола", "audioEmpty": "Нет аудио", "template": "Шабл<PERSON>н", "selectRequest": "Пожалуйста, выберите", "agentTemplateTips": "Пожалуйста, выберите шаблон ИИ", "abandonFigureConfirm": "Перегенерация аватара снова потребует кристаллов, хотите ли вы отказаться от текущего изображения аватара?", "agentInfoTips": "Пожалуйста, заполните информацию об ИИ", "backgroundStoryTips": "Пожалуйста, введите историю", "characterIntroTips": "Пожалуйста, введите короткое представление", "dialogueExample": "Диалоговые примеры", "agentFeatureTips": "Пожалуйста, заполните характеристики ИИ", "diyFigureSave": "Персонально созданные аватары будут сохранены здесь", "cancelCreateTips": "Отмена создания не сохранит текущие настройки", "cancelCreateConfirm": "Вы хотите отменить текущее создание?", "createAgentSuccess": "Создание ИИ прошло успешно", "characterSetTips": "Пожалуйста, введите настройки персонажа", "tagEmptyTips": "Пожалуйста, выберите этикетку", "fansOption": "Фанаты", "likesOption": "Лайки", "followOption": "Подписаться", "nicknameOption": "Ник", "genderOption": "Пол", "birthdaySelection": "День рождения", "personalDescription": "Личное описание", "youRoleChatPersonalDesc": "Ваш персонаж будет общаться с вами на основе вашего личного описания, вы можете вводить ваши предпочтения, вещи, которые вы ненавидите, и другую информацию~", "selectBirthday": "Выбор дня рождения", "unfollowOption": "Отписаться", "basicInfo": "Мой профиль", "enterInviteCode": "Введите код", "invalidInviteCode": "Код не существует", "rewardAlreadyClaimed": "Премия уже получена", "accountDeactivation": "Удалить аккаунт", "deactivationTerms": "Условия отмены", "deactivationTermsContent1": "1. Учетная запись отменяется для текущей учетной записи.", "deactivationTermsContent2": "2. После успешной отмены вы больше не сможете войти в систему, активировать, использовать или восстановить эту учетную запись.", "deactivationTermsContent3": "3. Если в учетной записи есть неиспользованные членские взносы, золотые монеты и бриллианты, они будут удалены при успешной отмене. Пожалуйста, действуйте осторожно.", "deactivationTermsContent4": "4. После успешной отмены ваши личные данные и другие данные будут удалены навсегда и не могут быть восстановлены.", "deactivationTermsContent5": "5. После запроса отмены ваша учетная запись будет храниться в течение 15 дней. Если вы входите в систему снова в течение этого периода, отмена будет автоматически отменена. Если вы не отмените отмену в течение 15 дней, учетная запись будет автоматически отменена.", "termsAgreement": "Я ознакомлен и согласен с вышеуказанными условиями", "accountVerification": "Учетная запись верификации", "otherVerificationOptions": "Другие способы проверки", "deactivationConfirmation": "Пожалуйста, внимательно прочитайте и подтвердите снова", "deactivationNotice1": "1. После успешной отмены ваши личные данные и другие данные будут навсегда отменены и не могут быть восстановлены.", "deactivationNotice2": "2. После запроса на отмену ваша учетная запись будет сохранена в течение 15 дней. Если вы снова войдете в систему в течение этого времени, отмена будет автоматически отменена. Если отмену не отмените в течение 15 дней, учетная запись будет автоматически отменена.", "deactivationSuccessful": "Успешная отмена учетной записи", "verificationSuccess": "Успешная верификация", "networkErrorRetry": "Сеть неисправна, пожалуйста, попробуйте позже", "accountVerificationFailed": "Неудачная верификация аккаунта, недействительный адрес электронной почты", "invalidEmailVerification": "Неверный код подтверждения, пожалуйста, попробуйте позже", "profile": "Личный профиль", "choosePreference": "Выберите ваше предпочтение", "recommend": "Рекомендуется", "abandonAccountCancellation": "Отказаться от отмены учетной записи", "abandonCancellation": "Отказаться от отмены", "discardLogoutPrompt": "Ваша учетная запись подала заявку на отмену и будет успешно отменена в [时间]. Перед тем как отмена будет успешной, если вам нужно восстановить аккаунт, пожалуйста, выберите Отменить отмену, чтобы снова войти в аккаунт, и ваш запрос будет отменен. Если вам не нужно отменять отмену, вы можете нажать Войти с другой учетной записью.", "focusonSuccess": "Подписка выполнена успешно~", "unfollowed": "Отписаться от подписки", "fansAmount": "xxx под<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ов", "rolesAmount": "xxx персона<PERSON>ей", "emailFormatError": "Неправильный формат электронной почты", "inconsistentAccounts": "Проверенная учетная запись не является текущей учетной записью, с которой вы вошли", "inviteCode": "<PERSON>од", "inviteAmount": "Пригласили в общем xx человек", "termTips": "Пожалуйста, внимательно прочтите приведенные выше условия. Ваша учетная запись не может быть восстановлена ​​после ее закрытия, поэтому выбирайте внимательно.", "invitationLink": "Пригласить ссылку", "day": "дни", "encounteredAlready": "Встречаюсь с UGenie уже X дней", "seeMore": "Посмотреть больше", "upgradePlanBenefits": "Подписка", "activateNow": "Обновление", "logoutInfo": "Магазин для членов", "orderHistory": "Еще не член", "noMembershipActivated": "Получите преимущества VIP после регистрации", "activateVIPBenefits": "Купить сейчас", "purchaseNow": "Центр <PERSON><PERSON><PERSON><PERSON>ов", "superMembershipOffer": "Мгновенная скидка на пополнение суперчленства", "dailyGiftForSuperMembers": "Ежедневный подарок супер-членам", "chooseCombo": "Пожалуйста, выберите план членства", "NonMember": "В настоящее время нет активной подписки", "subscribe": "Подписка", "weekSubscriptionDesc": "", "monthSubscriptionDesc": "", "maturity": "", "membershipCenter": "Подтверждение отмены заказа", "orderCancellationConfirm": "Обратите внимание", "cancellationNotice": "Скидки на возврат при отмене заказа", "cancellationRefundDetails": "Условия для первого заказа участника", "ensureNoPaymentBeforeCancel": "Пожалуйста, подтвердите, что вы еще не оплатили, прежде чем отменять!", "cancelUnpaidOrder": "Я не оплатил, пожалуйста, немедленно отмените заказ", "orderRecords": "История заказов", "orderType": "Тип заказа", "creationTime": "Время создания", "productName": "Название продукта", "paymentMethod": "Способ оплаты", "actualPayment": "Фактическая оплата", "processingOrder": "Обработка заказа", "closedOrder": "Закрыто", "cancelOrder": "Отменить заказ", "changeTime": "Время изменения", "records": "Запись", "quantity": "Количество", "awaitingOrderCompletion": "Подождите завершения заказа", "orderNumber": "Номер заказа", "paymentProcessing": "Усердно платить", "watiForPayment": "Из-за причин, связанных с каналами оплаты, обработка платежа может занять 5-10 минут, пожалуйста, подождите стерпеливо.", "noMoreOrder": "Нет записей", "rechargeSuccessful": "Пополнение прошло успешно", "cashRegister": "Касса", "selectPaymentMethod": "Пожалуйста, выберите способ оплаты", "selectPaymentWay": "Пожалуйста, выберите способ оплаты", "paySuccess": "Платеж успешен", "payAmount": "Фактическая выплаченная сумма", "selectPayCountry": "Выберите страну платежа", "immediatePayment": "Оплатить сейчас", "orderCompleted": "Заказ выполнен", "payedOrder": "Заказ оплачен", "refundOrder": "Заказ возвращен.", "canceledOrder": "Заказ отменен", "purchaseCancelled": "Покупка отменена", "buyFail": "Покупка не удалась", "taskForFreeGold": "Заработать больше бесплатных золотых монет за задания", "task": "Задания", "dailyTasks": "Ежедневные задания", "specialTasks": "Специальные задания", "checkIn": "Проверка", "rewardClaimed": "Получено", "goComplete": "Выполнить", "claimReward": "Забрать", "notAchieved": "Не достигнуто", "inviteFriends": "Пригласить друзей", "currentInvitedCount": "В настоящее время приглашено", "taskAcquisition": "Получение задачи", "weeklyTasks": "Еженедельные задачи", "noAds": "Убрать Рекламу", "from": "от", "unlockVipPower": "Доступ к {vipNum} VIP-функциям", "subscribeNow": "Подписаться", "waitAction": "Ожидание действия...", "moreCallTime": "Больше времени", "vipTips": "Нажимая «Подписаться», вы соглашаетесь с условиями обслуживания. С вас будет взиматься плата, и подписка будет автоматически продлеваться по той же цене и на тот же срок. Вы можете отменить подписку в любое время в Play Store. Это акционная цена на ограниченное время. Мы уведомим вас за 30 дней до окончания акционного периода. Существующие покупатели могут продолжать пользоваться продлением по акционной цене.", "onlineTips": "Разблокируй Полный Опыт", "onlineDesc": "Скачай UGenie для большего веселья с шансом играть без рекламы!", "onlineJump": "Перейти Сейчас", "community": "Сообщество", "communityCrystal": "Отправить Кристаллы", "globalSoundSwitch": "Глобальный переключатель громкости", "bgmSwitch": "Переключатель фоновой музыки", "buyCrystal": "Ку<PERSON>и<PERSON>ь Кристаллы", "loadAdFail": "Не удалось загрузить рекламу. Повторите попытку позже.", "selectYourGender": "Выберите Ваш Пол", "other": "Другое", "wayYouLike": "Ваш Предпочитаемый Стиль Взаимодействия", "selectYourPreference": "Выберите Ваши Предпочтения", "preference1": "Чат", "preference2": "Короткий текст, Без вариантов", "preference3": "История", "preference4": "Длинный текст, Повествование", "preference5": "Игра", "preference6": "Правила, Статистика", "intimacyUpgrade": "Уровень близости повышен до LV{level}", "contentGameUnlock": "Разблокирован эксклюзивный геймплей LV{level}"}