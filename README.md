# 组件文档

## useModal 函数文档

* 参数
  options: UseModalOptions 类型的对象，包含以下属性：
  message: string - Modal的消息内容。
  duration: number (可选, 默认值为 1000) - Modal的显示持续时间（毫秒）。
  autoClose: boolean (可选, 默认值为 true) - 是否在显示持续时间结束后自动关闭Modal。
  onClose: Function (可选) - Modal关闭时的回调函数。
  loading: boolean (可选, 默认值为 false) - 是否显示加载中的状态。
* 返回值
  如果 autoClose 为 false，则返回一个对象 { close: closeModal }，其中 closeModal 是一个函数，用于手动关闭Modal。
* 关闭逻辑:
  如果 autoClose 为 false，则返回一个包含 closeModal 函数的对象，以便外部调用。
* 示例用法

```
  // loading modal
  const loadingModal = useModal({
    message: '数据加载中',
    autoClose: false,
    loading: true
  })
  
  // 当需要手动关闭Modal时
  showModal.close()
  
  //信息modal
  useModal({
    message: '操作成功',
    duration: 1000,
    autoClose: true
  })
```

* 注意事项
  如果 autoClose 设置为 false，则不会自动关闭Modal，需要通过返回的 close 方法手动关闭。
  如果 onClose 回调函数被定义，当Modal关闭时会执行该回调。
  loading 属性用于控制Modal内部是否显示加载状态，loading为true时，autoClose不应为true。
  autoClose为false时，duration无效

## useConfirm 函数文档

* 参数
  options: UseConfirmOptions 类型的对象，包含以下属性：
  message: string - Confirm的消息内容。
  title: string - Confirm的消息内容。
  cancelText: string - 取消按钮文字
  confirmText: string - 确定按钮文字
  onClose: Function (可选) -点击Confirm关闭时的回调函数。
  onConfirm: Function (可选) -点击Confirm确定时的回调函数。
* 示例用法

```

 useConfirm({
    content: '确定删除角色吗？',
    title: '删除角色',
    cancelText: '取消',
    confirmText: '确定',
    onConfirm: () => {
    console.log('confirm')
   }
 })
```

注：复杂弹窗自行编写

## SoundPlayer 组件文档

* 概述
  SoundPlayer组件用于播放音频文件，**仅限使用链接的音频**。该组件支持预加载音频元数据、播放、停止。组件提供了多个插槽绑定的方法和状态变量，方便外部使用。
* 属性
  soundUrl: string - 必传，音频文件的URL。
* 插槽数据
  isPlaying: ref<boolean> - 当前音频是否正在播放。
  sound: ref<Howl | null> - Howl 实例。
  duration: ref<number> - 音频的持续时间（秒）。
  loading: ref<boolean> - 音频是否正在加载。
* 插槽方法
  play: () => void - 播放音频。
  stop: () => void - 停止音频。
* 在默认插槽自己编写内容与样式，默认内容仅占位

## SoundLoading 组件文档

* 概述
  纯样式组件，显示音频播放动画，高度自适应

## CrystalBalance 组件文档

* 概述
  显示已拥有钻石与充值入口
* 属性
  banlance: number - 必传，当前剩余钻石

## v-scroll-gradation-bg 指令文档

* 指令加在需要渐变背景色的元素上
* scrollElement, completedHeight, color, startHeight分别为滚动容器、渐变完成高度、开始渐变高度和背景色，默认滚动容器为绑定的元素本身，默认渐变完成高度为50px，默认开始高度为0，默认背景色为rgba(18,11,15,1)
* 使用时与ui沟通好渐变完成高度(渐变开始高度)，绑定元素即可，参考pages/AgentMessage/[id].vue

# Vue 3 + TypeScript + Vite

This template should help get you started developing with Vue 3 and TypeScript in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

## Recommended Setup

- [VS Code](https://code.visualstudio.com/) + [Vue - Official](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (previously Volar) and disable Vetur
- Use [vue-tsc](https://github.com/vuejs/language-tools/tree/master/packages/tsc) for performing the same type checking from the command line, or for generating d.ts files for SFCs.

## 注意事项

- ### 所有vue文件都使用大驼峰PascalCase格式命名， 此为官方推荐，具体请看[vue3官网](https://cn.vuejs.org/guide/components/registration.html#component-name-casing)
- ### 数字、字符串常量请写在src/constants文件夹下，枚举写在src/enums文件夹下，方便管理
- ### 在api文件夹内新建文件夹分类管理接口
- ### types文件夹内有常用类型，可新增扩展
  
# 禁止在主分支直接修改代码的操作！！

## Features

- ⚡️ [Vue 3](https://github.com/vuejs/core), [Vite 5](https://github.com/vitejs/vite), [pnpm](https://pnpm.io/), [esbuild](https://github.com/evanw/esbuild) - 就是快！
- 🗂 [基于文件的路由(Unplugin Vue Router)](https://uvr.esm.is/)
- 📦 [组件自动化加载](./src/components)
- 🍍 [使用 Pinia 的状态管理](https://pinia.vuejs.org)
- 🌍 [I18n 国际化开箱即用](./src/locales)
- 📥 [API 自动加载](https://github.com/antfu/unplugin-auto-import) - 直接使用 Composition API 无需引入
- 💪 TypeScript, 当然
- 💾 [本地数据模拟(Vite-plugin-mock-dev-server)](https://vite-plugin-mock-dev-server.netlify.app/)的支持
- 🌈 Git [hooks](./.husky) - 提交代码 eslint，stylelint, prettier 检测 和 提交规范检测
- 🪶 [Vant](https://github.com/youzan/vant) - 移动端 Vue 组件库
- 🔭 [vConsole](https://github.com/vadxq/vite-plugin-vconsole) - 移动端网页开发工具
- 📱 浏览器适配 - 使用 viewport vw/vh 单位布局
- 🌓 支持深色模式

### UI 框架

- [Vant](https://github.com/youzan/vant) - 移动端 Vue 组件库
  - [`vant-use`](https://github.com/youzan/vant/tree/main/packages/vant-use) - Vant 内置的组合式 API

### 插件

- Vue Router [unplugin-vue-router](https://github.com/posva/unplugin-vue-router) - 以文件系统为基础的路由
- [Pinia](https://pinia.vuejs.org) - 直接的, 类型安全的, 使用 Composition API 的轻便灵活的 Vue 状态管理库
- [Vue I18n](https://github.com/intlify/vue-i18n-next) - 国际化
  - [`unplugin-vue-i18n`](https://github.com/intlify/bundle-tools/tree/main/packages/unplugin-vue-i18n) - Vue I18n 的 Vite 插件
- [unplugin-vue-components](https://github.com/antfu/unplugin-vue-components) - 自动加载组件
- [unplugin-auto-import](https://github.com/antfu/unplugin-auto-import) - 直接使用 Composition API 等，无需导入
- [vite-plugin-vconsole](https://github.com/vadxq/vite-plugin-vconsole) - vConsole 的 vite 插件
- [vite-plugin-mock-dev-server](https://github.com/pengzhanbo/vite-plugin-mock-dev-server) - vite mock 开发服务（mock-dev-server）插件
- [postcss-mobile-forever](https://github.com/wswmsword/postcss-mobile-forever) - 一款 PostCSS 插件，将固定尺寸的移动端视图转为具有最大宽度的可伸缩的移动端视图
- [vite-plugin-vue-devtools](https://github.com/vuejs/devtools-next) - 旨在增强Vue开发者体验的Vite插件
- [vueuse](https://github.com/antfu/vueuse) - 实用的 Composition API 工具合集
- [vite-plugin-sitemap](https://github.com/jbaubree/vite-plugin-sitemap) - sitemap 和 robots 生成器

### 开发工具

- [TypeScript](https://www.typescriptlang.org/)
- [pnpm](https://pnpm.js.org/) - 快, 节省磁盘空间的包管理器

### 目录结构

```
  build
  ├── vite
    └── index.ts //插件配置
    └── vconsole.ts //vconsole配置
  mock
  ├── modules
    └── ****.mock.ts 数据配置
  ├──data.ts
  └── index.ts
  public
  src
  │  typed-router.d.ts //路由插件自动生成，禁止修改
  ├─api // 接口配置
  ├─assets
  │  └─styles
  │          common.scss // 常用样式
  │          index.scss
  │          variables.scss // 变量
  ├─components // 自动加载组件
  |
  ├─constant // 常量
  │  
  ├─composables
  │      dark.ts // 暗黑模式
  ├─enums // 枚举
  │  httpEnum.ts
  ├─locales // 国际化
  │      en-US.json
  │      zh-CN.json
  └─pages // 页面，该文件夹下所有文件都会自动注册路由,/ 对应 index.vue，/User 对应 User/index.vue，具体请看上方文档
      │  index.vue
      │  [...all].vue
  ├─router // 路由
  │      index.ts
  ├─stores // 状态管理
  ├─types // 类型
  ├─utils
  │      auth.ts
  │      i18n.ts
  │      request.ts
  │      service.ts // 封装请求
```
